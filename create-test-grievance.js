/**
 * Create Test Grievance with Status History
 * Simple script to create a test grievance and add status changes
 */

import axios from 'axios';

const log = (message, type = 'info') => {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    error: '\x1b[31m',
    warning: '\x1b[33m',
    header: '\x1b[35m\x1b[1m'
  };
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[type]}[${timestamp}] ${message}\x1b[0m`);
};

const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

async function createTestGrievance() {
  log('🚀 CREATING TEST GRIEVANCE WITH STATUS HISTORY', 'header');
  console.log('='.repeat(80));

  try {
    // Step 1: Create admin user if needed, then authenticate
    log('1️⃣ Setting up admin user...', 'info');

    // Try to register admin user first
    try {
      await apiClient.post('/auth/register', {
        gmail: '<EMAIL>',
        password: 'AdminPassword123!',
        fullName: 'Admin User',
        mobile: '9999999999',
        role: 'admin'
      });
      log('✅ Admin user created', 'success');
    } catch (regError) {
      if (regError.response?.data?.error === 'DUPLICATE_EMAIL') {
        log('⚠️  Admin user already exists', 'warning');
      } else {
        log('⚠️  Admin registration failed, trying with existing user', 'warning');
      }
    }

    // Now authenticate
    log('🔑 Authenticating as admin...', 'info');
    const loginResponse = await apiClient.post('/auth/login', {
      gmail: '<EMAIL>',
      password: 'AdminPassword123!'
    });

    if (!loginResponse.data.success) {
      throw new Error('Authentication failed');
    }

    const authToken = loginResponse.data.data.accessToken;
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
    log('✅ Authentication successful', 'success');

    // Step 2: Create a test grievance
    log('2️⃣ Creating test grievance...', 'info');
    const grievanceData = {
      title: 'Timeline Test - Street Light Not Working',
      description: 'This is a test grievance to verify timeline functionality. The street light has been malfunctioning for several days and needs immediate attention.',
      category: 'infrastructure',
      priority: 'high',
      location: {
        address: '123 Timeline Test Street',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456',
        coordinates: {
          latitude: 40.7128,
          longitude: -74.0060
        }
      },
      tags: ['timeline-test', 'street-light', 'urgent'],
      isPublic: true,
      isAnonymous: false
    };

    const createResponse = await apiClient.post('/grievances', grievanceData);
    
    if (!createResponse.data.success) {
      throw new Error('Grievance creation failed');
    }

    const grievanceId = createResponse.data.data.grievance._id;
    const referenceId = createResponse.data.data.grievance.referenceId;
    log(`✅ Grievance created: ${referenceId} (${grievanceId})`, 'success');

    // Step 3: Add status changes to create timeline history
    log('3️⃣ Adding status changes to create timeline history...', 'info');
    
    const statusChanges = [
      { status: 'pending', reason: 'Moving to initial review for assessment' },
      { status: 'desk_1', reason: 'Assigned to technical team for verification' },
      { status: 'in_progress', reason: 'Field team dispatched to investigate the issue' },
      { status: 'resolved', reason: 'Street light repaired and tested successfully' }
    ];

    for (let i = 0; i < statusChanges.length; i++) {
      const change = statusChanges[i];
      
      log(`   📝 Changing status to: ${change.status}`, 'info');
      
      const statusResponse = await apiClient.patch(`/grievances/${grievanceId}/status`, {
        status: change.status,
        reason: change.reason
      });

      if (statusResponse.data.success) {
        log(`   ✅ Status changed to: ${change.status}`, 'success');
      } else {
        log(`   ❌ Failed to change status to: ${change.status}`, 'error');
      }

      // Add delay between status changes
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Step 4: Verify the final grievance data
    log('4️⃣ Verifying final grievance data...', 'info');
    const finalResponse = await apiClient.get(`/grievances/${grievanceId}`);
    
    if (finalResponse.data.success && finalResponse.data.data.grievance) {
      const grievance = finalResponse.data.data.grievance;
      
      log('📊 Final Grievance Data:', 'info');
      console.log(`   ID: ${grievance._id}`);
      console.log(`   Reference: ${grievance.referenceId}`);
      console.log(`   Current Status: ${grievance.status}`);
      console.log(`   Status History Entries: ${grievance.statusHistory?.length || 0}`);
      
      if (grievance.statusHistory && grievance.statusHistory.length > 0) {
        log('📜 Status History:', 'info');
        grievance.statusHistory.forEach((entry, index) => {
          console.log(`   ${index + 1}. ${entry.status} - ${entry.reason} (${entry.changedAt})`);
        });
        log('✅ Test grievance with timeline history created successfully!', 'success');
      } else {
        log('❌ No status history found in grievance', 'error');
      }
    }

    log('🎯 TEST GRIEVANCE CREATION COMPLETE', 'header');
    log(`🔗 You can now test the timeline by viewing grievance: ${referenceId}`, 'info');
    
  } catch (error) {
    log(`❌ CRITICAL ERROR: ${error.message}`, 'error');
    console.error('Full error:', error.response?.data || error);
  }
}

// Run the script
createTestGrievance();
