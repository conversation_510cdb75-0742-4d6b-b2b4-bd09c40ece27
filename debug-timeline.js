/**
 * Debug Timeline Service Issues
 * Quick test to identify the root cause of the timeline errors
 */

console.log('🔍 Starting Timeline Service Debug...\n');

// Test data that might be causing the issue
const testGrievance = {
  _id: '68790ae5fd28f480c7b9b355',
  status: 'closed',
  priority: 'high',
  submittedAt: '2025-01-15T10:00:00Z',
  lastUpdatedAt: '2025-01-15T15:30:00Z',
  statusHistory: [
    {
      status: 'submitted',
      changedBy: 'system',
      changedAt: '2025-01-15T10:00:00Z',
      reason: 'Initial submission'
    },
    {
      status: 'pending',
      changedBy: { _id: '123', fullName: '<PERSON>', role: 'admin' },
      changedAt: '2025-01-15T11:00:00Z',
      reason: 'Under review'
    },
    {
      status: 'resolved',
      changedBy: { _id: '123', fullName: 'John Admin', role: 'admin' },
      changedAt: '2025-01-15T14:00:00Z',
      reason: 'Issue resolved'
    },
    {
      status: 'closed',
      changedBy: { _id: '123', fullName: '<PERSON>', role: 'admin' },
      changedAt: '2025-01-15T15:30:00Z',
      reason: 'Case closed'
    }
  ],
  previousTimelines: [],
  reopenCount: 0
};

console.log('📋 Test Grievance Data:');
console.log('- ID:', testGrievance._id);
console.log('- Status:', testGrievance.status);
console.log('- Priority:', testGrievance.priority);
console.log('- Status History Length:', testGrievance.statusHistory?.length || 0);
console.log('- Previous Timelines:', testGrievance.previousTimelines?.length || 0);

// Test edge cases that might cause undefined errors
const edgeCases = [
  {
    name: 'Empty Status History',
    data: { ...testGrievance, statusHistory: [] }
  },
  {
    name: 'Null Status History',
    data: { ...testGrievance, statusHistory: null }
  },
  {
    name: 'Undefined Status History',
    data: { ...testGrievance, statusHistory: undefined }
  },
  {
    name: 'Malformed Status History Entry',
    data: {
      ...testGrievance,
      statusHistory: [
        { status: 'submitted' }, // Missing required fields
        null, // Null entry
        undefined, // Undefined entry
        { status: 'closed', changedBy: null, changedAt: null }
      ]
    }
  }
];

console.log('\n🧪 Testing Edge Cases:');
edgeCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}:`);
  console.log('   - statusHistory type:', typeof testCase.data.statusHistory);
  console.log('   - statusHistory isArray:', Array.isArray(testCase.data.statusHistory));
  console.log('   - statusHistory length:', testCase.data.statusHistory?.length || 'N/A');
  
  if (Array.isArray(testCase.data.statusHistory)) {
    console.log('   - statusHistory entries:');
    testCase.data.statusHistory.forEach((entry, i) => {
      console.log(`     [${i}]:`, typeof entry, entry ? Object.keys(entry) : 'null/undefined');
    });
  }
});

console.log('\n🔍 Potential Issues Identified:');
console.log('1. statusHistory might be null/undefined when passed to calculateStepDuration');
console.log('2. statusHistory entries might be null/undefined');
console.log('3. findIndex might be called on a non-array value');
console.log('4. The error might be from cached/old code');

console.log('\n✅ Debug Complete - Check the actual timeline service for these patterns');
