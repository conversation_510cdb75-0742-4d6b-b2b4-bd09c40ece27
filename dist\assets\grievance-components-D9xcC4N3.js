import{r as f,j as o,a as nc,R as Ee,C as Gs,b as sc,c as xm,X as Wt,d as ot,T as br,e as qs,f as gn,g as Ct,h as Gt,M as xn,i as ie,S as bm,k as vm,l as Ks,B as ym,E as vr,m as wm,D as Ys,A as Er,F as Nm,U as _e,n as Xs,o as Nt,p as Ue,P as Sm,q as qt,s as km,G as jm,t as Cm,u as bn,v as qe,w as Je,x as Kt,y as oc,z as Fo,H as kt,I as $o,J as On,K as yr,L as Ma,N as La,O as vs,Q as ys,V as Tm,W as Em,Y as Rm,Z as _m,_ as Am,$ as tn,a0 as Fa,a1 as $a,a2 as Dm,a3 as Pm,a4 as Ua,a5 as Om,a6 as Im}from"./ui-vendor-CJ9vBRYM.js";import{g as Mm,a as Lm,r as Fm}from"./react-vendor-DavUf6mE.js";import{c as Uo}from"./state-vendor-CoPsor3D.js";const $m="modulepreload",Um=function(e){return"/"+e},Ba={},ac=function(t,r,n){let s=Promise.resolve();if(r&&r.length>0){let l=function(u){return Promise.all(u.map(d=>Promise.resolve(d).then(m=>({status:"fulfilled",value:m}),m=>({status:"rejected",reason:m}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),c=i?.nonce||i?.getAttribute("nonce");s=l(r.map(u=>{if(u=Um(u),u in Ba)return;Ba[u]=!0;const d=u.endsWith(".css"),m=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${m}`))return;const h=document.createElement("link");if(h.rel=d?"stylesheet":$m,d||(h.as="script"),h.crossOrigin="",h.href=u,c&&h.setAttribute("nonce",c),document.head.appendChild(h),d)return new Promise((x,g)=>{h.addEventListener("load",x),h.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${u}`)))})}))}function a(i){const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i}return s.then(i=>{for(const c of i||[])c.status==="rejected"&&a(c.reason);return t().catch(a)})};function Ha(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function In(...e){return t=>{let r=!1;const n=e.map(s=>{const a=Ha(s,t);return!r&&typeof a=="function"&&(r=!0),a});if(r)return()=>{for(let s=0;s<n.length;s++){const a=n[s];typeof a=="function"?a():Ha(e[s],null)}}}}function G(...e){return f.useCallback(In(...e),e)}function mt(e){const t=Hm(e),r=f.forwardRef((n,s)=>{const{children:a,...i}=n,c=f.Children.toArray(a),l=c.find(zm);if(l){const u=l.props.children,d=c.map(m=>m===l?f.Children.count(u)>1?f.Children.only(null):f.isValidElement(u)?u.props.children:null:m);return o.jsx(t,{...i,ref:s,children:f.isValidElement(u)?f.cloneElement(u,void 0,d):null})}return o.jsx(t,{...i,ref:s,children:a})});return r.displayName=`${e}.Slot`,r}var Bm=mt("Slot");function Hm(e){const t=f.forwardRef((r,n)=>{const{children:s,...a}=r;if(f.isValidElement(s)){const i=Gm(s),c=Wm(a,s.props);return s.type!==f.Fragment&&(c.ref=n?In(n,i):i),f.cloneElement(s,c)}return f.Children.count(s)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var ic=Symbol("radix.slottable");function Vm(e){const t=({children:r})=>o.jsx(o.Fragment,{children:r});return t.displayName=`${e}.Slottable`,t.__radixId=ic,t}function zm(e){return f.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===ic}function Wm(e,t){const r={...t};for(const n in t){const s=e[n],a=t[n];/^on[A-Z]/.test(n)?s&&a?r[n]=(...c)=>{const l=a(...c);return s(...c),l}:s&&(r[n]=s):n==="style"?r[n]={...s,...a}:n==="className"&&(r[n]=[s,a].filter(Boolean).join(" "))}return{...e,...r}}function Gm(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}function cc(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(r=cc(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function lc(){for(var e,t,r=0,n="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=cc(e))&&(n&&(n+=" "),n+=t);return n}const Va=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,za=lc,Bo=(e,t)=>r=>{var n;if(t?.variants==null)return za(e,r?.class,r?.className);const{variants:s,defaultVariants:a}=t,i=Object.keys(s).map(u=>{const d=r?.[u],m=a?.[u];if(d===null)return null;const h=Va(d)||Va(m);return s[u][h]}),c=r&&Object.entries(r).reduce((u,d)=>{let[m,h]=d;return h===void 0||(u[m]=h),u},{}),l=t==null||(n=t.compoundVariants)===null||n===void 0?void 0:n.reduce((u,d)=>{let{class:m,className:h,...x}=d;return Object.entries(x).every(g=>{let[p,b]=g;return Array.isArray(b)?b.includes({...a,...c}[p]):{...a,...c}[p]===b})?[...u,m,h]:u},[]);return za(e,i,l,r?.class,r?.className)},Ho="-",qm=e=>{const t=Ym(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:i=>{const c=i.split(Ho);return c[0]===""&&c.length!==1&&c.shift(),dc(c,t)||Km(i)},getConflictingClassGroupIds:(i,c)=>{const l=r[i]||[];return c&&n[i]?[...l,...n[i]]:l}}},dc=(e,t)=>{if(e.length===0)return t.classGroupId;const r=e[0],n=t.nextPart.get(r),s=n?dc(e.slice(1),n):void 0;if(s)return s;if(t.validators.length===0)return;const a=e.join(Ho);return t.validators.find(({validator:i})=>i(a))?.classGroupId},Wa=/^\[(.+)\]$/,Km=e=>{if(Wa.test(e)){const t=Wa.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},Ym=e=>{const{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(const s in r)Js(r[s],n,s,t);return n},Js=(e,t,r,n)=>{e.forEach(s=>{if(typeof s=="string"){const a=s===""?t:Ga(t,s);a.classGroupId=r;return}if(typeof s=="function"){if(Xm(s)){Js(s(n),t,r,n);return}t.validators.push({validator:s,classGroupId:r});return}Object.entries(s).forEach(([a,i])=>{Js(i,Ga(t,a),r,n)})})},Ga=(e,t)=>{let r=e;return t.split(Ho).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Xm=e=>e.isThemeGetter,Jm=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const s=(a,i)=>{r.set(a,i),t++,t>e&&(t=0,n=r,r=new Map)};return{get(a){let i=r.get(a);if(i!==void 0)return i;if((i=n.get(a))!==void 0)return s(a,i),i},set(a,i){r.has(a)?r.set(a,i):s(a,i)}}},Qs="!",Zs=":",Qm=Zs.length,Zm=e=>{const{prefix:t,experimentalParseClassName:r}=e;let n=s=>{const a=[];let i=0,c=0,l=0,u;for(let g=0;g<s.length;g++){let p=s[g];if(i===0&&c===0){if(p===Zs){a.push(s.slice(l,g)),l=g+Qm;continue}if(p==="/"){u=g;continue}}p==="["?i++:p==="]"?i--:p==="("?c++:p===")"&&c--}const d=a.length===0?s:s.substring(l),m=eh(d),h=m!==d,x=u&&u>l?u-l:void 0;return{modifiers:a,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:x}};if(t){const s=t+Zs,a=n;n=i=>i.startsWith(s)?a(i.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:i,maybePostfixModifierPosition:void 0}}if(r){const s=n;n=a=>r({className:a,parseClassName:s})}return n},eh=e=>e.endsWith(Qs)?e.substring(0,e.length-1):e.startsWith(Qs)?e.substring(1):e,th=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(n=>[n,!0]));return n=>{if(n.length<=1)return n;const s=[];let a=[];return n.forEach(i=>{i[0]==="["||t[i]?(s.push(...a.sort(),i),a=[]):a.push(i)}),s.push(...a.sort()),s}},rh=e=>({cache:Jm(e.cacheSize),parseClassName:Zm(e),sortModifiers:th(e),...qm(e)}),nh=/\s+/,sh=(e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:s,sortModifiers:a}=t,i=[],c=e.trim().split(nh);let l="";for(let u=c.length-1;u>=0;u-=1){const d=c[u],{isExternal:m,modifiers:h,hasImportantModifier:x,baseClassName:g,maybePostfixModifierPosition:p}=r(d);if(m){l=d+(l.length>0?" "+l:l);continue}let b=!!p,v=n(b?g.substring(0,p):g);if(!v){if(!b){l=d+(l.length>0?" "+l:l);continue}if(v=n(g),!v){l=d+(l.length>0?" "+l:l);continue}b=!1}const y=a(h).join(":"),w=x?y+Qs:y,S=w+v;if(i.includes(S))continue;i.push(S);const j=s(v,b);for(let D=0;D<j.length;++D){const E=j[D];i.push(w+E)}l=d+(l.length>0?" "+l:l)}return l};function oh(){let e=0,t,r,n="";for(;e<arguments.length;)(t=arguments[e++])&&(r=uc(t))&&(n&&(n+=" "),n+=r);return n}const uc=e=>{if(typeof e=="string")return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=uc(e[n]))&&(r&&(r+=" "),r+=t);return r};function ah(e,...t){let r,n,s,a=i;function i(l){const u=t.reduce((d,m)=>m(d),e());return r=rh(u),n=r.cache.get,s=r.cache.set,a=c,c(l)}function c(l){const u=n(l);if(u)return u;const d=sh(l,r);return s(l,d),d}return function(){return a(oh.apply(null,arguments))}}const me=e=>{const t=r=>r[e]||[];return t.isThemeGetter=!0,t},fc=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,mc=/^\((?:(\w[\w-]*):)?(.+)\)$/i,ih=/^\d+\/\d+$/,ch=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,lh=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,dh=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,uh=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,fh=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ot=e=>ih.test(e),Y=e=>!!e&&!Number.isNaN(Number(e)),ct=e=>!!e&&Number.isInteger(Number(e)),ws=e=>e.endsWith("%")&&Y(e.slice(0,-1)),nt=e=>ch.test(e),mh=()=>!0,hh=e=>lh.test(e)&&!dh.test(e),hc=()=>!1,ph=e=>uh.test(e),gh=e=>fh.test(e),xh=e=>!M(e)&&!L(e),bh=e=>Zt(e,xc,hc),M=e=>fc.test(e),wt=e=>Zt(e,bc,hh),Ns=e=>Zt(e,Sh,Y),qa=e=>Zt(e,pc,hc),vh=e=>Zt(e,gc,gh),Br=e=>Zt(e,vc,ph),L=e=>mc.test(e),dr=e=>er(e,bc),yh=e=>er(e,kh),Ka=e=>er(e,pc),wh=e=>er(e,xc),Nh=e=>er(e,gc),Hr=e=>er(e,vc,!0),Zt=(e,t,r)=>{const n=fc.exec(e);return n?n[1]?t(n[1]):r(n[2]):!1},er=(e,t,r=!1)=>{const n=mc.exec(e);return n?n[1]?t(n[1]):r:!1},pc=e=>e==="position"||e==="percentage",gc=e=>e==="image"||e==="url",xc=e=>e==="length"||e==="size"||e==="bg-size",bc=e=>e==="length",Sh=e=>e==="number",kh=e=>e==="family-name",vc=e=>e==="shadow",jh=()=>{const e=me("color"),t=me("font"),r=me("text"),n=me("font-weight"),s=me("tracking"),a=me("leading"),i=me("breakpoint"),c=me("container"),l=me("spacing"),u=me("radius"),d=me("shadow"),m=me("inset-shadow"),h=me("text-shadow"),x=me("drop-shadow"),g=me("blur"),p=me("perspective"),b=me("aspect"),v=me("ease"),y=me("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],j=()=>[...S(),L,M],D=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],T=()=>[L,M,l],N=()=>[Ot,"full","auto",...T()],_=()=>[ct,"none","subgrid",L,M],I=()=>["auto",{span:["full",ct,L,M]},ct,L,M],H=()=>[ct,"auto",L,M],A=()=>["auto","min","max","fr",L,M],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],V=()=>["start","end","center","stretch","center-safe","end-safe"],F=()=>["auto",...T()],z=()=>[Ot,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],C=()=>[e,L,M],R=()=>[...S(),Ka,qa,{position:[L,M]}],K=()=>["no-repeat",{repeat:["","x","y","space","round"]}],he=()=>["auto","cover","contain",wh,bh,{size:[L,M]}],Se=()=>[ws,dr,wt],se=()=>["","none","full",u,L,M],re=()=>["",Y,dr,wt],je=()=>["solid","dashed","dotted","double"],be=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],O=()=>[Y,ws,Ka,qa],ee=()=>["","none",g,L,M],le=()=>["none",Y,L,M],Z=()=>["none",Y,L,M],te=()=>[Y,L,M],oe=()=>[Ot,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[nt],breakpoint:[nt],color:[mh],container:[nt],"drop-shadow":[nt],ease:["in","out","in-out"],font:[xh],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[nt],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[nt],shadow:[nt],spacing:["px",Y],text:[nt],"text-shadow":[nt],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ot,M,L,b]}],container:["container"],columns:[{columns:[Y,M,L,c]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:j()}],overflow:[{overflow:D()}],"overflow-x":[{"overflow-x":D()}],"overflow-y":[{"overflow-y":D()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[ct,"auto",L,M]}],basis:[{basis:[Ot,"full","auto",c,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Y,Ot,"auto","initial","none",M]}],grow:[{grow:["",Y,L,M]}],shrink:[{shrink:["",Y,L,M]}],order:[{order:[ct,"first","last","none",L,M]}],"grid-cols":[{"grid-cols":_()}],"col-start-end":[{col:I()}],"col-start":[{"col-start":H()}],"col-end":[{"col-end":H()}],"grid-rows":[{"grid-rows":_()}],"row-start-end":[{row:I()}],"row-start":[{"row-start":H()}],"row-end":[{"row-end":H()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[...V(),"normal"]}],"justify-self":[{"justify-self":["auto",...V()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[...V(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...V(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[...V(),"baseline"]}],"place-self":[{"place-self":["auto",...V()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:F()}],mx:[{mx:F()}],my:[{my:F()}],ms:[{ms:F()}],me:[{me:F()}],mt:[{mt:F()}],mr:[{mr:F()}],mb:[{mb:F()}],ml:[{ml:F()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:z()}],w:[{w:[c,"screen",...z()]}],"min-w":[{"min-w":[c,"screen","none",...z()]}],"max-w":[{"max-w":[c,"screen","none","prose",{screen:[i]},...z()]}],h:[{h:["screen","lh",...z()]}],"min-h":[{"min-h":["screen","lh","none",...z()]}],"max-h":[{"max-h":["screen","lh",...z()]}],"font-size":[{text:["base",r,dr,wt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,L,Ns]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ws,M]}],"font-family":[{font:[yh,M,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,L,M]}],"line-clamp":[{"line-clamp":[Y,"none",L,Ns]}],leading:[{leading:[a,...T()]}],"list-image":[{"list-image":["none",L,M]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,M]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:C()}],"text-color":[{text:C()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...je(),"wavy"]}],"text-decoration-thickness":[{decoration:[Y,"from-font","auto",L,wt]}],"text-decoration-color":[{decoration:C()}],"underline-offset":[{"underline-offset":[Y,"auto",L,M]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,M]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,M]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:R()}],"bg-repeat":[{bg:K()}],"bg-size":[{bg:he()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ct,L,M],radial:["",L,M],conic:[ct,L,M]},Nh,vh]}],"bg-color":[{bg:C()}],"gradient-from-pos":[{from:Se()}],"gradient-via-pos":[{via:Se()}],"gradient-to-pos":[{to:Se()}],"gradient-from":[{from:C()}],"gradient-via":[{via:C()}],"gradient-to":[{to:C()}],rounded:[{rounded:se()}],"rounded-s":[{"rounded-s":se()}],"rounded-e":[{"rounded-e":se()}],"rounded-t":[{"rounded-t":se()}],"rounded-r":[{"rounded-r":se()}],"rounded-b":[{"rounded-b":se()}],"rounded-l":[{"rounded-l":se()}],"rounded-ss":[{"rounded-ss":se()}],"rounded-se":[{"rounded-se":se()}],"rounded-ee":[{"rounded-ee":se()}],"rounded-es":[{"rounded-es":se()}],"rounded-tl":[{"rounded-tl":se()}],"rounded-tr":[{"rounded-tr":se()}],"rounded-br":[{"rounded-br":se()}],"rounded-bl":[{"rounded-bl":se()}],"border-w":[{border:re()}],"border-w-x":[{"border-x":re()}],"border-w-y":[{"border-y":re()}],"border-w-s":[{"border-s":re()}],"border-w-e":[{"border-e":re()}],"border-w-t":[{"border-t":re()}],"border-w-r":[{"border-r":re()}],"border-w-b":[{"border-b":re()}],"border-w-l":[{"border-l":re()}],"divide-x":[{"divide-x":re()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":re()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...je(),"hidden","none"]}],"divide-style":[{divide:[...je(),"hidden","none"]}],"border-color":[{border:C()}],"border-color-x":[{"border-x":C()}],"border-color-y":[{"border-y":C()}],"border-color-s":[{"border-s":C()}],"border-color-e":[{"border-e":C()}],"border-color-t":[{"border-t":C()}],"border-color-r":[{"border-r":C()}],"border-color-b":[{"border-b":C()}],"border-color-l":[{"border-l":C()}],"divide-color":[{divide:C()}],"outline-style":[{outline:[...je(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Y,L,M]}],"outline-w":[{outline:["",Y,dr,wt]}],"outline-color":[{outline:C()}],shadow:[{shadow:["","none",d,Hr,Br]}],"shadow-color":[{shadow:C()}],"inset-shadow":[{"inset-shadow":["none",m,Hr,Br]}],"inset-shadow-color":[{"inset-shadow":C()}],"ring-w":[{ring:re()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:C()}],"ring-offset-w":[{"ring-offset":[Y,wt]}],"ring-offset-color":[{"ring-offset":C()}],"inset-ring-w":[{"inset-ring":re()}],"inset-ring-color":[{"inset-ring":C()}],"text-shadow":[{"text-shadow":["none",h,Hr,Br]}],"text-shadow-color":[{"text-shadow":C()}],opacity:[{opacity:[Y,L,M]}],"mix-blend":[{"mix-blend":[...be(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":be()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Y]}],"mask-image-linear-from-pos":[{"mask-linear-from":O()}],"mask-image-linear-to-pos":[{"mask-linear-to":O()}],"mask-image-linear-from-color":[{"mask-linear-from":C()}],"mask-image-linear-to-color":[{"mask-linear-to":C()}],"mask-image-t-from-pos":[{"mask-t-from":O()}],"mask-image-t-to-pos":[{"mask-t-to":O()}],"mask-image-t-from-color":[{"mask-t-from":C()}],"mask-image-t-to-color":[{"mask-t-to":C()}],"mask-image-r-from-pos":[{"mask-r-from":O()}],"mask-image-r-to-pos":[{"mask-r-to":O()}],"mask-image-r-from-color":[{"mask-r-from":C()}],"mask-image-r-to-color":[{"mask-r-to":C()}],"mask-image-b-from-pos":[{"mask-b-from":O()}],"mask-image-b-to-pos":[{"mask-b-to":O()}],"mask-image-b-from-color":[{"mask-b-from":C()}],"mask-image-b-to-color":[{"mask-b-to":C()}],"mask-image-l-from-pos":[{"mask-l-from":O()}],"mask-image-l-to-pos":[{"mask-l-to":O()}],"mask-image-l-from-color":[{"mask-l-from":C()}],"mask-image-l-to-color":[{"mask-l-to":C()}],"mask-image-x-from-pos":[{"mask-x-from":O()}],"mask-image-x-to-pos":[{"mask-x-to":O()}],"mask-image-x-from-color":[{"mask-x-from":C()}],"mask-image-x-to-color":[{"mask-x-to":C()}],"mask-image-y-from-pos":[{"mask-y-from":O()}],"mask-image-y-to-pos":[{"mask-y-to":O()}],"mask-image-y-from-color":[{"mask-y-from":C()}],"mask-image-y-to-color":[{"mask-y-to":C()}],"mask-image-radial":[{"mask-radial":[L,M]}],"mask-image-radial-from-pos":[{"mask-radial-from":O()}],"mask-image-radial-to-pos":[{"mask-radial-to":O()}],"mask-image-radial-from-color":[{"mask-radial-from":C()}],"mask-image-radial-to-color":[{"mask-radial-to":C()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[Y]}],"mask-image-conic-from-pos":[{"mask-conic-from":O()}],"mask-image-conic-to-pos":[{"mask-conic-to":O()}],"mask-image-conic-from-color":[{"mask-conic-from":C()}],"mask-image-conic-to-color":[{"mask-conic-to":C()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:R()}],"mask-repeat":[{mask:K()}],"mask-size":[{mask:he()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,M]}],filter:[{filter:["","none",L,M]}],blur:[{blur:ee()}],brightness:[{brightness:[Y,L,M]}],contrast:[{contrast:[Y,L,M]}],"drop-shadow":[{"drop-shadow":["","none",x,Hr,Br]}],"drop-shadow-color":[{"drop-shadow":C()}],grayscale:[{grayscale:["",Y,L,M]}],"hue-rotate":[{"hue-rotate":[Y,L,M]}],invert:[{invert:["",Y,L,M]}],saturate:[{saturate:[Y,L,M]}],sepia:[{sepia:["",Y,L,M]}],"backdrop-filter":[{"backdrop-filter":["","none",L,M]}],"backdrop-blur":[{"backdrop-blur":ee()}],"backdrop-brightness":[{"backdrop-brightness":[Y,L,M]}],"backdrop-contrast":[{"backdrop-contrast":[Y,L,M]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Y,L,M]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Y,L,M]}],"backdrop-invert":[{"backdrop-invert":["",Y,L,M]}],"backdrop-opacity":[{"backdrop-opacity":[Y,L,M]}],"backdrop-saturate":[{"backdrop-saturate":[Y,L,M]}],"backdrop-sepia":[{"backdrop-sepia":["",Y,L,M]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,M]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Y,"initial",L,M]}],ease:[{ease:["linear","initial",v,L,M]}],delay:[{delay:[Y,L,M]}],animate:[{animate:["none",y,L,M]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[p,L,M]}],"perspective-origin":[{"perspective-origin":j()}],rotate:[{rotate:le()}],"rotate-x":[{"rotate-x":le()}],"rotate-y":[{"rotate-y":le()}],"rotate-z":[{"rotate-z":le()}],scale:[{scale:Z()}],"scale-x":[{"scale-x":Z()}],"scale-y":[{"scale-y":Z()}],"scale-z":[{"scale-z":Z()}],"scale-3d":["scale-3d"],skew:[{skew:te()}],"skew-x":[{"skew-x":te()}],"skew-y":[{"skew-y":te()}],transform:[{transform:[L,M,"","none","gpu","cpu"]}],"transform-origin":[{origin:j()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:oe()}],"translate-x":[{"translate-x":oe()}],"translate-y":[{"translate-y":oe()}],"translate-z":[{"translate-z":oe()}],"translate-none":["translate-none"],accent:[{accent:C()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:C()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,M]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,M]}],fill:[{fill:["none",...C()]}],"stroke-w":[{stroke:[Y,dr,wt,Ns]}],stroke:[{stroke:["none",...C()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ch=ah(jh);function B(...e){return Ch(lc(e))}const Th=Bo("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),X=f.forwardRef(({className:e,variant:t,size:r,asChild:n=!1,...s},a)=>{const i=n?Bm:"button";return o.jsx(i,{className:B(Th({variant:t,size:r,className:e})),ref:a,...s})});X.displayName="Button";var Rr=Lm();const Eh=Mm(Rr);var Rh=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],U=Rh.reduce((e,t)=>{const r=mt(`Primitive.${t}`),n=f.forwardRef((s,a)=>{const{asChild:i,...c}=s,l=i?r:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),o.jsx(l,{...c,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function yc(e,t){e&&Rr.flushSync(()=>e.dispatchEvent(t))}var _h="Separator",Ya="horizontal",Ah=["horizontal","vertical"],wc=f.forwardRef((e,t)=>{const{decorative:r,orientation:n=Ya,...s}=e,a=Dh(n)?n:Ya,c=r?{role:"none"}:{"aria-orientation":a==="vertical"?a:void 0,role:"separator"};return o.jsx(U.div,{"data-orientation":a,...c,...s,ref:t})});wc.displayName=_h;function Dh(e){return Ah.includes(e)}var Nc=wc;const Sc=f.forwardRef(({className:e,orientation:t="horizontal",decorative:r=!0,...n},s)=>o.jsx(Nc,{ref:s,decorative:r,orientation:t,className:B("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...n}));Sc.displayName=Nc.displayName;function P(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),r===!1||!s.defaultPrevented)return t?.(s)}}function Ph(e,t){const r=f.createContext(t),n=a=>{const{children:i,...c}=a,l=f.useMemo(()=>c,Object.values(c));return o.jsx(r.Provider,{value:l,children:i})};n.displayName=e+"Provider";function s(a){const i=f.useContext(r);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${a}\` must be used within \`${e}\``)}return[n,s]}function Ne(e,t=[]){let r=[];function n(a,i){const c=f.createContext(i),l=r.length;r=[...r,i];const u=m=>{const{scope:h,children:x,...g}=m,p=h?.[e]?.[l]||c,b=f.useMemo(()=>g,Object.values(g));return o.jsx(p.Provider,{value:b,children:x})};u.displayName=a+"Provider";function d(m,h){const x=h?.[e]?.[l]||c,g=f.useContext(x);if(g)return g;if(i!==void 0)return i;throw new Error(`\`${m}\` must be used within \`${a}\``)}return[u,d]}const s=()=>{const a=r.map(i=>f.createContext(i));return function(c){const l=c?.[e]||a;return f.useMemo(()=>({[`__scope${e}`]:{...c,[e]:l}}),[c,l])}};return s.scopeName=e,[n,Oh(s,...t)]}function Oh(...e){const t=e[0];if(e.length===1)return t;const r=()=>{const n=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(a){const i=n.reduce((c,{useScope:l,scopeName:u})=>{const m=l(a)[`__scope${u}`];return{...c,...m}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}var fe=globalThis?.document?f.useLayoutEffect:()=>{},Ih=nc[" useId ".trim().toString()]||(()=>{}),Mh=0;function Ie(e){const[t,r]=f.useState(Ih());return fe(()=>{r(n=>n??String(Mh++))},[e]),e||(t?`radix-${t}`:"")}var Lh=nc[" useInsertionEffect ".trim().toString()]||fe;function Qe({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){const[s,a,i]=Fh({defaultProp:t,onChange:r}),c=e!==void 0,l=c?e:s;{const d=f.useRef(e!==void 0);f.useEffect(()=>{const m=d.current;m!==c&&console.warn(`${n} is changing from ${m?"controlled":"uncontrolled"} to ${c?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=c},[c,n])}const u=f.useCallback(d=>{if(c){const m=$h(d)?d(e):d;m!==e&&i.current?.(m)}else a(d)},[c,e,a,i]);return[l,u]}function Fh({defaultProp:e,onChange:t}){const[r,n]=f.useState(e),s=f.useRef(r),a=f.useRef(t);return Lh(()=>{a.current=t},[t]),f.useEffect(()=>{s.current!==r&&(a.current?.(r),s.current=r)},[r,s]),[r,n,a]}function $h(e){return typeof e=="function"}function pe(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...r)=>t.current?.(...r),[])}function Uh(e,t=globalThis?.document){const r=pe(e);f.useEffect(()=>{const n=s=>{s.key==="Escape"&&r(s)};return t.addEventListener("keydown",n,{capture:!0}),()=>t.removeEventListener("keydown",n,{capture:!0})},[r,t])}var Bh="DismissableLayer",eo="dismissableLayer.update",Hh="dismissableLayer.pointerDownOutside",Vh="dismissableLayer.focusOutside",Xa,kc=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),tr=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:n,onPointerDownOutside:s,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...l}=e,u=f.useContext(kc),[d,m]=f.useState(null),h=d?.ownerDocument??globalThis?.document,[,x]=f.useState({}),g=G(t,E=>m(E)),p=Array.from(u.layers),[b]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),v=p.indexOf(b),y=d?p.indexOf(d):-1,w=u.layersWithOutsidePointerEventsDisabled.size>0,S=y>=v,j=Gh(E=>{const T=E.target,N=[...u.branches].some(_=>_.contains(T));!S||N||(s?.(E),i?.(E),E.defaultPrevented||c?.())},h),D=qh(E=>{const T=E.target;[...u.branches].some(_=>_.contains(T))||(a?.(E),i?.(E),E.defaultPrevented||c?.())},h);return Uh(E=>{y===u.layers.size-1&&(n?.(E),!E.defaultPrevented&&c&&(E.preventDefault(),c()))},h),f.useEffect(()=>{if(d)return r&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Xa=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Ja(),()=>{r&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Xa)}},[d,h,r,u]),f.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Ja())},[d,u]),f.useEffect(()=>{const E=()=>x({});return document.addEventListener(eo,E),()=>document.removeEventListener(eo,E)},[]),o.jsx(U.div,{...l,ref:g,style:{pointerEvents:w?S?"auto":"none":void 0,...e.style},onFocusCapture:P(e.onFocusCapture,D.onFocusCapture),onBlurCapture:P(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:P(e.onPointerDownCapture,j.onPointerDownCapture)})});tr.displayName=Bh;var zh="DismissableLayerBranch",Wh=f.forwardRef((e,t)=>{const r=f.useContext(kc),n=f.useRef(null),s=G(t,n);return f.useEffect(()=>{const a=n.current;if(a)return r.branches.add(a),()=>{r.branches.delete(a)}},[r.branches]),o.jsx(U.div,{...e,ref:s})});Wh.displayName=zh;function Gh(e,t=globalThis?.document){const r=pe(e),n=f.useRef(!1),s=f.useRef(()=>{});return f.useEffect(()=>{const a=c=>{if(c.target&&!n.current){let l=function(){jc(Hh,r,u,{discrete:!0})};const u={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=l,t.addEventListener("click",s.current,{once:!0})):l()}else t.removeEventListener("click",s.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",s.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}function qh(e,t=globalThis?.document){const r=pe(e),n=f.useRef(!1);return f.useEffect(()=>{const s=a=>{a.target&&!n.current&&jc(Vh,r,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}function Ja(){const e=new CustomEvent(eo);document.dispatchEvent(e)}function jc(e,t,r,{discrete:n}){const s=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),n?yc(s,a):s.dispatchEvent(a)}var Ss="focusScope.autoFocusOnMount",ks="focusScope.autoFocusOnUnmount",Qa={bubbles:!1,cancelable:!0},Kh="FocusScope",_r=f.forwardRef((e,t)=>{const{loop:r=!1,trapped:n=!1,onMountAutoFocus:s,onUnmountAutoFocus:a,...i}=e,[c,l]=f.useState(null),u=pe(s),d=pe(a),m=f.useRef(null),h=G(t,p=>l(p)),x=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(n){let p=function(w){if(x.paused||!c)return;const S=w.target;c.contains(S)?m.current=S:lt(m.current,{select:!0})},b=function(w){if(x.paused||!c)return;const S=w.relatedTarget;S!==null&&(c.contains(S)||lt(m.current,{select:!0}))},v=function(w){if(document.activeElement===document.body)for(const j of w)j.removedNodes.length>0&&lt(c)};document.addEventListener("focusin",p),document.addEventListener("focusout",b);const y=new MutationObserver(v);return c&&y.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",p),document.removeEventListener("focusout",b),y.disconnect()}}},[n,c,x.paused]),f.useEffect(()=>{if(c){ei.add(x);const p=document.activeElement;if(!c.contains(p)){const v=new CustomEvent(Ss,Qa);c.addEventListener(Ss,u),c.dispatchEvent(v),v.defaultPrevented||(Yh(ep(Cc(c)),{select:!0}),document.activeElement===p&&lt(c))}return()=>{c.removeEventListener(Ss,u),setTimeout(()=>{const v=new CustomEvent(ks,Qa);c.addEventListener(ks,d),c.dispatchEvent(v),v.defaultPrevented||lt(p??document.body,{select:!0}),c.removeEventListener(ks,d),ei.remove(x)},0)}}},[c,u,d,x]);const g=f.useCallback(p=>{if(!r&&!n||x.paused)return;const b=p.key==="Tab"&&!p.altKey&&!p.ctrlKey&&!p.metaKey,v=document.activeElement;if(b&&v){const y=p.currentTarget,[w,S]=Xh(y);w&&S?!p.shiftKey&&v===S?(p.preventDefault(),r&&lt(w,{select:!0})):p.shiftKey&&v===w&&(p.preventDefault(),r&&lt(S,{select:!0})):v===y&&p.preventDefault()}},[r,n,x.paused]);return o.jsx(U.div,{tabIndex:-1,...i,ref:h,onKeyDown:g})});_r.displayName=Kh;function Yh(e,{select:t=!1}={}){const r=document.activeElement;for(const n of e)if(lt(n,{select:t}),document.activeElement!==r)return}function Xh(e){const t=Cc(e),r=Za(t,e),n=Za(t.reverse(),e);return[r,n]}function Cc(e){const t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:n=>{const s=n.tagName==="INPUT"&&n.type==="hidden";return n.disabled||n.hidden||s?NodeFilter.FILTER_SKIP:n.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function Za(e,t){for(const r of e)if(!Jh(r,{upTo:t}))return r}function Jh(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Qh(e){return e instanceof HTMLInputElement&&"select"in e}function lt(e,{select:t=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&Qh(e)&&t&&e.select()}}var ei=Zh();function Zh(){let e=[];return{add(t){const r=e[0];t!==r&&r?.pause(),e=ti(e,t),e.unshift(t)},remove(t){e=ti(e,t),e[0]?.resume()}}}function ti(e,t){const r=[...e],n=r.indexOf(t);return n!==-1&&r.splice(n,1),r}function ep(e){return e.filter(t=>t.tagName!=="A")}var tp="Portal",Ar=f.forwardRef((e,t)=>{const{container:r,...n}=e,[s,a]=f.useState(!1);fe(()=>a(!0),[]);const i=r||s&&globalThis?.document?.body;return i?Eh.createPortal(o.jsx(U.div,{...n,ref:t}),i):null});Ar.displayName=tp;function rp(e,t){return f.useReducer((r,n)=>t[r][n]??r,e)}var xe=e=>{const{present:t,children:r}=e,n=np(t),s=typeof r=="function"?r({present:n.isPresent}):f.Children.only(r),a=G(n.ref,sp(s));return typeof r=="function"||n.isPresent?f.cloneElement(s,{ref:a}):null};xe.displayName="Presence";function np(e){const[t,r]=f.useState(),n=f.useRef(null),s=f.useRef(e),a=f.useRef("none"),i=e?"mounted":"unmounted",[c,l]=rp(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const u=Vr(n.current);a.current=c==="mounted"?u:"none"},[c]),fe(()=>{const u=n.current,d=s.current;if(d!==e){const h=a.current,x=Vr(u);e?l("MOUNT"):x==="none"||u?.display==="none"?l("UNMOUNT"):l(d&&h!==x?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,l]),fe(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,m=x=>{const p=Vr(n.current).includes(x.animationName);if(x.target===t&&p&&(l("ANIMATION_END"),!s.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},h=x=>{x.target===t&&(a.current=Vr(n.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:f.useCallback(u=>{n.current=u?getComputedStyle(u):null,r(u)},[])}}function Vr(e){return e?.animationName||"none"}function sp(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var js=0;function Mn(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??ri()),document.body.insertAdjacentElement("beforeend",e[1]??ri()),js++,()=>{js===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),js--}},[])}function ri(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var to=function(e,t){return to=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(r[s]=n[s])},to(e,t)};function TS(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");to(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}var Ge=function(){return Ge=Object.assign||function(t){for(var r,n=1,s=arguments.length;n<s;n++){r=arguments[n];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(t[a]=r[a])}return t},Ge.apply(this,arguments)};function Tc(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,n=Object.getOwnPropertySymbols(e);s<n.length;s++)t.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(e,n[s])&&(r[n[s]]=e[n[s]]);return r}function op(e,t,r){if(r||arguments.length===2)for(var n=0,s=t.length,a;n<s;n++)(a||!(n in t))&&(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}var rn="right-scroll-bar-position",nn="width-before-scroll-bar",ap="with-scroll-bars-hidden",ip="--removed-body-scroll-bar-size";function Cs(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function cp(e,t){var r=f.useState(function(){return{value:e,callback:t,facade:{get current(){return r.value},set current(n){var s=r.value;s!==n&&(r.value=n,r.callback(n,s))}}}})[0];return r.callback=t,r.facade}var lp=typeof window<"u"?f.useLayoutEffect:f.useEffect,ni=new WeakMap;function dp(e,t){var r=cp(null,function(n){return e.forEach(function(s){return Cs(s,n)})});return lp(function(){var n=ni.get(r);if(n){var s=new Set(n),a=new Set(e),i=r.current;s.forEach(function(c){a.has(c)||Cs(c,null)}),a.forEach(function(c){s.has(c)||Cs(c,i)})}ni.set(r,e)},[e]),r}function up(e){return e}function fp(e,t){t===void 0&&(t=up);var r=[],n=!1,s={read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(a){var i=t(a,n);return r.push(i),function(){r=r.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(n=!0;r.length;){var i=r;r=[],i.forEach(a)}r={push:function(c){return a(c)},filter:function(){return r}}},assignMedium:function(a){n=!0;var i=[];if(r.length){var c=r;r=[],c.forEach(a),i=r}var l=function(){var d=i;i=[],d.forEach(a)},u=function(){return Promise.resolve().then(l)};u(),r={push:function(d){i.push(d),u()},filter:function(d){return i=i.filter(d),r}}}};return s}function mp(e){e===void 0&&(e={});var t=fp(null);return t.options=Ge({async:!0,ssr:!1},e),t}var Ec=function(e){var t=e.sideCar,r=Tc(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw new Error("Sidecar medium not found");return f.createElement(n,Ge({},r))};Ec.isSideCarExport=!0;function hp(e,t){return e.useMedium(t),Ec}var Rc=mp(),Ts=function(){},Ln=f.forwardRef(function(e,t){var r=f.useRef(null),n=f.useState({onScrollCapture:Ts,onWheelCapture:Ts,onTouchMoveCapture:Ts}),s=n[0],a=n[1],i=e.forwardProps,c=e.children,l=e.className,u=e.removeScrollBar,d=e.enabled,m=e.shards,h=e.sideCar,x=e.noRelative,g=e.noIsolation,p=e.inert,b=e.allowPinchZoom,v=e.as,y=v===void 0?"div":v,w=e.gapMode,S=Tc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=h,D=dp([r,t]),E=Ge(Ge({},S),s);return f.createElement(f.Fragment,null,d&&f.createElement(j,{sideCar:Rc,removeScrollBar:u,shards:m,noRelative:x,noIsolation:g,inert:p,setCallbacks:a,allowPinchZoom:!!b,lockRef:r,gapMode:w}),i?f.cloneElement(f.Children.only(c),Ge(Ge({},E),{ref:D})):f.createElement(y,Ge({},E,{className:l,ref:D}),c))});Ln.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ln.classNames={fullWidth:nn,zeroRight:rn};var pp=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function gp(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=pp();return t&&e.setAttribute("nonce",t),e}function xp(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function bp(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var vp=function(){var e=0,t=null;return{add:function(r){e==0&&(t=gp())&&(xp(t,r),bp(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},yp=function(){var e=vp();return function(t,r){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},_c=function(){var e=yp(),t=function(r){var n=r.styles,s=r.dynamic;return e(n,s),null};return t},wp={left:0,top:0,right:0,gap:0},Es=function(e){return parseInt(e||"",10)||0},Np=function(e){var t=window.getComputedStyle(document.body),r=t[e==="padding"?"paddingLeft":"marginLeft"],n=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[Es(r),Es(n),Es(s)]},Sp=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return wp;var t=Np(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},kp=_c(),Ht="data-scroll-locked",jp=function(e,t,r,n){var s=e.left,a=e.top,i=e.right,c=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(ap,` {
   overflow: hidden `).concat(n,`;
   padding-right: `).concat(c,"px ").concat(n,`;
  }
  body[`).concat(Ht,`] {
    overflow: hidden `).concat(n,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(n,";"),r==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(n,`;
    `),r==="padding"&&"padding-right: ".concat(c,"px ").concat(n,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(rn,` {
    right: `).concat(c,"px ").concat(n,`;
  }
  
  .`).concat(nn,` {
    margin-right: `).concat(c,"px ").concat(n,`;
  }
  
  .`).concat(rn," .").concat(rn,` {
    right: 0 `).concat(n,`;
  }
  
  .`).concat(nn," .").concat(nn,` {
    margin-right: 0 `).concat(n,`;
  }
  
  body[`).concat(Ht,`] {
    `).concat(ip,": ").concat(c,`px;
  }
`)},si=function(){var e=parseInt(document.body.getAttribute(Ht)||"0",10);return isFinite(e)?e:0},Cp=function(){f.useEffect(function(){return document.body.setAttribute(Ht,(si()+1).toString()),function(){var e=si()-1;e<=0?document.body.removeAttribute(Ht):document.body.setAttribute(Ht,e.toString())}},[])},Tp=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,s=n===void 0?"margin":n;Cp();var a=f.useMemo(function(){return Sp(s)},[s]);return f.createElement(kp,{styles:jp(a,!t,s,r?"":"!important")})},ro=!1;if(typeof window<"u")try{var zr=Object.defineProperty({},"passive",{get:function(){return ro=!0,!0}});window.addEventListener("test",zr,zr),window.removeEventListener("test",zr,zr)}catch{ro=!1}var It=ro?{passive:!1}:!1,Ep=function(e){return e.tagName==="TEXTAREA"},Ac=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return r[t]!=="hidden"&&!(r.overflowY===r.overflowX&&!Ep(e)&&r[t]==="visible")},Rp=function(e){return Ac(e,"overflowY")},_p=function(e){return Ac(e,"overflowX")},oi=function(e,t){var r=t.ownerDocument,n=t;do{typeof ShadowRoot<"u"&&n instanceof ShadowRoot&&(n=n.host);var s=Dc(e,n);if(s){var a=Pc(e,n),i=a[1],c=a[2];if(i>c)return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Ap=function(e){var t=e.scrollTop,r=e.scrollHeight,n=e.clientHeight;return[t,r,n]},Dp=function(e){var t=e.scrollLeft,r=e.scrollWidth,n=e.clientWidth;return[t,r,n]},Dc=function(e,t){return e==="v"?Rp(t):_p(t)},Pc=function(e,t){return e==="v"?Ap(t):Dp(t)},Pp=function(e,t){return e==="h"&&t==="rtl"?-1:1},Op=function(e,t,r,n,s){var a=Pp(e,window.getComputedStyle(t).direction),i=a*n,c=r.target,l=t.contains(c),u=!1,d=i>0,m=0,h=0;do{if(!c)break;var x=Pc(e,c),g=x[0],p=x[1],b=x[2],v=p-b-a*g;(g||v)&&Dc(e,c)&&(m+=v,h+=g);var y=c.parentNode;c=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return(d&&Math.abs(m)<1||!d&&Math.abs(h)<1)&&(u=!0),u},Wr=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ai=function(e){return[e.deltaX,e.deltaY]},ii=function(e){return e&&"current"in e?e.current:e},Ip=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Mp=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Lp=0,Mt=[];function Fp(e){var t=f.useRef([]),r=f.useRef([0,0]),n=f.useRef(),s=f.useState(Lp++)[0],a=f.useState(_c)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var p=op([e.lockRef.current],(e.shards||[]).map(ii),!0).filter(Boolean);return p.forEach(function(b){return b.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),p.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var c=f.useCallback(function(p,b){if("touches"in p&&p.touches.length===2||p.type==="wheel"&&p.ctrlKey)return!i.current.allowPinchZoom;var v=Wr(p),y=r.current,w="deltaX"in p?p.deltaX:y[0]-v[0],S="deltaY"in p?p.deltaY:y[1]-v[1],j,D=p.target,E=Math.abs(w)>Math.abs(S)?"h":"v";if("touches"in p&&E==="h"&&D.type==="range")return!1;var T=oi(E,D);if(!T)return!0;if(T?j=E:(j=E==="v"?"h":"v",T=oi(E,D)),!T)return!1;if(!n.current&&"changedTouches"in p&&(w||S)&&(n.current=j),!j)return!0;var N=n.current||j;return Op(N,b,p,N==="h"?w:S)},[]),l=f.useCallback(function(p){var b=p;if(!(!Mt.length||Mt[Mt.length-1]!==a)){var v="deltaY"in b?ai(b):Wr(b),y=t.current.filter(function(j){return j.name===b.type&&(j.target===b.target||b.target===j.shadowParent)&&Ip(j.delta,v)})[0];if(y&&y.should){b.cancelable&&b.preventDefault();return}if(!y){var w=(i.current.shards||[]).map(ii).filter(Boolean).filter(function(j){return j.contains(b.target)}),S=w.length>0?c(b,w[0]):!i.current.noIsolation;S&&b.cancelable&&b.preventDefault()}}},[]),u=f.useCallback(function(p,b,v,y){var w={name:p,delta:b,target:v,should:y,shadowParent:$p(v)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(S){return S!==w})},1)},[]),d=f.useCallback(function(p){r.current=Wr(p),n.current=void 0},[]),m=f.useCallback(function(p){u(p.type,ai(p),p.target,c(p,e.lockRef.current))},[]),h=f.useCallback(function(p){u(p.type,Wr(p),p.target,c(p,e.lockRef.current))},[]);f.useEffect(function(){return Mt.push(a),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",l,It),document.addEventListener("touchmove",l,It),document.addEventListener("touchstart",d,It),function(){Mt=Mt.filter(function(p){return p!==a}),document.removeEventListener("wheel",l,It),document.removeEventListener("touchmove",l,It),document.removeEventListener("touchstart",d,It)}},[]);var x=e.removeScrollBar,g=e.inert;return f.createElement(f.Fragment,null,g?f.createElement(a,{styles:Mp(s)}):null,x?f.createElement(Tp,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function $p(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Up=hp(Rc,Fp);var Dr=f.forwardRef(function(e,t){return f.createElement(Ln,Ge({},e,{ref:t,sideCar:Up}))});Dr.classNames=Ln.classNames;var Bp=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Lt=new WeakMap,Gr=new WeakMap,qr={},Rs=0,Oc=function(e){return e&&(e.host||Oc(e.parentNode))},Hp=function(e,t){return t.map(function(r){if(e.contains(r))return r;var n=Oc(r);return n&&e.contains(n)?n:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},Vp=function(e,t,r,n){var s=Hp(t,Array.isArray(e)?e:[e]);qr[r]||(qr[r]=new WeakMap);var a=qr[r],i=[],c=new Set,l=new Set(s),u=function(m){!m||c.has(m)||(c.add(m),u(m.parentNode))};s.forEach(u);var d=function(m){!m||l.has(m)||Array.prototype.forEach.call(m.children,function(h){if(c.has(h))d(h);else try{var x=h.getAttribute(n),g=x!==null&&x!=="false",p=(Lt.get(h)||0)+1,b=(a.get(h)||0)+1;Lt.set(h,p),a.set(h,b),i.push(h),p===1&&g&&Gr.set(h,!0),b===1&&h.setAttribute(r,"true"),g||h.setAttribute(n,"true")}catch(v){console.error("aria-hidden: cannot operate on ",h,v)}})};return d(t),c.clear(),Rs++,function(){i.forEach(function(m){var h=Lt.get(m)-1,x=a.get(m)-1;Lt.set(m,h),a.set(m,x),h||(Gr.has(m)||m.removeAttribute(n),Gr.delete(m)),x||m.removeAttribute(r)}),Rs--,Rs||(Lt=new WeakMap,Lt=new WeakMap,Gr=new WeakMap,qr={})}},Fn=function(e,t,r){r===void 0&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),s=Bp(e);return s?(n.push.apply(n,Array.from(s.querySelectorAll("[aria-live], script"))),Vp(n,s,r,"aria-hidden")):function(){return null}},$n="Dialog",[Ic,ES]=Ne($n),[zp,Ve]=Ic($n),Mc=e=>{const{__scopeDialog:t,children:r,open:n,defaultOpen:s,onOpenChange:a,modal:i=!0}=e,c=f.useRef(null),l=f.useRef(null),[u,d]=Qe({prop:n,defaultProp:s??!1,onChange:a,caller:$n});return o.jsx(zp,{scope:t,triggerRef:c,contentRef:l,contentId:Ie(),titleId:Ie(),descriptionId:Ie(),open:u,onOpenChange:d,onOpenToggle:f.useCallback(()=>d(m=>!m),[d]),modal:i,children:r})};Mc.displayName=$n;var Lc="DialogTrigger",Fc=f.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,s=Ve(Lc,r),a=G(t,s.triggerRef);return o.jsx(U.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Wo(s.open),...n,ref:a,onClick:P(e.onClick,s.onOpenToggle)})});Fc.displayName=Lc;var Vo="DialogPortal",[Wp,$c]=Ic(Vo,{forceMount:void 0}),Uc=e=>{const{__scopeDialog:t,forceMount:r,children:n,container:s}=e,a=Ve(Vo,t);return o.jsx(Wp,{scope:t,forceMount:r,children:f.Children.map(n,i=>o.jsx(xe,{present:r||a.open,children:o.jsx(Ar,{asChild:!0,container:s,children:i})}))})};Uc.displayName=Vo;var vn="DialogOverlay",Bc=f.forwardRef((e,t)=>{const r=$c(vn,e.__scopeDialog),{forceMount:n=r.forceMount,...s}=e,a=Ve(vn,e.__scopeDialog);return a.modal?o.jsx(xe,{present:n||a.open,children:o.jsx(qp,{...s,ref:t})}):null});Bc.displayName=vn;var Gp=mt("DialogOverlay.RemoveScroll"),qp=f.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,s=Ve(vn,r);return o.jsx(Dr,{as:Gp,allowPinchZoom:!0,shards:[s.contentRef],children:o.jsx(U.div,{"data-state":Wo(s.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),Tt="DialogContent",Hc=f.forwardRef((e,t)=>{const r=$c(Tt,e.__scopeDialog),{forceMount:n=r.forceMount,...s}=e,a=Ve(Tt,e.__scopeDialog);return o.jsx(xe,{present:n||a.open,children:a.modal?o.jsx(Kp,{...s,ref:t}):o.jsx(Yp,{...s,ref:t})})});Hc.displayName=Tt;var Kp=f.forwardRef((e,t)=>{const r=Ve(Tt,e.__scopeDialog),n=f.useRef(null),s=G(t,r.contentRef,n);return f.useEffect(()=>{const a=n.current;if(a)return Fn(a)},[]),o.jsx(Vc,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,a=>{a.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:P(e.onFocusOutside,a=>a.preventDefault())})}),Yp=f.forwardRef((e,t)=>{const r=Ve(Tt,e.__scopeDialog),n=f.useRef(!1),s=f.useRef(!1);return o.jsx(Vc,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(n.current||r.triggerRef.current?.focus(),a.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(n.current=!0,a.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const i=a.target;r.triggerRef.current?.contains(i)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&s.current&&a.preventDefault()}})}),Vc=f.forwardRef((e,t)=>{const{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:a,...i}=e,c=Ve(Tt,r),l=f.useRef(null),u=G(t,l);return Mn(),o.jsxs(o.Fragment,{children:[o.jsx(_r,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:a,children:o.jsx(tr,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Wo(c.open),...i,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),o.jsxs(o.Fragment,{children:[o.jsx(Xp,{titleId:c.titleId}),o.jsx(Qp,{contentRef:l,descriptionId:c.descriptionId})]})]})}),zo="DialogTitle",zc=f.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,s=Ve(zo,r);return o.jsx(U.h2,{id:s.titleId,...n,ref:t})});zc.displayName=zo;var Wc="DialogDescription",Gc=f.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,s=Ve(Wc,r);return o.jsx(U.p,{id:s.descriptionId,...n,ref:t})});Gc.displayName=Wc;var qc="DialogClose",Kc=f.forwardRef((e,t)=>{const{__scopeDialog:r,...n}=e,s=Ve(qc,r);return o.jsx(U.button,{type:"button",...n,ref:t,onClick:P(e.onClick,()=>s.onOpenChange(!1))})});Kc.displayName=qc;function Wo(e){return e?"open":"closed"}var Yc="DialogTitleWarning",[RS,Xc]=Ph(Yc,{contentName:Tt,titleName:zo,docsSlug:"dialog"}),Xp=({titleId:e})=>{const t=Xc(Yc),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return f.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Jp="DialogDescriptionWarning",Qp=({contentRef:e,descriptionId:t})=>{const n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Xc(Jp).contentName}}.`;return f.useEffect(()=>{const s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(n))},[n,e,t]),null},Zp=Mc,eg=Fc,tg=Uc,Jc=Bc,Qc=Hc,Zc=zc,el=Gc,rg=Kc;const ng=["top","right","bottom","left"],ht=Math.min,Re=Math.max,yn=Math.round,Kr=Math.floor,Xe=e=>({x:e,y:e}),sg={left:"right",right:"left",bottom:"top",top:"bottom"},og={start:"end",end:"start"};function no(e,t,r){return Re(e,ht(t,r))}function at(e,t){return typeof e=="function"?e(t):e}function it(e){return e.split("-")[0]}function rr(e){return e.split("-")[1]}function Go(e){return e==="x"?"y":"x"}function qo(e){return e==="y"?"height":"width"}const ag=new Set(["top","bottom"]);function Ke(e){return ag.has(it(e))?"y":"x"}function Ko(e){return Go(Ke(e))}function ig(e,t,r){r===void 0&&(r=!1);const n=rr(e),s=Ko(e),a=qo(s);let i=s==="x"?n===(r?"end":"start")?"right":"left":n==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=wn(i)),[i,wn(i)]}function cg(e){const t=wn(e);return[so(e),t,so(t)]}function so(e){return e.replace(/start|end/g,t=>og[t])}const ci=["left","right"],li=["right","left"],lg=["top","bottom"],dg=["bottom","top"];function ug(e,t,r){switch(e){case"top":case"bottom":return r?t?li:ci:t?ci:li;case"left":case"right":return t?lg:dg;default:return[]}}function fg(e,t,r,n){const s=rr(e);let a=ug(it(e),r==="start",n);return s&&(a=a.map(i=>i+"-"+s),t&&(a=a.concat(a.map(so)))),a}function wn(e){return e.replace(/left|right|bottom|top/g,t=>sg[t])}function mg(e){return{top:0,right:0,bottom:0,left:0,...e}}function tl(e){return typeof e!="number"?mg(e):{top:e,right:e,bottom:e,left:e}}function Nn(e){const{x:t,y:r,width:n,height:s}=e;return{width:n,height:s,top:r,left:t,right:t+n,bottom:r+s,x:t,y:r}}function di(e,t,r){let{reference:n,floating:s}=e;const a=Ke(t),i=Ko(t),c=qo(i),l=it(t),u=a==="y",d=n.x+n.width/2-s.width/2,m=n.y+n.height/2-s.height/2,h=n[c]/2-s[c]/2;let x;switch(l){case"top":x={x:d,y:n.y-s.height};break;case"bottom":x={x:d,y:n.y+n.height};break;case"right":x={x:n.x+n.width,y:m};break;case"left":x={x:n.x-s.width,y:m};break;default:x={x:n.x,y:n.y}}switch(rr(t)){case"start":x[i]-=h*(r&&u?-1:1);break;case"end":x[i]+=h*(r&&u?-1:1);break}return x}const hg=async(e,t,r)=>{const{placement:n="bottom",strategy:s="absolute",middleware:a=[],platform:i}=r,c=a.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:d,y:m}=di(u,n,l),h=n,x={},g=0;for(let p=0;p<c.length;p++){const{name:b,fn:v}=c[p],{x:y,y:w,data:S,reset:j}=await v({x:d,y:m,initialPlacement:n,placement:h,strategy:s,middlewareData:x,rects:u,platform:i,elements:{reference:e,floating:t}});d=y??d,m=w??m,x={...x,[b]:{...x[b],...S}},j&&g<=50&&(g++,typeof j=="object"&&(j.placement&&(h=j.placement),j.rects&&(u=j.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):j.rects),{x:d,y:m}=di(u,h,l)),p=-1)}return{x:d,y:m,placement:h,strategy:s,middlewareData:x}};async function wr(e,t){var r;t===void 0&&(t={});const{x:n,y:s,platform:a,rects:i,elements:c,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:m="floating",altBoundary:h=!1,padding:x=0}=at(t,e),g=tl(x),b=c[h?m==="floating"?"reference":"floating":m],v=Nn(await a.getClippingRect({element:(r=await(a.isElement==null?void 0:a.isElement(b)))==null||r?b:b.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:u,rootBoundary:d,strategy:l})),y=m==="floating"?{x:n,y:s,width:i.floating.width,height:i.floating.height}:i.reference,w=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),S=await(a.isElement==null?void 0:a.isElement(w))?await(a.getScale==null?void 0:a.getScale(w))||{x:1,y:1}:{x:1,y:1},j=Nn(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:y,offsetParent:w,strategy:l}):y);return{top:(v.top-j.top+g.top)/S.y,bottom:(j.bottom-v.bottom+g.bottom)/S.y,left:(v.left-j.left+g.left)/S.x,right:(j.right-v.right+g.right)/S.x}}const pg=e=>({name:"arrow",options:e,async fn(t){const{x:r,y:n,placement:s,rects:a,platform:i,elements:c,middlewareData:l}=t,{element:u,padding:d=0}=at(e,t)||{};if(u==null)return{};const m=tl(d),h={x:r,y:n},x=Ko(s),g=qo(x),p=await i.getDimensions(u),b=x==="y",v=b?"top":"left",y=b?"bottom":"right",w=b?"clientHeight":"clientWidth",S=a.reference[g]+a.reference[x]-h[x]-a.floating[g],j=h[x]-a.reference[x],D=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let E=D?D[w]:0;(!E||!await(i.isElement==null?void 0:i.isElement(D)))&&(E=c.floating[w]||a.floating[g]);const T=S/2-j/2,N=E/2-p[g]/2-1,_=ht(m[v],N),I=ht(m[y],N),H=_,A=E-p[g]-I,$=E/2-p[g]/2+T,V=no(H,$,A),F=!l.arrow&&rr(s)!=null&&$!==V&&a.reference[g]/2-($<H?_:I)-p[g]/2<0,z=F?$<H?$-H:$-A:0;return{[x]:h[x]+z,data:{[x]:V,centerOffset:$-V-z,...F&&{alignmentOffset:z}},reset:F}}}),gg=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var r,n;const{placement:s,middlewareData:a,rects:i,initialPlacement:c,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:p=!0,...b}=at(e,t);if((r=a.arrow)!=null&&r.alignmentOffset)return{};const v=it(s),y=Ke(c),w=it(c)===c,S=await(l.isRTL==null?void 0:l.isRTL(u.floating)),j=h||(w||!p?[wn(c)]:cg(c)),D=g!=="none";!h&&D&&j.push(...fg(c,p,g,S));const E=[c,...j],T=await wr(t,b),N=[];let _=((n=a.flip)==null?void 0:n.overflows)||[];if(d&&N.push(T[v]),m){const $=ig(s,i,S);N.push(T[$[0]],T[$[1]])}if(_=[..._,{placement:s,overflows:N}],!N.every($=>$<=0)){var I,H;const $=(((I=a.flip)==null?void 0:I.index)||0)+1,V=E[$];if(V&&(!(m==="alignment"?y!==Ke(V):!1)||_.every(C=>C.overflows[0]>0&&Ke(C.placement)===y)))return{data:{index:$,overflows:_},reset:{placement:V}};let F=(H=_.filter(z=>z.overflows[0]<=0).sort((z,C)=>z.overflows[1]-C.overflows[1])[0])==null?void 0:H.placement;if(!F)switch(x){case"bestFit":{var A;const z=(A=_.filter(C=>{if(D){const R=Ke(C.placement);return R===y||R==="y"}return!0}).map(C=>[C.placement,C.overflows.filter(R=>R>0).reduce((R,K)=>R+K,0)]).sort((C,R)=>C[1]-R[1])[0])==null?void 0:A[0];z&&(F=z);break}case"initialPlacement":F=c;break}if(s!==F)return{reset:{placement:F}}}return{}}}};function ui(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function fi(e){return ng.some(t=>e[t]>=0)}const xg=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:r}=t,{strategy:n="referenceHidden",...s}=at(e,t);switch(n){case"referenceHidden":{const a=await wr(t,{...s,elementContext:"reference"}),i=ui(a,r.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:fi(i)}}}case"escaped":{const a=await wr(t,{...s,altBoundary:!0}),i=ui(a,r.floating);return{data:{escapedOffsets:i,escaped:fi(i)}}}default:return{}}}}},rl=new Set(["left","top"]);async function bg(e,t){const{placement:r,platform:n,elements:s}=e,a=await(n.isRTL==null?void 0:n.isRTL(s.floating)),i=it(r),c=rr(r),l=Ke(r)==="y",u=rl.has(i)?-1:1,d=a&&l?-1:1,m=at(t,e);let{mainAxis:h,crossAxis:x,alignmentAxis:g}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return c&&typeof g=="number"&&(x=c==="end"?g*-1:g),l?{x:x*d,y:h*u}:{x:h*u,y:x*d}}const vg=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var r,n;const{x:s,y:a,placement:i,middlewareData:c}=t,l=await bg(t,e);return i===((r=c.offset)==null?void 0:r.placement)&&(n=c.arrow)!=null&&n.alignmentOffset?{}:{x:s+l.x,y:a+l.y,data:{...l,placement:i}}}}},yg=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:r,y:n,placement:s}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:b=>{let{x:v,y}=b;return{x:v,y}}},...l}=at(e,t),u={x:r,y:n},d=await wr(t,l),m=Ke(it(s)),h=Go(m);let x=u[h],g=u[m];if(a){const b=h==="y"?"top":"left",v=h==="y"?"bottom":"right",y=x+d[b],w=x-d[v];x=no(y,x,w)}if(i){const b=m==="y"?"top":"left",v=m==="y"?"bottom":"right",y=g+d[b],w=g-d[v];g=no(y,g,w)}const p=c.fn({...t,[h]:x,[m]:g});return{...p,data:{x:p.x-r,y:p.y-n,enabled:{[h]:a,[m]:i}}}}}},wg=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:r,y:n,placement:s,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:l=!0,crossAxis:u=!0}=at(e,t),d={x:r,y:n},m=Ke(s),h=Go(m);let x=d[h],g=d[m];const p=at(c,t),b=typeof p=="number"?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(l){const w=h==="y"?"height":"width",S=a.reference[h]-a.floating[w]+b.mainAxis,j=a.reference[h]+a.reference[w]-b.mainAxis;x<S?x=S:x>j&&(x=j)}if(u){var v,y;const w=h==="y"?"width":"height",S=rl.has(it(s)),j=a.reference[m]-a.floating[w]+(S&&((v=i.offset)==null?void 0:v[m])||0)+(S?0:b.crossAxis),D=a.reference[m]+a.reference[w]+(S?0:((y=i.offset)==null?void 0:y[m])||0)-(S?b.crossAxis:0);g<j?g=j:g>D&&(g=D)}return{[h]:x,[m]:g}}}},Ng=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var r,n;const{placement:s,rects:a,platform:i,elements:c}=t,{apply:l=()=>{},...u}=at(e,t),d=await wr(t,u),m=it(s),h=rr(s),x=Ke(s)==="y",{width:g,height:p}=a.floating;let b,v;m==="top"||m==="bottom"?(b=m,v=h===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(v=m,b=h==="end"?"top":"bottom");const y=p-d.top-d.bottom,w=g-d.left-d.right,S=ht(p-d[b],y),j=ht(g-d[v],w),D=!t.middlewareData.shift;let E=S,T=j;if((r=t.middlewareData.shift)!=null&&r.enabled.x&&(T=w),(n=t.middlewareData.shift)!=null&&n.enabled.y&&(E=y),D&&!h){const _=Re(d.left,0),I=Re(d.right,0),H=Re(d.top,0),A=Re(d.bottom,0);x?T=g-2*(_!==0||I!==0?_+I:Re(d.left,d.right)):E=p-2*(H!==0||A!==0?H+A:Re(d.top,d.bottom))}await l({...t,availableWidth:T,availableHeight:E});const N=await i.getDimensions(c.floating);return g!==N.width||p!==N.height?{reset:{rects:!0}}:{}}}};function Un(){return typeof window<"u"}function nr(e){return nl(e)?(e.nodeName||"").toLowerCase():"#document"}function Ae(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function tt(e){var t;return(t=(nl(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function nl(e){return Un()?e instanceof Node||e instanceof Ae(e).Node:!1}function Be(e){return Un()?e instanceof Element||e instanceof Ae(e).Element:!1}function Ze(e){return Un()?e instanceof HTMLElement||e instanceof Ae(e).HTMLElement:!1}function mi(e){return!Un()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ae(e).ShadowRoot}const Sg=new Set(["inline","contents"]);function Pr(e){const{overflow:t,overflowX:r,overflowY:n,display:s}=He(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!Sg.has(s)}const kg=new Set(["table","td","th"]);function jg(e){return kg.has(nr(e))}const Cg=[":popover-open",":modal"];function Bn(e){return Cg.some(t=>{try{return e.matches(t)}catch{return!1}})}const Tg=["transform","translate","scale","rotate","perspective"],Eg=["transform","translate","scale","rotate","perspective","filter"],Rg=["paint","layout","strict","content"];function Yo(e){const t=Xo(),r=Be(e)?He(e):e;return Tg.some(n=>r[n]?r[n]!=="none":!1)||(r.containerType?r.containerType!=="normal":!1)||!t&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!t&&(r.filter?r.filter!=="none":!1)||Eg.some(n=>(r.willChange||"").includes(n))||Rg.some(n=>(r.contain||"").includes(n))}function _g(e){let t=pt(e);for(;Ze(t)&&!Yt(t);){if(Yo(t))return t;if(Bn(t))return null;t=pt(t)}return null}function Xo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Ag=new Set(["html","body","#document"]);function Yt(e){return Ag.has(nr(e))}function He(e){return Ae(e).getComputedStyle(e)}function Hn(e){return Be(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function pt(e){if(nr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||mi(e)&&e.host||tt(e);return mi(t)?t.host:t}function sl(e){const t=pt(e);return Yt(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ze(t)&&Pr(t)?t:sl(t)}function Nr(e,t,r){var n;t===void 0&&(t=[]),r===void 0&&(r=!0);const s=sl(e),a=s===((n=e.ownerDocument)==null?void 0:n.body),i=Ae(s);if(a){const c=oo(i);return t.concat(i,i.visualViewport||[],Pr(s)?s:[],c&&r?Nr(c):[])}return t.concat(s,Nr(s,[],r))}function oo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ol(e){const t=He(e);let r=parseFloat(t.width)||0,n=parseFloat(t.height)||0;const s=Ze(e),a=s?e.offsetWidth:r,i=s?e.offsetHeight:n,c=yn(r)!==a||yn(n)!==i;return c&&(r=a,n=i),{width:r,height:n,$:c}}function Jo(e){return Be(e)?e:e.contextElement}function Vt(e){const t=Jo(e);if(!Ze(t))return Xe(1);const r=t.getBoundingClientRect(),{width:n,height:s,$:a}=ol(t);let i=(a?yn(r.width):r.width)/n,c=(a?yn(r.height):r.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const Dg=Xe(0);function al(e){const t=Ae(e);return!Xo()||!t.visualViewport?Dg:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Pg(e,t,r){return t===void 0&&(t=!1),!r||t&&r!==Ae(e)?!1:t}function Et(e,t,r,n){t===void 0&&(t=!1),r===void 0&&(r=!1);const s=e.getBoundingClientRect(),a=Jo(e);let i=Xe(1);t&&(n?Be(n)&&(i=Vt(n)):i=Vt(e));const c=Pg(a,r,n)?al(a):Xe(0);let l=(s.left+c.x)/i.x,u=(s.top+c.y)/i.y,d=s.width/i.x,m=s.height/i.y;if(a){const h=Ae(a),x=n&&Be(n)?Ae(n):n;let g=h,p=oo(g);for(;p&&n&&x!==g;){const b=Vt(p),v=p.getBoundingClientRect(),y=He(p),w=v.left+(p.clientLeft+parseFloat(y.paddingLeft))*b.x,S=v.top+(p.clientTop+parseFloat(y.paddingTop))*b.y;l*=b.x,u*=b.y,d*=b.x,m*=b.y,l+=w,u+=S,g=Ae(p),p=oo(g)}}return Nn({width:d,height:m,x:l,y:u})}function Qo(e,t){const r=Hn(e).scrollLeft;return t?t.left+r:Et(tt(e)).left+r}function il(e,t,r){r===void 0&&(r=!1);const n=e.getBoundingClientRect(),s=n.left+t.scrollLeft-(r?0:Qo(e,n)),a=n.top+t.scrollTop;return{x:s,y:a}}function Og(e){let{elements:t,rect:r,offsetParent:n,strategy:s}=e;const a=s==="fixed",i=tt(n),c=t?Bn(t.floating):!1;if(n===i||c&&a)return r;let l={scrollLeft:0,scrollTop:0},u=Xe(1);const d=Xe(0),m=Ze(n);if((m||!m&&!a)&&((nr(n)!=="body"||Pr(i))&&(l=Hn(n)),Ze(n))){const x=Et(n);u=Vt(n),d.x=x.x+n.clientLeft,d.y=x.y+n.clientTop}const h=i&&!m&&!a?il(i,l,!0):Xe(0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-l.scrollLeft*u.x+d.x+h.x,y:r.y*u.y-l.scrollTop*u.y+d.y+h.y}}function Ig(e){return Array.from(e.getClientRects())}function Mg(e){const t=tt(e),r=Hn(e),n=e.ownerDocument.body,s=Re(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),a=Re(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight);let i=-r.scrollLeft+Qo(e);const c=-r.scrollTop;return He(n).direction==="rtl"&&(i+=Re(t.clientWidth,n.clientWidth)-s),{width:s,height:a,x:i,y:c}}function Lg(e,t){const r=Ae(e),n=tt(e),s=r.visualViewport;let a=n.clientWidth,i=n.clientHeight,c=0,l=0;if(s){a=s.width,i=s.height;const u=Xo();(!u||u&&t==="fixed")&&(c=s.offsetLeft,l=s.offsetTop)}return{width:a,height:i,x:c,y:l}}const Fg=new Set(["absolute","fixed"]);function $g(e,t){const r=Et(e,!0,t==="fixed"),n=r.top+e.clientTop,s=r.left+e.clientLeft,a=Ze(e)?Vt(e):Xe(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,l=s*a.x,u=n*a.y;return{width:i,height:c,x:l,y:u}}function hi(e,t,r){let n;if(t==="viewport")n=Lg(e,r);else if(t==="document")n=Mg(tt(e));else if(Be(t))n=$g(t,r);else{const s=al(e);n={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return Nn(n)}function cl(e,t){const r=pt(e);return r===t||!Be(r)||Yt(r)?!1:He(r).position==="fixed"||cl(r,t)}function Ug(e,t){const r=t.get(e);if(r)return r;let n=Nr(e,[],!1).filter(c=>Be(c)&&nr(c)!=="body"),s=null;const a=He(e).position==="fixed";let i=a?pt(e):e;for(;Be(i)&&!Yt(i);){const c=He(i),l=Yo(i);!l&&c.position==="fixed"&&(s=null),(a?!l&&!s:!l&&c.position==="static"&&!!s&&Fg.has(s.position)||Pr(i)&&!l&&cl(e,i))?n=n.filter(d=>d!==i):s=c,i=pt(i)}return t.set(e,n),n}function Bg(e){let{element:t,boundary:r,rootBoundary:n,strategy:s}=e;const i=[...r==="clippingAncestors"?Bn(t)?[]:Ug(t,this._c):[].concat(r),n],c=i[0],l=i.reduce((u,d)=>{const m=hi(t,d,s);return u.top=Re(m.top,u.top),u.right=ht(m.right,u.right),u.bottom=ht(m.bottom,u.bottom),u.left=Re(m.left,u.left),u},hi(t,c,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Hg(e){const{width:t,height:r}=ol(e);return{width:t,height:r}}function Vg(e,t,r){const n=Ze(t),s=tt(t),a=r==="fixed",i=Et(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const l=Xe(0);function u(){l.x=Qo(s)}if(n||!n&&!a)if((nr(t)!=="body"||Pr(s))&&(c=Hn(t)),n){const x=Et(t,!0,a,t);l.x=x.x+t.clientLeft,l.y=x.y+t.clientTop}else s&&u();a&&!n&&s&&u();const d=s&&!n&&!a?il(s,c):Xe(0),m=i.left+c.scrollLeft-l.x-d.x,h=i.top+c.scrollTop-l.y-d.y;return{x:m,y:h,width:i.width,height:i.height}}function _s(e){return He(e).position==="static"}function pi(e,t){if(!Ze(e)||He(e).position==="fixed")return null;if(t)return t(e);let r=e.offsetParent;return tt(e)===r&&(r=r.ownerDocument.body),r}function ll(e,t){const r=Ae(e);if(Bn(e))return r;if(!Ze(e)){let s=pt(e);for(;s&&!Yt(s);){if(Be(s)&&!_s(s))return s;s=pt(s)}return r}let n=pi(e,t);for(;n&&jg(n)&&_s(n);)n=pi(n,t);return n&&Yt(n)&&_s(n)&&!Yo(n)?r:n||_g(e)||r}const zg=async function(e){const t=this.getOffsetParent||ll,r=this.getDimensions,n=await r(e.floating);return{reference:Vg(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}};function Wg(e){return He(e).direction==="rtl"}const Gg={convertOffsetParentRelativeRectToViewportRelativeRect:Og,getDocumentElement:tt,getClippingRect:Bg,getOffsetParent:ll,getElementRects:zg,getClientRects:Ig,getDimensions:Hg,getScale:Vt,isElement:Be,isRTL:Wg};function dl(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function qg(e,t){let r=null,n;const s=tt(e);function a(){var c;clearTimeout(n),(c=r)==null||c.disconnect(),r=null}function i(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),a();const u=e.getBoundingClientRect(),{left:d,top:m,width:h,height:x}=u;if(c||t(),!h||!x)return;const g=Kr(m),p=Kr(s.clientWidth-(d+h)),b=Kr(s.clientHeight-(m+x)),v=Kr(d),w={rootMargin:-g+"px "+-p+"px "+-b+"px "+-v+"px",threshold:Re(0,ht(1,l))||1};let S=!0;function j(D){const E=D[0].intersectionRatio;if(E!==l){if(!S)return i();E?i(!1,E):n=setTimeout(()=>{i(!1,1e-7)},1e3)}E===1&&!dl(u,e.getBoundingClientRect())&&i(),S=!1}try{r=new IntersectionObserver(j,{...w,root:s.ownerDocument})}catch{r=new IntersectionObserver(j,w)}r.observe(e)}return i(!0),a}function Kg(e,t,r,n){n===void 0&&(n={});const{ancestorScroll:s=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=n,u=Jo(e),d=s||a?[...u?Nr(u):[],...Nr(t)]:[];d.forEach(v=>{s&&v.addEventListener("scroll",r,{passive:!0}),a&&v.addEventListener("resize",r)});const m=u&&c?qg(u,r):null;let h=-1,x=null;i&&(x=new ResizeObserver(v=>{let[y]=v;y&&y.target===u&&x&&(x.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var w;(w=x)==null||w.observe(t)})),r()}),u&&!l&&x.observe(u),x.observe(t));let g,p=l?Et(e):null;l&&b();function b(){const v=Et(e);p&&!dl(p,v)&&r(),p=v,g=requestAnimationFrame(b)}return r(),()=>{var v;d.forEach(y=>{s&&y.removeEventListener("scroll",r),a&&y.removeEventListener("resize",r)}),m?.(),(v=x)==null||v.disconnect(),x=null,l&&cancelAnimationFrame(g)}}const Yg=vg,Xg=yg,Jg=gg,Qg=Ng,Zg=xg,gi=pg,ex=wg,tx=(e,t,r)=>{const n=new Map,s={platform:Gg,...r},a={...s.platform,_c:n};return hg(e,t,{...s,platform:a})};var rx=typeof document<"u",nx=function(){},sn=rx?f.useLayoutEffect:nx;function Sn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let r,n,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(r=e.length,r!==t.length)return!1;for(n=r;n--!==0;)if(!Sn(e[n],t[n]))return!1;return!0}if(s=Object.keys(e),r=s.length,r!==Object.keys(t).length)return!1;for(n=r;n--!==0;)if(!{}.hasOwnProperty.call(t,s[n]))return!1;for(n=r;n--!==0;){const a=s[n];if(!(a==="_owner"&&e.$$typeof)&&!Sn(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function ul(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function xi(e,t){const r=ul(e);return Math.round(t*r)/r}function As(e){const t=f.useRef(e);return sn(()=>{t.current=e}),t}function sx(e){e===void 0&&(e={});const{placement:t="bottom",strategy:r="absolute",middleware:n=[],platform:s,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:l,open:u}=e,[d,m]=f.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,x]=f.useState(n);Sn(h,n)||x(n);const[g,p]=f.useState(null),[b,v]=f.useState(null),y=f.useCallback(C=>{C!==D.current&&(D.current=C,p(C))},[]),w=f.useCallback(C=>{C!==E.current&&(E.current=C,v(C))},[]),S=a||g,j=i||b,D=f.useRef(null),E=f.useRef(null),T=f.useRef(d),N=l!=null,_=As(l),I=As(s),H=As(u),A=f.useCallback(()=>{if(!D.current||!E.current)return;const C={placement:t,strategy:r,middleware:h};I.current&&(C.platform=I.current),tx(D.current,E.current,C).then(R=>{const K={...R,isPositioned:H.current!==!1};$.current&&!Sn(T.current,K)&&(T.current=K,Rr.flushSync(()=>{m(K)}))})},[h,t,r,I,H]);sn(()=>{u===!1&&T.current.isPositioned&&(T.current.isPositioned=!1,m(C=>({...C,isPositioned:!1})))},[u]);const $=f.useRef(!1);sn(()=>($.current=!0,()=>{$.current=!1}),[]),sn(()=>{if(S&&(D.current=S),j&&(E.current=j),S&&j){if(_.current)return _.current(S,j,A);A()}},[S,j,A,_,N]);const V=f.useMemo(()=>({reference:D,floating:E,setReference:y,setFloating:w}),[y,w]),F=f.useMemo(()=>({reference:S,floating:j}),[S,j]),z=f.useMemo(()=>{const C={position:r,left:0,top:0};if(!F.floating)return C;const R=xi(F.floating,d.x),K=xi(F.floating,d.y);return c?{...C,transform:"translate("+R+"px, "+K+"px)",...ul(F.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:R,top:K}},[r,c,F.floating,d.x,d.y]);return f.useMemo(()=>({...d,update:A,refs:V,elements:F,floatingStyles:z}),[d,A,V,F,z])}const ox=e=>{function t(r){return{}.hasOwnProperty.call(r,"current")}return{name:"arrow",options:e,fn(r){const{element:n,padding:s}=typeof e=="function"?e(r):e;return n&&t(n)?n.current!=null?gi({element:n.current,padding:s}).fn(r):{}:n?gi({element:n,padding:s}).fn(r):{}}}},ax=(e,t)=>({...Yg(e),options:[e,t]}),ix=(e,t)=>({...Xg(e),options:[e,t]}),cx=(e,t)=>({...ex(e),options:[e,t]}),lx=(e,t)=>({...Jg(e),options:[e,t]}),dx=(e,t)=>({...Qg(e),options:[e,t]}),ux=(e,t)=>({...Zg(e),options:[e,t]}),fx=(e,t)=>({...ox(e),options:[e,t]});var mx="Arrow",fl=f.forwardRef((e,t)=>{const{children:r,width:n=10,height:s=5,...a}=e;return o.jsx(U.svg,{...a,ref:t,width:n,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:o.jsx("polygon",{points:"0,0 30,0 15,10"})})});fl.displayName=mx;var hx=fl;function Zo(e){const[t,r]=f.useState(void 0);return fe(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const n=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const a=s[0];let i,c;if("borderBoxSize"in a){const l=a.borderBoxSize,u=Array.isArray(l)?l[0]:l;i=u.inlineSize,c=u.blockSize}else i=e.offsetWidth,c=e.offsetHeight;r({width:i,height:c})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}else r(void 0)},[e]),t}var ea="Popper",[ml,gt]=Ne(ea),[px,hl]=ml(ea),pl=e=>{const{__scopePopper:t,children:r}=e,[n,s]=f.useState(null);return o.jsx(px,{scope:t,anchor:n,onAnchorChange:s,children:r})};pl.displayName=ea;var gl="PopperAnchor",xl=f.forwardRef((e,t)=>{const{__scopePopper:r,virtualRef:n,...s}=e,a=hl(gl,r),i=f.useRef(null),c=G(t,i);return f.useEffect(()=>{a.onAnchorChange(n?.current||i.current)}),n?null:o.jsx(U.div,{...s,ref:c})});xl.displayName=gl;var ta="PopperContent",[gx,xx]=ml(ta),bl=f.forwardRef((e,t)=>{const{__scopePopper:r,side:n="bottom",sideOffset:s=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:l=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:x="optimized",onPlaced:g,...p}=e,b=hl(ta,r),[v,y]=f.useState(null),w=G(t,O=>y(O)),[S,j]=f.useState(null),D=Zo(S),E=D?.width??0,T=D?.height??0,N=n+(a!=="center"?"-"+a:""),_=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},I=Array.isArray(u)?u:[u],H=I.length>0,A={padding:_,boundary:I.filter(vx),altBoundary:H},{refs:$,floatingStyles:V,placement:F,isPositioned:z,middlewareData:C}=sx({strategy:"fixed",placement:N,whileElementsMounted:(...O)=>Kg(...O,{animationFrame:x==="always"}),elements:{reference:b.anchor},middleware:[ax({mainAxis:s+T,alignmentAxis:i}),l&&ix({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?cx():void 0,...A}),l&&lx({...A}),dx({...A,apply:({elements:O,rects:ee,availableWidth:le,availableHeight:Z})=>{const{width:te,height:oe}=ee.reference,De=O.floating.style;De.setProperty("--radix-popper-available-width",`${le}px`),De.setProperty("--radix-popper-available-height",`${Z}px`),De.setProperty("--radix-popper-anchor-width",`${te}px`),De.setProperty("--radix-popper-anchor-height",`${oe}px`)}}),S&&fx({element:S,padding:c}),yx({arrowWidth:E,arrowHeight:T}),h&&ux({strategy:"referenceHidden",...A})]}),[R,K]=wl(F),he=pe(g);fe(()=>{z&&he?.()},[z,he]);const Se=C.arrow?.x,se=C.arrow?.y,re=C.arrow?.centerOffset!==0,[je,be]=f.useState();return fe(()=>{v&&be(window.getComputedStyle(v).zIndex)},[v]),o.jsx("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:z?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:je,"--radix-popper-transform-origin":[C.transformOrigin?.x,C.transformOrigin?.y].join(" "),...C.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:o.jsx(gx,{scope:r,placedSide:R,onArrowChange:j,arrowX:Se,arrowY:se,shouldHideArrow:re,children:o.jsx(U.div,{"data-side":R,"data-align":K,...p,ref:w,style:{...p.style,animation:z?void 0:"none"}})})})});bl.displayName=ta;var vl="PopperArrow",bx={top:"bottom",right:"left",bottom:"top",left:"right"},yl=f.forwardRef(function(t,r){const{__scopePopper:n,...s}=t,a=xx(vl,n),i=bx[a.placedSide];return o.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:o.jsx(hx,{...s,ref:r,style:{...s.style,display:"block"}})})});yl.displayName=vl;function vx(e){return e!==null}var yx=e=>({name:"transformOrigin",options:e,fn(t){const{placement:r,rects:n,middlewareData:s}=t,i=s.arrow?.centerOffset!==0,c=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,d]=wl(r),m={start:"0%",center:"50%",end:"100%"}[d],h=(s.arrow?.x??0)+c/2,x=(s.arrow?.y??0)+l/2;let g="",p="";return u==="bottom"?(g=i?m:`${h}px`,p=`${-l}px`):u==="top"?(g=i?m:`${h}px`,p=`${n.floating.height+l}px`):u==="right"?(g=`${-l}px`,p=i?m:`${x}px`):u==="left"&&(g=`${n.floating.width+l}px`,p=i?m:`${x}px`),{data:{x:g,y:p}}}});function wl(e){const[t,r="center"]=e.split("-");return[t,r]}var Vn=pl,Or=xl,zn=bl,Wn=yl,Nl=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),wx="VisuallyHidden",Sl=f.forwardRef((e,t)=>o.jsx(U.span,{...e,ref:t,style:{...Nl,...e.style}}));Sl.displayName=wx;var Nx=Sl,[Gn,_S]=Ne("Tooltip",[gt]),qn=gt(),kl="TooltipProvider",Sx=700,ao="tooltip.open",[kx,ra]=Gn(kl),jl=e=>{const{__scopeTooltip:t,delayDuration:r=Sx,skipDelayDuration:n=300,disableHoverableContent:s=!1,children:a}=e,i=f.useRef(!0),c=f.useRef(!1),l=f.useRef(0);return f.useEffect(()=>{const u=l.current;return()=>window.clearTimeout(u)},[]),o.jsx(kx,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:f.useCallback(()=>{window.clearTimeout(l.current),i.current=!1},[]),onClose:f.useCallback(()=>{window.clearTimeout(l.current),l.current=window.setTimeout(()=>i.current=!0,n)},[n]),isPointerInTransitRef:c,onPointerInTransitChange:f.useCallback(u=>{c.current=u},[]),disableHoverableContent:s,children:a})};jl.displayName=kl;var Sr="Tooltip",[jx,Kn]=Gn(Sr),Cl=e=>{const{__scopeTooltip:t,children:r,open:n,defaultOpen:s,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,l=ra(Sr,e.__scopeTooltip),u=qn(t),[d,m]=f.useState(null),h=Ie(),x=f.useRef(0),g=i??l.disableHoverableContent,p=c??l.delayDuration,b=f.useRef(!1),[v,y]=Qe({prop:n,defaultProp:s??!1,onChange:E=>{E?(l.onOpen(),document.dispatchEvent(new CustomEvent(ao))):l.onClose(),a?.(E)},caller:Sr}),w=f.useMemo(()=>v?b.current?"delayed-open":"instant-open":"closed",[v]),S=f.useCallback(()=>{window.clearTimeout(x.current),x.current=0,b.current=!1,y(!0)},[y]),j=f.useCallback(()=>{window.clearTimeout(x.current),x.current=0,y(!1)},[y]),D=f.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{b.current=!0,y(!0),x.current=0},p)},[p,y]);return f.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),o.jsx(Vn,{...u,children:o.jsx(jx,{scope:t,contentId:h,open:v,stateAttribute:w,trigger:d,onTriggerChange:m,onTriggerEnter:f.useCallback(()=>{l.isOpenDelayedRef.current?D():S()},[l.isOpenDelayedRef,D,S]),onTriggerLeave:f.useCallback(()=>{g?j():(window.clearTimeout(x.current),x.current=0)},[j,g]),onOpen:S,onClose:j,disableHoverableContent:g,children:r})})};Cl.displayName=Sr;var io="TooltipTrigger",Tl=f.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,s=Kn(io,r),a=ra(io,r),i=qn(r),c=f.useRef(null),l=G(t,c,s.onTriggerChange),u=f.useRef(!1),d=f.useRef(!1),m=f.useCallback(()=>u.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),o.jsx(Or,{asChild:!0,...i,children:o.jsx(U.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...n,ref:l,onPointerMove:P(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!a.isPointerInTransitRef.current&&(s.onTriggerEnter(),d.current=!0)}),onPointerLeave:P(e.onPointerLeave,()=>{s.onTriggerLeave(),d.current=!1}),onPointerDown:P(e.onPointerDown,()=>{s.open&&s.onClose(),u.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:P(e.onFocus,()=>{u.current||s.onOpen()}),onBlur:P(e.onBlur,s.onClose),onClick:P(e.onClick,s.onClose)})})});Tl.displayName=io;var Cx="TooltipPortal",[AS,Tx]=Gn(Cx,{forceMount:void 0}),Xt="TooltipContent",El=f.forwardRef((e,t)=>{const r=Tx(Xt,e.__scopeTooltip),{forceMount:n=r.forceMount,side:s="top",...a}=e,i=Kn(Xt,e.__scopeTooltip);return o.jsx(xe,{present:n||i.open,children:i.disableHoverableContent?o.jsx(Rl,{side:s,...a,ref:t}):o.jsx(Ex,{side:s,...a,ref:t})})}),Ex=f.forwardRef((e,t)=>{const r=Kn(Xt,e.__scopeTooltip),n=ra(Xt,e.__scopeTooltip),s=f.useRef(null),a=G(t,s),[i,c]=f.useState(null),{trigger:l,onClose:u}=r,d=s.current,{onPointerInTransitChange:m}=n,h=f.useCallback(()=>{c(null),m(!1)},[m]),x=f.useCallback((g,p)=>{const b=g.currentTarget,v={x:g.clientX,y:g.clientY},y=Px(v,b.getBoundingClientRect()),w=Ox(v,y),S=Ix(p.getBoundingClientRect()),j=Lx([...w,...S]);c(j),m(!0)},[m]);return f.useEffect(()=>()=>h(),[h]),f.useEffect(()=>{if(l&&d){const g=b=>x(b,d),p=b=>x(b,l);return l.addEventListener("pointerleave",g),d.addEventListener("pointerleave",p),()=>{l.removeEventListener("pointerleave",g),d.removeEventListener("pointerleave",p)}}},[l,d,x,h]),f.useEffect(()=>{if(i){const g=p=>{const b=p.target,v={x:p.clientX,y:p.clientY},y=l?.contains(b)||d?.contains(b),w=!Mx(v,i);y?h():w&&(h(),u())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[l,d,i,u,h]),o.jsx(Rl,{...e,ref:a})}),[Rx,_x]=Gn(Sr,{isInside:!1}),Ax=Vm("TooltipContent"),Rl=f.forwardRef((e,t)=>{const{__scopeTooltip:r,children:n,"aria-label":s,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,l=Kn(Xt,r),u=qn(r),{onClose:d}=l;return f.useEffect(()=>(document.addEventListener(ao,d),()=>document.removeEventListener(ao,d)),[d]),f.useEffect(()=>{if(l.trigger){const m=h=>{h.target?.contains(l.trigger)&&d()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[l.trigger,d]),o.jsx(tr,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:m=>m.preventDefault(),onDismiss:d,children:o.jsxs(zn,{"data-state":l.stateAttribute,...u,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[o.jsx(Ax,{children:n}),o.jsx(Rx,{scope:r,isInside:!0,children:o.jsx(Nx,{id:l.contentId,role:"tooltip",children:s||n})})]})})});El.displayName=Xt;var _l="TooltipArrow",Dx=f.forwardRef((e,t)=>{const{__scopeTooltip:r,...n}=e,s=qn(r);return _x(_l,r).isInside?null:o.jsx(Wn,{...s,...n,ref:t})});Dx.displayName=_l;function Px(e,t){const r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,s,a)){case a:return"left";case s:return"right";case r:return"top";case n:return"bottom";default:throw new Error("unreachable")}}function Ox(e,t,r=5){const n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r});break}return n}function Ix(e){const{top:t,right:r,bottom:n,left:s}=e;return[{x:s,y:t},{x:r,y:t},{x:r,y:n},{x:s,y:n}]}function Mx(e,t){const{x:r,y:n}=e;let s=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],l=t[i],u=c.x,d=c.y,m=l.x,h=l.y;d>n!=h>n&&r<(m-u)*(n-d)/(h-d)+u&&(s=!s)}return s}function Lx(e){const t=e.slice();return t.sort((r,n)=>r.x<n.x?-1:r.x>n.x?1:r.y<n.y?-1:r.y>n.y?1:0),Fx(t)}function Fx(e){if(e.length<=1)return e.slice();const t=[];for(let n=0;n<e.length;n++){const s=e[n];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}t.pop();const r=[];for(let n=e.length-1;n>=0;n--){const s=e[n];for(;r.length>=2;){const a=r[r.length-1],i=r[r.length-2];if((a.x-i.x)*(s.y-i.y)>=(a.y-i.y)*(s.x-i.x))r.pop();else break}r.push(s)}return r.pop(),t.length===1&&r.length===1&&t[0].x===r[0].x&&t[0].y===r[0].y?t:t.concat(r)}var DS=jl,PS=Cl,OS=Tl,IS=El;function Al(e,t){return function(){return e.apply(t,arguments)}}const{toString:$x}=Object.prototype,{getPrototypeOf:na}=Object,{iterator:Yn,toStringTag:Dl}=Symbol,Xn=(e=>t=>{const r=$x.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ze=e=>(e=e.toLowerCase(),t=>Xn(t)===e),Jn=e=>t=>typeof t===e,{isArray:sr}=Array,kr=Jn("undefined");function Ux(e){return e!==null&&!kr(e)&&e.constructor!==null&&!kr(e.constructor)&&Ce(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Pl=ze("ArrayBuffer");function Bx(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Pl(e.buffer),t}const Hx=Jn("string"),Ce=Jn("function"),Ol=Jn("number"),Qn=e=>e!==null&&typeof e=="object",Vx=e=>e===!0||e===!1,on=e=>{if(Xn(e)!=="object")return!1;const t=na(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Dl in e)&&!(Yn in e)},zx=ze("Date"),Wx=ze("File"),Gx=ze("Blob"),qx=ze("FileList"),Kx=e=>Qn(e)&&Ce(e.pipe),Yx=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ce(e.append)&&((t=Xn(e))==="formdata"||t==="object"&&Ce(e.toString)&&e.toString()==="[object FormData]"))},Xx=ze("URLSearchParams"),[Jx,Qx,Zx,eb]=["ReadableStream","Request","Response","Headers"].map(ze),tb=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Ir(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),sr(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const a=r?Object.getOwnPropertyNames(e):Object.keys(e),i=a.length;let c;for(n=0;n<i;n++)c=a[n],t.call(null,e[c],c,e)}}function Il(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const St=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Ml=e=>!kr(e)&&e!==St;function co(){const{caseless:e}=Ml(this)&&this||{},t={},r=(n,s)=>{const a=e&&Il(t,s)||s;on(t[a])&&on(n)?t[a]=co(t[a],n):on(n)?t[a]=co({},n):sr(n)?t[a]=n.slice():t[a]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&Ir(arguments[n],r);return t}const rb=(e,t,r,{allOwnKeys:n}={})=>(Ir(t,(s,a)=>{r&&Ce(s)?e[a]=Al(s,r):e[a]=s},{allOwnKeys:n}),e),nb=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),sb=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},ob=(e,t,r,n)=>{let s,a,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),a=s.length;a-- >0;)i=s[a],(!n||n(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=r!==!1&&na(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},ab=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},ib=e=>{if(!e)return null;if(sr(e))return e;let t=e.length;if(!Ol(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},cb=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&na(Uint8Array)),lb=(e,t)=>{const n=(e&&e[Yn]).call(e);let s;for(;(s=n.next())&&!s.done;){const a=s.value;t.call(e,a[0],a[1])}},db=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},ub=ze("HTMLFormElement"),fb=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),bi=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),mb=ze("RegExp"),Ll=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};Ir(r,(s,a)=>{let i;(i=t(s,a,e))!==!1&&(n[a]=i||s)}),Object.defineProperties(e,n)},hb=e=>{Ll(e,(t,r)=>{if(Ce(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(Ce(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},pb=(e,t)=>{const r={},n=s=>{s.forEach(a=>{r[a]=!0})};return sr(e)?n(e):n(String(e).split(t)),r},gb=()=>{},xb=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function bb(e){return!!(e&&Ce(e.append)&&e[Dl]==="FormData"&&e[Yn])}const vb=e=>{const t=new Array(10),r=(n,s)=>{if(Qn(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const a=sr(n)?[]:{};return Ir(n,(i,c)=>{const l=r(i,s+1);!kr(l)&&(a[c]=l)}),t[s]=void 0,a}}return n};return r(e,0)},yb=ze("AsyncFunction"),wb=e=>e&&(Qn(e)||Ce(e))&&Ce(e.then)&&Ce(e.catch),Fl=((e,t)=>e?setImmediate:t?((r,n)=>(St.addEventListener("message",({source:s,data:a})=>{s===St&&a===r&&n.length&&n.shift()()},!1),s=>{n.push(s),St.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",Ce(St.postMessage)),Nb=typeof queueMicrotask<"u"?queueMicrotask.bind(St):typeof process<"u"&&process.nextTick||Fl,Sb=e=>e!=null&&Ce(e[Yn]),k={isArray:sr,isArrayBuffer:Pl,isBuffer:Ux,isFormData:Yx,isArrayBufferView:Bx,isString:Hx,isNumber:Ol,isBoolean:Vx,isObject:Qn,isPlainObject:on,isReadableStream:Jx,isRequest:Qx,isResponse:Zx,isHeaders:eb,isUndefined:kr,isDate:zx,isFile:Wx,isBlob:Gx,isRegExp:mb,isFunction:Ce,isStream:Kx,isURLSearchParams:Xx,isTypedArray:cb,isFileList:qx,forEach:Ir,merge:co,extend:rb,trim:tb,stripBOM:nb,inherits:sb,toFlatObject:ob,kindOf:Xn,kindOfTest:ze,endsWith:ab,toArray:ib,forEachEntry:lb,matchAll:db,isHTMLForm:ub,hasOwnProperty:bi,hasOwnProp:bi,reduceDescriptors:Ll,freezeMethods:hb,toObjectSet:pb,toCamelCase:fb,noop:gb,toFiniteNumber:xb,findKey:Il,global:St,isContextDefined:Ml,isSpecCompliantForm:bb,toJSONObject:vb,isAsyncFn:yb,isThenable:wb,setImmediate:Fl,asap:Nb,isIterable:Sb};function W(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}k.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:k.toJSONObject(this.config),code:this.code,status:this.status}}});const $l=W.prototype,Ul={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ul[e]={value:e}});Object.defineProperties(W,Ul);Object.defineProperty($l,"isAxiosError",{value:!0});W.from=(e,t,r,n,s,a)=>{const i=Object.create($l);return k.toFlatObject(e,i,function(l){return l!==Error.prototype},c=>c!=="isAxiosError"),W.call(i,e.message,t,r,n,s),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const kb=null;function lo(e){return k.isPlainObject(e)||k.isArray(e)}function Bl(e){return k.endsWith(e,"[]")?e.slice(0,-2):e}function vi(e,t,r){return e?e.concat(t).map(function(s,a){return s=Bl(s),!r&&a?"["+s+"]":s}).join(r?".":""):t}function jb(e){return k.isArray(e)&&!e.some(lo)}const Cb=k.toFlatObject(k,{},null,function(t){return/^is[A-Z]/.test(t)});function Zn(e,t,r){if(!k.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=k.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(p,b){return!k.isUndefined(b[p])});const n=r.metaTokens,s=r.visitor||d,a=r.dots,i=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&k.isSpecCompliantForm(t);if(!k.isFunction(s))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(k.isDate(g))return g.toISOString();if(k.isBoolean(g))return g.toString();if(!l&&k.isBlob(g))throw new W("Blob is not supported. Use a Buffer instead.");return k.isArrayBuffer(g)||k.isTypedArray(g)?l&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function d(g,p,b){let v=g;if(g&&!b&&typeof g=="object"){if(k.endsWith(p,"{}"))p=n?p:p.slice(0,-2),g=JSON.stringify(g);else if(k.isArray(g)&&jb(g)||(k.isFileList(g)||k.endsWith(p,"[]"))&&(v=k.toArray(g)))return p=Bl(p),v.forEach(function(w,S){!(k.isUndefined(w)||w===null)&&t.append(i===!0?vi([p],S,a):i===null?p:p+"[]",u(w))}),!1}return lo(g)?!0:(t.append(vi(b,p,a),u(g)),!1)}const m=[],h=Object.assign(Cb,{defaultVisitor:d,convertValue:u,isVisitable:lo});function x(g,p){if(!k.isUndefined(g)){if(m.indexOf(g)!==-1)throw Error("Circular reference detected in "+p.join("."));m.push(g),k.forEach(g,function(v,y){(!(k.isUndefined(v)||v===null)&&s.call(t,v,k.isString(y)?y.trim():y,p,h))===!0&&x(v,p?p.concat(y):[y])}),m.pop()}}if(!k.isObject(e))throw new TypeError("data must be an object");return x(e),t}function yi(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function sa(e,t){this._pairs=[],e&&Zn(e,this,t)}const Hl=sa.prototype;Hl.append=function(t,r){this._pairs.push([t,r])};Hl.toString=function(t){const r=t?function(n){return t.call(this,n,yi)}:yi;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function Tb(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Vl(e,t,r){if(!t)return e;const n=r&&r.encode||Tb;k.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let a;if(s?a=s(t,r):a=k.isURLSearchParams(t)?t.toString():new sa(t,r).toString(n),a){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+a}return e}class wi{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){k.forEach(this.handlers,function(n){n!==null&&t(n)})}}const zl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Eb=typeof URLSearchParams<"u"?URLSearchParams:sa,Rb=typeof FormData<"u"?FormData:null,_b=typeof Blob<"u"?Blob:null,Ab={isBrowser:!0,classes:{URLSearchParams:Eb,FormData:Rb,Blob:_b},protocols:["http","https","file","blob","url","data"]},oa=typeof window<"u"&&typeof document<"u",uo=typeof navigator=="object"&&navigator||void 0,Db=oa&&(!uo||["ReactNative","NativeScript","NS"].indexOf(uo.product)<0),Pb=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ob=oa&&window.location.href||"http://localhost",Ib=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:oa,hasStandardBrowserEnv:Db,hasStandardBrowserWebWorkerEnv:Pb,navigator:uo,origin:Ob},Symbol.toStringTag,{value:"Module"})),we={...Ib,...Ab};function Mb(e,t){return Zn(e,new we.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,a){return we.isNode&&k.isBuffer(r)?(this.append(n,r.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},t))}function Lb(e){return k.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Fb(e){const t={},r=Object.keys(e);let n;const s=r.length;let a;for(n=0;n<s;n++)a=r[n],t[a]=e[a];return t}function Wl(e){function t(r,n,s,a){let i=r[a++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),l=a>=r.length;return i=!i&&k.isArray(s)?s.length:i,l?(k.hasOwnProp(s,i)?s[i]=[s[i],n]:s[i]=n,!c):((!s[i]||!k.isObject(s[i]))&&(s[i]=[]),t(r,n,s[i],a)&&k.isArray(s[i])&&(s[i]=Fb(s[i])),!c)}if(k.isFormData(e)&&k.isFunction(e.entries)){const r={};return k.forEachEntry(e,(n,s)=>{t(Lb(n),s,r,0)}),r}return null}function $b(e,t,r){if(k.isString(e))try{return(t||JSON.parse)(e),k.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const Mr={transitional:zl,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,a=k.isObject(t);if(a&&k.isHTMLForm(t)&&(t=new FormData(t)),k.isFormData(t))return s?JSON.stringify(Wl(t)):t;if(k.isArrayBuffer(t)||k.isBuffer(t)||k.isStream(t)||k.isFile(t)||k.isBlob(t)||k.isReadableStream(t))return t;if(k.isArrayBufferView(t))return t.buffer;if(k.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Mb(t,this.formSerializer).toString();if((c=k.isFileList(t))||n.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Zn(c?{"files[]":t}:t,l&&new l,this.formSerializer)}}return a||s?(r.setContentType("application/json",!1),$b(t)):t}],transformResponse:[function(t){const r=this.transitional||Mr.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(k.isResponse(t)||k.isReadableStream(t))return t;if(t&&k.isString(t)&&(n&&!this.responseType||s)){const i=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?W.from(c,W.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};k.forEach(["delete","get","head","post","put","patch"],e=>{Mr.headers[e]={}});const Ub=k.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Bb=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),r=i.substring(0,s).trim().toLowerCase(),n=i.substring(s+1).trim(),!(!r||t[r]&&Ub[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Ni=Symbol("internals");function ur(e){return e&&String(e).trim().toLowerCase()}function an(e){return e===!1||e==null?e:k.isArray(e)?e.map(an):String(e)}function Hb(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Vb=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ds(e,t,r,n,s){if(k.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!k.isString(t)){if(k.isString(n))return t.indexOf(n)!==-1;if(k.isRegExp(n))return n.test(t)}}function zb(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function Wb(e,t){const r=k.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,a,i){return this[n].call(this,t,s,a,i)},configurable:!0})})}let Te=class{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function a(c,l,u){const d=ur(l);if(!d)throw new Error("header name must be a non-empty string");const m=k.findKey(s,d);(!m||s[m]===void 0||u===!0||u===void 0&&s[m]!==!1)&&(s[m||l]=an(c))}const i=(c,l)=>k.forEach(c,(u,d)=>a(u,d,l));if(k.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(k.isString(t)&&(t=t.trim())&&!Vb(t))i(Bb(t),r);else if(k.isObject(t)&&k.isIterable(t)){let c={},l,u;for(const d of t){if(!k.isArray(d))throw TypeError("Object iterator must return a key-value pair");c[u=d[0]]=(l=c[u])?k.isArray(l)?[...l,d[1]]:[l,d[1]]:d[1]}i(c,r)}else t!=null&&a(r,t,n);return this}get(t,r){if(t=ur(t),t){const n=k.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return Hb(s);if(k.isFunction(r))return r.call(this,s,n);if(k.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=ur(t),t){const n=k.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||Ds(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function a(i){if(i=ur(i),i){const c=k.findKey(n,i);c&&(!r||Ds(n,n[c],c,r))&&(delete n[c],s=!0)}}return k.isArray(t)?t.forEach(a):a(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const a=r[n];(!t||Ds(this,this[a],a,t,!0))&&(delete this[a],s=!0)}return s}normalize(t){const r=this,n={};return k.forEach(this,(s,a)=>{const i=k.findKey(n,a);if(i){r[i]=an(s),delete r[a];return}const c=t?zb(a):String(a).trim();c!==a&&delete r[a],r[c]=an(s),n[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return k.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&k.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[Ni]=this[Ni]={accessors:{}}).accessors,s=this.prototype;function a(i){const c=ur(i);n[c]||(Wb(s,i),n[c]=!0)}return k.isArray(t)?t.forEach(a):a(t),this}};Te.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);k.reduceDescriptors(Te.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});k.freezeMethods(Te);function Ps(e,t){const r=this||Mr,n=t||r,s=Te.from(n.headers);let a=n.data;return k.forEach(e,function(c){a=c.call(r,a,s.normalize(),t?t.status:void 0)}),s.normalize(),a}function Gl(e){return!!(e&&e.__CANCEL__)}function or(e,t,r){W.call(this,e??"canceled",W.ERR_CANCELED,t,r),this.name="CanceledError"}k.inherits(or,W,{__CANCEL__:!0});function ql(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new W("Request failed with status code "+r.status,[W.ERR_BAD_REQUEST,W.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Gb(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function qb(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,a=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),d=n[a];i||(i=u),r[s]=l,n[s]=u;let m=a,h=0;for(;m!==s;)h+=r[m++],m=m%e;if(s=(s+1)%e,s===a&&(a=(a+1)%e),u-i<t)return;const x=d&&u-d;return x?Math.round(h*1e3/x):void 0}}function Kb(e,t){let r=0,n=1e3/t,s,a;const i=(u,d=Date.now())=>{r=d,s=null,a&&(clearTimeout(a),a=null),e.apply(null,u)};return[(...u)=>{const d=Date.now(),m=d-r;m>=n?i(u,d):(s=u,a||(a=setTimeout(()=>{a=null,i(s)},n-m)))},()=>s&&i(s)]}const kn=(e,t,r=3)=>{let n=0;const s=qb(50,250);return Kb(a=>{const i=a.loaded,c=a.lengthComputable?a.total:void 0,l=i-n,u=s(l),d=i<=c;n=i;const m={loaded:i,total:c,progress:c?i/c:void 0,bytes:l,rate:u||void 0,estimated:u&&c&&d?(c-i)/u:void 0,event:a,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(m)},r)},Si=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},ki=e=>(...t)=>k.asap(()=>e(...t)),Yb=we.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,we.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,Xb=we.hasStandardBrowserEnv?{write(e,t,r,n,s,a){const i=[e+"="+encodeURIComponent(t)];k.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),k.isString(n)&&i.push("path="+n),k.isString(s)&&i.push("domain="+s),a===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Jb(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Qb(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Kl(e,t,r){let n=!Jb(t);return e&&(n||r==!1)?Qb(e,t):t}const ji=e=>e instanceof Te?{...e}:e;function Rt(e,t){t=t||{};const r={};function n(u,d,m,h){return k.isPlainObject(u)&&k.isPlainObject(d)?k.merge.call({caseless:h},u,d):k.isPlainObject(d)?k.merge({},d):k.isArray(d)?d.slice():d}function s(u,d,m,h){if(k.isUndefined(d)){if(!k.isUndefined(u))return n(void 0,u,m,h)}else return n(u,d,m,h)}function a(u,d){if(!k.isUndefined(d))return n(void 0,d)}function i(u,d){if(k.isUndefined(d)){if(!k.isUndefined(u))return n(void 0,u)}else return n(void 0,d)}function c(u,d,m){if(m in t)return n(u,d);if(m in e)return n(void 0,u)}const l={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,d,m)=>s(ji(u),ji(d),m,!0)};return k.forEach(Object.keys(Object.assign({},e,t)),function(d){const m=l[d]||s,h=m(e[d],t[d],d);k.isUndefined(h)&&m!==c||(r[d]=h)}),r}const Yl=e=>{const t=Rt({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:a,headers:i,auth:c}=t;t.headers=i=Te.from(i),t.url=Vl(Kl(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let l;if(k.isFormData(r)){if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...d]=l?l.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...d].join("; "))}}if(we.hasStandardBrowserEnv&&(n&&k.isFunction(n)&&(n=n(t)),n||n!==!1&&Yb(t.url))){const u=s&&a&&Xb.read(a);u&&i.set(s,u)}return t},Zb=typeof XMLHttpRequest<"u",e0=Zb&&function(e){return new Promise(function(r,n){const s=Yl(e);let a=s.data;const i=Te.from(s.headers).normalize();let{responseType:c,onUploadProgress:l,onDownloadProgress:u}=s,d,m,h,x,g;function p(){x&&x(),g&&g(),s.cancelToken&&s.cancelToken.unsubscribe(d),s.signal&&s.signal.removeEventListener("abort",d)}let b=new XMLHttpRequest;b.open(s.method.toUpperCase(),s.url,!0),b.timeout=s.timeout;function v(){if(!b)return;const w=Te.from("getAllResponseHeaders"in b&&b.getAllResponseHeaders()),j={data:!c||c==="text"||c==="json"?b.responseText:b.response,status:b.status,statusText:b.statusText,headers:w,config:e,request:b};ql(function(E){r(E),p()},function(E){n(E),p()},j),b=null}"onloadend"in b?b.onloadend=v:b.onreadystatechange=function(){!b||b.readyState!==4||b.status===0&&!(b.responseURL&&b.responseURL.indexOf("file:")===0)||setTimeout(v)},b.onabort=function(){b&&(n(new W("Request aborted",W.ECONNABORTED,e,b)),b=null)},b.onerror=function(){n(new W("Network Error",W.ERR_NETWORK,e,b)),b=null},b.ontimeout=function(){let S=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const j=s.transitional||zl;s.timeoutErrorMessage&&(S=s.timeoutErrorMessage),n(new W(S,j.clarifyTimeoutError?W.ETIMEDOUT:W.ECONNABORTED,e,b)),b=null},a===void 0&&i.setContentType(null),"setRequestHeader"in b&&k.forEach(i.toJSON(),function(S,j){b.setRequestHeader(j,S)}),k.isUndefined(s.withCredentials)||(b.withCredentials=!!s.withCredentials),c&&c!=="json"&&(b.responseType=s.responseType),u&&([h,g]=kn(u,!0),b.addEventListener("progress",h)),l&&b.upload&&([m,x]=kn(l),b.upload.addEventListener("progress",m),b.upload.addEventListener("loadend",x)),(s.cancelToken||s.signal)&&(d=w=>{b&&(n(!w||w.type?new or(null,e,b):w),b.abort(),b=null)},s.cancelToken&&s.cancelToken.subscribe(d),s.signal&&(s.signal.aborted?d():s.signal.addEventListener("abort",d)));const y=Gb(s.url);if(y&&we.protocols.indexOf(y)===-1){n(new W("Unsupported protocol "+y+":",W.ERR_BAD_REQUEST,e));return}b.send(a||null)})},t0=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const a=function(u){if(!s){s=!0,c();const d=u instanceof Error?u:this.reason;n.abort(d instanceof W?d:new or(d instanceof Error?d.message:d))}};let i=t&&setTimeout(()=>{i=null,a(new W(`timeout ${t} of ms exceeded`,W.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(a):u.removeEventListener("abort",a)}),e=null)};e.forEach(u=>u.addEventListener("abort",a));const{signal:l}=n;return l.unsubscribe=()=>k.asap(c),l}},r0=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},n0=async function*(e,t){for await(const r of s0(e))yield*r0(r,t)},s0=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Ci=(e,t,r,n)=>{const s=n0(e,t);let a=0,i,c=l=>{i||(i=!0,n&&n(l))};return new ReadableStream({async pull(l){try{const{done:u,value:d}=await s.next();if(u){c(),l.close();return}let m=d.byteLength;if(r){let h=a+=m;r(h)}l.enqueue(new Uint8Array(d))}catch(u){throw c(u),u}},cancel(l){return c(l),s.return()}},{highWaterMark:2})},es=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Xl=es&&typeof ReadableStream=="function",o0=es&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Jl=(e,...t)=>{try{return!!e(...t)}catch{return!1}},a0=Xl&&Jl(()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ti=64*1024,fo=Xl&&Jl(()=>k.isReadableStream(new Response("").body)),jn={stream:fo&&(e=>e.body)};es&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!jn[t]&&(jn[t]=k.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new W(`Response type '${t}' is not supported`,W.ERR_NOT_SUPPORT,n)})})})(new Response);const i0=async e=>{if(e==null)return 0;if(k.isBlob(e))return e.size;if(k.isSpecCompliantForm(e))return(await new Request(we.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(k.isArrayBufferView(e)||k.isArrayBuffer(e))return e.byteLength;if(k.isURLSearchParams(e)&&(e=e+""),k.isString(e))return(await o0(e)).byteLength},c0=async(e,t)=>{const r=k.toFiniteNumber(e.getContentLength());return r??i0(t)},l0=es&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:a,timeout:i,onDownloadProgress:c,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:h}=Yl(e);u=u?(u+"").toLowerCase():"text";let x=t0([s,a&&a.toAbortSignal()],i),g;const p=x&&x.unsubscribe&&(()=>{x.unsubscribe()});let b;try{if(l&&a0&&r!=="get"&&r!=="head"&&(b=await c0(d,n))!==0){let j=new Request(t,{method:"POST",body:n,duplex:"half"}),D;if(k.isFormData(n)&&(D=j.headers.get("content-type"))&&d.setContentType(D),j.body){const[E,T]=Si(b,kn(ki(l)));n=Ci(j.body,Ti,E,T)}}k.isString(m)||(m=m?"include":"omit");const v="credentials"in Request.prototype;g=new Request(t,{...h,signal:x,method:r.toUpperCase(),headers:d.normalize().toJSON(),body:n,duplex:"half",credentials:v?m:void 0});let y=await fetch(g,h);const w=fo&&(u==="stream"||u==="response");if(fo&&(c||w&&p)){const j={};["status","statusText","headers"].forEach(N=>{j[N]=y[N]});const D=k.toFiniteNumber(y.headers.get("content-length")),[E,T]=c&&Si(D,kn(ki(c),!0))||[];y=new Response(Ci(y.body,Ti,E,()=>{T&&T(),p&&p()}),j)}u=u||"text";let S=await jn[k.findKey(jn,u)||"text"](y,e);return!w&&p&&p(),await new Promise((j,D)=>{ql(j,D,{data:S,headers:Te.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:g})})}catch(v){throw p&&p(),v&&v.name==="TypeError"&&/Load failed|fetch/i.test(v.message)?Object.assign(new W("Network Error",W.ERR_NETWORK,e,g),{cause:v.cause||v}):W.from(v,v&&v.code,e,g)}}),mo={http:kb,xhr:e0,fetch:l0};k.forEach(mo,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ei=e=>`- ${e}`,d0=e=>k.isFunction(e)||e===null||e===!1,Ql={getAdapter:e=>{e=k.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let a=0;a<t;a++){r=e[a];let i;if(n=r,!d0(r)&&(n=mo[(i=String(r)).toLowerCase()],n===void 0))throw new W(`Unknown adapter '${i}'`);if(n)break;s[i||"#"+a]=n}if(!n){const a=Object.entries(s).map(([c,l])=>`adapter ${c} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?a.length>1?`since :
`+a.map(Ei).join(`
`):" "+Ei(a[0]):"as no adapter specified";throw new W("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:mo};function Os(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new or(null,e)}function Ri(e){return Os(e),e.headers=Te.from(e.headers),e.data=Ps.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ql.getAdapter(e.adapter||Mr.adapter)(e).then(function(n){return Os(e),n.data=Ps.call(e,e.transformResponse,n),n.headers=Te.from(n.headers),n},function(n){return Gl(n)||(Os(e),n&&n.response&&(n.response.data=Ps.call(e,e.transformResponse,n.response),n.response.headers=Te.from(n.response.headers))),Promise.reject(n)})}const Zl="1.10.0",ts={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ts[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const _i={};ts.transitional=function(t,r,n){function s(a,i){return"[Axios v"+Zl+"] Transitional option '"+a+"'"+i+(n?". "+n:"")}return(a,i,c)=>{if(t===!1)throw new W(s(i," has been removed"+(r?" in "+r:"")),W.ERR_DEPRECATED);return r&&!_i[i]&&(_i[i]=!0,console.warn(s(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(a,i,c):!0}};ts.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function u0(e,t,r){if(typeof e!="object")throw new W("options must be an object",W.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const a=n[s],i=t[a];if(i){const c=e[a],l=c===void 0||i(c,a,e);if(l!==!0)throw new W("option "+a+" must be "+l,W.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new W("Unknown option "+a,W.ERR_BAD_OPTION)}}const cn={assertOptions:u0,validators:ts},We=cn.validators;let jt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new wi,response:new wi}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const a=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?a&&!String(n.stack).endsWith(a.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+a):n.stack=a}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=Rt(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:a}=r;n!==void 0&&cn.assertOptions(n,{silentJSONParsing:We.transitional(We.boolean),forcedJSONParsing:We.transitional(We.boolean),clarifyTimeoutError:We.transitional(We.boolean)},!1),s!=null&&(k.isFunction(s)?r.paramsSerializer={serialize:s}:cn.assertOptions(s,{encode:We.function,serialize:We.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),cn.assertOptions(r,{baseUrl:We.spelling("baseURL"),withXsrfToken:We.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=a&&k.merge(a.common,a[r.method]);a&&k.forEach(["delete","get","head","post","put","patch","common"],g=>{delete a[g]}),r.headers=Te.concat(i,a);const c=[];let l=!0;this.interceptors.request.forEach(function(p){typeof p.runWhen=="function"&&p.runWhen(r)===!1||(l=l&&p.synchronous,c.unshift(p.fulfilled,p.rejected))});const u=[];this.interceptors.response.forEach(function(p){u.push(p.fulfilled,p.rejected)});let d,m=0,h;if(!l){const g=[Ri.bind(this),void 0];for(g.unshift.apply(g,c),g.push.apply(g,u),h=g.length,d=Promise.resolve(r);m<h;)d=d.then(g[m++],g[m++]);return d}h=c.length;let x=r;for(m=0;m<h;){const g=c[m++],p=c[m++];try{x=g(x)}catch(b){p.call(this,b);break}}try{d=Ri.call(this,x)}catch(g){return Promise.reject(g)}for(m=0,h=u.length;m<h;)d=d.then(u[m++],u[m++]);return d}getUri(t){t=Rt(this.defaults,t);const r=Kl(t.baseURL,t.url,t.allowAbsoluteUrls);return Vl(r,t.params,t.paramsSerializer)}};k.forEach(["delete","get","head","options"],function(t){jt.prototype[t]=function(r,n){return this.request(Rt(n||{},{method:t,url:r,data:(n||{}).data}))}});k.forEach(["post","put","patch"],function(t){function r(n){return function(a,i,c){return this.request(Rt(c||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:a,data:i}))}}jt.prototype[t]=r(),jt.prototype[t+"Form"]=r(!0)});let f0=class ed{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(a){r=a});const n=this;this.promise.then(s=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](s);n._listeners=null}),this.promise.then=s=>{let a;const i=new Promise(c=>{n.subscribe(c),a=c}).then(s);return i.cancel=function(){n.unsubscribe(a)},i},t(function(a,i,c){n.reason||(n.reason=new or(a,i,c),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new ed(function(s){t=s}),cancel:t}}};function m0(e){return function(r){return e.apply(null,r)}}function h0(e){return k.isObject(e)&&e.isAxiosError===!0}const ho={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ho).forEach(([e,t])=>{ho[t]=e});function td(e){const t=new jt(e),r=Al(jt.prototype.request,t);return k.extend(r,jt.prototype,t,{allOwnKeys:!0}),k.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return td(Rt(e,s))},r}const ce=td(Mr);ce.Axios=jt;ce.CanceledError=or;ce.CancelToken=f0;ce.isCancel=Gl;ce.VERSION=Zl;ce.toFormData=Zn;ce.AxiosError=W;ce.Cancel=ce.CanceledError;ce.all=function(t){return Promise.all(t)};ce.spread=m0;ce.isAxiosError=h0;ce.mergeConfig=Rt;ce.AxiosHeaders=Te;ce.formToJSON=e=>Wl(k.isHTMLForm(e)?new FormData(e):e);ce.getAdapter=Ql.getAdapter;ce.HttpStatusCode=ho;ce.default=ce;const{Axios:FS,AxiosError:$S,CanceledError:US,isCancel:BS,CancelToken:HS,VERSION:VS,all:zS,Cancel:WS,isAxiosError:GS,spread:qS,toFormData:KS,AxiosHeaders:YS,HttpStatusCode:XS,formToJSON:JS,getAdapter:QS,mergeConfig:ZS}=ce,Q=ce.create({baseURL:"http://localhost:5000/api/v1",withCredentials:!0,timeout:1e4,headers:{"Content-Type":"application/json"}});let Is=!1,Ms=null,po=[];const Cn=new Map,p0=e=>`${e.method?.toUpperCase()||"GET"}:${e.url}:${JSON.stringify(e.data||{})}`,Ai=(e,t=null)=>{po.forEach(({resolve:r,reject:n})=>{e?n(e):t?r(t):n(new Error("No token provided"))}),po=[]},g0=async()=>{const{refreshToken:e}=st.getState();if(console.log("Attempting token refresh..."),!e)throw console.error("No refresh token available"),st.getState().logout(),typeof window<"u"&&(window.location.href="/auth"),new Error("Session expired. Please login again.");try{console.log("Making refresh request to:",`${Q.defaults.baseURL}/auth/refresh`);const t=await ce.post(`${Q.defaults.baseURL}/auth/refresh`,{refreshToken:e},{headers:{"Content-Type":"application/json"},timeout:1e4,withCredentials:!0});if(console.log("Refresh response:",t.data),t.data.success&&t.data.data?.accessToken){const r=t.data.data.accessToken,n=t.data.data.refreshToken||e;return console.log("Token refresh successful"),st.getState().setTokens(r,n),r}else throw new Error("Invalid refresh response structure")}catch(t){throw console.error("Token refresh failed:",t.response?.data||t.message),st.getState().logout(),new Error(t.response?.data?.message||t.message||"Failed to refresh authentication token")}};Q.interceptors.request.use(e=>{const{accessToken:t}=st.getState();if(t&&(e.headers.Authorization=`Bearer ${t}`),e.method?.toUpperCase()!=="GET"){const r=p0(e);if(Cn.has(r))return console.warn("Duplicate request detected, returning existing promise:",r),Cn.get(r);e._deduplicationKey=r}return e},e=>Promise.reject(e));Q.interceptors.response.use(e=>{if(e.config&&e.config.method?.toUpperCase()!=="GET"){const t=e.config._deduplicationKey;t&&Cn.delete(t)}return e.data&&typeof e.data=="object"&&(e.data.success===void 0&&(console.warn("API response missing success field:",e.config?.url),e.data.success=e.status>=200&&e.status<300),!e.data.message&&e.data.success&&(e.data.message="Operation completed successfully")),e},async e=>{if(e.config&&e.config.method?.toUpperCase()!=="GET"){const n=e.config._deduplicationKey;n&&Cn.delete(n)}const t=e.config;console.error("API Request Failed:",{url:t?.url,method:t?.method,status:e.response?.status,statusText:e.response?.statusText,message:e.response?.data?.message||e.message,data:e.response?.data,headers:e.response?.headers,config:t,fullError:e});const r=t?.url?.includes("/auth/login")||t?.url?.includes("/auth/register")||t?.url?.includes("/auth/forgot-password")||t?.url?.includes("/auth/reset-password")||t?.url?.includes("/auth/refresh");if(e.response?.status===401&&!t._retry&&!r){if(t._retry=!0,Is)return new Promise((n,s)=>{po.push({resolve:n,reject:s})}).then(n=>(t.headers.Authorization=`Bearer ${n}`,Q(t))).catch(n=>Promise.reject(n));Is=!0,Ms=g0();try{const n=await Ms;return Ai(null,n),t.headers.Authorization=`Bearer ${n}`,Q(t)}catch(n){return console.error("Token refresh failed:",n),Ai(n,null),st.getState().logout(),typeof window<"u"&&(window.location.href="/auth"),Promise.reject(n)}finally{Is=!1,Ms=null}}return Promise.reject(e)});const et=Object.create(null);et.open="0";et.close="1";et.ping="2";et.pong="3";et.message="4";et.upgrade="5";et.noop="6";const ln=Object.create(null);Object.keys(et).forEach(e=>{ln[et[e]]=e});const go={type:"error",data:"parser error"},rd=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",nd=typeof ArrayBuffer=="function",sd=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,aa=({type:e,data:t},r,n)=>rd&&t instanceof Blob?r?n(t):Di(t,n):nd&&(t instanceof ArrayBuffer||sd(t))?r?n(t):Di(new Blob([t]),n):n(et[e]+(t||"")),Di=(e,t)=>{const r=new FileReader;return r.onload=function(){const n=r.result.split(",")[1];t("b"+(n||""))},r.readAsDataURL(e)};function Pi(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Ls;function x0(e,t){if(rd&&e.data instanceof Blob)return e.data.arrayBuffer().then(Pi).then(t);if(nd&&(e.data instanceof ArrayBuffer||sd(e.data)))return t(Pi(e.data));aa(e,!1,r=>{Ls||(Ls=new TextEncoder),t(Ls.encode(r))})}const Oi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",mr=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Oi.length;e++)mr[Oi.charCodeAt(e)]=e;const b0=e=>{let t=e.length*.75,r=e.length,n,s=0,a,i,c,l;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const u=new ArrayBuffer(t),d=new Uint8Array(u);for(n=0;n<r;n+=4)a=mr[e.charCodeAt(n)],i=mr[e.charCodeAt(n+1)],c=mr[e.charCodeAt(n+2)],l=mr[e.charCodeAt(n+3)],d[s++]=a<<2|i>>4,d[s++]=(i&15)<<4|c>>2,d[s++]=(c&3)<<6|l&63;return u},v0=typeof ArrayBuffer=="function",ia=(e,t)=>{if(typeof e!="string")return{type:"message",data:od(e,t)};const r=e.charAt(0);return r==="b"?{type:"message",data:y0(e.substring(1),t)}:ln[r]?e.length>1?{type:ln[r],data:e.substring(1)}:{type:ln[r]}:go},y0=(e,t)=>{if(v0){const r=b0(e);return od(r,t)}else return{base64:!0,data:e}},od=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},ad="",w0=(e,t)=>{const r=e.length,n=new Array(r);let s=0;e.forEach((a,i)=>{aa(a,!1,c=>{n[i]=c,++s===r&&t(n.join(ad))})})},N0=(e,t)=>{const r=e.split(ad),n=[];for(let s=0;s<r.length;s++){const a=ia(r[s],t);if(n.push(a),a.type==="error")break}return n};function S0(){return new TransformStream({transform(e,t){x0(e,r=>{const n=r.length;let s;if(n<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,n);else if(n<65536){s=new Uint8Array(3);const a=new DataView(s.buffer);a.setUint8(0,126),a.setUint16(1,n)}else{s=new Uint8Array(9);const a=new DataView(s.buffer);a.setUint8(0,127),a.setBigUint64(1,BigInt(n))}e.data&&typeof e.data!="string"&&(s[0]|=128),t.enqueue(s),t.enqueue(r)})}})}let Fs;function Yr(e){return e.reduce((t,r)=>t+r.length,0)}function Xr(e,t){if(e[0].length===t)return e.shift();const r=new Uint8Array(t);let n=0;for(let s=0;s<t;s++)r[s]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function k0(e,t){Fs||(Fs=new TextDecoder);const r=[];let n=0,s=-1,a=!1;return new TransformStream({transform(i,c){for(r.push(i);;){if(n===0){if(Yr(r)<1)break;const l=Xr(r,1);a=(l[0]&128)===128,s=l[0]&127,s<126?n=3:s===126?n=1:n=2}else if(n===1){if(Yr(r)<2)break;const l=Xr(r,2);s=new DataView(l.buffer,l.byteOffset,l.length).getUint16(0),n=3}else if(n===2){if(Yr(r)<8)break;const l=Xr(r,8),u=new DataView(l.buffer,l.byteOffset,l.length),d=u.getUint32(0);if(d>Math.pow(2,21)-1){c.enqueue(go);break}s=d*Math.pow(2,32)+u.getUint32(4),n=3}else{if(Yr(r)<s)break;const l=Xr(r,s);c.enqueue(ia(a?l:Fs.decode(l),t)),n=0}if(s===0||s>e){c.enqueue(go);break}}}})}const id=4;function ue(e){if(e)return j0(e)}function j0(e){for(var t in ue.prototype)e[t]=ue.prototype[t];return e}ue.prototype.on=ue.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};ue.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this};ue.prototype.off=ue.prototype.removeListener=ue.prototype.removeAllListeners=ue.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var r=this._callbacks["$"+e];if(!r)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var n,s=0;s<r.length;s++)if(n=r[s],n===t||n.fn===t){r.splice(s,1);break}return r.length===0&&delete this._callbacks["$"+e],this};ue.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,s=r.length;n<s;++n)r[n].apply(this,t)}return this};ue.prototype.emitReserved=ue.prototype.emit;ue.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};ue.prototype.hasListeners=function(e){return!!this.listeners(e).length};const rs=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,r)=>r(t,0),Oe=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),C0="arraybuffer";function cd(e,...t){return t.reduce((r,n)=>(e.hasOwnProperty(n)&&(r[n]=e[n]),r),{})}const T0=Oe.setTimeout,E0=Oe.clearTimeout;function ns(e,t){t.useNativeTimers?(e.setTimeoutFn=T0.bind(Oe),e.clearTimeoutFn=E0.bind(Oe)):(e.setTimeoutFn=Oe.setTimeout.bind(Oe),e.clearTimeoutFn=Oe.clearTimeout.bind(Oe))}const R0=1.33;function _0(e){return typeof e=="string"?A0(e):Math.ceil((e.byteLength||e.size)*R0)}function A0(e){let t=0,r=0;for(let n=0,s=e.length;n<s;n++)t=e.charCodeAt(n),t<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}function ld(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function D0(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}function P0(e){let t={},r=e.split("&");for(let n=0,s=r.length;n<s;n++){let a=r[n].split("=");t[decodeURIComponent(a[0])]=decodeURIComponent(a[1])}return t}class O0 extends Error{constructor(t,r,n){super(t),this.description=r,this.context=n,this.type="TransportError"}}class ca extends ue{constructor(t){super(),this.writable=!1,ns(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,r,n){return super.emitReserved("error",new O0(t,r,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const r=ia(t,this.socket.binaryType);this.onPacket(r)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,r={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(r)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const r=D0(t);return r.length?"?"+r:""}}class I0 extends ca{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const r=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let n=0;this._polling&&(n++,this.once("pollComplete",function(){--n||r()})),this.writable||(n++,this.once("drain",function(){--n||r()}))}else r()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const r=n=>{if(this.readyState==="opening"&&n.type==="open"&&this.onOpen(),n.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(n)};N0(t,this.socket.binaryType).forEach(r),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,w0(t,r=>{this.doWrite(r,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",r=this.query||{};return this.opts.timestampRequests!==!1&&(r[this.opts.timestampParam]=ld()),!this.supportsBinary&&!r.sid&&(r.b64=1),this.createUri(t,r)}}let dd=!1;try{dd=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const M0=dd;function L0(){}class F0 extends I0{constructor(t){if(super(t),typeof location<"u"){const r=location.protocol==="https:";let n=location.port;n||(n=r?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||n!==t.port}}doWrite(t,r){const n=this.request({method:"POST",data:t});n.on("success",r),n.on("error",(s,a)=>{this.onError("xhr post error",s,a)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(r,n)=>{this.onError("xhr poll error",r,n)}),this.pollXhr=t}}let zt=class dn extends ue{constructor(t,r,n){super(),this.createRequest=t,ns(this,n),this._opts=n,this._method=n.method||"GET",this._uri=r,this._data=n.data!==void 0?n.data:null,this._create()}_create(){var t;const r=cd(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");r.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(r);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let s in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(s)&&n.setRequestHeader(s,this._opts.extraHeaders[s])}}catch{}if(this._method==="POST")try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{n.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var s;n.readyState===3&&((s=this._opts.cookieJar)===null||s===void 0||s.parseCookies(n.getResponseHeader("set-cookie"))),n.readyState===4&&(n.status===200||n.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof n.status=="number"?n.status:0)},0))},n.send(this._data)}catch(s){this.setTimeoutFn(()=>{this._onError(s)},0);return}typeof document<"u"&&(this._index=dn.requestsCount++,dn.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=L0,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete dn.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};zt.requestsCount=0;zt.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Ii);else if(typeof addEventListener=="function"){const e="onpagehide"in Oe?"pagehide":"unload";addEventListener(e,Ii,!1)}}function Ii(){for(let e in zt.requests)zt.requests.hasOwnProperty(e)&&zt.requests[e].abort()}const $0=function(){const e=ud({xdomain:!1});return e&&e.responseType!==null}();class U0 extends F0{constructor(t){super(t);const r=t&&t.forceBase64;this.supportsBinary=$0&&!r}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new zt(ud,this.uri(),t)}}function ud(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||M0))return new XMLHttpRequest}catch{}if(!t)try{return new Oe[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const fd=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class B0 extends ca{get name(){return"websocket"}doOpen(){const t=this.uri(),r=this.opts.protocols,n=fd?{}:cd(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,r,n)}catch(s){return this.emitReserved("error",s)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const n=t[r],s=r===t.length-1;aa(n,this.supportsBinary,a=>{try{this.doWrite(n,a)}catch{}s&&rs(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",r=this.query||{};return this.opts.timestampRequests&&(r[this.opts.timestampParam]=ld()),this.supportsBinary||(r.b64=1),this.createUri(t,r)}}const $s=Oe.WebSocket||Oe.MozWebSocket;class H0 extends B0{createSocket(t,r,n){return fd?new $s(t,r,n):r?new $s(t,r):new $s(t)}doWrite(t,r){this.ws.send(r)}}class V0 extends ca{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const r=k0(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=t.readable.pipeThrough(r).getReader(),s=S0();s.readable.pipeTo(t.writable),this._writer=s.writable.getWriter();const a=()=>{n.read().then(({done:c,value:l})=>{c||(this.onPacket(l),a())}).catch(c=>{})};a();const i={type:"open"};this.query.sid&&(i.data=`{"sid":"${this.query.sid}"}`),this._writer.write(i).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let r=0;r<t.length;r++){const n=t[r],s=r===t.length-1;this._writer.write(n).then(()=>{s&&rs(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const z0={websocket:H0,webtransport:V0,polling:U0},W0=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,G0=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function xo(e){if(e.length>8e3)throw"URI too long";const t=e,r=e.indexOf("["),n=e.indexOf("]");r!=-1&&n!=-1&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let s=W0.exec(e||""),a={},i=14;for(;i--;)a[G0[i]]=s[i]||"";return r!=-1&&n!=-1&&(a.source=t,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a.pathNames=q0(a,a.path),a.queryKey=K0(a,a.query),a}function q0(e,t){const r=/\/{2,9}/g,n=t.replace(r,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&n.splice(0,1),t.slice(-1)=="/"&&n.splice(n.length-1,1),n}function K0(e,t){const r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(n,s,a){s&&(r[s]=a)}),r}const bo=typeof addEventListener=="function"&&typeof removeEventListener=="function",un=[];bo&&addEventListener("offline",()=>{un.forEach(e=>e())},!1);class ft extends ue{constructor(t,r){if(super(),this.binaryType=C0,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(r=t,t=null),t){const n=xo(t);r.hostname=n.host,r.secure=n.protocol==="https"||n.protocol==="wss",r.port=n.port,n.query&&(r.query=n.query)}else r.host&&(r.hostname=xo(r.host).host);ns(this,r),this.secure=r.secure!=null?r.secure:typeof location<"u"&&location.protocol==="https:",r.hostname&&!r.port&&(r.port=this.secure?"443":"80"),this.hostname=r.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=r.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},r.transports.forEach(n=>{const s=n.prototype.name;this.transports.push(s),this._transportsByName[s]=n}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},r),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=P0(this.opts.query)),bo&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},un.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const r=Object.assign({},this.opts.query);r.EIO=id,r.transport=t,this.id&&(r.sid=this.id);const n=Object.assign({},this.opts,{query:r,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](n)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&ft.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const r=this.createTransport(t);r.open(),this.setTransport(r)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",r=>this._onClose("transport close",r))}onOpen(){this.readyState="open",ft.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const r=new Error("server error");r.code=t.data,this._onError(r);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let r=1;for(let n=0;n<this.writeBuffer.length;n++){const s=this.writeBuffer[n].data;if(s&&(r+=_0(s)),n>0&&r>this._maxPayload)return this.writeBuffer.slice(0,n);r+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,rs(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,r,n){return this._sendPacket("message",t,r,n),this}send(t,r,n){return this._sendPacket("message",t,r,n),this}_sendPacket(t,r,n,s){if(typeof r=="function"&&(s=r,r=void 0),typeof n=="function"&&(s=n,n=null),this.readyState==="closing"||this.readyState==="closed")return;n=n||{},n.compress=n.compress!==!1;const a={type:t,data:r,options:n};this.emitReserved("packetCreate",a),this.writeBuffer.push(a),s&&this.once("flush",s),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},r=()=>{this.off("upgrade",r),this.off("upgradeError",r),t()},n=()=>{this.once("upgrade",r),this.once("upgradeError",r)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():t()}):this.upgrading?n():t()),this}_onError(t){if(ft.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,r){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),bo&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const n=un.indexOf(this._offlineEventListener);n!==-1&&un.splice(n,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,r),this.writeBuffer=[],this._prevBufferLen=0}}}ft.protocol=id;class Y0 extends ft{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let r=this.createTransport(t),n=!1;ft.priorWebsocketSuccess=!1;const s=()=>{n||(r.send([{type:"ping",data:"probe"}]),r.once("packet",m=>{if(!n)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",r),!r)return;ft.priorWebsocketSuccess=r.name==="websocket",this.transport.pause(()=>{n||this.readyState!=="closed"&&(d(),this.setTransport(r),r.send([{type:"upgrade"}]),this.emitReserved("upgrade",r),r=null,this.upgrading=!1,this.flush())})}else{const h=new Error("probe error");h.transport=r.name,this.emitReserved("upgradeError",h)}}))};function a(){n||(n=!0,d(),r.close(),r=null)}const i=m=>{const h=new Error("probe error: "+m);h.transport=r.name,a(),this.emitReserved("upgradeError",h)};function c(){i("transport closed")}function l(){i("socket closed")}function u(m){r&&m.name!==r.name&&a()}const d=()=>{r.removeListener("open",s),r.removeListener("error",i),r.removeListener("close",c),this.off("close",l),this.off("upgrading",u)};r.once("open",s),r.once("error",i),r.once("close",c),this.once("close",l),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{n||r.open()},200):r.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const r=[];for(let n=0;n<t.length;n++)~this.transports.indexOf(t[n])&&r.push(t[n]);return r}}let X0=class extends Y0{constructor(t,r={}){const n=typeof t=="object"?t:r;(!n.transports||n.transports&&typeof n.transports[0]=="string")&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(s=>z0[s]).filter(s=>!!s)),super(t,n)}};function J0(e,t="",r){let n=e;r=r||typeof location<"u"&&location,e==null&&(e=r.protocol+"//"+r.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=r.protocol+e:e=r.host+e),/^(https?|wss?):\/\//.test(e)||(typeof r<"u"?e=r.protocol+"//"+e:e="https://"+e),n=xo(e)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";const a=n.host.indexOf(":")!==-1?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+a+":"+n.port+t,n.href=n.protocol+"://"+a+(r&&r.port===n.port?"":":"+n.port),n}const Q0=typeof ArrayBuffer=="function",Z0=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,md=Object.prototype.toString,ev=typeof Blob=="function"||typeof Blob<"u"&&md.call(Blob)==="[object BlobConstructor]",tv=typeof File=="function"||typeof File<"u"&&md.call(File)==="[object FileConstructor]";function la(e){return Q0&&(e instanceof ArrayBuffer||Z0(e))||ev&&e instanceof Blob||tv&&e instanceof File}function fn(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let r=0,n=e.length;r<n;r++)if(fn(e[r]))return!0;return!1}if(la(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return fn(e.toJSON(),!0);for(const r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&fn(e[r]))return!0;return!1}function rv(e){const t=[],r=e.data,n=e;return n.data=vo(r,t),n.attachments=t.length,{packet:n,buffers:t}}function vo(e,t){if(!e)return e;if(la(e)){const r={_placeholder:!0,num:t.length};return t.push(e),r}else if(Array.isArray(e)){const r=new Array(e.length);for(let n=0;n<e.length;n++)r[n]=vo(e[n],t);return r}else if(typeof e=="object"&&!(e instanceof Date)){const r={};for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=vo(e[n],t));return r}return e}function nv(e,t){return e.data=yo(e.data,t),delete e.attachments,e}function yo(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let r=0;r<e.length;r++)e[r]=yo(e[r],t);else if(typeof e=="object")for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=yo(e[r],t));return e}const sv=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ov=5;var J;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(J||(J={}));class av{constructor(t){this.replacer=t}encode(t){return(t.type===J.EVENT||t.type===J.ACK)&&fn(t)?this.encodeAsBinary({type:t.type===J.EVENT?J.BINARY_EVENT:J.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let r=""+t.type;return(t.type===J.BINARY_EVENT||t.type===J.BINARY_ACK)&&(r+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(r+=t.nsp+","),t.id!=null&&(r+=t.id),t.data!=null&&(r+=JSON.stringify(t.data,this.replacer)),r}encodeAsBinary(t){const r=rv(t),n=this.encodeAsString(r.packet),s=r.buffers;return s.unshift(n),s}}function Mi(e){return Object.prototype.toString.call(e)==="[object Object]"}class da extends ue{constructor(t){super(),this.reviver=t}add(t){let r;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");r=this.decodeString(t);const n=r.type===J.BINARY_EVENT;n||r.type===J.BINARY_ACK?(r.type=n?J.EVENT:J.ACK,this.reconstructor=new iv(r),r.attachments===0&&super.emitReserved("decoded",r)):super.emitReserved("decoded",r)}else if(la(t)||t.base64)if(this.reconstructor)r=this.reconstructor.takeBinaryData(t),r&&(this.reconstructor=null,super.emitReserved("decoded",r));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let r=0;const n={type:Number(t.charAt(0))};if(J[n.type]===void 0)throw new Error("unknown packet type "+n.type);if(n.type===J.BINARY_EVENT||n.type===J.BINARY_ACK){const a=r+1;for(;t.charAt(++r)!=="-"&&r!=t.length;);const i=t.substring(a,r);if(i!=Number(i)||t.charAt(r)!=="-")throw new Error("Illegal attachments");n.attachments=Number(i)}if(t.charAt(r+1)==="/"){const a=r+1;for(;++r&&!(t.charAt(r)===","||r===t.length););n.nsp=t.substring(a,r)}else n.nsp="/";const s=t.charAt(r+1);if(s!==""&&Number(s)==s){const a=r+1;for(;++r;){const i=t.charAt(r);if(i==null||Number(i)!=i){--r;break}if(r===t.length)break}n.id=Number(t.substring(a,r+1))}if(t.charAt(++r)){const a=this.tryParse(t.substr(r));if(da.isPayloadValid(n.type,a))n.data=a;else throw new Error("invalid payload")}return n}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,r){switch(t){case J.CONNECT:return Mi(r);case J.DISCONNECT:return r===void 0;case J.CONNECT_ERROR:return typeof r=="string"||Mi(r);case J.EVENT:case J.BINARY_EVENT:return Array.isArray(r)&&(typeof r[0]=="number"||typeof r[0]=="string"&&sv.indexOf(r[0])===-1);case J.ACK:case J.BINARY_ACK:return Array.isArray(r)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class iv{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const r=nv(this.reconPack,this.buffers);return this.finishedReconstruction(),r}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const cv=Object.freeze(Object.defineProperty({__proto__:null,Decoder:da,Encoder:av,get PacketType(){return J},protocol:ov},Symbol.toStringTag,{value:"Module"}));function $e(e,t,r){return e.on(t,r),function(){e.off(t,r)}}const lv=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class hd extends ue{constructor(t,r,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=r,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[$e(t,"open",this.onopen.bind(this)),$e(t,"packet",this.onpacket.bind(this)),$e(t,"error",this.onerror.bind(this)),$e(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...r){var n,s,a;if(lv.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(r.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(r),this;const i={type:J.EVENT,data:r};if(i.options={},i.options.compress=this.flags.compress!==!1,typeof r[r.length-1]=="function"){const d=this.ids++,m=r.pop();this._registerAckCallback(d,m),i.id=d}const c=(s=(n=this.io.engine)===null||n===void 0?void 0:n.transport)===null||s===void 0?void 0:s.writable,l=this.connected&&!(!((a=this.io.engine)===null||a===void 0)&&a._hasPingExpired());return this.flags.volatile&&!c||(l?(this.notifyOutgoingListeners(i),this.packet(i)):this.sendBuffer.push(i)),this.flags={},this}_registerAckCallback(t,r){var n;const s=(n=this.flags.timeout)!==null&&n!==void 0?n:this._opts.ackTimeout;if(s===void 0){this.acks[t]=r;return}const a=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let c=0;c<this.sendBuffer.length;c++)this.sendBuffer[c].id===t&&this.sendBuffer.splice(c,1);r.call(this,new Error("operation has timed out"))},s),i=(...c)=>{this.io.clearTimeoutFn(a),r.apply(this,c)};i.withError=!0,this.acks[t]=i}emitWithAck(t,...r){return new Promise((n,s)=>{const a=(i,c)=>i?s(i):n(c);a.withError=!0,r.push(a),this.emit(t,...r)})}_addToQueue(t){let r;typeof t[t.length-1]=="function"&&(r=t.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((s,...a)=>n!==this._queue[0]?void 0:(s!==null?n.tryCount>this._opts.retries&&(this._queue.shift(),r&&r(s)):(this._queue.shift(),r&&r(null,...a)),n.pending=!1,this._drainQueue())),this._queue.push(n),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const r=this._queue[0];r.pending&&!t||(r.pending=!0,r.tryCount++,this.flags=r.flags,this.emit.apply(this,r.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:J.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,r){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,r),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(n=>String(n.id)===t)){const n=this.acks[t];delete this.acks[t],n.withError&&n.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case J.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case J.EVENT:case J.BINARY_EVENT:this.onevent(t);break;case J.ACK:case J.BINARY_ACK:this.onack(t);break;case J.DISCONNECT:this.ondisconnect();break;case J.CONNECT_ERROR:this.destroy();const n=new Error(t.data.message);n.data=t.data.data,this.emitReserved("connect_error",n);break}}onevent(t){const r=t.data||[];t.id!=null&&r.push(this.ack(t.id)),this.connected?this.emitEvent(r):this.receiveBuffer.push(Object.freeze(r))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const r=this._anyListeners.slice();for(const n of r)n.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const r=this;let n=!1;return function(...s){n||(n=!0,r.packet({type:J.ACK,id:t,data:s}))}}onack(t){const r=this.acks[t.id];typeof r=="function"&&(delete this.acks[t.id],r.withError&&t.data.unshift(null),r.apply(this,t.data))}onconnect(t,r){this.id=t,this.recovered=r&&this._pid===r,this._pid=r,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:J.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const r=this._anyListeners;for(let n=0;n<r.length;n++)if(t===r[n])return r.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const r=this._anyOutgoingListeners;for(let n=0;n<r.length;n++)if(t===r[n])return r.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const r=this._anyOutgoingListeners.slice();for(const n of r)n.apply(this,t.data)}}}function ar(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ar.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=(Math.floor(t*10)&1)==0?e-r:e+r}return Math.min(e,this.max)|0};ar.prototype.reset=function(){this.attempts=0};ar.prototype.setMin=function(e){this.ms=e};ar.prototype.setMax=function(e){this.max=e};ar.prototype.setJitter=function(e){this.jitter=e};class wo extends ue{constructor(t,r){var n;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(r=t,t=void 0),r=r||{},r.path=r.path||"/socket.io",this.opts=r,ns(this,r),this.reconnection(r.reconnection!==!1),this.reconnectionAttempts(r.reconnectionAttempts||1/0),this.reconnectionDelay(r.reconnectionDelay||1e3),this.reconnectionDelayMax(r.reconnectionDelayMax||5e3),this.randomizationFactor((n=r.randomizationFactor)!==null&&n!==void 0?n:.5),this.backoff=new ar({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(r.timeout==null?2e4:r.timeout),this._readyState="closed",this.uri=t;const s=r.parser||cv;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=r.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var r;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(r=this.backoff)===null||r===void 0||r.setMin(t),this)}randomizationFactor(t){var r;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(r=this.backoff)===null||r===void 0||r.setJitter(t),this)}reconnectionDelayMax(t){var r;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(r=this.backoff)===null||r===void 0||r.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new X0(this.uri,this.opts);const r=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const s=$e(r,"open",function(){n.onopen(),t&&t()}),a=c=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",c),t?t(c):this.maybeReconnectOnOpen()},i=$e(r,"error",a);if(this._timeout!==!1){const c=this._timeout,l=this.setTimeoutFn(()=>{s(),a(new Error("timeout")),r.close()},c);this.opts.autoUnref&&l.unref(),this.subs.push(()=>{this.clearTimeoutFn(l)})}return this.subs.push(s),this.subs.push(i),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push($e(t,"ping",this.onping.bind(this)),$e(t,"data",this.ondata.bind(this)),$e(t,"error",this.onerror.bind(this)),$e(t,"close",this.onclose.bind(this)),$e(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(r){this.onclose("parse error",r)}}ondecoded(t){rs(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,r){let n=this.nsps[t];return n?this._autoConnect&&!n.active&&n.connect():(n=new hd(this,t,r),this.nsps[t]=n),n}_destroy(t){const r=Object.keys(this.nsps);for(const n of r)if(this.nsps[n].active)return;this._close()}_packet(t){const r=this.encoder.encode(t);for(let n=0;n<r.length;n++)this.engine.write(r[n],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,r){var n;this.cleanup(),(n=this.engine)===null||n===void 0||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,r),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const r=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(s=>{s?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",s)):t.onreconnect()}))},r);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const fr={};function mn(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const r=J0(e,t.path||"/socket.io"),n=r.source,s=r.id,a=r.path,i=fr[s]&&a in fr[s].nsps,c=t.forceNew||t["force new connection"]||t.multiplex===!1||i;let l;return c?l=new wo(n,t):(fr[s]||(fr[s]=new wo(n,t)),l=fr[s]),r.query&&!t.query&&(t.query=r.queryKey),l.socket(r.path,t)}Object.assign(mn,{Manager:wo,Socket:hd,io:mn,connect:mn});const pd=e=>["image/jpeg","image/png","image/gif","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","video/mp4","video/webm","audio/mpeg","audio/wav"].includes(e.type)?e.size>10485760?{isValid:!1,error:"File size too large. Maximum size is 10MB."}:{isValid:!0}:{isValid:!1,error:"File type not supported. Please upload images, PDFs, documents, or media files."},Li=(e,t)=>{const r=t?.split(".").pop()?.toLowerCase();return e.includes("pdf")||r==="pdf"?"PDF":r==="doc"||r==="docx"?"DOC":r==="xls"||r==="xlsx"?"XLS":r==="ppt"||r==="pptx"?"PPT":r==="txt"?"TXT":e.startsWith("image/")?"IMG":e.startsWith("video/")?"VID":e.startsWith("audio/")?"AUD":["zip","rar","7z"].includes(r||"")?"ZIP":"FILE"},Fi=e=>{if(e===0)return"0 Bytes";const t=1024,r=["Bytes","KB","MB","GB","TB"],n=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,n)).toFixed(2))+" "+r[n]},No=e=>{if(!e)return"Unknown File";try{let t=decodeURIComponent(e).replace(/[<>:"/\\|?*\x00-\x1f]/g,"").replace(/\s+/g," ").trim();if(!t||t.length<2){const r=e.split(".").pop();t=r?`File.${r}`:"Unknown File"}if(t.length>50){const r=t.lastIndexOf(".");if(r>0){const n=t.substring(r);t=`${t.substring(0,r).substring(0,40-n.length)}...${n}`}else t=t.substring(0,47)+"..."}return t}catch(t){return console.error("Error sanitizing filename:",t),"Unknown File"}},$i=(e,t)=>{const r=document.createElement("a");r.href=e,r.download=No(t),r.target="_blank",r.rel="noopener noreferrer",document.body.appendChild(r),r.click(),document.body.removeChild(r)},dv=(e,t=1920,r=1080,n=.8)=>new Promise((s,a)=>{if(!e.type.startsWith("image/")){s(e);return}const i=document.createElement("canvas"),c=i.getContext("2d"),l=new Image;l.onload=()=>{let{width:u,height:d}=l;u>t&&(d=d*t/u,u=t),d>r&&(u=u*r/d,d=r),i.width=u,i.height=d,c?.drawImage(l,0,0,u,d),i.toBlob(m=>{if(m){const h=new File([m],e.name,{type:e.type,lastModified:Date.now()});s(h)}else a(new Error("Failed to compress image"))},e.type,n)},l.onerror=()=>a(new Error("Failed to load image")),l.src=URL.createObjectURL(e)});class uv{cache=new Map;cacheTimeout=5*60*1e3;setStore(t){console.log("Store set for grievance service:",!!t)}getCachedData(t){const r=this.cache.get(t);return r&&Date.now()-r.timestamp<this.cacheTimeout?r.data:(this.cache.delete(t),null)}setCachedData(t,r){this.cache.set(t,{data:r,timestamp:Date.now()})}invalidateCache(t){if(t)for(const r of this.cache.keys())r.includes(t)&&this.cache.delete(r);else this.cache.clear()}async getGrievanceById(t,r=!0){const n=`grievance-${t}`;if(r){const s=this.getCachedData(n);if(s)return s}try{const s=await Q.get(`/grievances/${t}`),a=s.data?.data?.grievance||s.data;return this.setCachedData(n,a),a}catch(s){console.error("Error fetching grievance:",s);const a=s.response?.data?.message||"Failed to load grievance data. Please try again.";throw new Error(a)}}async updateGrievanceStatus(t,r,n){try{const s=await Q.patch(`/grievances/${t}/status`,{status:r,reason:n}),a=s.data?.data?.grievance||s.data,i=`grievance-${t}`;return this.setCachedData(i,a),{success:!0,grievance:a}}catch(s){console.error("Error updating grievance status:",s);const a=s.response?.data?.message||"Failed to update grievance status. Please try again.";throw new Error(a)}}async addComment(t,r,n="community"){try{return await Q.post(`/grievances/${t}/comments`,{content:r,type:n}),this.invalidateCache(t),!0}catch(s){console.error("Error adding comment:",s);const a=s.response?.data?.message||"Failed to add comment. Please try again.";throw new Error(a)}}async uploadAttachment(t,r){const n=pd(r);if(!n.isValid)throw new Error(n.error);try{const s=new FormData;return s.append("file",r),await Q.post(`/grievances/${t}/attachments`,s,{headers:{"Content-Type":"multipart/form-data"}}),this.invalidateCache(t),!0}catch(s){console.error("Error uploading attachment:",s);const a=s.response?.data?.message||"Failed to upload file. Please try again.";throw new Error(a)}}async removeAttachment(t,r){try{return await Q.delete(`/grievances/${t}/attachments/${r}`),this.invalidateCache(t),!0}catch(n){console.error("Error removing attachment:",n);const s=n.response?.data?.message||"Failed to remove attachment. Please try again.";throw new Error(s)}}async getGrievanceComments(t,r){const n=`comments-${t}-${r||"all"}`,s=this.getCachedData(n);if(s)return s;try{const a=r?`/grievances/${t}/comments?filter=${r}`:`/grievances/${t}/comments`,i=await Q.get(a),c=i.data?.data||i.data;return this.setCachedData(n,c),c}catch(a){console.error("Error fetching comments:",a);const i=a.response?.data?.message||"Failed to load comments. Please try again.";throw new Error(i)}}async validateStatusTransition(t,r){try{return(await Q.post("/grievances/validate-transition",{currentStatus:t,newStatus:r})).data?.valid||!1}catch(n){return console.error("Error validating status transition:",n),!1}}async batchUpdateGrievances(t){try{return await Q.put("/grievances/batch-update",{updates:t}),t.forEach(r=>this.invalidateCache(r.id)),!0}catch(r){console.error("Error in batch update:",r);const n=r.response?.data?.message||"Failed to update grievances. Please try again.";throw new Error(n)}}async exportGrievanceData(t,r="pdf"){try{return(await Q.get(`/grievances/${t}/export?format=${r}`,{responseType:"blob"})).data}catch(n){console.error("Error exporting grievance data:",n);const s=n.response?.data?.message||"Failed to export data. Please try again.";throw new Error(s)}}clearCache(){this.invalidateCache()}}const fv=new uv,hn=Uo((e,t)=>({grievances:[],currentGrievance:null,myGrievances:[],stats:null,isLoading:!1,isSubmitting:!1,isUploading:!1,error:null,success:null,pagination:null,clearError:()=>e({error:null}),clearSuccess:()=>e({success:null}),setCurrentGrievance:r=>e({currentGrievance:r}),createGrievance:async r=>{e({isSubmitting:!0,error:null,success:null});try{const n=await Q.post("/grievances",r);if(n.data.success)return e({success:"Grievance submitted successfully!",isSubmitting:!1}),t().getGrievances(),!0;throw new Error(n.data.message||"Failed to create grievance")}catch(n){return e({error:n.response?.data?.message||n.message||"Failed to create grievance",isSubmitting:!1}),!1}},getGrievanceCount:async()=>{try{const r=await Q.get("/grievances/count");return r.data.success?r.data.data.totalCount:1e5}catch(r){return console.error("Error fetching grievance count:",r),1e5}},getGrievances:async(r={})=>{const s={limit:await t().getGrievanceCount(),page:1,...r};if(!t().isLoading){e({isLoading:!0,error:null});try{const i=new URLSearchParams;Object.entries(s).forEach(([l,u])=>{u!=null&&u!==""&&i.append(l,u.toString())}),console.log("🔍 Fetching grievances with params:",i.toString());const c=await Q.get(`/grievances?${i.toString()}`);if(c.data.success){const l=c.data.data.grievances||[];console.log("📊 Received grievances:",{count:l.length,pagination:c.data.data.pagination,total:c.data.data.pagination?.totalItems});const{user:u}=st.getState(),d=u?.role==="admin"||u?.role==="officer";e(d?{grievances:l,pagination:c.data.data.pagination||null,isLoading:!1}:{myGrievances:l,pagination:c.data.data.pagination||null,isLoading:!1})}else throw new Error(c.data.message||"Failed to fetch grievances")}catch(i){if(console.error("Error fetching grievances:",i),i.response?.status===401){console.warn("Authentication failed - user may need to re-login"),e({isLoading:!1,error:"Authentication expired. Please log in again."});return}e({error:i.response?.data?.message||i.message||"Failed to fetch grievances",isLoading:!1})}}},getGrievanceById:async(r,n=!1)=>{e({isLoading:!0,error:null}),console.log("Fetching grievance by ID:",r,n?"(FORCE REFRESH)":"");try{if(!r)throw new Error("Grievance ID is required");const s=n?`/grievances/${r}?_t=${Date.now()}`:`/grievances/${r}`,a=await Q.get(s);if(console.log("Grievance fetch response:",a.data),a.data.success&&a.data.data?.grievance){const i=a.data.data.grievance,c={...i,title:i.title||"",description:i.description||"",category:i.category||"other",priority:i.priority||"medium",status:i.status||"pending",location:{address:i.location?.address||"",city:i.location?.city||"",state:i.location?.state||"",pincode:i.location?.pincode||"",coordinates:i.location?.coordinates||null},attachments:Array.isArray(i.attachments)?i.attachments:[],comments:Array.isArray(i.comments)?i.comments:[],statusHistory:Array.isArray(i.statusHistory)?i.statusHistory:[],previousTimelines:Array.isArray(i.previousTimelines)?i.previousTimelines:[],timeline:Array.isArray(i.timeline)?i.timeline:[],reopenCount:i.reopenCount||0};e({currentGrievance:c,isLoading:!1})}else throw new Error(a.data.message||"Failed to fetch grievance")}catch(s){console.error("Grievance fetch error:",{error:s.response?.data||s.message,httpStatus:s.response?.status,grievanceId:r});const a=s.response?.data?.message||s.response?.data?.error||s.message||"Failed to fetch grievance";e({error:a,isLoading:!1})}},updateGrievance:async(r,n)=>{e({isSubmitting:!0,error:null,success:null});try{const s=await Q.put(`/grievances/${r}`,n);if(s.data.success)return e({success:"Grievance updated successfully!",isSubmitting:!1,currentGrievance:s.data.data.grievance}),t().getGrievances(),!0;throw new Error(s.data.message||"Failed to update grievance")}catch(s){return e({error:s.response?.data?.message||s.message||"Failed to update grievance",isSubmitting:!1}),!1}},changeStatus:async(r,n,s)=>{const a=t(),i=`${r}-${n}-${s||"no-reason"}`,c=Date.now();if(a._lastRequest&&a._lastRequest.key===i&&c-a._lastRequest.timestamp<5e3)return console.warn("Duplicate request detected, ignoring:",i),!1;if(a.isSubmitting)return console.warn("Status change already in progress, ignoring duplicate request"),!1;e({isSubmitting:!0,error:null,success:null,_lastRequest:{key:i,timestamp:c}}),console.log("Attempting to change status:",{id:r,status:n,reason:s});const l=a.currentGrievance?{...a.currentGrievance,statusHistory:[...a.currentGrievance.statusHistory],comments:[...a.currentGrievance.comments],attachments:[...a.currentGrievance.attachments]}:null;try{if(!r||!n)throw new Error("Grievance ID and status are required");const u=l?{...l,status:n,lastUpdatedAt:new Date().toISOString(),statusHistory:[...l.statusHistory,{status:n,changedBy:"Current User",changedAt:new Date().toISOString(),reason:s||void 0}]}:null;u&&e({currentGrievance:u});const d={status:n,...s&&s.trim()&&{reason:s.trim()}};console.log("Sending status change request:",d);const m=await fv.updateGrievanceStatus(r,n,s);if(console.log("Status change service response:",m),m.success&&m.grievance){const h=m.grievance;console.log("📦 Raw grievance from backend:",{id:h._id,status:h.status,statusHistoryCount:h.statusHistory?.length||0,previousTimelinesCount:h.previousTimelines?.length||0,timelineCount:h.timeline?.length||0});const x={...h,title:h.title||"",description:h.description||"",category:h.category||"other",priority:h.priority||"medium",status:h.status||"pending",location:{address:h.location?.address||"",city:h.location?.city||"",state:h.location?.state||"",pincode:h.location?.pincode||"",coordinates:h.location?.coordinates||null},attachments:Array.isArray(h.attachments)?h.attachments:[],comments:Array.isArray(h.comments)?h.comments:[],statusHistory:Array.isArray(h.statusHistory)?h.statusHistory:[],previousTimelines:Array.isArray(h.previousTimelines)?h.previousTimelines:[],timeline:Array.isArray(h.timeline)?h.timeline:[],reopenCount:h.reopenCount||0};console.log("✅ Normalized grievance data:",{id:x._id,status:x.status,statusHistoryCount:x.statusHistory.length,previousTimelinesCount:x.previousTimelines.length,timelineCount:x.timeline.length}),e({success:"Status updated successfully!",isSubmitting:!1,currentGrievance:x}),console.log("🔄 GrievanceStore: Triggering timeline refresh after status change");try{const{useTimelineStore:g}=await ac(async()=>{const{useTimelineStore:b}=await Promise.resolve().then(()=>Xu);return{useTimelineStore:b}},void 0),p=g.getState();p.refreshTimeline&&(await p.refreshTimeline(r),console.log("✅ GrievanceStore: Timeline refreshed successfully"),console.log("✅ GrievanceStore: Timeline will be refreshed with updated data from backend"))}catch(g){console.warn("⚠️ GrievanceStore: Failed to refresh timeline:",g)}return setTimeout(async()=>{try{t()._refreshInProgress||(e({_refreshInProgress:!0}),await t().getGrievances(),t().currentGrievance?._id===x._id?e({currentGrievance:x,_refreshInProgress:!1}):e({_refreshInProgress:!1}))}catch(g){console.warn("Failed to refresh grievances list:",g),e({_refreshInProgress:!1})}},100),!0}throw new Error("Failed to change status")}catch(u){const d=t();d.currentGrievance&&d.currentGrievance._id===r&&(l&&e({currentGrievance:l}),setTimeout(async()=>{try{await t().getGrievanceById(r)}catch(h){console.warn("Failed to fetch fresh grievance data:",h)}},200)),console.error("Status change error:",{error:u.response?.data||u.message,httpStatus:u.response?.status,httpStatusText:u.response?.statusText,id:r,requestedStatus:n,reason:s,fullError:u,errorStack:u.stack,responseHeaders:u.response?.headers,requestConfig:u.config});const m=u.response?.data?.message||u.response?.data?.error||u.message||"Failed to change status";return e({error:m,isSubmitting:!1}),!1}},addComment:async(r,n,s=!1)=>{if(t().isSubmitting)return console.warn("Comment submission already in progress"),!1;e({isSubmitting:!0,error:null,success:null});try{const i=await Q.post(`/grievances/${r}/comments`,{message:n,isInternal:s});if(i.data.success)return e({success:"Comment added successfully!",isSubmitting:!1}),await t().getGrievanceById(r),!0;throw new Error(i.data.message||"Failed to add comment")}catch(i){return e({error:i.response?.data?.message||i.message||"Failed to add comment",isSubmitting:!1}),!1}},submitFeedback:async(r,n,s)=>{e({isSubmitting:!0,error:null,success:null});try{const a=await Q.post(`/grievances/${r}/feedback`,{rating:n,comment:s});if(a.data.success)return e({success:"Feedback submitted successfully!",isSubmitting:!1}),t().getGrievanceById(r),!0;throw new Error(a.data.message||"Failed to submit feedback")}catch(a){return e({error:a.response?.data?.message||a.message||"Failed to submit feedback",isSubmitting:!1}),!1}},uploadAttachment:async(r,n)=>{e({isUploading:!0,error:null,success:null});try{const s=new FormData;s.append("attachment",n);const a=await Q.post(`/grievances/${r}/attachments`,s,{headers:{"Content-Type":"multipart/form-data"}});if(a.data.success)return e({success:"File uploaded successfully!",isUploading:!1}),t().getGrievanceById(r),!0;throw new Error(a.data.message||"Failed to upload file")}catch(s){return e({error:s.response?.data?.message||s.message||"Failed to upload file",isUploading:!1}),!1}},removeAttachment:async(r,n)=>{e({isSubmitting:!0,error:null,success:null});try{const s=await Q.delete(`/grievances/${r}/attachments/${n}`);if(s.data.success)return e({success:"Attachment removed successfully!",isSubmitting:!1}),t().getGrievanceById(r),!0;throw new Error(s.data.message||"Failed to remove attachment")}catch(s){return e({error:s.response?.data?.message||s.message||"Failed to remove attachment",isSubmitting:!1}),!1}},getStats:async()=>{e({isLoading:!0,error:null});try{const r=await Q.get("/grievances/stats");r.data.success&&e({stats:r.data.data.overview,isLoading:!1})}catch(r){e({error:r.response?.data?.message||"Failed to fetch statistics",isLoading:!1})}},changePriority:async(r,n,s)=>{e({isSubmitting:!0,error:null,success:null});try{const a=await Q.patch(`/grievances/${r}/priority`,{priority:n,note:s});if(a.data.success){e({success:"Priority updated successfully!",isSubmitting:!1});const i=t();return i.currentGrievance?._id===r&&e({currentGrievance:{...i.currentGrievance,priority:n,lastUpdatedAt:new Date().toISOString()}}),!0}throw new Error(a.data.message||"Failed to update priority")}catch(a){return e({error:a.response?.data?.message||a.message||"Failed to update priority",isSubmitting:!1}),!1}},changeAssignment:async(r,n,s)=>{e({isSubmitting:!0,error:null,success:null});try{const a=await Q.patch(`/grievances/${r}/assignment`,{assigneeId:n,note:s});if(a.data.success){e({success:"Assignment updated successfully!",isSubmitting:!1});const i=t();return i.currentGrievance?._id===r&&e({currentGrievance:{...i.currentGrievance,assignedTo:a.data.data.grievance.assignedTo,assignedAt:a.data.data.grievance.assignedAt,lastUpdatedAt:new Date().toISOString()}}),!0}throw new Error(a.data.message||"Failed to update assignment")}catch(a){return e({error:a.response?.data?.message||a.message||"Failed to update assignment",isSubmitting:!1}),!1}},addNote:async(r,n)=>{e({isSubmitting:!0,error:null,success:null});try{const s=await Q.post(`/grievances/${r}/notes`,{note:n});if(s.data.success){e({success:"Note added successfully!",isSubmitting:!1});const a=t();return a.currentGrievance?._id===r&&e({currentGrievance:{...a.currentGrievance,comments:[...a.currentGrievance.comments,s.data.data.comment],lastUpdatedAt:new Date().toISOString()}}),!0}throw new Error(s.data.message||"Failed to add note")}catch(s){return e({error:s.response?.data?.message||s.message||"Failed to add note",isSubmitting:!1}),!1}},getAvailableUsers:async()=>{try{const r=await Q.get("/grievances/users/available");if(r.data.success)return r.data.data.users;throw new Error(r.data.message||"Failed to get available users")}catch(r){return console.error("Failed to get available users:",r),[]}}}));class mv{socket=null;reconnectAttempts=0;maxReconnectAttempts=5;connect(){const{accessToken:t}=st.getState();!t||this.socket?.connected||(this.socket=mn("http://localhost:5000",{auth:{token:t},transports:["polling","websocket"],reconnection:!0,reconnectionAttempts:this.maxReconnectAttempts,reconnectionDelay:1e3,timeout:2e4,upgrade:!0,rememberUpgrade:!1}),this.setupEventListeners())}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{this.reconnectAttempts=0}),this.socket.on("disconnect",()=>{}),this.socket.on("connect_error",()=>{this.reconnectAttempts++,this.reconnectAttempts>=this.maxReconnectAttempts&&this.disconnect()}),this.socket.on("grievance:update",()=>{hn.getState().getGrievances()}),this.socket.on("grievance:status_change",t=>{const{grievanceId:r,newStatus:n,updatedGrievance:s}=t;console.log("🔄 WebSocket: Received status change event:",{grievanceId:r,newStatus:n});const a=hn.getState(),i=a.grievances.map(u=>u._id===r?{...u,status:n}:u),c=a.myGrievances.map(u=>u._id===r?{...u,status:n}:u);let l=a.currentGrievance;a.currentGrievance?._id===r&&(console.log("🔄 WebSocket: Updating current grievance with new status"),l=s||{...a.currentGrievance,status:n,lastUpdatedAt:new Date().toISOString()},console.log("🔄 WebSocket: Triggering timeline refresh for current grievance"),ac(async()=>{const{useTimelineStore:u}=await Promise.resolve().then(()=>Xu);return{useTimelineStore:u}},void 0).then(({useTimelineStore:u})=>{const d=u.getState();d.refreshTimeline&&d.refreshTimeline(r)})),hn.setState({grievances:i,myGrievances:c,currentGrievance:l}),console.log("✅ WebSocket: Store updated with new status"),console.log(`Grievance status updated to ${n}`)}),this.socket.on("notification",()=>{}),this.socket.on("chat:message",()=>{}),this.socket.on("system:announcement",()=>{}))}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null)}emit(t,r){this.socket?.connected&&this.socket.emit(t,r)}isConnected(){return this.socket?.connected||!1}}const Ft=new mv,st=Uo((e,t)=>({user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,clearError:()=>e({error:null}),setTokens:(r,n)=>{try{localStorage.setItem("accessToken",r),localStorage.setItem("refreshToken",n),e({accessToken:r,refreshToken:n,isAuthenticated:!0,error:null})}catch(s){console.error("Failed to store tokens:",s),e({accessToken:r,refreshToken:n,isAuthenticated:!0,error:"Warning: Failed to persist login session"})}},setUser:r=>{e({user:r})},initialize:async()=>{const r=localStorage.getItem("accessToken"),n=localStorage.getItem("refreshToken");if(!r){e({user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null});return}e({isLoading:!0});try{e({accessToken:r,refreshToken:n,isAuthenticated:!0,isLoading:!0});const s=await Q.get("/auth/me");if(s.data.success)console.log("🔍 DEBUG - Full user data from backend:",s.data.data),console.log("🔍 DEBUG - Profile photo URL from backend:",s.data.data.profilePhotoUrl),console.log("🔍 DEBUG - Cover photo URL from backend:",s.data.data.coverPhotoUrl),e({user:s.data.data,isAuthenticated:!0,accessToken:r,refreshToken:n,isLoading:!1,error:null}),Ft.connect();else throw new Error("Profile fetch failed")}catch(s){console.error("Auth initialization failed:",s),localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),e({user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null})}},login:async r=>{e({isLoading:!0,error:null});try{const n=await Q.post("/auth/login",r);if(n.data.success){const{accessToken:s,refreshToken:a,user:i}=n.data.data;if(!s||!a||!i)throw new Error("Invalid server response. Missing token or user data.");return t().setTokens(s,a),e({user:i,isAuthenticated:!0,isLoading:!1,error:null}),Ft.connect(),!0}else throw new Error(n.data.message||"Login failed")}catch(n){const s=n.response?.data?.message||n.response?.data?.error||n.message||"Network error. Please check your connection.";return e({error:s,isLoading:!1,isAuthenticated:!1}),!1}},register:async r=>{e({isLoading:!0,error:null});try{const n=await Q.post("/auth/register",r);if(n.data.success){const{token:s,refreshToken:a,user:i}=n.data.data;if(!s||!a||!i)throw new Error("Invalid server response. Missing token or user data.");return t().setTokens(s,a),e({user:i,isAuthenticated:!0,isLoading:!1,error:null}),Ft.connect(),!0}else throw new Error(n.data.message||"Registration failed")}catch(n){return e({error:n.response?.data?.message||"Registration failed",isLoading:!1}),!1}},logout:()=>{try{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),typeof Ft<"u"&&Ft.disconnect&&Ft.disconnect(),e({user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null})}catch(r){console.error("Logout cleanup failed:",r),e({user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null})}}}));var Us={exports:{}},Bs={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ui;function hv(){if(Ui)return Bs;Ui=1;var e=Fm();function t(m,h){return m===h&&(m!==0||1/m===1/h)||m!==m&&h!==h}var r=typeof Object.is=="function"?Object.is:t,n=e.useState,s=e.useEffect,a=e.useLayoutEffect,i=e.useDebugValue;function c(m,h){var x=h(),g=n({inst:{value:x,getSnapshot:h}}),p=g[0].inst,b=g[1];return a(function(){p.value=x,p.getSnapshot=h,l(p)&&b({inst:p})},[m,x,h]),s(function(){return l(p)&&b({inst:p}),m(function(){l(p)&&b({inst:p})})},[m]),i(x),x}function l(m){var h=m.getSnapshot;m=m.value;try{var x=h();return!r(m,x)}catch{return!0}}function u(m,h){return h()}var d=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:c;return Bs.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:d,Bs}var Bi;function pv(){return Bi||(Bi=1,Us.exports=hv()),Us.exports}var gv=pv();function xv(){return gv.useSyncExternalStore(bv,()=>!0,()=>!1)}function bv(){return()=>{}}var ua="Avatar",[vv,tk]=Ne(ua),[yv,gd]=vv(ua),xd=f.forwardRef((e,t)=>{const{__scopeAvatar:r,...n}=e,[s,a]=f.useState("idle");return o.jsx(yv,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:a,children:o.jsx(U.span,{...n,ref:t})})});xd.displayName=ua;var bd="AvatarImage",vd=f.forwardRef((e,t)=>{const{__scopeAvatar:r,src:n,onLoadingStatusChange:s=()=>{},...a}=e,i=gd(bd,r),c=wv(n,a),l=pe(u=>{s(u),i.onImageLoadingStatusChange(u)});return fe(()=>{c!=="idle"&&l(c)},[c,l]),c==="loaded"?o.jsx(U.img,{...a,ref:t,src:n}):null});vd.displayName=bd;var yd="AvatarFallback",wd=f.forwardRef((e,t)=>{const{__scopeAvatar:r,delayMs:n,...s}=e,a=gd(yd,r),[i,c]=f.useState(n===void 0);return f.useEffect(()=>{if(n!==void 0){const l=window.setTimeout(()=>c(!0),n);return()=>window.clearTimeout(l)}},[n]),i&&a.imageLoadingStatus!=="loaded"?o.jsx(U.span,{...s,ref:t}):null});wd.displayName=yd;function Hi(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function wv(e,{referrerPolicy:t,crossOrigin:r}){const n=xv(),s=f.useRef(null),a=n?(s.current||(s.current=new window.Image),s.current):null,[i,c]=f.useState(()=>Hi(a,e));return fe(()=>{c(Hi(a,e))},[a,e]),fe(()=>{const l=m=>()=>{c(m)};if(!a)return;const u=l("loaded"),d=l("error");return a.addEventListener("load",u),a.addEventListener("error",d),t&&(a.referrerPolicy=t),typeof r=="string"&&(a.crossOrigin=r),()=>{a.removeEventListener("load",u),a.removeEventListener("error",d)}},[a,r,t]),i}var rk=xd,nk=vd,sk=wd;function fa(e){const t=e+"CollectionProvider",[r,n]=Ne(t),[s,a]=r(t,{collectionRef:{current:null},itemMap:new Map}),i=p=>{const{scope:b,children:v}=p,y=Ee.useRef(null),w=Ee.useRef(new Map).current;return o.jsx(s,{scope:b,itemMap:w,collectionRef:y,children:v})};i.displayName=t;const c=e+"CollectionSlot",l=mt(c),u=Ee.forwardRef((p,b)=>{const{scope:v,children:y}=p,w=a(c,v),S=G(b,w.collectionRef);return o.jsx(l,{ref:S,children:y})});u.displayName=c;const d=e+"CollectionItemSlot",m="data-radix-collection-item",h=mt(d),x=Ee.forwardRef((p,b)=>{const{scope:v,children:y,...w}=p,S=Ee.useRef(null),j=G(b,S),D=a(d,v);return Ee.useEffect(()=>(D.itemMap.set(S,{ref:S,...w}),()=>void D.itemMap.delete(S))),o.jsx(h,{[m]:"",ref:j,children:y})});x.displayName=d;function g(p){const b=a(e+"CollectionConsumer",p);return Ee.useCallback(()=>{const y=b.collectionRef.current;if(!y)return[];const w=Array.from(y.querySelectorAll(`[${m}]`));return Array.from(b.itemMap.values()).sort((D,E)=>w.indexOf(D.ref.current)-w.indexOf(E.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:i,Slot:u,ItemSlot:x},g,n]}var Nv=f.createContext(void 0);function ir(e){const t=f.useContext(Nv);return e||t||"ltr"}var Hs="rovingFocusGroup.onEntryFocus",Sv={bubbles:!1,cancelable:!0},Lr="RovingFocusGroup",[So,Nd,kv]=fa(Lr),[jv,cr]=Ne(Lr,[kv]),[Cv,Tv]=jv(Lr),Sd=f.forwardRef((e,t)=>o.jsx(So.Provider,{scope:e.__scopeRovingFocusGroup,children:o.jsx(So.Slot,{scope:e.__scopeRovingFocusGroup,children:o.jsx(Ev,{...e,ref:t})})}));Sd.displayName=Lr;var Ev=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,orientation:n,loop:s=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...m}=e,h=f.useRef(null),x=G(t,h),g=ir(a),[p,b]=Qe({prop:i,defaultProp:c??null,onChange:l,caller:Lr}),[v,y]=f.useState(!1),w=pe(u),S=Nd(r),j=f.useRef(!1),[D,E]=f.useState(0);return f.useEffect(()=>{const T=h.current;if(T)return T.addEventListener(Hs,w),()=>T.removeEventListener(Hs,w)},[w]),o.jsx(Cv,{scope:r,orientation:n,dir:g,loop:s,currentTabStopId:p,onItemFocus:f.useCallback(T=>b(T),[b]),onItemShiftTab:f.useCallback(()=>y(!0),[]),onFocusableItemAdd:f.useCallback(()=>E(T=>T+1),[]),onFocusableItemRemove:f.useCallback(()=>E(T=>T-1),[]),children:o.jsx(U.div,{tabIndex:v||D===0?-1:0,"data-orientation":n,...m,ref:x,style:{outline:"none",...e.style},onMouseDown:P(e.onMouseDown,()=>{j.current=!0}),onFocus:P(e.onFocus,T=>{const N=!j.current;if(T.target===T.currentTarget&&N&&!v){const _=new CustomEvent(Hs,Sv);if(T.currentTarget.dispatchEvent(_),!_.defaultPrevented){const I=S().filter(F=>F.focusable),H=I.find(F=>F.active),A=I.find(F=>F.id===p),V=[H,A,...I].filter(Boolean).map(F=>F.ref.current);Cd(V,d)}}j.current=!1}),onBlur:P(e.onBlur,()=>y(!1))})})}),kd="RovingFocusGroupItem",jd=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:r,focusable:n=!0,active:s=!1,tabStopId:a,children:i,...c}=e,l=Ie(),u=a||l,d=Tv(kd,r),m=d.currentTabStopId===u,h=Nd(r),{onFocusableItemAdd:x,onFocusableItemRemove:g,currentTabStopId:p}=d;return f.useEffect(()=>{if(n)return x(),()=>g()},[n,x,g]),o.jsx(So.ItemSlot,{scope:r,id:u,focusable:n,active:s,children:o.jsx(U.span,{tabIndex:m?0:-1,"data-orientation":d.orientation,...c,ref:t,onMouseDown:P(e.onMouseDown,b=>{n?d.onItemFocus(u):b.preventDefault()}),onFocus:P(e.onFocus,()=>d.onItemFocus(u)),onKeyDown:P(e.onKeyDown,b=>{if(b.key==="Tab"&&b.shiftKey){d.onItemShiftTab();return}if(b.target!==b.currentTarget)return;const v=Av(b,d.orientation,d.dir);if(v!==void 0){if(b.metaKey||b.ctrlKey||b.altKey||b.shiftKey)return;b.preventDefault();let w=h().filter(S=>S.focusable).map(S=>S.ref.current);if(v==="last")w.reverse();else if(v==="prev"||v==="next"){v==="prev"&&w.reverse();const S=w.indexOf(b.currentTarget);w=d.loop?Dv(w,S+1):w.slice(S+1)}setTimeout(()=>Cd(w))}}),children:typeof i=="function"?i({isCurrentTabStop:m,hasTabStop:p!=null}):i})})});jd.displayName=kd;var Rv={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _v(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Av(e,t,r){const n=_v(e.key,r);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return Rv[n]}function Cd(e,t=!1){const r=document.activeElement;for(const n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}function Dv(e,t){return e.map((r,n)=>e[(t+n)%e.length])}var ma=Sd,ha=jd,ko=["Enter"," "],Pv=["ArrowDown","PageUp","Home"],Td=["ArrowUp","PageDown","End"],Ov=[...Pv,...Td],Iv={ltr:[...ko,"ArrowRight"],rtl:[...ko,"ArrowLeft"]},Mv={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Fr="Menu",[jr,Lv,Fv]=fa(Fr),[Dt,Ed]=Ne(Fr,[Fv,gt,cr]),ss=gt(),Rd=cr(),[$v,Pt]=Dt(Fr),[Uv,$r]=Dt(Fr),_d=e=>{const{__scopeMenu:t,open:r=!1,children:n,dir:s,onOpenChange:a,modal:i=!0}=e,c=ss(t),[l,u]=f.useState(null),d=f.useRef(!1),m=pe(a),h=ir(s);return f.useEffect(()=>{const x=()=>{d.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>d.current=!1;return document.addEventListener("keydown",x,{capture:!0}),()=>{document.removeEventListener("keydown",x,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),o.jsx(Vn,{...c,children:o.jsx($v,{scope:t,open:r,onOpenChange:m,content:l,onContentChange:u,children:o.jsx(Uv,{scope:t,onClose:f.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:d,dir:h,modal:i,children:n})})})};_d.displayName=Fr;var Bv="MenuAnchor",pa=f.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e,s=ss(r);return o.jsx(Or,{...s,...n,ref:t})});pa.displayName=Bv;var ga="MenuPortal",[Hv,Ad]=Dt(ga,{forceMount:void 0}),Dd=e=>{const{__scopeMenu:t,forceMount:r,children:n,container:s}=e,a=Pt(ga,t);return o.jsx(Hv,{scope:t,forceMount:r,children:o.jsx(xe,{present:r||a.open,children:o.jsx(Ar,{asChild:!0,container:s,children:n})})})};Dd.displayName=ga;var Me="MenuContent",[Vv,xa]=Dt(Me),Pd=f.forwardRef((e,t)=>{const r=Ad(Me,e.__scopeMenu),{forceMount:n=r.forceMount,...s}=e,a=Pt(Me,e.__scopeMenu),i=$r(Me,e.__scopeMenu);return o.jsx(jr.Provider,{scope:e.__scopeMenu,children:o.jsx(xe,{present:n||a.open,children:o.jsx(jr.Slot,{scope:e.__scopeMenu,children:i.modal?o.jsx(zv,{...s,ref:t}):o.jsx(Wv,{...s,ref:t})})})})}),zv=f.forwardRef((e,t)=>{const r=Pt(Me,e.__scopeMenu),n=f.useRef(null),s=G(t,n);return f.useEffect(()=>{const a=n.current;if(a)return Fn(a)},[]),o.jsx(ba,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:P(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),Wv=f.forwardRef((e,t)=>{const r=Pt(Me,e.__scopeMenu);return o.jsx(ba,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Gv=mt("MenuContent.ScrollLock"),ba=f.forwardRef((e,t)=>{const{__scopeMenu:r,loop:n=!1,trapFocus:s,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:h,onDismiss:x,disableOutsideScroll:g,...p}=e,b=Pt(Me,r),v=$r(Me,r),y=ss(r),w=Rd(r),S=Lv(r),[j,D]=f.useState(null),E=f.useRef(null),T=G(t,E,b.onContentChange),N=f.useRef(0),_=f.useRef(""),I=f.useRef(0),H=f.useRef(null),A=f.useRef("right"),$=f.useRef(0),V=g?Dr:f.Fragment,F=g?{as:Gv,allowPinchZoom:!0}:void 0,z=R=>{const K=_.current+R,he=S().filter(O=>!O.disabled),Se=document.activeElement,se=he.find(O=>O.ref.current===Se)?.textValue,re=he.map(O=>O.textValue),je=sy(re,K,se),be=he.find(O=>O.textValue===je)?.ref.current;(function O(ee){_.current=ee,window.clearTimeout(N.current),ee!==""&&(N.current=window.setTimeout(()=>O(""),1e3))})(K),be&&setTimeout(()=>be.focus())};f.useEffect(()=>()=>window.clearTimeout(N.current),[]),Mn();const C=f.useCallback(R=>A.current===H.current?.side&&ay(R,H.current?.area),[]);return o.jsx(Vv,{scope:r,searchRef:_,onItemEnter:f.useCallback(R=>{C(R)&&R.preventDefault()},[C]),onItemLeave:f.useCallback(R=>{C(R)||(E.current?.focus(),D(null))},[C]),onTriggerLeave:f.useCallback(R=>{C(R)&&R.preventDefault()},[C]),pointerGraceTimerRef:I,onPointerGraceIntentChange:f.useCallback(R=>{H.current=R},[]),children:o.jsx(V,{...F,children:o.jsx(_r,{asChild:!0,trapped:s,onMountAutoFocus:P(a,R=>{R.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:o.jsx(tr,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:m,onInteractOutside:h,onDismiss:x,children:o.jsx(ma,{asChild:!0,...w,dir:v.dir,orientation:"vertical",loop:n,currentTabStopId:j,onCurrentTabStopIdChange:D,onEntryFocus:P(l,R=>{v.isUsingKeyboardRef.current||R.preventDefault()}),preventScrollOnEntryFocus:!0,children:o.jsx(zn,{role:"menu","aria-orientation":"vertical","data-state":Yd(b.open),"data-radix-menu-content":"",dir:v.dir,...y,...p,ref:T,style:{outline:"none",...p.style},onKeyDown:P(p.onKeyDown,R=>{const he=R.target.closest("[data-radix-menu-content]")===R.currentTarget,Se=R.ctrlKey||R.altKey||R.metaKey,se=R.key.length===1;he&&(R.key==="Tab"&&R.preventDefault(),!Se&&se&&z(R.key));const re=E.current;if(R.target!==re||!Ov.includes(R.key))return;R.preventDefault();const be=S().filter(O=>!O.disabled).map(O=>O.ref.current);Td.includes(R.key)&&be.reverse(),ry(be)}),onBlur:P(e.onBlur,R=>{R.currentTarget.contains(R.target)||(window.clearTimeout(N.current),_.current="")}),onPointerMove:P(e.onPointerMove,Cr(R=>{const K=R.target,he=$.current!==R.clientX;if(R.currentTarget.contains(K)&&he){const Se=R.clientX>$.current?"right":"left";A.current=Se,$.current=R.clientX}}))})})})})})})});Pd.displayName=Me;var qv="MenuGroup",va=f.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e;return o.jsx(U.div,{role:"group",...n,ref:t})});va.displayName=qv;var Kv="MenuLabel",Od=f.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e;return o.jsx(U.div,{...n,ref:t})});Od.displayName=Kv;var Tn="MenuItem",Vi="menu.itemSelect",os=f.forwardRef((e,t)=>{const{disabled:r=!1,onSelect:n,...s}=e,a=f.useRef(null),i=$r(Tn,e.__scopeMenu),c=xa(Tn,e.__scopeMenu),l=G(t,a),u=f.useRef(!1),d=()=>{const m=a.current;if(!r&&m){const h=new CustomEvent(Vi,{bubbles:!0,cancelable:!0});m.addEventListener(Vi,x=>n?.(x),{once:!0}),yc(m,h),h.defaultPrevented?u.current=!1:i.onClose()}};return o.jsx(Id,{...s,ref:l,disabled:r,onClick:P(e.onClick,d),onPointerDown:m=>{e.onPointerDown?.(m),u.current=!0},onPointerUp:P(e.onPointerUp,m=>{u.current||m.currentTarget?.click()}),onKeyDown:P(e.onKeyDown,m=>{const h=c.searchRef.current!=="";r||h&&m.key===" "||ko.includes(m.key)&&(m.currentTarget.click(),m.preventDefault())})})});os.displayName=Tn;var Id=f.forwardRef((e,t)=>{const{__scopeMenu:r,disabled:n=!1,textValue:s,...a}=e,i=xa(Tn,r),c=Rd(r),l=f.useRef(null),u=G(t,l),[d,m]=f.useState(!1),[h,x]=f.useState("");return f.useEffect(()=>{const g=l.current;g&&x((g.textContent??"").trim())},[a.children]),o.jsx(jr.ItemSlot,{scope:r,disabled:n,textValue:s??h,children:o.jsx(ha,{asChild:!0,...c,focusable:!n,children:o.jsx(U.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":n||void 0,"data-disabled":n?"":void 0,...a,ref:u,onPointerMove:P(e.onPointerMove,Cr(g=>{n?i.onItemLeave(g):(i.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:P(e.onPointerLeave,Cr(g=>i.onItemLeave(g))),onFocus:P(e.onFocus,()=>m(!0)),onBlur:P(e.onBlur,()=>m(!1))})})})}),Yv="MenuCheckboxItem",Md=f.forwardRef((e,t)=>{const{checked:r=!1,onCheckedChange:n,...s}=e;return o.jsx(Bd,{scope:e.__scopeMenu,checked:r,children:o.jsx(os,{role:"menuitemcheckbox","aria-checked":En(r)?"mixed":r,...s,ref:t,"data-state":wa(r),onSelect:P(s.onSelect,()=>n?.(En(r)?!0:!r),{checkForDefaultPrevented:!1})})})});Md.displayName=Yv;var Ld="MenuRadioGroup",[Xv,Jv]=Dt(Ld,{value:void 0,onValueChange:()=>{}}),Fd=f.forwardRef((e,t)=>{const{value:r,onValueChange:n,...s}=e,a=pe(n);return o.jsx(Xv,{scope:e.__scopeMenu,value:r,onValueChange:a,children:o.jsx(va,{...s,ref:t})})});Fd.displayName=Ld;var $d="MenuRadioItem",Ud=f.forwardRef((e,t)=>{const{value:r,...n}=e,s=Jv($d,e.__scopeMenu),a=r===s.value;return o.jsx(Bd,{scope:e.__scopeMenu,checked:a,children:o.jsx(os,{role:"menuitemradio","aria-checked":a,...n,ref:t,"data-state":wa(a),onSelect:P(n.onSelect,()=>s.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});Ud.displayName=$d;var ya="MenuItemIndicator",[Bd,Qv]=Dt(ya,{checked:!1}),Hd=f.forwardRef((e,t)=>{const{__scopeMenu:r,forceMount:n,...s}=e,a=Qv(ya,r);return o.jsx(xe,{present:n||En(a.checked)||a.checked===!0,children:o.jsx(U.span,{...s,ref:t,"data-state":wa(a.checked)})})});Hd.displayName=ya;var Zv="MenuSeparator",Vd=f.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e;return o.jsx(U.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});Vd.displayName=Zv;var ey="MenuArrow",zd=f.forwardRef((e,t)=>{const{__scopeMenu:r,...n}=e,s=ss(r);return o.jsx(Wn,{...s,...n,ref:t})});zd.displayName=ey;var ty="MenuSub",[ok,Wd]=Dt(ty),hr="MenuSubTrigger",Gd=f.forwardRef((e,t)=>{const r=Pt(hr,e.__scopeMenu),n=$r(hr,e.__scopeMenu),s=Wd(hr,e.__scopeMenu),a=xa(hr,e.__scopeMenu),i=f.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=a,u={__scopeMenu:e.__scopeMenu},d=f.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return f.useEffect(()=>d,[d]),f.useEffect(()=>{const m=c.current;return()=>{window.clearTimeout(m),l(null)}},[c,l]),o.jsx(pa,{asChild:!0,...u,children:o.jsx(Id,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":s.contentId,"data-state":Yd(r.open),...e,ref:In(t,s.onTriggerChange),onClick:m=>{e.onClick?.(m),!(e.disabled||m.defaultPrevented)&&(m.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:P(e.onPointerMove,Cr(m=>{a.onItemEnter(m),!m.defaultPrevented&&!e.disabled&&!r.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{r.onOpenChange(!0),d()},100))})),onPointerLeave:P(e.onPointerLeave,Cr(m=>{d();const h=r.content?.getBoundingClientRect();if(h){const x=r.content?.dataset.side,g=x==="right",p=g?-5:5,b=h[g?"left":"right"],v=h[g?"right":"left"];a.onPointerGraceIntentChange({area:[{x:m.clientX+p,y:m.clientY},{x:b,y:h.top},{x:v,y:h.top},{x:v,y:h.bottom},{x:b,y:h.bottom}],side:x}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(m),m.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:P(e.onKeyDown,m=>{const h=a.searchRef.current!=="";e.disabled||h&&m.key===" "||Iv[n.dir].includes(m.key)&&(r.onOpenChange(!0),r.content?.focus(),m.preventDefault())})})})});Gd.displayName=hr;var qd="MenuSubContent",Kd=f.forwardRef((e,t)=>{const r=Ad(Me,e.__scopeMenu),{forceMount:n=r.forceMount,...s}=e,a=Pt(Me,e.__scopeMenu),i=$r(Me,e.__scopeMenu),c=Wd(qd,e.__scopeMenu),l=f.useRef(null),u=G(t,l);return o.jsx(jr.Provider,{scope:e.__scopeMenu,children:o.jsx(xe,{present:n||a.open,children:o.jsx(jr.Slot,{scope:e.__scopeMenu,children:o.jsx(ba,{id:c.contentId,"aria-labelledby":c.triggerId,...s,ref:u,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:d=>{i.isUsingKeyboardRef.current&&l.current?.focus(),d.preventDefault()},onCloseAutoFocus:d=>d.preventDefault(),onFocusOutside:P(e.onFocusOutside,d=>{d.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:P(e.onEscapeKeyDown,d=>{i.onClose(),d.preventDefault()}),onKeyDown:P(e.onKeyDown,d=>{const m=d.currentTarget.contains(d.target),h=Mv[i.dir].includes(d.key);m&&h&&(a.onOpenChange(!1),c.trigger?.focus(),d.preventDefault())})})})})})});Kd.displayName=qd;function Yd(e){return e?"open":"closed"}function En(e){return e==="indeterminate"}function wa(e){return En(e)?"indeterminate":e?"checked":"unchecked"}function ry(e){const t=document.activeElement;for(const r of e)if(r===t||(r.focus(),document.activeElement!==t))return}function ny(e,t){return e.map((r,n)=>e[(t+n)%e.length])}function sy(e,t,r){const s=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,a=r?e.indexOf(r):-1;let i=ny(e,Math.max(a,0));s.length===1&&(i=i.filter(u=>u!==r));const l=i.find(u=>u.toLowerCase().startsWith(s.toLowerCase()));return l!==r?l:void 0}function oy(e,t){const{x:r,y:n}=e;let s=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a],l=t[i],u=c.x,d=c.y,m=l.x,h=l.y;d>n!=h>n&&r<(m-u)*(n-d)/(h-d)+u&&(s=!s)}return s}function ay(e,t){if(!t)return!1;const r={x:e.clientX,y:e.clientY};return oy(r,t)}function Cr(e){return t=>t.pointerType==="mouse"?e(t):void 0}var iy=_d,cy=pa,ly=Dd,dy=Pd,uy=va,fy=Od,my=os,hy=Md,py=Fd,gy=Ud,xy=Hd,by=Vd,vy=zd,yy=Gd,wy=Kd,as="DropdownMenu",[Ny,ak]=Ne(as,[Ed]),ke=Ed(),[Sy,Xd]=Ny(as),Jd=e=>{const{__scopeDropdownMenu:t,children:r,dir:n,open:s,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,l=ke(t),u=f.useRef(null),[d,m]=Qe({prop:s,defaultProp:a??!1,onChange:i,caller:as});return o.jsx(Sy,{scope:t,triggerId:Ie(),triggerRef:u,contentId:Ie(),open:d,onOpenChange:m,onOpenToggle:f.useCallback(()=>m(h=>!h),[m]),modal:c,children:o.jsx(iy,{...l,open:d,onOpenChange:m,dir:n,modal:c,children:r})})};Jd.displayName=as;var Qd="DropdownMenuTrigger",Zd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,disabled:n=!1,...s}=e,a=Xd(Qd,r),i=ke(r);return o.jsx(cy,{asChild:!0,...i,children:o.jsx(U.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...s,ref:In(t,a.triggerRef),onPointerDown:P(e.onPointerDown,c=>{!n&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:P(e.onKeyDown,c=>{n||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});Zd.displayName=Qd;var ky="DropdownMenuPortal",eu=e=>{const{__scopeDropdownMenu:t,...r}=e,n=ke(t);return o.jsx(ly,{...n,...r})};eu.displayName=ky;var tu="DropdownMenuContent",ru=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=Xd(tu,r),a=ke(r),i=f.useRef(!1);return o.jsx(dy,{id:s.contentId,"aria-labelledby":s.triggerId,...a,...n,ref:t,onCloseAutoFocus:P(e.onCloseAutoFocus,c=>{i.current||s.triggerRef.current?.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:P(e.onInteractOutside,c=>{const l=c.detail.originalEvent,u=l.button===0&&l.ctrlKey===!0,d=l.button===2||u;(!s.modal||d)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ru.displayName=tu;var jy="DropdownMenuGroup",Cy=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(uy,{...s,...n,ref:t})});Cy.displayName=jy;var Ty="DropdownMenuLabel",nu=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(fy,{...s,...n,ref:t})});nu.displayName=Ty;var Ey="DropdownMenuItem",su=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(my,{...s,...n,ref:t})});su.displayName=Ey;var Ry="DropdownMenuCheckboxItem",ou=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(hy,{...s,...n,ref:t})});ou.displayName=Ry;var _y="DropdownMenuRadioGroup",Ay=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(py,{...s,...n,ref:t})});Ay.displayName=_y;var Dy="DropdownMenuRadioItem",au=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(gy,{...s,...n,ref:t})});au.displayName=Dy;var Py="DropdownMenuItemIndicator",iu=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(xy,{...s,...n,ref:t})});iu.displayName=Py;var Oy="DropdownMenuSeparator",cu=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(by,{...s,...n,ref:t})});cu.displayName=Oy;var Iy="DropdownMenuArrow",My=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(vy,{...s,...n,ref:t})});My.displayName=Iy;var Ly="DropdownMenuSubTrigger",lu=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(yy,{...s,...n,ref:t})});lu.displayName=Ly;var Fy="DropdownMenuSubContent",du=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:r,...n}=e,s=ke(r);return o.jsx(wy,{...s,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});du.displayName=Fy;var $y=Jd,Uy=Zd,By=eu,uu=ru,fu=nu,mu=su,hu=ou,pu=au,gu=iu,xu=cu,bu=lu,vu=du;const zi=$y,Wi=Uy,Hy=f.forwardRef(({className:e,inset:t,children:r,...n},s)=>o.jsxs(bu,{ref:s,className:B("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...n,children:[r,o.jsx(Gs,{className:"ml-auto h-4 w-4"})]}));Hy.displayName=bu.displayName;const Vy=f.forwardRef(({className:e,...t},r)=>o.jsx(vu,{ref:r,className:B("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));Vy.displayName=vu.displayName;const jo=f.forwardRef(({className:e,sideOffset:t=4,...r},n)=>o.jsx(By,{children:o.jsx(uu,{ref:n,sideOffset:t,className:B("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));jo.displayName=uu.displayName;const $t=f.forwardRef(({className:e,inset:t,...r},n)=>o.jsx(mu,{ref:n,className:B("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...r}));$t.displayName=mu.displayName;const zy=f.forwardRef(({className:e,children:t,checked:r,...n},s)=>o.jsxs(hu,{ref:s,className:B("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:r,...n,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(gu,{children:o.jsx(sc,{className:"h-4 w-4"})})}),t]}));zy.displayName=hu.displayName;const Wy=f.forwardRef(({className:e,children:t,...r},n)=>o.jsxs(pu,{ref:n,className:B("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(gu,{children:o.jsx(xm,{className:"h-2 w-2 fill-current"})})}),t]}));Wy.displayName=pu.displayName;const Gy=f.forwardRef(({className:e,inset:t,...r},n)=>o.jsx(fu,{ref:n,className:B("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...r}));Gy.displayName=fu.displayName;const yu=f.forwardRef(({className:e,...t},r)=>o.jsx(xu,{ref:r,className:B("-mx-1 my-1 h-px bg-muted",e),...t}));yu.displayName=xu.displayName;const ae=f.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:B("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ae.displayName="Card";const ge=f.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:B("flex flex-col space-y-1.5 p-6",e),...t}));ge.displayName="CardHeader";const qy=f.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:B("flex items-center",e),...t}));qy.displayName="CardAction";const ye=f.forwardRef(({className:e,...t},r)=>o.jsx("h3",{ref:r,className:B("text-2xl font-semibold leading-none tracking-tight",e),...t}));ye.displayName="CardTitle";const Ky=f.forwardRef(({className:e,...t},r)=>o.jsx("p",{ref:r,className:B("text-sm text-muted-foreground",e),...t}));Ky.displayName="CardDescription";const de=f.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:B("p-6 pt-0",e),...t}));de.displayName="CardContent";const Yy=f.forwardRef(({className:e,...t},r)=>o.jsx("div",{ref:r,className:B("flex items-center p-6 pt-0",e),...t}));Yy.displayName="CardFooter";const Xy=Bo("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function q({className:e,variant:t,...r}){return o.jsx("div",{className:B(Xy({variant:t}),e),...r})}var Jy="Label",wu=f.forwardRef((e,t)=>o.jsx(U.label,{...e,ref:t,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));wu.displayName=Jy;var Nu=wu;const Qy=Bo("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),ne=f.forwardRef(({className:e,...t},r)=>o.jsx(Nu,{ref:r,className:B(Qy(),e),...t}));ne.displayName=Nu.displayName;const pr=Zp,ik=eg,Zy=tg,Su=f.forwardRef(({className:e,...t},r)=>o.jsx(Jc,{ref:r,className:B("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Su.displayName=Jc.displayName;const Ut=f.forwardRef(({className:e,children:t,...r},n)=>o.jsxs(Zy,{children:[o.jsx(Su,{}),o.jsxs(Qc,{ref:n,className:B("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...r,children:[t,o.jsxs(rg,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[o.jsx(Wt,{className:"h-4 w-4"}),o.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Ut.displayName=Qc.displayName;const ku=({className:e,...t})=>o.jsx("div",{className:B("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ku.displayName="DialogHeader";const ju=({className:e,...t})=>o.jsx("div",{className:B("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});ju.displayName="DialogFooter";const Bt=f.forwardRef(({className:e,...t},r)=>o.jsx(Zc,{ref:r,className:B("text-lg font-semibold leading-none tracking-tight",e),...t}));Bt.displayName=Zc.displayName;const ew=f.forwardRef(({className:e,...t},r)=>o.jsx(el,{ref:r,className:B("text-sm text-muted-foreground",e),...t}));ew.displayName=el.displayName;const xr=f.forwardRef(({className:e,value:t,...r},n)=>{const s=t!==void 0?t:"";return o.jsx("textarea",{value:s,className:B("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...r})});xr.displayName="Textarea";var is="Popover",[Cu,ck]=Ne(is,[gt]),Ur=gt(),[tw,xt]=Cu(is),Tu=e=>{const{__scopePopover:t,children:r,open:n,defaultOpen:s,onOpenChange:a,modal:i=!1}=e,c=Ur(t),l=f.useRef(null),[u,d]=f.useState(!1),[m,h]=Qe({prop:n,defaultProp:s??!1,onChange:a,caller:is});return o.jsx(Vn,{...c,children:o.jsx(tw,{scope:t,contentId:Ie(),triggerRef:l,open:m,onOpenChange:h,onOpenToggle:f.useCallback(()=>h(x=>!x),[h]),hasCustomAnchor:u,onCustomAnchorAdd:f.useCallback(()=>d(!0),[]),onCustomAnchorRemove:f.useCallback(()=>d(!1),[]),modal:i,children:r})})};Tu.displayName=is;var Eu="PopoverAnchor",rw=f.forwardRef((e,t)=>{const{__scopePopover:r,...n}=e,s=xt(Eu,r),a=Ur(r),{onCustomAnchorAdd:i,onCustomAnchorRemove:c}=s;return f.useEffect(()=>(i(),()=>c()),[i,c]),o.jsx(Or,{...a,...n,ref:t})});rw.displayName=Eu;var Ru="PopoverTrigger",_u=f.forwardRef((e,t)=>{const{__scopePopover:r,...n}=e,s=xt(Ru,r),a=Ur(r),i=G(t,s.triggerRef),c=o.jsx(U.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Iu(s.open),...n,ref:i,onClick:P(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?c:o.jsx(Or,{asChild:!0,...a,children:c})});_u.displayName=Ru;var Na="PopoverPortal",[nw,sw]=Cu(Na,{forceMount:void 0}),Au=e=>{const{__scopePopover:t,forceMount:r,children:n,container:s}=e,a=xt(Na,t);return o.jsx(nw,{scope:t,forceMount:r,children:o.jsx(xe,{present:r||a.open,children:o.jsx(Ar,{asChild:!0,container:s,children:n})})})};Au.displayName=Na;var Jt="PopoverContent",Du=f.forwardRef((e,t)=>{const r=sw(Jt,e.__scopePopover),{forceMount:n=r.forceMount,...s}=e,a=xt(Jt,e.__scopePopover);return o.jsx(xe,{present:n||a.open,children:a.modal?o.jsx(aw,{...s,ref:t}):o.jsx(iw,{...s,ref:t})})});Du.displayName=Jt;var ow=mt("PopoverContent.RemoveScroll"),aw=f.forwardRef((e,t)=>{const r=xt(Jt,e.__scopePopover),n=f.useRef(null),s=G(t,n),a=f.useRef(!1);return f.useEffect(()=>{const i=n.current;if(i)return Fn(i)},[]),o.jsx(Dr,{as:ow,allowPinchZoom:!0,children:o.jsx(Pu,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,i=>{i.preventDefault(),a.current||r.triggerRef.current?.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,i=>{const c=i.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0,u=c.button===2||l;a.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:P(e.onFocusOutside,i=>i.preventDefault(),{checkForDefaultPrevented:!1})})})}),iw=f.forwardRef((e,t)=>{const r=xt(Jt,e.__scopePopover),n=f.useRef(!1),s=f.useRef(!1);return o.jsx(Pu,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(n.current||r.triggerRef.current?.focus(),a.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(n.current=!0,a.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const i=a.target;r.triggerRef.current?.contains(i)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&s.current&&a.preventDefault()}})}),Pu=f.forwardRef((e,t)=>{const{__scopePopover:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:d,...m}=e,h=xt(Jt,r),x=Ur(r);return Mn(),o.jsx(_r,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:a,children:o.jsx(tr,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:d,onEscapeKeyDown:c,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>h.onOpenChange(!1),children:o.jsx(zn,{"data-state":Iu(h.open),role:"dialog",id:h.contentId,...x,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Ou="PopoverClose",cw=f.forwardRef((e,t)=>{const{__scopePopover:r,...n}=e,s=xt(Ou,r);return o.jsx(U.button,{type:"button",...n,ref:t,onClick:P(e.onClick,()=>s.onOpenChange(!1))})});cw.displayName=Ou;var lw="PopoverArrow",dw=f.forwardRef((e,t)=>{const{__scopePopover:r,...n}=e,s=Ur(r);return o.jsx(Wn,{...s,...n,ref:t})});dw.displayName=lw;function Iu(e){return e?"open":"closed"}var lk=Tu,dk=_u,uk=Au,fk=Du,cs="Tabs",[uw,mk]=Ne(cs,[cr]),Mu=cr(),[fw,Sa]=uw(cs),Lu=f.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,onValueChange:s,defaultValue:a,orientation:i="horizontal",dir:c,activationMode:l="automatic",...u}=e,d=ir(c),[m,h]=Qe({prop:n,onChange:s,defaultProp:a??"",caller:cs});return o.jsx(fw,{scope:r,baseId:Ie(),value:m,onValueChange:h,orientation:i,dir:d,activationMode:l,children:o.jsx(U.div,{dir:d,"data-orientation":i,...u,ref:t})})});Lu.displayName=cs;var Fu="TabsList",$u=f.forwardRef((e,t)=>{const{__scopeTabs:r,loop:n=!0,...s}=e,a=Sa(Fu,r),i=Mu(r);return o.jsx(ma,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:n,children:o.jsx(U.div,{role:"tablist","aria-orientation":a.orientation,...s,ref:t})})});$u.displayName=Fu;var Uu="TabsTrigger",Bu=f.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,disabled:s=!1,...a}=e,i=Sa(Uu,r),c=Mu(r),l=zu(i.baseId,n),u=Wu(i.baseId,n),d=n===i.value;return o.jsx(ha,{asChild:!0,...c,focusable:!s,active:d,children:o.jsx(U.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:l,...a,ref:t,onMouseDown:P(e.onMouseDown,m=>{!s&&m.button===0&&m.ctrlKey===!1?i.onValueChange(n):m.preventDefault()}),onKeyDown:P(e.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&i.onValueChange(n)}),onFocus:P(e.onFocus,()=>{const m=i.activationMode!=="manual";!d&&!s&&m&&i.onValueChange(n)})})})});Bu.displayName=Uu;var Hu="TabsContent",Vu=f.forwardRef((e,t)=>{const{__scopeTabs:r,value:n,forceMount:s,children:a,...i}=e,c=Sa(Hu,r),l=zu(c.baseId,n),u=Wu(c.baseId,n),d=n===c.value,m=f.useRef(d);return f.useEffect(()=>{const h=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(h)},[]),o.jsx(xe,{present:s||d,children:({present:h})=>o.jsx(U.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!h,id:u,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:h&&a})})});Vu.displayName=Hu;function zu(e,t){return`${e}-trigger-${t}`}function Wu(e,t){return`${e}-content-${t}`}var mw=Lu,Gu=$u,qu=Bu,Ku=Vu;const Yu=mw,ka=f.forwardRef(({className:e,...t},r)=>o.jsx(Gu,{ref:r,className:B("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));ka.displayName=Gu.displayName;const dt=f.forwardRef(({className:e,...t},r)=>o.jsx(qu,{ref:r,className:B("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));dt.displayName=qu.displayName;const ut=f.forwardRef(({className:e,...t},r)=>o.jsx(Ku,{ref:r,className:B("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));ut.displayName=Ku.displayName;const Gi={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"http://localhost:5000/api/v1",VITE_USER_NODE_ENV:"production",VITE_WEBSOCKET_URL:"http://localhost:5000"},Tr=new Map,Jr=e=>{const t=Tr.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([r,n])=>[r,n.getState()])):{}},hw=(e,t,r)=>{if(e===void 0)return{type:"untracked",connection:t.connect(r)};const n=Tr.get(r.name);if(n)return{type:"tracked",store:e,...n};const s={connection:t.connect(r),stores:{}};return Tr.set(r.name,s),{type:"tracked",store:e,...s}},pw=(e,t)=>{if(t===void 0)return;const r=Tr.get(e);r&&(delete r.stores[t],Object.keys(r.stores).length===0&&Tr.delete(e))},gw=e=>{var t,r;if(!e)return;const n=e.split(`
`),s=n.findIndex(i=>i.includes("api.setState"));if(s<0)return;const a=((t=n[s+1])==null?void 0:t.trim())||"";return(r=/.+ (.+) .+/.exec(a))==null?void 0:r[1]},xw=(e,t={})=>(r,n,s)=>{const{enabled:a,anonymousActionType:i,store:c,...l}=t;let u;try{u=(a??(Gi?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!u)return e(r,n,s);const{connection:d,...m}=hw(c,u,l);let h=!0;s.setState=(p,b,v)=>{const y=r(p,b);if(!h)return y;const w=v===void 0?{type:i||gw(new Error().stack)||"anonymous"}:typeof v=="string"?{type:v}:v;return c===void 0?(d?.send(w,n()),y):(d?.send({...w,type:`${c}/${w.type}`},{...Jr(l.name),[c]:s.getState()}),y)},s.devtools={cleanup:()=>{d&&typeof d.unsubscribe=="function"&&d.unsubscribe(),pw(l.name,c)}};const x=(...p)=>{const b=h;h=!1,r(...p),h=b},g=e(s.setState,n,s);if(m.type==="untracked"?d?.init(g):(m.stores[m.store]=s,d?.init(Object.fromEntries(Object.entries(m.stores).map(([p,b])=>[p,p===m.store?g:b.getState()])))),s.dispatchFromDevtools&&typeof s.dispatch=="function"){let p=!1;const b=s.dispatch;s.dispatch=(...v)=>{(Gi?"production":void 0)!=="production"&&v[0].type==="__setState"&&!p&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),p=!0),b(...v)}}return d.subscribe(p=>{var b;switch(p.type){case"ACTION":if(typeof p.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Vs(p.payload,v=>{if(v.type==="__setState"){if(c===void 0){x(v.state);return}Object.keys(v.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const y=v.state[c];if(y==null)return;JSON.stringify(s.getState())!==JSON.stringify(y)&&x(y);return}s.dispatchFromDevtools&&typeof s.dispatch=="function"&&s.dispatch(v)});case"DISPATCH":switch(p.payload.type){case"RESET":return x(g),c===void 0?d?.init(s.getState()):d?.init(Jr(l.name));case"COMMIT":if(c===void 0){d?.init(s.getState());return}return d?.init(Jr(l.name));case"ROLLBACK":return Vs(p.state,v=>{if(c===void 0){x(v),d?.init(s.getState());return}x(v[c]),d?.init(Jr(l.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Vs(p.state,v=>{if(c===void 0){x(v);return}JSON.stringify(s.getState())!==JSON.stringify(v[c])&&x(v[c])});case"IMPORT_STATE":{const{nextLiftedState:v}=p.payload,y=(b=v.computedStates.slice(-1)[0])==null?void 0:b.state;if(!y)return;x(c===void 0?y:y[c]),d?.send(null,v);return}case"PAUSE_RECORDING":return h=!h}return}}),g},bw=xw,Vs=(e,t)=>{let r;try{r=JSON.parse(e)}catch(n){console.error("[zustand devtools middleware] Could not parse the received json",n)}r!==void 0&&t(r)},vw={submitted:{title:"Submission Confirmed",description:"Grievance has been successfully submitted and acknowledged",icon:"CheckCircle",color:"bg-green-500",weight:1,estimatedDuration:5,requirements:["Valid submission data","User authentication"],nextPossibleStatuses:["pending","rejected","cancelled"]},pending:{title:"Initial Review",description:"Grievance is under initial review and verification",icon:"Send",color:"bg-yellow-500",weight:2,estimatedDuration:360,requirements:["Document verification","Category assignment"],nextPossibleStatuses:["desk_1","rejected","cancelled"]},desk_1:{title:"Initial Verification",description:"First level verification and preliminary assessment",icon:"Eye",color:"bg-blue-500",weight:3,estimatedDuration:720,requirements:["Evidence review","Initial assessment"],nextPossibleStatuses:["desk_2","rejected","cancelled"]},desk_2:{title:"Fact Checking",description:"Detailed fact-checking and evidence validation",icon:"Shield",color:"bg-blue-500",weight:4,estimatedDuration:1440,requirements:["Evidence validation","Stakeholder verification"],nextPossibleStatuses:["desk_3","rejected","cancelled"]},desk_3:{title:"Administrative Review",description:"Final administrative review and approval",icon:"UserCheck",color:"bg-blue-500",weight:5,estimatedDuration:720,requirements:["Administrative approval","Resource allocation"],nextPossibleStatuses:["officer","rejected","cancelled"]},officer:{title:"Officer Assignment",description:"Assignment to appropriate officer for investigation",icon:"Settings",color:"bg-purple-500",weight:6,estimatedDuration:480,requirements:["Officer availability","Jurisdiction verification"],nextPossibleStatuses:["in_progress","rejected","cancelled"]},in_progress:{title:"Investigation",description:"Active investigation and resolution in progress",icon:"Play",color:"bg-green-500",weight:7,estimatedDuration:2880,requirements:["Field investigation","Evidence collection"],nextPossibleStatuses:["resolved","rejected","cancelled"]},resolved:{title:"Resolved",description:"Grievance has been successfully resolved",icon:"CheckCircle",color:"bg-green-500",weight:8,estimatedDuration:0,requirements:["Resolution documentation","User notification"],nextPossibleStatuses:["closed","reopened"]},closed:{title:"Closed",description:"Grievance is permanently closed",icon:"Archive",color:"bg-gray-500",weight:9,estimatedDuration:0,requirements:["Final documentation","Archive process"],nextPossibleStatuses:["reopened"]},rejected:{title:"Rejected",description:"Grievance has been rejected",icon:"XCircle",color:"bg-red-500",weight:0,estimatedDuration:0,requirements:["Rejection reason","User notification"],nextPossibleStatuses:["reopened"]},cancelled:{title:"Cancelled",description:"Grievance has been cancelled",icon:"XCircle",color:"bg-red-500",weight:0,estimatedDuration:0,requirements:["Cancellation reason","User notification"],nextPossibleStatuses:["reopened"]},reopened:{title:"Reopened",description:"Grievance has been reopened for further review",icon:"RotateCcw",color:"bg-orange-500",weight:1,estimatedDuration:60,requirements:["Reopen justification","Status reset"],nextPossibleStatuses:["pending","rejected","cancelled"]}},yw={urgent:{totalHours:48,checkpoints:{submitted:{targetHours:.5,warningThreshold:.25,criticalThreshold:.4},pending:{targetHours:6,warningThreshold:4,criticalThreshold:5},desk_1:{targetHours:12,warningThreshold:8,criticalThreshold:10},desk_2:{targetHours:18,warningThreshold:14,criticalThreshold:16},desk_3:{targetHours:24,warningThreshold:20,criticalThreshold:22},officer:{targetHours:30,warningThreshold:26,criticalThreshold:28},in_progress:{targetHours:48,warningThreshold:40,criticalThreshold:44},resolved:{targetHours:48,warningThreshold:48,criticalThreshold:48},closed:{targetHours:48,warningThreshold:48,criticalThreshold:48},rejected:{targetHours:24,warningThreshold:20,criticalThreshold:22},cancelled:{targetHours:24,warningThreshold:20,criticalThreshold:22},reopened:{targetHours:2,warningThreshold:1,criticalThreshold:1.5}}},high:{totalHours:96,checkpoints:{submitted:{targetHours:1,warningThreshold:.5,criticalThreshold:.75},pending:{targetHours:12,warningThreshold:8,criticalThreshold:10},desk_1:{targetHours:24,warningThreshold:18,criticalThreshold:21},desk_2:{targetHours:36,warningThreshold:30,criticalThreshold:33},desk_3:{targetHours:48,warningThreshold:42,criticalThreshold:45},officer:{targetHours:60,warningThreshold:54,criticalThreshold:57},in_progress:{targetHours:96,warningThreshold:84,criticalThreshold:90},resolved:{targetHours:96,warningThreshold:96,criticalThreshold:96},closed:{targetHours:96,warningThreshold:96,criticalThreshold:96},rejected:{targetHours:48,warningThreshold:42,criticalThreshold:45},cancelled:{targetHours:48,warningThreshold:42,criticalThreshold:45},reopened:{targetHours:4,warningThreshold:2,criticalThreshold:3}}},medium:{totalHours:144,checkpoints:{submitted:{targetHours:2,warningThreshold:1,criticalThreshold:1.5},pending:{targetHours:18,warningThreshold:14,criticalThreshold:16},desk_1:{targetHours:36,warningThreshold:30,criticalThreshold:33},desk_2:{targetHours:54,warningThreshold:48,criticalThreshold:51},desk_3:{targetHours:72,warningThreshold:66,criticalThreshold:69},officer:{targetHours:90,warningThreshold:84,criticalThreshold:87},in_progress:{targetHours:144,warningThreshold:132,criticalThreshold:138},resolved:{targetHours:144,warningThreshold:144,criticalThreshold:144},closed:{targetHours:144,warningThreshold:144,criticalThreshold:144},rejected:{targetHours:72,warningThreshold:66,criticalThreshold:69},cancelled:{targetHours:72,warningThreshold:66,criticalThreshold:69},reopened:{targetHours:6,warningThreshold:4,criticalThreshold:5}}},low:{totalHours:192,checkpoints:{submitted:{targetHours:4,warningThreshold:2,criticalThreshold:3},pending:{targetHours:24,warningThreshold:20,criticalThreshold:22},desk_1:{targetHours:48,warningThreshold:42,criticalThreshold:45},desk_2:{targetHours:72,warningThreshold:66,criticalThreshold:69},desk_3:{targetHours:96,warningThreshold:90,criticalThreshold:93},officer:{targetHours:120,warningThreshold:114,criticalThreshold:117},in_progress:{targetHours:192,warningThreshold:180,criticalThreshold:186},resolved:{targetHours:192,warningThreshold:192,criticalThreshold:192},closed:{targetHours:192,warningThreshold:192,criticalThreshold:192},rejected:{targetHours:96,warningThreshold:90,criticalThreshold:93},cancelled:{targetHours:96,warningThreshold:90,criticalThreshold:93},reopened:{targetHours:8,warningThreshold:6,criticalThreshold:7}}}},zs=e=>vw[e],qi=e=>yw[e],ww=e=>["resolved","closed","rejected","cancelled"].includes(e);class Co{generateTimeline(t){console.log("🔄 TimelineService: Generating timeline for grievance:",t._id),console.log("📊 Input data:",{status:t.status,priority:t.priority,statusHistoryCount:t.statusHistory?.length||0,previousTimelinesCount:t.previousTimelines?.length||0,reopenCount:t.reopenCount||0});const r=this.generateTimelineEntries(t),n=this.calculateStatistics(r,t),s=ww(t.status),a=["resolved","closed","rejected","cancelled"].includes(t.status),i={id:`timeline-${t._id}-${t.reopenCount||0}`,entries:r,startDate:t.submittedAt,endDate:s?t.lastUpdatedAt:void 0,finalStatus:t.status,cycleNumber:(t.reopenCount||0)+1,isCompleted:s,isTerminal:a,duration:this.calculateDuration(t.submittedAt,t.lastUpdatedAt),statistics:n};return console.log("✅ TimelineService: Timeline generated:",{entriesCount:r.length,completedSteps:n.completedSteps,totalSteps:n.totalSteps,progress:`${(n.completedSteps/n.totalSteps*100).toFixed(1)}%`}),i}generateTimelineEntries(t){const r=[],n=t.statusHistory||[],s=t.status,a=["submitted","pending","desk_1","desk_2","desk_3","officer","in_progress","resolved","closed"];if(n.forEach((l,u)=>{const d=this.createTimelineEntry(l,u+1,t,u===n.length-1);r.push(d)}),n.length===0){const l={status:s,changedBy:"system",changedAt:t.submittedAt,reason:"Initial status"},u=this.createTimelineEntry(l,1,t,!0);r.push(u)}const i=n.length>0?n[n.length-1].status:s,c=a.indexOf(i);if(c>=0&&c<a.length-1)for(let l=c+1;l<a.length;l++){const u=a[l],d=this.createPendingTimelineEntry(u,r.length+1,t);r.push(d)}return r}createTimelineEntry(t,r,n,s){const a=zs(t.status),i=this.calculateSLAStatus(t.changedAt,n.priority);return{id:`entry-${n._id}-${r}`,status:t.status,timestamp:t.changedAt,reason:t.reason,notes:t.notes,triggeredBy:{id:t.userId||"system",name:this.getUserName(t.changedBy),role:this.getUserRole(t.changedBy)},metadata:{stepNumber:r,hasBeenReached:!0,isCurrentStep:s,duration:this.calculateStepDuration(t,n.statusHistory),slaStatus:i,checkpointInfo:a}}}createPendingTimelineEntry(t,r,n){const s=zs(t);return{id:`pending-${n._id}-${r}`,status:t,timestamp:"",triggeredBy:{id:"",name:"Pending",role:"user"},metadata:{stepNumber:r,hasBeenReached:!1,isCurrentStep:!1,slaStatus:"ON_TRACK",checkpointInfo:s}}}calculateStatistics(t,r){const n=t.filter(u=>u.metadata.hasBeenReached).length,s=t.length,a=t.filter(u=>u.metadata.hasBeenReached&&u.metadata.duration),i=a.reduce((u,d)=>u+(d.metadata.duration||0),0),c=a.length>0?i/a.length:0,l=this.calculateSLACompliance(r);return{totalSteps:s,completedSteps:n,averageStepDuration:c,totalDuration:i,slaCompliance:l}}calculateSLACompliance(t){try{const r=new Date(t.submittedAt),n=new Date,s=qi(t.priority);if(!s||!s.totalHours)return console.warn("⚠️ TimelineService: Invalid SLA config for priority:",t.priority),{status:"ON_TRACK",progress:0,timeRemaining:72*60*60*1e3,isOverdue:!1};const a=s.totalHours*60*60*1e3,i=n.getTime()-r.getTime(),c=a-i,l=c<0,u=Math.min(i/a*100,100);let d;return u>=100?d="BREACHED":u>=85?d="CRITICAL":u>=70?d="URGENT":d="ON_TRACK",{status:d,progress:u,timeRemaining:Math.max(c,0),isOverdue:l}}catch(r){return console.error("❌ TimelineService: Error calculating SLA compliance:",r),{status:"ON_TRACK",progress:0,timeRemaining:72*60*60*1e3,isOverdue:!1}}}calculateSLAStatus(t,r){try{const n=new Date(t),s=new Date,a=qi(r);if(!a||!a.totalHours)return console.warn("⚠️ TimelineService: Invalid SLA config for priority:",r),"ON_TRACK";const i=a.totalHours*60*60*1e3,l=(s.getTime()-n.getTime())/i*100;return l>=100?"BREACHED":l>=85?"CRITICAL":l>=70?"URGENT":"ON_TRACK"}catch(n){return console.error("❌ TimelineService: Error calculating SLA status:",n),"ON_TRACK"}}calculateDuration(t,r){const n=new Date(t),a=new Date(r).getTime()-n.getTime(),i=Math.floor(a/(1e3*60*60*24)),c=Math.floor(a%(1e3*60*60*24)/(1e3*60*60)),l=Math.floor(a%(1e3*60*60)/(1e3*60));return i>0?`${i}d ${c}h`:c>0?`${c}h ${l}m`:`${l}m`}calculateStepDuration(t,r){const n=r.findIndex(c=>c===t);if(n<=0)return;const s=r[n-1],a=new Date(t.changedAt).getTime(),i=new Date(s.changedAt).getTime();return Math.floor((a-i)/(1e3*60))}getUserName(t){return t==="system"?"System":t||"Unknown User"}getUserRole(t){return t==="system"?"admin":"user"}generatePreviousTimelines(t){const r=[];return t.previousTimelines&&t.previousTimelines.length>0&&t.previousTimelines.forEach((n,s)=>{const a={id:`prev-timeline-${t._id}-${s}`,cycleNumber:s+1,startDate:n.startDate||t.submittedAt,endDate:n.endDate||n.completedAt||"",finalStatus:n.finalStatus,entries:this.generateEntriesFromHistory(n.statusHistory||[]),completionReason:n.completionReason,reopenReason:n.reopenReason,statistics:this.calculateStatisticsFromHistory(n.statusHistory||[])};r.push(a)}),r}generateEntriesFromHistory(t){return t.map((r,n)=>({id:`hist-entry-${n}`,status:r.status,timestamp:r.changedAt,reason:r.reason,triggeredBy:{id:r.userId||"system",name:this.getUserName(r.changedBy),role:this.getUserRole(r.changedBy)},metadata:{stepNumber:n+1,hasBeenReached:!0,isCurrentStep:!1,slaStatus:"ON_TRACK",checkpointInfo:zs(r.status)}}))}calculateStatisticsFromHistory(t){return{totalSteps:t.length,completedSteps:t.length,averageStepDuration:0,totalDuration:0,slaCompliance:{status:"ON_TRACK",progress:100,timeRemaining:0,isOverdue:!1}}}}const Ki={currentTimeline:null,previousTimelines:[],isLoading:!1,error:null,lastUpdated:null},Rn=Uo()(bw((e,t)=>({...Ki,generateTimeline:r=>{console.log("🔄 TimelineStore: Generating timeline for grievance:",r._id);try{const s=new Co().generateTimeline(r);return e({currentTimeline:s,error:null,lastUpdated:new Date().toISOString()}),console.log("✅ TimelineStore: Timeline generated successfully"),s}catch(n){throw console.error("❌ TimelineStore: Error generating timeline:",n),e({error:n instanceof Error?n.message:"Failed to generate timeline",currentTimeline:null}),n}},updateTimelineEntry:(r,n)=>{const s=t();if(!s.currentTimeline)return;console.log("🔄 TimelineStore: Updating timeline entry:",r);const a=s.currentTimeline.entries.map(c=>c.id===r?{...c,...n}:c),i={...s.currentTimeline,entries:a};e({currentTimeline:i,lastUpdated:new Date().toISOString()}),console.log("✅ TimelineStore: Timeline entry updated")},addTimelineEntry:r=>{const n=t();if(!n.currentTimeline)return;console.log("🔄 TimelineStore: Adding timeline entry:",r.status);const s={...r,id:`entry-${Date.now()}-${Math.random().toString(36).substr(2,9)}`},a=[...n.currentTimeline.entries,s],i={...n.currentTimeline,entries:a};e({currentTimeline:i,lastUpdated:new Date().toISOString()}),console.log("✅ TimelineStore: Timeline entry added")},moveToNextStatus:async r=>{if(!t().currentTimeline)return console.error("❌ TimelineStore: No current timeline available"),!1;console.log("🔄 TimelineStore: Moving to status:",r),e({isLoading:!0,error:null});try{return console.log("✅ TimelineStore: Status change initiated"),!0}catch(s){return console.error("❌ TimelineStore: Error changing status:",s),e({error:s instanceof Error?s.message:"Failed to change status",isLoading:!1}),!1}finally{e({isLoading:!1})}},refreshTimeline:async r=>{console.log("🔄 TimelineStore: Refreshing timeline for grievance:",r),e({isLoading:!0,error:null});try{const n=await Q.get(`/grievances/${r}?_t=${Date.now()}`);if(n.data.success){const s=n.data.data;console.log("📦 TimelineStore: Fresh grievance data received");const i=new Co().generateTimeline(s);e({currentTimeline:i,isLoading:!1,error:null,lastUpdated:new Date().toISOString()}),console.log("✅ TimelineStore: Timeline refreshed successfully")}else throw new Error("Failed to fetch grievance data")}catch(n){console.error("❌ TimelineStore: Error refreshing timeline:",n),e({error:n instanceof Error?n.message:"Failed to refresh timeline",isLoading:!1})}},clearTimeline:()=>{console.log("🔄 TimelineStore: Clearing timeline data"),e({...Ki,lastUpdated:new Date().toISOString()})},subscribe:r=>Rn.subscribe(r),getState:()=>t(),setState:r=>{e(r)}}),{name:"timeline-store",enabled:!1})),Xu=Object.freeze(Object.defineProperty({__proto__:null,timelineStore:Rn,useTimelineStore:Rn},Symbol.toStringTag,{value:"Module"})),Ye=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"N/A",Nw=e=>e?typeof e=="string"?e:e.fullName||e.gmail||e._id||"Unknown User":"Unknown User",Sw=e=>{switch(e){case"infrastructure":return"bg-stone-50 text-stone-700 border-stone-200 dark:bg-stone-950 dark:text-stone-300 dark:border-stone-800";case"utilities":return"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800";case"transportation":return"bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800";case"healthcare":return"bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800";case"education":return"bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800";case"environment":return"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800";case"safety":return"bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:border-orange-800";case"corruption":return"bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800";default:return"bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800"}},kw=e=>{const t=["bg-violet-50 text-violet-700 border-violet-200 dark:bg-violet-950 dark:text-violet-300 dark:border-violet-800","bg-rose-50 text-rose-700 border-rose-200 dark:bg-rose-950 dark:text-rose-300 dark:border-rose-800","bg-cyan-50 text-cyan-700 border-cyan-200 dark:bg-cyan-950 dark:text-cyan-300 dark:border-cyan-800","bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800","bg-lime-50 text-lime-700 border-lime-200 dark:bg-lime-950 dark:text-lime-300 dark:border-lime-800","bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300 dark:border-pink-800","bg-teal-50 text-teal-700 border-teal-200 dark:bg-teal-950 dark:text-teal-300 dark:border-teal-800","bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800"];return t[e%t.length]},ja=e=>{const t={urgent:{icon:"🚨",label:"Urgent",color:"bg-red-100 text-red-800 border-red-300 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700",description:"Requires immediate attention - 24-48 hours"},high:{icon:"⚡",label:"High Priority",color:"bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-700",description:"Important issue - 3-5 days resolution"},medium:{icon:"📋",label:"Medium Priority",color:"bg-blue-100 text-blue-800 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-700",description:"Standard processing - 5-7 days"},low:{icon:"📝",label:"Low Priority",color:"bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-700",description:"Non-urgent matter - 7-10 days"}};return t[e]||t.medium},Qr=e=>e?typeof e=="string"?e:typeof e=="object"?[e.address,e.city,e.state,e.pincode].filter(Boolean).join(", "):"":"",jw={high:{totalDays:4,totalHours:96,checkpoints:{submitted:{days:.25,hours:6,description:"Confirm submission received",icon:"✅",title:"Submission Confirmed",weight:2},pending:{days:.5,hours:12,description:"Initial review and verification",icon:"📝",title:"Initial Review",weight:5},desk_1:{days:.5,hours:12,description:"Initial verification",icon:"🔍",title:"Initial Verification",weight:10},desk_2:{days:.5,hours:12,description:"Fact checking",icon:"📋",title:"Fact Checking",weight:15},desk_3:{days:.5,hours:12,description:"Admin review",icon:"👤",title:"Administrative Review",weight:20},officer:{days:.5,hours:12,description:"Officer assignment",icon:"🛡️",title:"Officer Assignment",weight:25},in_progress:{days:1.5,hours:36,description:"Field investigation (priority)",icon:"🔧",title:"Field Visit & Investigation",weight:70},resolved:{days:.25,hours:6,description:"Resolution review",icon:"🎯",title:"Resolution",weight:90},closed:{days:.25,hours:6,description:"Case closure",icon:"🔒",title:"Case Closed",weight:100}}},medium:{totalDays:6,totalHours:144,checkpoints:{submitted:{days:.25,hours:6,description:"Confirm submission received",icon:"✅",title:"Submission Confirmed",weight:2},pending:{days:.5,hours:12,description:"Initial review and verification",icon:"📝",title:"Initial Review",weight:5},desk_1:{days:.75,hours:18,description:"Initial verification",icon:"🔍",title:"Initial Verification",weight:10},desk_2:{days:.75,hours:18,description:"Fact checking",icon:"📋",title:"Fact Checking",weight:15},desk_3:{days:.75,hours:18,description:"Admin review",icon:"👤",title:"Administrative Review",weight:20},officer:{days:.5,hours:12,description:"Officer assignment",icon:"🛡️",title:"Officer Assignment",weight:25},in_progress:{days:2.25,hours:54,description:"Field investigation",icon:"🔧",title:"Field Visit & Investigation",weight:70},resolved:{days:.25,hours:6,description:"Resolution review",icon:"🎯",title:"Resolution",weight:90},closed:{days:.25,hours:6,description:"Case closure",icon:"🔒",title:"Case Closed",weight:100}}},low:{totalDays:8,totalHours:192,checkpoints:{submitted:{days:.5,hours:12,description:"Confirm submission received",icon:"✅",title:"Submission Confirmed",weight:2},pending:{days:.75,hours:18,description:"Initial review and verification",icon:"📝",title:"Initial Review",weight:5},desk_1:{days:1,hours:24,description:"Initial verification",icon:"🔍",title:"Initial Verification",weight:10},desk_2:{days:1,hours:24,description:"Fact checking",icon:"📋",title:"Fact Checking",weight:15},desk_3:{days:1,hours:24,description:"Admin review",icon:"👤",title:"Administrative Review",weight:20},officer:{days:.75,hours:18,description:"Officer assignment",icon:"🛡️",title:"Officer Assignment",weight:25},in_progress:{days:3,hours:72,description:"Field investigation",icon:"🔧",title:"Field Visit & Investigation",weight:70},resolved:{days:.25,hours:6,description:"Resolution review",icon:"🎯",title:"Resolution",weight:90},closed:{days:.25,hours:6,description:"Case closure",icon:"🔒",title:"Case Closed",weight:100}}},lowest:{totalDays:10,totalHours:240,checkpoints:{submitted:{days:.5,hours:12,description:"Confirm submission received",icon:"✅",title:"Submission Confirmed",weight:2},pending:{days:1,hours:24,description:"Initial review and verification",icon:"📝",title:"Initial Review",weight:5},desk_1:{days:1.25,hours:30,description:"Initial verification",icon:"🔍",title:"Initial Verification",weight:10},desk_2:{days:1.25,hours:30,description:"Fact checking",icon:"📋",title:"Fact Checking",weight:15},desk_3:{days:1.25,hours:30,description:"Admin review",icon:"👤",title:"Administrative Review",weight:20},officer:{days:1,hours:24,description:"Officer assignment",icon:"🛡️",title:"Officer Assignment",weight:25},in_progress:{days:3.75,hours:90,description:"Field investigation",icon:"🔧",title:"Field Visit & Investigation",weight:70},resolved:{days:.25,hours:6,description:"Resolution review",icon:"🎯",title:"Resolution",weight:90},closed:{days:.25,hours:6,description:"Case closure",icon:"🔒",title:"Case Closed",weight:100}}}},Cw=e=>{if(e<1)return`${Math.round(e*60)}m`;if(e<24)return`${Math.round(e)}h`;{const t=Math.floor(e/24),r=Math.round(e%24);return r>0?`${t}d ${r}h`:`${t}d`}},Tw=(e,t)=>jw[t].checkpoints[e],Ew=e=>["high","medium","low","lowest"].includes(e),Rw=()=>"medium",Ca=(e,t="medium")=>{const r=Ew(t)?t:Rw(),n=Tw(e,r);return n?{title:n.title,description:n.description,secondary:_w(e),icon:n.icon,color:"blue",step:n.weight/10,estTime:Cw(n.hours),hours:n.hours,days:n.days,priority:r,weight:n.weight,category:Aw(e),isTerminal:["resolved","closed","rejected","cancelled"].includes(e),isActive:["pending","desk_1","desk_2","desk_3","officer","in_progress"].includes(e)}:{rejected:{title:"Application Rejected",secondary:"Grievance rejected after review",icon:"❌",color:"red",step:0,estTime:"Final",hours:0,days:0,weight:0},cancelled:{title:"Case Cancelled",secondary:"Grievance cancelled by request",icon:"🚫",color:"gray",step:0,estTime:"Final",hours:0,days:0,weight:0},reopened:{title:"Case Reopened",secondary:"Grievance reopened for further investigation",icon:"🔄",color:"orange",step:2,estTime:"Variable",hours:0,days:0,weight:0}}[e]||{title:"📋 Status Update",secondary:"Grievance status updated",icon:"❓",color:"gray",step:0,estTime:"N/A",hours:0,days:0,weight:0}},_w=e=>{const t={pending:"Grievance has been submitted and is awaiting initial review by the administrative team.",desk_1:"Initial verification of grievance details, documentation, and eligibility is in progress.",desk_2:"Grievance is being routed to the appropriate department based on category and jurisdiction.",desk_3:"Technical review and analysis by subject matter experts is underway.",officer:"A dedicated officer has been assigned and is actively working on your case.",in_progress:"Investigation and resolution efforts are actively being pursued.",resolved:"Your grievance has been successfully resolved and appropriate actions have been taken.",closed:"The grievance case has been officially closed and archived.",rejected:"After careful review, the grievance has been rejected due to specific criteria not being met.",cancelled:"The grievance has been cancelled, either by request or due to procedural reasons.",reopened:"The grievance has been reopened for additional review or action."};return t[e]||t.pending},Aw=e=>({pending:"submission",desk_1:"verification",desk_2:"routing",desk_3:"analysis",officer:"assignment",in_progress:"investigation",resolved:"completion",closed:"closure",rejected:"rejection",cancelled:"cancellation",reopened:"reopening"})[e]||"unknown",Dw=({grievance:e,onClose:t,showActions:r=!0,compact:n=!1})=>{const[s,a]=f.useState(!1);if(!e)return null;const i=ja(e.priority||"medium"),c=Ca(e.status,e.priority||"medium");return n?o.jsx(ae,{className:"relative border-b rounded-none bg-background/50 dark:bg-[#0D0D0D]/50 shadow-sm",children:o.jsxs(de,{className:"p-4 bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("div",{className:"flex items-center gap-3 min-w-0 flex-1",children:o.jsxs("div",{className:"min-w-0 flex-1",children:[o.jsx("h1",{className:"text-lg font-semibold truncate",children:e.title}),o.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[o.jsxs(q,{className:`${c.color} text-xs flex items-center gap-1`,children:[o.jsx(ot,{className:"h-3 w-3"}),c.title]}),o.jsxs(q,{className:`${i.color} text-xs flex items-center gap-1`,children:[o.jsx(br,{className:"h-3 w-3"}),i.label]}),o.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o.jsx(qs,{className:"h-3 w-3"}),o.jsxs("span",{children:["#",e.referenceId||e._id.slice(-8)]})]})]})]})}),o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(X,{variant:"ghost",size:"sm",onClick:()=>a(!s),className:"flex items-center gap-1",children:s?o.jsx(gn,{className:"h-4 w-4"}):o.jsx(Ct,{className:"h-4 w-4"})}),t&&o.jsx(X,{variant:"ghost",size:"sm",onClick:t,children:o.jsx(Wt,{className:"h-4 w-4"})})]})]}),s&&o.jsxs("div",{className:"mt-4 pt-3 border-t border-border/30 space-y-3 animate-in slide-in-from-top-2 duration-200",children:[o.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o.jsx(Gt,{className:"h-3 w-3"}),o.jsxs("span",{children:["Submitted: ",Ye(e.submittedAt)]})]}),e.location&&Qr(e.location)&&o.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[o.jsx(xn,{className:"h-3 w-3"}),o.jsx("span",{children:Qr(e.location)})]}),e.lastUpdatedAt&&e.lastUpdatedAt!==e.submittedAt&&o.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[o.jsx(ie,{className:"h-3 w-3"}),o.jsxs("span",{children:["Last updated: ",Ye(e.lastUpdatedAt)]})]})]})]})}):o.jsxs(ae,{className:"relative border-b rounded-none bg-background/50 dark:bg-[#0D0D0D]/50 shadow-sm",children:[o.jsxs(de,{className:"p-6 bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsxs("div",{className:"flex items-start justify-between gap-4",children:[o.jsx("div",{className:"min-w-0 flex-1",children:o.jsx("div",{className:"flex items-start gap-3",children:o.jsxs("div",{className:"min-w-0 flex-1",children:[o.jsx("h1",{className:"text-2xl font-bold text-foreground mb-3",children:e.title}),o.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[o.jsx("span",{children:"Grievance ID:"}),o.jsxs(q,{variant:"outline",className:"font-mono font-semibold",children:["#",e.referenceId||e._id]})]})]})})}),o.jsxs(X,{variant:"ghost",size:"sm",onClick:()=>a(!s),className:"flex items-center gap-2 text-muted-foreground hover:text-foreground",title:s?"Hide details":"Show details",children:[o.jsxs("span",{className:"text-sm",children:[s?"Hide":"Show"," Details"]}),s?o.jsx(gn,{className:"h-4 w-4"}):o.jsx(Ct,{className:"h-4 w-4"})]})]}),o.jsxs("div",{className:"flex items-center gap-3 mt-4",children:[o.jsxs(q,{className:`${c.color} flex items-center gap-1`,children:[o.jsx(ot,{className:"h-3 w-3"}),c.title]}),o.jsxs(q,{className:`${i.color} flex items-center gap-1`,children:[o.jsx(br,{className:"h-3 w-3"}),i.label]}),o.jsxs("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[o.jsx(Gt,{className:"h-4 w-4"}),o.jsxs("span",{children:["Submitted: ",Ye(e.submittedAt)]})]})]}),s&&o.jsxs("div",{className:"mt-6 pt-4 border-t border-border/30 space-y-4 animate-in slide-in-from-top-2 duration-200",children:[r&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsxs(zi,{children:[o.jsx(Wi,{asChild:!0,children:o.jsx(X,{variant:"ghost",size:"sm",title:"Share",children:o.jsx(bm,{className:"h-4 w-4"})})}),o.jsxs(jo,{align:"end",children:[o.jsxs($t,{onClick:()=>navigator.clipboard.writeText(window.location.href),children:[o.jsx(vm,{className:"h-4 w-4 mr-2"}),"Copy Link"]}),o.jsxs($t,{onClick:()=>{const l=`Grievance: ${e.title}`,u=`Please review this grievance: ${window.location.href}`;window.open(`mailto:?subject=${encodeURIComponent(l)}&body=${encodeURIComponent(u)}`)},children:[o.jsx(Ks,{className:"h-4 w-4 mr-2"}),"Share via Email"]})]})]}),o.jsx(X,{variant:"ghost",size:"sm",title:"Bookmark",onClick:()=>{"addToHomescreen"in window&&console.log("Adding to bookmarks...")},children:o.jsx(ym,{className:"h-4 w-4"})}),o.jsx(X,{variant:"ghost",size:"sm",title:"Open in new tab",onClick:()=>window.open(window.location.href,"_blank"),children:o.jsx(vr,{className:"h-4 w-4"})}),o.jsxs(zi,{children:[o.jsx(Wi,{asChild:!0,children:o.jsx(X,{variant:"ghost",size:"sm",title:"More actions",children:o.jsx(wm,{className:"h-4 w-4"})})}),o.jsxs(jo,{align:"end",children:[o.jsxs($t,{onClick:()=>{const l=document.createElement("div");l.innerHTML=`
                          <h1>${e.title}</h1>
                          <p><strong>ID:</strong> ${e.referenceId||e._id}</p>
                          <p><strong>Status:</strong> ${e.status}</p>
                          <p><strong>Priority:</strong> ${e.priority}</p>
                          <p><strong>Description:</strong> ${e.description}</p>
                        `;const u=window.open("","_blank");u?.document.write(l.innerHTML),u?.print()},children:[o.jsx(Ys,{className:"h-4 w-4 mr-2"}),"Print Details"]}),o.jsxs($t,{onClick:()=>{const l={title:e.title,id:e.referenceId||e._id,status:e.status,priority:e.priority,description:e.description},u=new Blob([JSON.stringify(l,null,2)],{type:"application/json"}),d=URL.createObjectURL(u),m=document.createElement("a");m.href=d,m.download=`grievance-${e.referenceId||e._id}.json`,m.click(),URL.revokeObjectURL(d)},children:[o.jsx(Er,{className:"h-4 w-4 mr-2"}),"Export Data"]}),o.jsx(yu,{}),o.jsxs($t,{onClick:()=>{console.log("Flagging grievance for review...")},children:[o.jsx(Nm,{className:"h-4 w-4 mr-2"}),"Flag for Review"]})]})]})]}),e.assignedTo&&o.jsx("div",{className:"mt-4 pt-4 border-t",children:o.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[o.jsx(_e,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-muted-foreground",children:"Assigned to:"}),"net star",o.jsx("span",{className:"font-medium",children:Nw(e.assignedTo)})]})}),e.location&&Qr(e.location)&&o.jsx("div",{className:"mt-2",children:o.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[o.jsx(xn,{className:"h-4 w-4"}),o.jsx("span",{children:Qr(e.location)})]})}),e.tags&&e.tags.length>0&&o.jsx("div",{className:"mt-2",children:o.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[o.jsx(qs,{className:"h-4 w-4 text-muted-foreground"}),e.tags.map((l,u)=>o.jsx(q,{variant:"secondary",className:"text-xs",children:l},u))]})}),e.lastUpdatedAt&&e.lastUpdatedAt!==e.submittedAt&&o.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[o.jsx(ie,{className:"h-3 w-3"}),o.jsxs("span",{children:["Last updated: ",Ye(e.lastUpdatedAt)]})]})]})]}),t&&o.jsx("div",{className:"absolute top-4 right-4",children:o.jsx(X,{variant:"ghost",size:"sm",onClick:t,children:o.jsx(Wt,{className:"h-4 w-4"})})})]})},Pw=({grievance:e})=>{const[t,r]=f.useState(!1),[n,s]=f.useState(new Date);if(!e)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Xs,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground mb-4",children:"No grievance data available"}),o.jsxs(X,{variant:"outline",onClick:()=>window.location.reload(),className:"flex items-center gap-2",children:[o.jsx(Nt,{className:"h-4 w-4"}),"Refresh Page"]})]})});const a=async()=>{r(!0);try{await new Promise(N=>setTimeout(N,1e3)),s(new Date)}catch(N){console.error("Failed to refresh:",N)}finally{r(!1)}},i=e.priority||"medium",c=new Date(e.submittedAt),l=new Date(e.lastUpdatedAt||e.submittedAt),u=new Date;f.useEffect(()=>{s(new Date)},[e.lastUpdatedAt,e.status]),f.useEffect(()=>{const N=setInterval(()=>{s(new Date)},6e4);return()=>clearInterval(N)},[]);const d=()=>{const N=u.getTime()-l.getTime(),_=Math.floor(N/(1e3*60*60)),I=Math.floor(_/24),H=Math.floor(N%(1e3*60*60)/(1e3*60));return I>0?`${I} day${I>1?"s":""} ago`:_>0?`${_} hour${_>1?"s":""} ago`:H>0?`${H} minute${H>1?"s":""} ago`:"Just now"},m={high:{totalDays:4,totalHours:96},medium:{totalDays:6,totalHours:144},low:{totalDays:8,totalHours:192},urgent:{totalDays:2,totalHours:48}},h=m[i]||m.medium,x=new Date(c.getTime()+h.totalHours*60*60*1e3),g=e.resolvedAt?new Date(e.resolvedAt):null,p=x.getTime()-u.getTime(),b=p<0,v=Math.ceil(Math.abs(p)/(1e3*60*60*24)),y=Ca(e.status),w=ja(e.priority),S=(N,_)=>{if(N==="priority"){const I={urgent:"bg-red-600",high:"bg-orange-600",medium:"bg-blue-600",low:"bg-gray-600"};return I[_]||I.medium}else{const I={pending:"bg-yellow-600",desk_1:"bg-blue-600",desk_2:"bg-indigo-600",desk_3:"bg-purple-600",officer:"bg-cyan-600",in_progress:"bg-orange-600",resolved:"bg-green-600",closed:"bg-gray-600",reopened:"bg-amber-600",rejected:"bg-red-600",cancelled:"bg-slate-600"};return I[_]||I.pending}},j=e,D=j.userId||{},E=j.contactInfo||{},T=j.assignedTo||null;return o.jsx("div",{className:"space-y-6 p-6 bg-background/50 dark:bg-[#0D0D0D]/50 min-h-full",children:o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[o.jsxs(ae,{className:"bg-gradient-to-br from-background/50 to-background/30 dark:from-[#0D0D0D]/50 dark:to-[#0D0D0D]/30 border-border/30 shadow-lg",children:[o.jsx(ge,{className:"pb-4",children:o.jsxs(ye,{className:"flex items-center gap-3 text-lg",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center",children:o.jsx(Ue,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"})}),o.jsx("span",{className:"text-foreground font-semibold",children:"Basic Information"})]})}),o.jsxs(de,{className:"space-y-5",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Reference ID"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"font-mono text-lg font-semibold text-primary",children:e.referenceId||e._id})})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Title"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-base font-medium leading-relaxed",children:e.title})})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Description"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border max-h-32 overflow-y-auto custom-scrollbar",children:o.jsx("p",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:e.description})})]}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Category"}),o.jsx("div",{children:o.jsx(q,{className:`${Sw(e.category)} border font-medium px-3 py-1`,children:e.category.charAt(0).toUpperCase()+e.category.slice(1)})})]}),e.subCategory&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Sub-Category"}),o.jsx("div",{children:o.jsx(q,{variant:"outline",className:"border font-medium px-3 py-1 capitalize",children:e.subCategory})})]})]})]})]}),o.jsxs(ae,{className:"bg-gradient-to-br from-background/50 to-background/30 dark:from-[#0D0D0D]/50 dark:to-[#0D0D0D]/30 border-border/30 shadow-lg",children:[o.jsx(ge,{className:"pb-4",children:o.jsxs(ye,{className:"flex items-center gap-3 text-lg",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center",children:o.jsx(ot,{className:"h-4 w-4 text-green-600 dark:text-green-400"})}),o.jsx("span",{className:"text-foreground font-semibold",children:"Status & Priority"})]})}),o.jsxs(de,{className:"space-y-5",children:[o.jsxs("div",{className:"bg-gradient-to-r from-blue-50/50 to-indigo-50/30 dark:from-blue-950/20 dark:to-indigo-950/10 rounded-lg p-4 border border-blue-200/30 dark:border-blue-800/30",children:[o.jsxs("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-300 mb-3 flex items-center gap-2",children:[o.jsx(ie,{className:"h-4 w-4"}),"SLA Timeline Information"]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-1",children:[o.jsx(ne,{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"Submitted"}),o.jsx("div",{className:"text-sm font-semibold text-blue-900 dark:text-blue-100",children:c.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx(ne,{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"Last Updated"}),o.jsxs("div",{className:"text-sm font-semibold text-blue-900 dark:text-blue-100",children:[l.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),o.jsx("div",{className:"text-xs mt-1 text-blue-600 dark:text-blue-400",children:d()})]})]}),o.jsxs("div",{className:"space-y-1",children:[o.jsx(ne,{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"Expected Resolution"}),o.jsxs("div",{className:`text-sm font-semibold ${b?"text-red-600 dark:text-red-400":"text-green-600 dark:text-green-400"}`,children:[x.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),o.jsx("div",{className:"text-xs mt-1",children:b?o.jsxs("span",{className:"text-red-600 dark:text-red-400",children:["⚠️ ",v," days overdue"]}):o.jsxs("span",{className:"text-green-600 dark:text-green-400",children:["✅ ",v," days remaining"]})})]})]}),g&&o.jsxs("div",{className:"space-y-1",children:[o.jsx(ne,{className:"text-xs font-medium text-green-700 dark:text-green-300",children:"Resolved"}),o.jsxs("div",{className:"text-sm font-semibold text-green-900 dark:text-green-100",children:[g.toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),o.jsx("div",{className:"text-xs mt-1",children:g<=x?o.jsx("span",{className:"text-green-600 dark:text-green-400",children:"✅ Within SLA"}):o.jsx("span",{className:"text-orange-600 dark:text-orange-400",children:"⚠️ SLA Exceeded"})})]})]})]}),o.jsx("div",{className:"mt-4 pt-3 border-t border-blue-200/50 dark:border-blue-800/50",children:o.jsxs("div",{className:"flex items-center justify-between text-xs",children:[o.jsxs("span",{className:"text-blue-700 dark:text-blue-300",children:["SLA Target: ",h.totalDays," days (",h.totalHours," hours) for ",i.toUpperCase()," priority"]}),o.jsx(q,{className:`${b?"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300":"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"}`,children:b?"SLA BREACHED":"ON TRACK"})]})})]}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Current Status"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border h-[120px] flex items-center",children:o.jsxs("div",{className:"flex items-center gap-3 w-full",children:[o.jsx("div",{className:`w-10 h-10 rounded-full ${S("status",e.status)} flex items-center justify-center text-white text-lg shadow-lg flex-shrink-0`,children:y.icon}),o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsx(q,{className:`${y.color} border font-medium px-3 py-2 text-sm mb-1`,children:y.title}),o.jsx("p",{className:"text-xs text-muted-foreground leading-relaxed line-clamp-2",children:y.description}),o.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[o.jsxs("span",{className:"text-xs font-medium text-primary",children:["Step ",y.step||y.weight||1,"/8"]}),o.jsxs("span",{className:"text-xs text-muted-foreground",children:["• Est: ",y.estTime||`${y.hours||24}h`]})]})]})]})})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Priority Level"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border h-[120px] flex items-center",children:o.jsxs("div",{className:"flex items-center gap-3 w-full",children:[o.jsx("div",{className:`w-10 h-10 rounded-full ${S("priority",e.priority)} flex items-center justify-center text-white text-lg shadow-lg flex-shrink-0`,children:w.icon}),o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsx(q,{className:`${w.color} border font-medium px-3 py-2 text-sm mb-1`,children:w.label}),o.jsx("p",{className:"text-xs text-muted-foreground leading-relaxed line-clamp-2",children:w.description})]})]})})]})]})]})]}),o.jsxs(ae,{className:"bg-gradient-to-br from-background/50 to-background/30 dark:from-[#0D0D0D]/50 dark:to-[#0D0D0D]/30 border-border/30 shadow-lg",children:[o.jsx(ge,{className:"pb-4",children:o.jsxs(ye,{className:"flex items-center gap-3 text-lg",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center",children:o.jsx(xn,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"})}),o.jsx("span",{className:"text-foreground font-semibold",children:"Location Details"})]})}),o.jsxs(de,{className:"space-y-4",children:[e.location?.address&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Full Address"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-sm leading-relaxed",children:e.location.address})})]}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.location?.city&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"City"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-sm font-medium",children:e.location.city})})]}),e.location?.state&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"State"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-sm font-medium",children:e.location.state})})]})]}),e.location?.pincode&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Pincode"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-sm font-mono font-semibold",children:e.location.pincode})})]}),e.location?.coordinates&&o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"GPS Coordinates"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("p",{className:"text-sm font-mono",children:(()=>{const N=e.location.coordinates;return N&&typeof N.latitude=="number"&&typeof N.longitude=="number"?`${N.latitude.toFixed(6)}, ${N.longitude.toFixed(6)}`:"Invalid coordinates format"})()})})]}),o.jsxs(X,{variant:"outline",size:"sm",className:"w-full bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 hover:bg-muted",onClick:()=>{const N=e.location?.coordinates;!N||typeof N.latitude!="number"||typeof N.longitude!="number"||window.open(`https://maps.google.com/?q=${N.latitude},${N.longitude}`,"_blank")},children:[o.jsx(xn,{className:"h-4 w-4 mr-2"}),"View on Google Maps"]})]})]})]}),o.jsxs(ae,{className:"bg-gradient-to-br from-background/50 to-background/30 dark:from-[#0D0D0D]/50 dark:to-[#0D0D0D]/30 border-border/30 shadow-lg",children:[o.jsx(ge,{className:"pb-4",children:o.jsxs(ye,{className:"flex items-center gap-3 text-lg",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900 flex items-center justify-center",children:o.jsx(_e,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"})}),o.jsx("span",{className:"text-foreground font-semibold",children:"Contact Information"})]})}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"space-y-3",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Submitted By"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border",children:o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(_e,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm font-medium",children:D.fullName||"Anonymous User"})]}),D.gmail&&!e.isAnonymous&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Ks,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm text-muted-foreground",children:D.gmail})]})]}),o.jsxs("div",{className:"space-y-2",children:[E.phone&&!e.isAnonymous&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Sm,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm text-muted-foreground",children:E.phone})]}),E.preferredContact&&o.jsxs("div",{className:"text-xs text-muted-foreground",children:["Preferred: ",E.preferredContact]})]})]})})]}),T&&o.jsxs("div",{className:"space-y-3",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Assigned Officer"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-4 border",children:o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(_e,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm font-medium",children:T.fullName||"Not assigned"})]}),T.gmail&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Ks,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm text-muted-foreground",children:T.gmail})]})]}),o.jsxs("div",{className:"space-y-2",children:[T.role&&o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(qt,{className:"h-4 w-4 text-muted-foreground"}),o.jsx(q,{variant:"outline",className:"text-xs capitalize",children:T.role})]}),j.assignedAt&&o.jsxs("div",{className:"text-xs text-muted-foreground",children:["Assigned: ",Ye(j.assignedAt)]})]})]})})]}),(j.department||T?.department)&&o.jsxs("div",{className:"space-y-3",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Department"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(km,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm font-medium",children:j.department||T?.department||"Not specified"})]})})]})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-sm",children:[o.jsx(ge,{className:"pb-3",children:o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(qs,{className:"h-5 w-5 text-primary"}),"Additional Information"]})}),o.jsxs(de,{className:"space-y-5",children:[e.tags&&e.tags.length>0&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Tags"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map((N,_)=>o.jsxs(q,{className:`${kw(_)} border font-medium px-3 py-1 text-xs`,children:["#",N]},_))})})]}),o.jsxs("div",{className:"space-y-3",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Privacy Settings"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border",children:o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:`p-2 rounded-full ${e.isPublic?"bg-green-100 dark:bg-green-900":"bg-orange-100 dark:bg-orange-900"}`,children:o.jsx(jm,{className:`h-4 w-4 ${e.isPublic?"text-green-600 dark:text-green-400":"text-orange-600 dark:text-orange-400"}`})}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:e.isPublic?"Public":"Private"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:e.isPublic?"Visible to all":"Restricted access"})]})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:`p-2 rounded-full ${e.isAnonymous?"bg-blue-100 dark:bg-blue-900":"bg-purple-100 dark:bg-purple-900"}`,children:o.jsx(qt,{className:`h-4 w-4 ${e.isAnonymous?"text-blue-600 dark:text-blue-400":"text-purple-600 dark:text-purple-400"}`})}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:e.isAnonymous?"Anonymous":"Identified"}),o.jsx("p",{className:"text-xs text-muted-foreground",children:e.isAnonymous?"Identity hidden":"User identified"})]})]})]})})]}),e.feedback&&o.jsxs("div",{children:[o.jsx(ne,{className:"text-sm font-medium text-muted-foreground",children:"User Feedback"}),o.jsxs("div",{className:"mt-1",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"flex",children:[1,2,3,4,5].map(N=>o.jsx(Cm,{className:`h-4 w-4 ${N<=(e.feedback?.rating||0)?"text-yellow-400 fill-current":"text-gray-300"}`},N))}),o.jsxs("span",{className:"text-sm text-muted-foreground",children:["(",e.feedback.rating,"/5)"]})]}),e.feedback.comment&&o.jsxs("p",{className:"text-sm text-muted-foreground mt-1",children:['"',e.feedback.comment,'"']})]})]})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-sm",children:[o.jsx(ge,{className:"pb-3",children:o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(ie,{className:"h-5 w-5 text-primary"}),"Update History",o.jsx(X,{variant:"ghost",size:"sm",onClick:a,disabled:t,className:"ml-auto",children:o.jsx(Nt,{className:`h-4 w-4 ${t?"animate-spin":""}`})})]})}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Last Updated"}),o.jsxs("div",{className:"bg-muted/30 rounded-lg p-3 border",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(ie,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm font-medium",children:Ye(l.toISOString())})]}),o.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:d()})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Data Refreshed"}),o.jsxs("div",{className:"bg-muted/30 rounded-lg p-3 border",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(Nt,{className:"h-4 w-4 text-muted-foreground"}),o.jsx("span",{className:"text-sm font-medium",children:n.toLocaleTimeString()})]}),o.jsxs("div",{className:"text-xs text-muted-foreground mt-1",children:["Local time • Data last changed ",d()]})]})]})]}),e.statusHistory&&e.statusHistory.length>0&&o.jsxs("div",{className:"space-y-2",children:[o.jsx(ne,{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Recent Status Changes"}),o.jsx("div",{className:"bg-muted/30 rounded-lg p-3 border max-h-32 overflow-y-auto custom-scrollbar",children:o.jsx("div",{className:"space-y-2",children:e.statusHistory.slice(-3).reverse().map((N,_)=>o.jsxs("div",{className:"flex items-center justify-between text-xs",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx("div",{className:"w-2 h-2 rounded-full bg-primary"}),o.jsx("span",{className:"font-medium capitalize",children:N.status})]}),o.jsx("span",{className:"text-muted-foreground",children:Ye(N.changedAt)})]},_))})})]}),o.jsx("div",{className:"pt-3 border-t",children:o.jsxs("div",{className:"flex flex-wrap gap-2",children:[o.jsxs(X,{variant:"outline",size:"sm",onClick:()=>window.open(`/grievances/${e._id}/timeline`,"_blank"),className:"flex items-center gap-2",children:[o.jsx(vr,{className:"h-3 w-3"}),"Full Timeline"]}),o.jsxs(X,{variant:"outline",size:"sm",onClick:()=>window.open(`/grievances/${e._id}/export`,"_blank"),className:"flex items-center gap-2",children:[o.jsx(vr,{className:"h-3 w-3"}),"Export Details"]})]})})]})]})]})})},Ow=e=>({pending:ie,desk_1:_e,desk_2:oc,desk_3:ot,officer:_e,in_progress:Kt,resolved:ot,closed:Je,reopened:Kt,rejected:Je,cancelled:br})[e]||ie,Yi=e=>({pending:"bg-yellow-500",desk_1:"bg-blue-400",desk_2:"bg-blue-500",desk_3:"bg-blue-600",officer:"bg-purple-500",in_progress:"bg-indigo-500",resolved:"bg-green-500",closed:"bg-gray-500",reopened:"bg-orange-500",rejected:"bg-red-500",cancelled:"bg-red-400"})[e]||"bg-gray-400",Iw=e=>({pending:"Pending Review",desk_1:"Initial Verification",desk_2:"Fact Checking",desk_3:"Admin Review",officer:"Officer Assignment",in_progress:"In Progress",resolved:"resolved",closed:"Closed",reopened:"Reopened",rejected:"rejected",cancelled:"cancelled"})[e]||e,Mw=e=>e<60?`${e}m`:e<1440?`${Math.round(e/60)}h`:`${Math.round(e/1440)}d`,Lw=({entries:e,className:t,showDuration:r=!0,collapsible:n=!0})=>{const[s,a]=f.useState(new Set),[i,c]=f.useState(!1),l=d=>{const m=new Set(s);m.has(d)?m.delete(d):m.add(d),a(m)},u=[...e].sort((d,m)=>new Date(m.timestamp).getTime()-new Date(d.timestamp).getTime());return o.jsxs("div",{className:B("w-full",t),children:[o.jsxs("div",{className:"flex items-center justify-between mb-6",children:[o.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Status History"}),n&&o.jsx(X,{variant:"ghost",size:"sm",onClick:()=>c(!i),className:"text-muted-foreground hover:text-foreground",children:i?o.jsxs(o.Fragment,{children:[o.jsx(Gs,{className:"h-4 w-4 mr-1"}),"Show History"]}):o.jsxs(o.Fragment,{children:[o.jsx(Ct,{className:"h-4 w-4 mr-1"}),"Hide History"]})})]}),o.jsx(bn,{children:!i&&o.jsxs(qe.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"relative",children:[o.jsx("div",{className:"absolute left-6 top-0 bottom-0 w-0.5 bg-border"}),o.jsx("div",{className:"space-y-4",children:u.map((d,m)=>{const h=Ow(d.status),x=s.has(d.id),g=m===0;return o.jsxs(qe.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:m*.1},className:"relative flex items-start gap-4",children:[o.jsx("div",{className:"relative z-10 flex-shrink-0",children:o.jsx(qe.div,{className:B("w-12 h-12 rounded-full border-4 border-background flex items-center justify-center text-white",Yi(d.status),g&&"ring-4 ring-blue-200 dark:ring-blue-800"),whileHover:{scale:1.05},animate:g?{boxShadow:["0 0 0 0 rgba(59, 130, 246, 0.7)","0 0 0 10px rgba(59, 130, 246, 0)","0 0 0 0 rgba(59, 130, 246, 0)"]}:{},transition:{duration:2,repeat:g?1/0:0},children:o.jsx(h,{className:"h-6 w-6"})})}),o.jsx("div",{className:"flex-1 min-w-0 pb-4",children:o.jsxs("div",{className:"bg-card border border-border rounded-lg p-4 shadow-sm",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(q,{className:B("text-white",Yi(d.status)),children:Iw(d.status)}),d.autoMoved&&o.jsx(q,{variant:"outline",className:"text-xs",children:"Auto"}),g&&o.jsx(q,{variant:"default",className:"text-xs",children:"Current"})]}),r&&d.duration&&o.jsxs("div",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[o.jsx(ie,{className:"h-3 w-3"}),Mw(d.duration)]})]}),o.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground mb-2",children:[o.jsx(ie,{className:"h-4 w-4"}),o.jsx("span",{children:new Date(d.timestamp).toLocaleString()}),o.jsx("span",{children:"•"}),o.jsx(_e,{className:"h-4 w-4"}),o.jsx("span",{children:d.triggeredBy.name}),o.jsx(q,{variant:"outline",className:"text-xs",children:d.triggeredBy.role})]}),(d.reason||d.notes)&&o.jsxs("div",{className:"text-sm",children:[d.reason&&o.jsxs("div",{className:"text-foreground",children:[o.jsx("strong",{children:"Reason:"})," ",d.reason]}),d.notes&&!x&&o.jsx("div",{className:"text-muted-foreground mt-1",children:d.notes.length>100?`${d.notes.substring(0,100)}...`:d.notes})]}),(d.notes||d.attachments?.length)&&o.jsx(X,{variant:"ghost",size:"sm",onClick:()=>l(d.id),className:"mt-2 h-auto p-1 text-xs text-muted-foreground hover:text-foreground",children:x?o.jsxs(o.Fragment,{children:[o.jsx(Ct,{className:"h-3 w-3 mr-1"}),"Show Less"]}):o.jsxs(o.Fragment,{children:[o.jsx(Gs,{className:"h-3 w-3 mr-1"}),"Show Details"]})}),o.jsx(bn,{children:x&&o.jsxs(qe.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},className:"mt-3 pt-3 border-t border-border",children:[d.notes&&o.jsxs("div",{className:"text-sm text-foreground mb-3",children:[o.jsx("strong",{children:"Notes:"}),o.jsx("div",{className:"mt-1 p-2 bg-muted rounded text-muted-foreground",children:d.notes})]}),d.attachments&&d.attachments.length>0&&o.jsxs("div",{className:"text-sm",children:[o.jsx("strong",{children:"Attachments:"}),o.jsx("div",{className:"mt-1 flex flex-wrap gap-2",children:d.attachments.map((p,b)=>o.jsx(q,{variant:"outline",className:"text-xs",children:p.name||`File ${b+1}`},b))})]})]})})]})})]},d.id)})}),u.length===0&&o.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[o.jsx(ie,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),o.jsx("p",{children:"No status history available"})]})]})})]})},Fw={submitted:yr,pending:ie,desk_1:On,desk_2:qt,desk_3:$o,officer:kt,in_progress:Fo,resolved:ot,closed:Er,rejected:Je,cancelled:Je,reopened:Kt},$w={completed:{bg:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20",border:"border-green-200 dark:border-green-800",text:"text-green-800 dark:text-green-200",icon:"text-green-600 dark:text-green-400",indicator:"bg-gradient-to-r from-green-500 to-green-600",line:"bg-green-400"},in_progress:{bg:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20",border:"border-blue-200 dark:border-blue-800",text:"text-blue-800 dark:text-blue-200",icon:"text-blue-600 dark:text-blue-400",indicator:"bg-gradient-to-r from-blue-500 to-blue-600",line:"bg-blue-400"},pending:{bg:"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-950/20 dark:to-gray-900/20",border:"border-gray-200 dark:border-gray-800",text:"text-gray-600 dark:text-gray-400",icon:"text-gray-400 dark:text-gray-500",indicator:"bg-gradient-to-r from-gray-400 to-gray-500",line:"bg-gray-300"},rejected:{bg:"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20",border:"border-red-200 dark:border-red-800",text:"text-red-800 dark:text-red-200",icon:"text-red-600 dark:text-red-400",indicator:"bg-gradient-to-r from-red-500 to-red-600",line:"bg-red-400"},cancelled:{bg:"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20",border:"border-red-200 dark:border-red-800",text:"text-red-800 dark:text-red-200",icon:"text-red-600 dark:text-red-400",indicator:"bg-gradient-to-r from-red-500 to-red-600",line:"bg-red-400"}},Uw=({checkpoint:e,isLast:t=!1,onClick:r})=>{const n=$w[e.checkpointStatus],s=Fw[e.status]||ie,a=c=>{if(!c)return"Pending";const l=new Date(c);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(l)},i=c=>c?c<60?`${c}m`:c<1440?`${Math.floor(c/60)}h ${c%60}m`:`${Math.floor(c/1440)}d ${Math.floor(c%1440/60)}h`:null;return o.jsxs("div",{className:"relative flex items-start gap-6",children:[!t&&o.jsx("div",{className:B("absolute left-8 top-20 w-0.5 h-24 -ml-px z-0",n.line)}),o.jsxs("div",{className:"relative flex-shrink-0 z-10",children:[o.jsx(qe.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.3,delay:.1},className:B("w-16 h-16 rounded-full border-4 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-105",n.border,n.indicator,e.checkpointStatus==="in_progress"&&"ring-4 ring-blue-400/30 animate-pulse"),children:o.jsx(s,{className:B("w-7 h-7 text-white transition-transform duration-300 hover:scale-110")})}),o.jsx("div",{className:"absolute -bottom-2 -right-2",children:o.jsx(q,{variant:e.checkpointStatus==="completed"?"default":e.checkpointStatus==="in_progress"?"secondary":e.checkpointStatus==="rejected"||e.checkpointStatus==="cancelled"?"destructive":"outline",className:"text-xs font-bold shadow-md",children:e.checkpointStatus.replace("_"," ").toUpperCase()})})]}),o.jsx("div",{className:"flex-1 min-w-0",children:o.jsx(qe.div,{initial:{x:20,opacity:0},animate:{x:0,opacity:1},transition:{duration:.4,delay:.2},children:o.jsx(ae,{className:B("transition-all duration-300 hover:shadow-xl cursor-pointer transform hover:-translate-y-1",n.bg,n.border,"border-2 shadow-lg backdrop-blur-sm"),onClick:()=>r?.(e),children:o.jsxs(de,{className:"p-6",children:[o.jsxs("div",{className:"flex items-start justify-between gap-4 mb-4",children:[o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsx("h3",{className:B("text-xl font-bold leading-tight mb-2",n.text),children:e.title}),o.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.description})]}),o.jsxs("div",{className:"text-right",children:[o.jsxs("div",{className:"flex items-center gap-1 text-sm text-muted-foreground mb-1",children:[o.jsx(Gt,{className:"w-4 h-4"}),o.jsx("span",{children:a(e.timestamp)})]}),e.duration&&o.jsxs("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[o.jsx(ie,{className:"w-4 h-4"}),o.jsx("span",{children:i(e.duration)})]})]})]}),o.jsxs("div",{className:"space-y-3",children:[e.triggeredBy&&o.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[o.jsx(_e,{className:"w-4 h-4 text-muted-foreground"}),o.jsx("span",{className:"text-muted-foreground",children:"Processed by:"}),o.jsx("span",{className:"font-medium",children:e.triggeredBy.name}),o.jsx(q,{variant:"outline",className:"text-xs",children:e.triggeredBy.role})]}),e.notes&&o.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[o.jsx(oc,{className:"w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0"}),o.jsxs("div",{children:[o.jsx("span",{className:"text-muted-foreground",children:"Notes:"}),o.jsx("p",{className:"mt-1 text-foreground font-medium leading-relaxed",children:e.notes})]})]})]})]})})})})]})},Bw=(e,t)=>{const r=["submitted","pending","desk_1","desk_2","desk_3","officer","in_progress","resolved","closed"],n=r.indexOf(t),s=r.indexOf(e);return e==="rejected"||e==="cancelled"?"rejected":e==="reopened"?"in_progress":e==="submitted"||s<n?"completed":s===n?"in_progress":"pending"},Ws={submitted:{title:"Grievance Submitted",description:"Your grievance has been successfully submitted and is now in the system for processing."},pending:{title:"Initial Review",description:"Your grievance is being reviewed for completeness and proper categorization."},desk_1:{title:"Initial Verification",description:"First level verification of facts and preliminary assessment of the grievance."},desk_2:{title:"Fact Checking",description:"Detailed fact-checking and validation of the information provided in your grievance."},desk_3:{title:"Administrative Review",description:"Administrative review and preparation for officer assignment or resolution."},officer:{title:"Officer Assignment",description:"A dedicated officer has been assigned to investigate and resolve your grievance."},in_progress:{title:"Investigation in Progress",description:"Active investigation is underway. The assigned officer is working on your case."},resolved:{title:"Grievance Resolved",description:"Your grievance has been successfully resolved. Resolution details are available."},closed:{title:"Case Closed",description:"The grievance case has been officially closed and archived in the system."},rejected:{title:"Grievance Rejected",description:"Your grievance has been rejected. Please review the rejection reason provided."},cancelled:{title:"Grievance Cancelled",description:"The grievance has been cancelled either by request or due to specific circumstances."},reopened:{title:"Grievance Reopened",description:"The grievance has been reopened for further investigation or review."}},Hw=({grievance:e,onCheckpointClick:t,className:r})=>{const s=(()=>{try{const a=[],i=e?.statusHistory||[],c=e?.status||"submitted";if(console.log("🔄 EnhancedTimelineDisplay: Generating checkpoints for status:",c),console.log("📊 Status history entries:",i.length),["submitted","pending","desk_1","desk_2","desk_3","officer","in_progress","resolved","closed"].forEach(d=>{const m=i.find(g=>g.status===d),h=Bw(d,c),x=Ws[d];a.push({id:`checkpoint-${d}`,status:d,title:x.title,description:x.description,timestamp:m?.changedAt||m?.timestamp,triggeredBy:m?{name:m.changedBy||"System",role:m.role||"Officer"}:void 0,notes:m?.reason||m?.notes,duration:m?.duration,checkpointStatus:h})}),c==="rejected"||i.some(d=>d.status==="rejected")){const d=i.find(m=>m.status==="rejected");a.push({id:"checkpoint-rejected",status:"rejected",title:Ws.rejected.title,description:Ws.rejected.description,timestamp:d?.changedAt||d?.timestamp,triggeredBy:d?{name:d.changedBy||"System",role:d.role||"Officer"}:void 0,notes:d?.reason||d?.notes,duration:d?.duration,checkpointStatus:"rejected"})}const u=a.filter(d=>d.checkpointStatus==="completed"||d.checkpointStatus==="in_progress"||d.checkpointStatus==="rejected"||d.checkpointStatus==="pending"&&a.some(m=>m.checkpointStatus==="in_progress"));return console.log("✅ EnhancedTimelineDisplay: Generated",u.length,"checkpoints"),u}catch(a){return console.error("❌ EnhancedTimelineDisplay: Error generating checkpoints:",a),[]}})();return o.jsxs("div",{className:B("space-y-6",r),children:[o.jsx(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:o.jsx(ge,{children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs(ye,{className:"flex items-center gap-2",children:[o.jsx(ie,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Progress Timeline"]}),o.jsx("div",{className:"flex items-center gap-2",children:o.jsxs(q,{variant:"outline",className:"bg-background/50 dark:bg-[#0D0D0D]/50",children:[s.filter(a=>a.checkpointStatus==="completed").length," / ",s.length," Steps"]})})]})})}),o.jsx(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:o.jsxs(de,{className:"p-8",children:[o.jsx("div",{className:"space-y-8",children:o.jsx(bn,{children:s.map((a,i)=>o.jsx(qe.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4,delay:i*.1},children:o.jsx(Uw,{checkpoint:a,isLast:i===s.length-1,onClick:t})},a.id))})}),s.length===0&&o.jsxs("div",{className:"text-center py-12",children:[o.jsx(ie,{className:"w-16 h-16 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground text-lg",children:"No timeline data available"}),o.jsx("p",{className:"text-muted-foreground text-sm mt-2",children:"Timeline will appear as your grievance progresses"})]})]})}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:[o.jsx(ge,{children:o.jsxs(ye,{className:"flex items-center gap-2",children:[o.jsx(Ue,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"}),"Timeline Summary"]})}),o.jsx(de,{children:o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx(Gt,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),o.jsx("p",{className:"text-sm font-semibold text-muted-foreground",children:"Started"})]}),o.jsx("p",{className:"text-lg font-bold text-foreground",children:e?.submittedAt?new Date(e.submittedAt).toLocaleDateString():"Unknown"})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx(ie,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),o.jsx("p",{className:"text-sm font-semibold text-muted-foreground",children:"Duration"})]}),o.jsx("p",{className:"text-lg font-bold text-foreground",children:(()=>{if(!e?.submittedAt)return"Unknown";const a=new Date(e.submittedAt);return`${Math.floor((new Date().getTime()-a.getTime())/(1e3*60*60*24))} days`})()})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[o.jsx(_e,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),o.jsx("p",{className:"text-sm font-semibold text-muted-foreground",children:"Current Stage"})]}),o.jsx("p",{className:"text-lg font-bold text-foreground",children:e?.status?.replace("_"," ").replace(/\b\w/g,a=>a.toUpperCase())||"Unknown"})]})]})})]})]})},Vw={submitted:Ue,pending:ie,desk_1:On,desk_2:qt,desk_3:$o,officer:kt,in_progress:Fo,resolved:ot,closed:Er,rejected:Je,cancelled:Je,reopened:Kt},zw={submitted:"text-blue-600 dark:text-blue-400",pending:"text-yellow-600 dark:text-yellow-400",desk_1:"text-purple-600 dark:text-purple-400",desk_2:"text-purple-600 dark:text-purple-400",desk_3:"text-purple-600 dark:text-purple-400",officer:"text-indigo-600 dark:text-indigo-400",in_progress:"text-blue-600 dark:text-blue-400",resolved:"text-green-600 dark:text-green-400",closed:"text-gray-600 dark:text-gray-400",rejected:"text-red-600 dark:text-red-400",cancelled:"text-red-600 dark:text-red-400",reopened:"text-orange-600 dark:text-orange-400"},Ww={closed:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",rejected:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",cancelled:"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"},Gw=({previousTimelines:e,className:t})=>{const[r,n]=f.useState(new Set),s=Ee.useMemo(()=>e.map((l,u)=>({...l,uniqueId:l.id||`timeline-${u}-${l.cycleNumber||u}-${Date.now()}`})),[e]),a=f.useCallback(l=>{try{n(u=>{const d=new Set(u);return d.has(l)?d.delete(l):d.add(l),d})}catch(u){console.error("Error toggling timeline cycle:",u)}},[]);f.useEffect(()=>{n(new Set)},[e.length]);const i=l=>l<60?`${l}m`:l<1440?`${Math.floor(l/60)}h ${l%60}m`:`${Math.floor(l/1440)}d ${Math.floor(l%1440/60)}h`,c=l=>{switch(l){case"ON_TRACK":return"text-green-600 dark:text-green-400";case"URGENT":return"text-yellow-600 dark:text-yellow-400";case"CRITICAL":return"text-orange-600 dark:text-orange-400";case"BREACHED":return"text-red-600 dark:text-red-400";default:return"text-gray-600 dark:text-gray-400"}};return!e||e.length===0?o.jsx(ae,{className:B("bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",t),children:o.jsxs(de,{className:"p-8 text-center",children:[o.jsx(ie,{className:"w-12 h-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"No previous timeline cycles available"}),o.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Previous cycles will appear here when the grievance is reopened"})]})}):o.jsxs("div",{className:B("space-y-4",t),children:[o.jsx(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:o.jsx(ge,{children:o.jsxs(ye,{className:"flex items-center gap-2",children:[o.jsx(Kt,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"}),"Timeline History",o.jsxs(q,{variant:"secondary",className:"bg-background/50 dark:bg-[#0D0D0D]/50",children:[e.length," Previous Cycle",e.length>1?"s":""]})]})})}),o.jsx("div",{className:"space-y-3",children:s.map((l,u)=>{const d=r.has(l.uniqueId),m=l.finalStatus==="closed"?Er:l.finalStatus==="rejected"?Je:Je;return o.jsx(qe.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:u*.1},children:o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm overflow-hidden",children:[o.jsxs(ge,{className:"cursor-pointer hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 transition-colors duration-200",onClick:()=>a(l.uniqueId),children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center gap-4",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center",children:o.jsx(m,{className:"w-5 h-5 text-white"})}),o.jsxs("div",{children:[o.jsxs(ye,{className:"text-lg",children:["Timeline Cycle ",l.cycleNumber]}),o.jsxs("p",{className:"text-sm text-muted-foreground",children:[new Date(l.startDate).toLocaleDateString()," - ",new Date(l.endDate).toLocaleDateString()]})]})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(q,{className:B("font-semibold",Ww[l.finalStatus]),children:l.finalStatus.toUpperCase()}),o.jsxs(q,{variant:"outline",className:B("font-semibold",c(l.slaCompliance?.status||"ON_TRACK")),children:["SLA: ",(l.slaCompliance?.status||"ON_TRACK").replace("_"," ")]}),o.jsx(X,{variant:"ghost",size:"sm",children:d?o.jsx(gn,{className:"h-4 w-4"}):o.jsx(Ct,{className:"h-4 w-4"})})]})]}),o.jsxs("div",{className:"grid grid-cols-3 gap-4 mt-4",children:[o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30",children:[o.jsx("p",{className:"text-xs text-muted-foreground font-semibold",children:"Duration"}),o.jsx("p",{className:"text-sm font-bold",children:i(l.slaCompliance?.totalDuration||0)})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30",children:[o.jsx("p",{className:"text-xs text-muted-foreground font-semibold",children:"Steps"}),o.jsxs("p",{className:"text-sm font-bold",children:[l.statusHistory?.length||0," checkpoints"]})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30",children:[o.jsx("p",{className:"text-xs text-muted-foreground font-semibold",children:"Completion"}),o.jsx("p",{className:"text-sm font-bold",children:l.completionReason||"Standard process"})]})]})]}),o.jsx(bn,{mode:"wait",children:d&&o.jsxs(qe.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:[o.jsx(Sc,{}),o.jsx(de,{className:"p-6",children:o.jsxs("div",{className:"space-y-4",children:[o.jsx("h4",{className:"font-semibold text-foreground mb-4",children:"Detailed Timeline"}),o.jsx("div",{className:"space-y-3",children:(l.statusHistory||[]).map((h,x)=>{if(!h)return null;const g=Vw[h.status]||ie,p=zw[h.status]||"text-gray-600";return o.jsxs("div",{className:"flex items-start gap-4 p-3 bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg border border-border/30",children:[o.jsx("div",{className:B("w-8 h-8 rounded-full flex items-center justify-center",h.status==="resolved"?"bg-green-100 dark:bg-green-900/20":h.status==="rejected"||h.status==="cancelled"?"bg-red-100 dark:bg-red-900/20":"bg-blue-100 dark:bg-blue-900/20"),children:o.jsx(g,{className:B("w-4 h-4",p)})}),o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsxs("div",{className:"flex items-center justify-between mb-1",children:[o.jsx("h5",{className:"font-medium text-foreground",children:h.status.replace("_"," ").replace(/\b\w/g,b=>b.toUpperCase())}),o.jsx("span",{className:"text-xs text-muted-foreground",children:new Date(h.changedAt).toLocaleString()})]}),o.jsxs("div",{className:"flex items-center gap-2 text-sm text-muted-foreground mb-2",children:[o.jsx(_e,{className:"w-3 h-3"}),o.jsx("span",{children:h.changedBy}),h.role&&o.jsx(q,{variant:"outline",className:"text-xs py-0 px-1",children:h.role}),h.duration&&o.jsxs(o.Fragment,{children:[o.jsx(ie,{className:"w-3 h-3 ml-2"}),o.jsx("span",{children:i(h.duration)})]})]}),(h.reason||h.notes)&&o.jsx("p",{className:"text-sm text-foreground bg-background/50 dark:bg-[#0D0D0D]/50 p-2 rounded border border-border/30",children:h.reason||h.notes})]})]},x)})}),o.jsxs("div",{className:"mt-6 p-4 bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg border border-border/30",children:[o.jsx("h5",{className:"font-semibold text-foreground mb-3",children:"SLA Performance"}),o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("p",{className:"text-xs text-muted-foreground font-semibold",children:"Actual Duration"}),o.jsx("p",{className:"text-sm font-bold",children:i(l.slaCompliance?.totalDuration||0)})]}),o.jsxs("div",{children:[o.jsx("p",{className:"text-xs text-muted-foreground font-semibold",children:"Expected Duration"}),o.jsx("p",{className:"text-sm font-bold",children:i(l.slaCompliance?.expectedDuration||0)})]})]}),o.jsxs("div",{className:"mt-3",children:[o.jsx("div",{className:"w-full bg-muted/30 rounded-full h-2",children:o.jsx("div",{className:B("h-2 rounded-full transition-all duration-500",(l.slaCompliance?.status||"ON_TRACK")==="BREACHED"?"bg-red-500":(l.slaCompliance?.status||"ON_TRACK")==="CRITICAL"?"bg-orange-500":(l.slaCompliance?.status||"ON_TRACK")==="URGENT"?"bg-yellow-500":"bg-green-500"),style:{width:`${Math.min((l.slaCompliance?.totalDuration||0)/(l.slaCompliance?.expectedDuration||1)*100,100)}%`}})}),o.jsxs("p",{className:B("text-xs font-semibold mt-1",c(l.slaCompliance?.status||"ON_TRACK")),children:[(l.slaCompliance?.status||"ON_TRACK").replace("_"," ")," - ",Math.round((l.slaCompliance?.totalDuration||0)/(l.slaCompliance?.expectedDuration||1)*100),"% of expected time"]})]})]})]})})]},`expanded-${l.uniqueId}`)})]})},l.uniqueId)})})]})},qw=({grievance:e,onTimelineEntryClick:t})=>{const[r,n]=f.useState("tracking"),[s,a]=f.useState(!1),{currentTimeline:i,previousTimelines:c,isLoading:l,error:u,generateTimeline:d,refreshTimeline:m}=Rn();f.useEffect(()=>{e&&e._id&&(console.log("🔄 TimelineTab: Generating timeline for grievance:",e._id),d(e))},[e,d]);const h=async()=>{a(!0);try{console.log("🔄 TimelineTab: Refreshing timeline data"),e&&e._id&&(await m(e._id),console.log("✅ TimelineTab: Timeline refreshed successfully")),t&&await new Promise(g=>setTimeout(g,500))}catch(g){console.error("❌ TimelineTab: Error refreshing timeline:",g)}finally{a(!1)}};if(!e)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Xs,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"No grievance data available"})]})});if(l)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Nt,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4 animate-spin"}),o.jsx("p",{className:"text-muted-foreground",children:"Loading timeline data..."})]})});const x=f.useMemo(()=>{try{return i&&i.entries&&i.entries.length>0?(console.log("📊 TimelineTab: Using timeline store data"),i.entries.map((g,p)=>({id:g?.id||`timeline-${p}`,status:g?.status||"unknown",timestamp:g?.timestamp||new Date().toISOString(),triggeredBy:{name:g?.triggeredBy?.name||g?.changedBy||"System",role:g?.triggeredBy?.role||g?.role||"Officer",id:g?.triggeredBy?.id||g?.userId||"system"},reason:g?.reason||g?.notes||"",notes:g?.notes||"",autoMoved:g?.autoMoved||!1,attachments:g?.attachments||[],duration:g?.duration||0,checkpointInfo:g?.checkpointInfo,slaStatus:g?.slaStatus,metadata:g?.metadata}))):!e||!e.status?[]:!e.statusHistory||e.statusHistory.length===0?[{id:"current",status:e.status,timestamp:e.lastUpdatedAt||e.submittedAt||new Date().toISOString(),triggeredBy:{name:"System",role:"Automated",id:"system"},reason:"Current status",autoMoved:!1}]:(console.log("📊 TimelineTab: Using grievance status history as fallback"),e.statusHistory.map((g,p)=>({id:g?.id||`history-${p}`,status:g?.status||"unknown",timestamp:g?.changedAt||g?.timestamp||new Date().toISOString(),triggeredBy:{name:g?.changedBy||"System",role:g?.role||"Officer",id:g?.userId||"system"},reason:g?.reason||g?.notes||"",notes:g?.notes||"",autoMoved:g?.autoMoved||!1,attachments:g?.attachments||[],duration:g?.duration||0})))}catch(g){return console.error("Error processing timeline entries:",g),[]}},[e,i]);return u?o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Xs,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),o.jsxs("p",{className:"text-red-500 mb-4",children:["Error loading timeline: ",u]}),o.jsxs(X,{onClick:h,variant:"outline",children:[o.jsx(Nt,{className:"h-4 w-4 mr-2"}),"Retry"]})]})}):o.jsx("div",{className:"h-full bg-background/50 dark:bg-[#0D0D0D]/50 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsxs("div",{className:"space-y-6 p-6",children:[o.jsx(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:o.jsxs(ge,{className:"pb-4",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center",children:o.jsx(Ma,{className:"h-5 w-5 text-white"})}),o.jsxs("div",{children:[o.jsx(ye,{className:"text-xl font-bold text-foreground",children:"Grievance Timeline"}),o.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Track progress and manage status changes"})]})]}),o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsxs(q,{variant:"outline",className:"bg-background/50 dark:bg-[#0D0D0D]/50",children:["ID: ",e._id?.slice(-6)||"N/A"]}),o.jsx(q,{variant:e.status==="resolved"?"default":"secondary",className:"bg-background/50 dark:bg-[#0D0D0D]/50",children:e.status?.toUpperCase()}),o.jsxs(X,{variant:"ghost",size:"sm",onClick:h,disabled:s,className:"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70",children:[o.jsx(Nt,{className:`h-4 w-4 mr-1 ${s?"animate-spin":""}`}),"Refresh"]})]})]}),o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-6",children:[o.jsxs("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-800/50",children:[o.jsx("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:x.length}),o.jsx("div",{className:"text-sm text-blue-700 dark:text-blue-300 font-medium",children:"Timeline Events"})]}),o.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20 rounded-lg p-4 border border-green-200/50 dark:border-green-800/50",children:[o.jsxs("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:[Math.round((x.length||1)/9*100),"%"]}),o.jsx("div",{className:"text-sm text-green-700 dark:text-green-300 font-medium",children:"Progress"})]}),o.jsxs("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20 rounded-lg p-4 border border-purple-200/50 dark:border-purple-800/50",children:[o.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:e.reopenCount||0}),o.jsx("div",{className:"text-sm text-purple-700 dark:text-purple-300 font-medium",children:"Times Reopened"})]}),o.jsxs("div",{className:"bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20 rounded-lg p-4 border border-orange-200/50 dark:border-orange-800/50",children:[o.jsx("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:(()=>{const g=new Date(e.submittedAt),b=Math.floor((new Date().getTime()-g.getTime())/(1e3*60*60));return b<24?`${b}h`:`${Math.floor(b/24)}d`})()}),o.jsx("div",{className:"text-sm text-orange-700 dark:text-orange-300 font-medium",children:"Total Duration"})]})]})]})}),o.jsx(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm",children:o.jsxs(ge,{className:"pb-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center",children:o.jsx(La,{className:"h-4 w-4 text-white"})}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"SLA Status"}),o.jsx("p",{className:"text-sm text-muted-foreground",children:"Service Level Agreement Tracking"})]})]}),o.jsxs(q,{variant:e.priority==="high"?"destructive":e.priority==="medium"?"default":"secondary",className:"bg-background/50 dark:bg-[#0D0D0D]/50 font-semibold",children:[(e.priority||"medium").toUpperCase()," PRIORITY"]})]}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx("div",{className:"w-full bg-muted/30 rounded-full h-3 shadow-inner",children:o.jsx("div",{className:`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${x.length>=8?"bg-gradient-to-r from-red-500 to-red-600":x.length>=6?"bg-gradient-to-r from-orange-500 to-orange-600":x.length>=4?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-green-500 to-green-600"}`,style:{width:`${Math.min((x.length||1)/9*100,100)}%`}})}),o.jsxs("div",{className:"absolute -top-1 -right-1 text-xs font-medium text-foreground bg-background/80 dark:bg-[#0D0D0D]/80 px-2 py-1 rounded-full border border-border/30 shadow-sm",children:[Math.round((x.length||1)/9*100),"%"]})]}),o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx(ie,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),o.jsx("p",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Expected Resolution"})]}),o.jsx("p",{className:"text-sm font-semibold text-foreground",children:(()=>{const g=new Date(e.submittedAt),p=e.priority||"medium",b=p==="high"?72:p==="medium"?120:168;return new Date(g.getTime()+b*60*60*1e3).toLocaleDateString()})()})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx(Ma,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),o.jsx("p",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Time Remaining"})]}),o.jsx("p",{className:"text-sm font-semibold text-foreground",children:(()=>{const g=new Date(e.submittedAt),p=new Date,b=e.priority||"medium",v=b==="high"?72:b==="medium"?120:168,w=new Date(g.getTime()+v*60*60*1e3).getTime()-p.getTime();if(w<=0)return"Overdue";const S=Math.floor(w/(1e3*60*60));return S<24?`${S}h`:`${Math.floor(S/24)}d`})()})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx(vs,{className:"h-4 w-4 text-purple-600 dark:text-purple-400"}),o.jsx("p",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"Current Stage"})]}),o.jsx("p",{className:"text-sm font-semibold text-foreground",children:e.status?.replace("_"," ").replace(/\b\w/g,g=>g.toUpperCase())||"Unknown"})]}),o.jsxs("div",{className:"bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx(Nt,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"}),o.jsx("p",{className:"text-xs font-semibold text-muted-foreground uppercase tracking-wide",children:"SLA Compliance"})]}),o.jsx("p",{className:`text-sm font-semibold ${x.length>=8?"text-red-600 dark:text-red-400":x.length>=6?"text-orange-600 dark:text-orange-400":x.length>=4?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"}`,children:x.length>=8?"BREACHED":x.length>=6?"CRITICAL":x.length>=4?"URGENT":"ON TRACK"})]})]})]})]})}),o.jsxs(Yu,{value:r,onValueChange:n,className:"w-full bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg border border-border/30 shadow-lg backdrop-blur-sm",children:[o.jsxs(ka,{className:"grid w-full grid-cols-2 bg-background/30 dark:bg-[#0D0D0D]/30 border border-border/30",children:[o.jsxs(dt,{value:"tracking",className:"flex items-center gap-2 data-[state=active]:bg-background/50 dark:data-[state=active]:bg-[#0D0D0D]/50",children:[o.jsx(La,{className:"h-4 w-4"}),"Progress Tracking"]}),o.jsxs(dt,{value:"history",className:"flex items-center gap-2 data-[state=active]:bg-background/50 dark:data-[state=active]:bg-[#0D0D0D]/50",children:[o.jsx(vs,{className:"h-4 w-4"}),"Timeline History"]})]}),o.jsx(ut,{value:"tracking",className:"mt-6",children:o.jsx(Hw,{grievance:e,onCheckpointClick:g=>{console.log("Checkpoint clicked:",g),t?.(g)},className:"w-full"})}),o.jsx(ut,{value:"history",className:"mt-6",children:o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"w-full bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg border border-border/30 shadow-lg backdrop-blur-sm p-6",children:[o.jsxs("h3",{className:"text-lg font-semibold text-foreground mb-4 flex items-center gap-2",children:[o.jsx(vs,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Current Timeline History"]}),o.jsx(Lw,{entries:x,className:"w-full",showDuration:!0,collapsible:!0})]}),(c&&c.length>0||e.previousTimelines&&e.previousTimelines.length>0)&&o.jsx(Gw,{previousTimelines:(c||e.previousTimelines).map(g=>({id:g.id||g._id||`timeline-${g.cycleNumber}`,cycleNumber:g.cycleNumber||1,startDate:g.startDate||g.createdAt||new Date().toISOString(),endDate:g.endDate||g.completedAt||new Date().toISOString(),finalStatus:g.finalStatus||"closed",completionReason:g.completionReason||g.reason,statusHistory:g.statusHistory||[],slaCompliance:g.slaCompliance||{status:"ON_TRACK",totalDuration:0,expectedDuration:72}})),className:"w-full"})]})})]})]})})};function To(e,[t,r]){return Math.min(r,Math.max(t,e))}function Kw(e,t){return f.useReducer((r,n)=>t[r][n]??r,e)}var Ta="ScrollArea",[Ju,hk]=Ne(Ta),[Yw,Le]=Ju(Ta),Qu=f.forwardRef((e,t)=>{const{__scopeScrollArea:r,type:n="hover",dir:s,scrollHideDelay:a=600,...i}=e,[c,l]=f.useState(null),[u,d]=f.useState(null),[m,h]=f.useState(null),[x,g]=f.useState(null),[p,b]=f.useState(null),[v,y]=f.useState(0),[w,S]=f.useState(0),[j,D]=f.useState(!1),[E,T]=f.useState(!1),N=G(t,I=>l(I)),_=ir(s);return o.jsx(Yw,{scope:r,type:n,dir:_,scrollHideDelay:a,scrollArea:c,viewport:u,onViewportChange:d,content:m,onContentChange:h,scrollbarX:x,onScrollbarXChange:g,scrollbarXEnabled:j,onScrollbarXEnabledChange:D,scrollbarY:p,onScrollbarYChange:b,scrollbarYEnabled:E,onScrollbarYEnabledChange:T,onCornerWidthChange:y,onCornerHeightChange:S,children:o.jsx(U.div,{dir:_,...i,ref:N,style:{position:"relative","--radix-scroll-area-corner-width":v+"px","--radix-scroll-area-corner-height":w+"px",...e.style}})})});Qu.displayName=Ta;var Zu="ScrollAreaViewport",ef=f.forwardRef((e,t)=>{const{__scopeScrollArea:r,children:n,nonce:s,...a}=e,i=Le(Zu,r),c=f.useRef(null),l=G(t,c,i.onViewportChange);return o.jsxs(o.Fragment,{children:[o.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:s}),o.jsx(U.div,{"data-radix-scroll-area-viewport":"",...a,ref:l,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...e.style},children:o.jsx("div",{ref:i.onContentChange,style:{minWidth:"100%",display:"table"},children:n})})]})});ef.displayName=Zu;var rt="ScrollAreaScrollbar",Ea=f.forwardRef((e,t)=>{const{forceMount:r,...n}=e,s=Le(rt,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=s,c=e.orientation==="horizontal";return f.useEffect(()=>(c?a(!0):i(!0),()=>{c?a(!1):i(!1)}),[c,a,i]),s.type==="hover"?o.jsx(Xw,{...n,ref:t,forceMount:r}):s.type==="scroll"?o.jsx(Jw,{...n,ref:t,forceMount:r}):s.type==="auto"?o.jsx(tf,{...n,ref:t,forceMount:r}):s.type==="always"?o.jsx(Ra,{...n,ref:t}):null});Ea.displayName=rt;var Xw=f.forwardRef((e,t)=>{const{forceMount:r,...n}=e,s=Le(rt,e.__scopeScrollArea),[a,i]=f.useState(!1);return f.useEffect(()=>{const c=s.scrollArea;let l=0;if(c){const u=()=>{window.clearTimeout(l),i(!0)},d=()=>{l=window.setTimeout(()=>i(!1),s.scrollHideDelay)};return c.addEventListener("pointerenter",u),c.addEventListener("pointerleave",d),()=>{window.clearTimeout(l),c.removeEventListener("pointerenter",u),c.removeEventListener("pointerleave",d)}}},[s.scrollArea,s.scrollHideDelay]),o.jsx(xe,{present:r||a,children:o.jsx(tf,{"data-state":a?"visible":"hidden",...n,ref:t})})}),Jw=f.forwardRef((e,t)=>{const{forceMount:r,...n}=e,s=Le(rt,e.__scopeScrollArea),a=e.orientation==="horizontal",i=ds(()=>l("SCROLL_END"),100),[c,l]=Kw("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return f.useEffect(()=>{if(c==="idle"){const u=window.setTimeout(()=>l("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(u)}},[c,s.scrollHideDelay,l]),f.useEffect(()=>{const u=s.viewport,d=a?"scrollLeft":"scrollTop";if(u){let m=u[d];const h=()=>{const x=u[d];m!==x&&(l("SCROLL"),i()),m=x};return u.addEventListener("scroll",h),()=>u.removeEventListener("scroll",h)}},[s.viewport,a,l,i]),o.jsx(xe,{present:r||c!=="hidden",children:o.jsx(Ra,{"data-state":c==="hidden"?"hidden":"visible",...n,ref:t,onPointerEnter:P(e.onPointerEnter,()=>l("POINTER_ENTER")),onPointerLeave:P(e.onPointerLeave,()=>l("POINTER_LEAVE"))})})}),tf=f.forwardRef((e,t)=>{const r=Le(rt,e.__scopeScrollArea),{forceMount:n,...s}=e,[a,i]=f.useState(!1),c=e.orientation==="horizontal",l=ds(()=>{if(r.viewport){const u=r.viewport.offsetWidth<r.viewport.scrollWidth,d=r.viewport.offsetHeight<r.viewport.scrollHeight;i(c?u:d)}},10);return Qt(r.viewport,l),Qt(r.content,l),o.jsx(xe,{present:n||a,children:o.jsx(Ra,{"data-state":a?"visible":"hidden",...s,ref:t})})}),Ra=f.forwardRef((e,t)=>{const{orientation:r="vertical",...n}=e,s=Le(rt,e.__scopeScrollArea),a=f.useRef(null),i=f.useRef(0),[c,l]=f.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=af(c.viewport,c.content),d={...n,sizes:c,onSizesChange:l,hasThumb:u>0&&u<1,onThumbChange:h=>a.current=h,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:h=>i.current=h};function m(h,x){return nN(h,i.current,c,x)}return r==="horizontal"?o.jsx(Qw,{...d,ref:t,onThumbPositionChange:()=>{if(s.viewport&&a.current){const h=s.viewport.scrollLeft,x=Xi(h,c,s.dir);a.current.style.transform=`translate3d(${x}px, 0, 0)`}},onWheelScroll:h=>{s.viewport&&(s.viewport.scrollLeft=h)},onDragScroll:h=>{s.viewport&&(s.viewport.scrollLeft=m(h,s.dir))}}):r==="vertical"?o.jsx(Zw,{...d,ref:t,onThumbPositionChange:()=>{if(s.viewport&&a.current){const h=s.viewport.scrollTop,x=Xi(h,c);a.current.style.transform=`translate3d(0, ${x}px, 0)`}},onWheelScroll:h=>{s.viewport&&(s.viewport.scrollTop=h)},onDragScroll:h=>{s.viewport&&(s.viewport.scrollTop=m(h))}}):null}),Qw=f.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...s}=e,a=Le(rt,e.__scopeScrollArea),[i,c]=f.useState(),l=f.useRef(null),u=G(t,l,a.onScrollbarXChange);return f.useEffect(()=>{l.current&&c(getComputedStyle(l.current))},[l]),o.jsx(nf,{"data-orientation":"horizontal",...s,ref:u,sizes:r,style:{bottom:0,left:a.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:a.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ls(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,m)=>{if(a.viewport){const h=a.viewport.scrollLeft+d.deltaX;e.onWheelScroll(h),lf(h,m)&&d.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:An(i.paddingLeft),paddingEnd:An(i.paddingRight)}})}})}),Zw=f.forwardRef((e,t)=>{const{sizes:r,onSizesChange:n,...s}=e,a=Le(rt,e.__scopeScrollArea),[i,c]=f.useState(),l=f.useRef(null),u=G(t,l,a.onScrollbarYChange);return f.useEffect(()=>{l.current&&c(getComputedStyle(l.current))},[l]),o.jsx(nf,{"data-orientation":"vertical",...s,ref:u,sizes:r,style:{top:0,right:a.dir==="ltr"?0:void 0,left:a.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ls(r)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,m)=>{if(a.viewport){const h=a.viewport.scrollTop+d.deltaY;e.onWheelScroll(h),lf(h,m)&&d.preventDefault()}},onResize:()=>{l.current&&a.viewport&&i&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:An(i.paddingTop),paddingEnd:An(i.paddingBottom)}})}})}),[eN,rf]=Ju(rt),nf=f.forwardRef((e,t)=>{const{__scopeScrollArea:r,sizes:n,hasThumb:s,onThumbChange:a,onThumbPointerUp:i,onThumbPointerDown:c,onThumbPositionChange:l,onDragScroll:u,onWheelScroll:d,onResize:m,...h}=e,x=Le(rt,r),[g,p]=f.useState(null),b=G(t,N=>p(N)),v=f.useRef(null),y=f.useRef(""),w=x.viewport,S=n.content-n.viewport,j=pe(d),D=pe(l),E=ds(m,10);function T(N){if(v.current){const _=N.clientX-v.current.left,I=N.clientY-v.current.top;u({x:_,y:I})}}return f.useEffect(()=>{const N=_=>{const I=_.target;g?.contains(I)&&j(_,S)};return document.addEventListener("wheel",N,{passive:!1}),()=>document.removeEventListener("wheel",N,{passive:!1})},[w,g,S,j]),f.useEffect(D,[n,D]),Qt(g,E),Qt(x.content,E),o.jsx(eN,{scope:r,scrollbar:g,hasThumb:s,onThumbChange:pe(a),onThumbPointerUp:pe(i),onThumbPositionChange:D,onThumbPointerDown:pe(c),children:o.jsx(U.div,{...h,ref:b,style:{position:"absolute",...h.style},onPointerDown:P(e.onPointerDown,N=>{N.button===0&&(N.target.setPointerCapture(N.pointerId),v.current=g.getBoundingClientRect(),y.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",x.viewport&&(x.viewport.style.scrollBehavior="auto"),T(N))}),onPointerMove:P(e.onPointerMove,T),onPointerUp:P(e.onPointerUp,N=>{const _=N.target;_.hasPointerCapture(N.pointerId)&&_.releasePointerCapture(N.pointerId),document.body.style.webkitUserSelect=y.current,x.viewport&&(x.viewport.style.scrollBehavior=""),v.current=null})})})}),_n="ScrollAreaThumb",sf=f.forwardRef((e,t)=>{const{forceMount:r,...n}=e,s=rf(_n,e.__scopeScrollArea);return o.jsx(xe,{present:r||s.hasThumb,children:o.jsx(tN,{ref:t,...n})})}),tN=f.forwardRef((e,t)=>{const{__scopeScrollArea:r,style:n,...s}=e,a=Le(_n,r),i=rf(_n,r),{onThumbPositionChange:c}=i,l=G(t,m=>i.onThumbChange(m)),u=f.useRef(void 0),d=ds(()=>{u.current&&(u.current(),u.current=void 0)},100);return f.useEffect(()=>{const m=a.viewport;if(m){const h=()=>{if(d(),!u.current){const x=sN(m,c);u.current=x,c()}};return c(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[a.viewport,d,c]),o.jsx(U.div,{"data-state":i.hasThumb?"visible":"hidden",...s,ref:l,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...n},onPointerDownCapture:P(e.onPointerDownCapture,m=>{const x=m.target.getBoundingClientRect(),g=m.clientX-x.left,p=m.clientY-x.top;i.onThumbPointerDown({x:g,y:p})}),onPointerUp:P(e.onPointerUp,i.onThumbPointerUp)})});sf.displayName=_n;var _a="ScrollAreaCorner",of=f.forwardRef((e,t)=>{const r=Le(_a,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return r.type!=="scroll"&&n?o.jsx(rN,{...e,ref:t}):null});of.displayName=_a;var rN=f.forwardRef((e,t)=>{const{__scopeScrollArea:r,...n}=e,s=Le(_a,r),[a,i]=f.useState(0),[c,l]=f.useState(0),u=!!(a&&c);return Qt(s.scrollbarX,()=>{const d=s.scrollbarX?.offsetHeight||0;s.onCornerHeightChange(d),l(d)}),Qt(s.scrollbarY,()=>{const d=s.scrollbarY?.offsetWidth||0;s.onCornerWidthChange(d),i(d)}),u?o.jsx(U.div,{...n,ref:t,style:{width:a,height:c,position:"absolute",right:s.dir==="ltr"?0:void 0,left:s.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function An(e){return e?parseInt(e,10):0}function af(e,t){const r=e/t;return isNaN(r)?0:r}function ls(e){const t=af(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,n=(e.scrollbar.size-r)*t;return Math.max(n,18)}function nN(e,t,r,n="ltr"){const s=ls(r),a=s/2,i=t||a,c=s-i,l=r.scrollbar.paddingStart+i,u=r.scrollbar.size-r.scrollbar.paddingEnd-c,d=r.content-r.viewport,m=n==="ltr"?[0,d]:[d*-1,0];return cf([l,u],m)(e)}function Xi(e,t,r="ltr"){const n=ls(t),s=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-s,i=t.content-t.viewport,c=a-n,l=r==="ltr"?[0,i]:[i*-1,0],u=To(e,l);return cf([0,i],[0,c])(u)}function cf(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}function lf(e,t){return e>0&&e<t}var sN=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function s(){const a={left:e.scrollLeft,top:e.scrollTop},i=r.left!==a.left,c=r.top!==a.top;(i||c)&&t(),r=a,n=window.requestAnimationFrame(s)}(),()=>window.cancelAnimationFrame(n)};function ds(e,t){const r=pe(e),n=f.useRef(0);return f.useEffect(()=>()=>window.clearTimeout(n.current),[]),f.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function Qt(e,t){const r=pe(t);fe(()=>{let n=0;if(e){const s=new ResizeObserver(()=>{cancelAnimationFrame(n),n=window.requestAnimationFrame(r)});return s.observe(e),()=>{window.cancelAnimationFrame(n),s.unobserve(e)}}},[e,r])}var df=Qu,oN=ef,aN=of;const Eo=f.forwardRef(({className:e,children:t,...r},n)=>o.jsxs(df,{ref:n,className:B("relative overflow-hidden",e),...r,children:[o.jsx(oN,{className:"h-full w-full rounded-[inherit]",children:t}),o.jsx(uf,{}),o.jsx(aN,{})]}));Eo.displayName=df.displayName;const uf=f.forwardRef(({className:e,orientation:t="vertical",...r},n)=>o.jsx(Ea,{ref:n,orientation:t,className:B("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...r,children:o.jsx(sf,{className:"relative flex-1 rounded-full bg-border"})}));uf.displayName=Ea.displayName;const iN=({grievance:e,onFileRemove:t,onFilePreview:r,onFileUpload:n})=>{const[s,a]=f.useState(null),[i,c]=f.useState(""),[l,u]=f.useState(!1),[d,m]=f.useState(!1),[h,x]=f.useState(!1),[g,p]=f.useState([]);if(f.useEffect(()=>{(async()=>{try{if(window.mammoth)u(!0);else{const _=document.createElement("script");_.src="https://cdn.jsdelivr.net/npm/mammoth@1.4.21/mammoth.browser.min.js",_.onload=()=>u(!0),document.head.appendChild(_)}}catch(_){console.error("Failed to load document libraries:",_),u(!0)}})()},[]),!e)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Ue,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"No grievance data available"})]})});const b=N=>{t&&t(N.filename||N.id)},v=N=>{a(N),r&&r(N)},y=N=>N.startsWith("image/")?o.jsx(Em,{className:"h-8 w-8"}):N.startsWith("video/")?o.jsx(Rm,{className:"h-8 w-8"}):N.startsWith("audio/")?o.jsx(_m,{className:"h-8 w-8"}):N.includes("pdf")?o.jsx(Ue,{className:"h-8 w-8"}):o.jsx(Am,{className:"h-8 w-8"}),w=async N=>{if(!N.length)return;x(!0),p([]);const _=[],I=[];for(const $ of N){const V=pd($);if(V.isValid)try{const F=$.type.startsWith("image/")?await dv($,1920,1080,.8):$;_.push(F)}catch(F){console.warn("Image compression failed, using original file:",F),_.push($)}else I.push(`${$.name}: ${V.error}`)}if(I.length>0&&_.length===0){p(I),x(!1);return}const H=e?.attachments?.length||0;if(H+_.length>10){I.push(`Maximum 10 files allowed. Current: ${H}, Trying to add: ${_.length}`),p(I),x(!1);return}let A=0;for(const $ of _)try{n&&(await n($)?A++:I.push(`Failed to upload ${$.name}`))}catch(V){I.push(`Error uploading ${$.name}: ${V instanceof Error?V.message:"Unknown error"}`)}p(I),x(!1),A>0&&console.log(`Successfully uploaded ${A} file(s)`)},S=N=>{N.preventDefault(),m(!0)},j=N=>{N.preventDefault(),m(!1)},D=N=>{N.preventDefault()},E=N=>{N.preventDefault(),m(!1);const _=Array.from(N.dataTransfer.files);w(_)},T=N=>{const _=Array.from(N.target.files||[]);w(_),N.target.value=""};return o.jsxs("div",{className:"space-y-6 p-6 bg-background/50 dark:bg-[#0D0D0D]/50 min-h-full",children:[o.jsxs("div",{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[o.jsx(Ue,{className:"h-4 w-4 text-primary"}),o.jsxs("h3",{className:"font-medium text-sm",children:["Attachments (",e.attachments?.length||0,")"]})]}),o.jsx("div",{className:"max-h-[400px] overflow-y-auto custom-scrollbar",children:e.attachments&&e.attachments.length>0?o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(X,{type:"button",variant:"outline",size:"sm",onClick:()=>document.getElementById("file-upload-input")?.click(),disabled:h,className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 shadow-sm transition-colors",children:h?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-primary mr-2"}),"Uploading..."]}):o.jsxs(o.Fragment,{children:[o.jsx(ys,{className:"h-3 w-3 mr-2"}),"Add More Files"]})}),o.jsx("input",{type:"file",multiple:!0,accept:"image/*,video/*,audio/*,.pdf,.docx,.txt,.ppt,.pptx,.xls,.xlsx",onChange:T,className:"hidden",id:"file-upload-input",disabled:h})]}),g.length>0&&o.jsxs("div",{className:"flex-1 ml-4 p-2 bg-destructive/10 border border-destructive/20 rounded text-xs",children:[o.jsx("span",{className:"text-destructive font-medium",children:"Errors: "}),o.jsx("span",{className:"text-destructive",children:g.join(", ")})]})]}),o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:e.attachments.map((N,_)=>o.jsx(qe.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:_*.1},whileHover:{scale:1.02},whileTap:{scale:.98},children:o.jsxs(ae,{className:"group relative overflow-hidden bg-gradient-to-br from-background/80 to-background/60 dark:from-[#0D0D0D]/80 dark:to-[#0D0D0D]/60 border-border dark:border-gray-800 hover:border-primary/30 transition-all duration-300 cursor-pointer shadow-sm hover:shadow-lg",onClick:()=>v({id:N.filename,name:No(N.originalName),originalName:N.originalName,type:N.mimetype,size:N.size,uploadedAt:N.uploadedAt,url:`/uploads/grievance-attachments/${N.filename}`}),children:[o.jsx("div",{className:"absolute top-2 right-2 z-10",children:o.jsx(q,{variant:"secondary",className:"text-xs bg-background/80 dark:bg-[#0D0D0D]/80 backdrop-blur-sm",children:Li(N.mimetype,N.originalName)})}),o.jsx("div",{className:"absolute top-2 left-2 z-10",children:o.jsx(X,{variant:"destructive",size:"sm",className:"h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200",onClick:I=>{I.stopPropagation(),b(N)},children:o.jsx(Tm,{className:"h-3 w-3"})})}),o.jsxs(de,{className:"p-4",children:[o.jsx("div",{className:"flex items-center justify-center mb-3 h-20",children:N.mimetype.startsWith("image/")?o.jsxs("div",{className:"relative w-full h-full rounded-lg overflow-hidden bg-muted/30",children:[o.jsx("img",{src:`/uploads/grievance-attachments/${N.filename}`,alt:N.originalName,className:"w-full h-full object-cover",onError:I=>{const H=I.target;H.style.display="none",H.nextElementSibling?.classList.remove("hidden")}}),o.jsx("div",{className:"hidden w-full h-full flex items-center justify-center text-muted-foreground",children:y(N.mimetype)})]}):o.jsx("div",{className:"w-full h-full flex items-center justify-center text-muted-foreground bg-muted/30 rounded-lg",children:y(N.mimetype)})}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium truncate",title:N.originalName,children:N.originalName}),o.jsx("p",{className:"text-xs text-muted-foreground",children:Fi(N.size)})]}),o.jsxs("div",{className:"flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[o.jsxs(X,{variant:"outline",size:"sm",className:"flex-1 h-7 text-xs",onClick:I=>{I.stopPropagation(),v({id:N.filename,name:No(N.originalName),originalName:N.originalName,type:N.mimetype,size:N.size,uploadedAt:N.uploadedAt,url:`/uploads/grievance-attachments/${N.filename}`})},children:[o.jsx(On,{className:"h-3 w-3 mr-1"}),"View"]}),o.jsxs(X,{variant:"outline",size:"sm",className:"flex-1 h-7 text-xs",onClick:I=>{I.stopPropagation();const H=`/uploads/grievance-attachments/${N.filename}`;$i(H,N.originalName)},children:[o.jsx(Ys,{className:"h-3 w-3 mr-1"}),"Download"]})]}),o.jsxs("div",{className:"text-xs text-muted-foreground",children:["Uploaded: ",new Date(N.uploadedAt).toLocaleDateString()]})]})]})]})},_))})]}):o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${d?"border-primary bg-primary/10 scale-[1.02] shadow-lg":"border-border dark:border-gray-800 bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,onDragEnter:S,onDragLeave:j,onDragOver:D,onDrop:E,children:[o.jsx(ys,{className:`h-12 w-12 mx-auto mb-4 transition-colors ${d?"text-primary animate-bounce":"text-muted-foreground"}`}),o.jsx("p",{className:"text-foreground mb-2 font-medium",children:d?"Drop files here":"Drag and drop files here, or click to browse"}),o.jsxs("p",{className:"text-sm text-muted-foreground mb-4",children:["Supports: Images, Videos, Audio, PDFs, Documents",o.jsx("br",{}),o.jsx("span",{className:"text-xs",children:"(Max 10MB each, 10 files total)"})]}),o.jsx("input",{type:"file",multiple:!0,accept:"image/*,video/*,audio/*,.pdf,.docx,.txt,.ppt,.pptx,.xls,.xlsx",onChange:T,className:"hidden",id:"file-upload-input",disabled:h}),o.jsx(X,{type:"button",variant:"outline",onClick:()=>document.getElementById("file-upload-input")?.click(),disabled:h,className:"mb-4 bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 shadow-sm transition-colors",children:h?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"}),"Uploading..."]}):o.jsxs(o.Fragment,{children:[o.jsx(ys,{className:"h-4 w-4 mr-2"}),"Choose Files"]})}),g.length>0&&o.jsx("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg shadow-sm",children:o.jsxs("div",{className:"flex items-start gap-3",children:[o.jsx(br,{className:"h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("h4",{className:"text-sm font-medium text-red-800 dark:text-red-200 mb-2",children:"Upload Issues"}),o.jsx("ul",{className:"text-xs text-red-700 dark:text-red-300 space-y-1",children:g.map((N,_)=>o.jsxs("li",{className:"flex items-start gap-1",children:[o.jsx("span",{className:"text-red-500 mt-1",children:"•"}),o.jsx("span",{children:N})]},_))})]}),o.jsx(X,{variant:"ghost",size:"sm",onClick:()=>p([]),className:"text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1",children:o.jsx(Wt,{className:"h-4 w-4"})})]})})]}),o.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[o.jsx("div",{className:"w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-4",children:o.jsx(Ue,{className:"h-8 w-8 text-muted-foreground"})}),o.jsx("h3",{className:"text-lg font-medium text-muted-foreground mb-2",children:"No attachments yet"}),o.jsx("p",{className:"text-sm text-muted-foreground max-w-sm",children:"Upload files using the area above. They will appear here once uploaded."})]})]})})]}),s&&o.jsx(pr,{open:!!s,onOpenChange:()=>a(null),children:o.jsxs(Ut,{className:"max-w-4xl max-h-[90vh] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[o.jsx(ku,{children:o.jsxs(Bt,{className:"flex items-center gap-2 text-foreground",children:[y(s.type),o.jsx("span",{className:"truncate",children:s.name}),o.jsx(q,{variant:"secondary",className:"ml-auto",children:Li(s.type,s.name)})]})}),o.jsxs("div",{className:"flex items-center justify-center p-4 min-h-[400px] max-h-[60vh] overflow-auto",children:[s.type.startsWith("image/")&&o.jsx("img",{src:s.url,alt:s.name,className:"max-w-full max-h-[60vh] object-contain rounded-lg",onError:N=>{const _=N.target;_.style.display="none";const I=_.nextElementSibling;I&&(I.style.display="flex")}}),s.type.startsWith("video/")&&o.jsx("video",{src:s.url,controls:!0,className:"max-w-full max-h-[60vh] rounded-lg",children:"Your browser does not support the video tag."}),(s.type.startsWith("audio/")||s.type.includes("mp3")||s.type.includes("wav")||s.type.includes("ogg")||s.type.includes("m4a")||s.type.includes("flac")||s.type.includes("aac"))&&o.jsx("div",{className:"w-full max-w-md",children:o.jsx("audio",{src:s.url,controls:!0,className:"w-full",children:"Your browser does not support the audio tag."})}),s.type.includes("pdf")&&o.jsxs("object",{data:s.url,type:"application/pdf",className:"w-full h-[70vh]",children:[o.jsx("embed",{src:s.url,type:"application/pdf",className:"w-full h-[70vh]"}),o.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-muted-foreground",children:[o.jsx(Ue,{className:"h-12 w-12 mb-4"}),o.jsx("p",{children:"PDF preview not supported in this browser."}),o.jsxs(X,{onClick:()=>window.open(s.url,"_blank"),className:"mt-4",children:[o.jsx(vr,{className:"h-4 w-4 mr-2"}),"Open in New Tab"]})]})]}),(s.type.includes("txt")||s.type.includes("text"))&&o.jsx(Eo,{className:"w-full h-[70vh]",children:o.jsx("pre",{className:"text-foreground text-sm font-mono whitespace-pre-wrap p-4",children:(fetch(s.url).then(N=>N.text()).then(N=>c(N)).catch(()=>c("Error loading text file")),i)})}),(s.type.includes("docx")||s.name.toLowerCase().endsWith(".docx"))&&o.jsx(Eo,{className:"w-full h-[70vh]",children:o.jsx("div",{className:"p-4 text-foreground text-sm",children:l?(fetch(s.url).then(N=>N.arrayBuffer()).then(async N=>{if(window.mammoth)try{const _=await window.mammoth.convertToHtml({arrayBuffer:N});c(_.value)}catch{c("Error reading DOCX file")}}).catch(()=>c("Error loading DOCX file")),o.jsx("div",{dangerouslySetInnerHTML:{__html:i},className:"text-foreground"})):o.jsx("div",{className:"text-muted-foreground text-center",children:"Loading DOCX viewer..."})})}),!s.type.startsWith("image/")&&!s.type.startsWith("video/")&&!s.type.startsWith("audio/")&&!s.type.includes("pdf")&&!s.type.includes("txt")&&!s.type.includes("text")&&!s.type.includes("docx")&&!s.name.toLowerCase().endsWith(".docx")&&o.jsxs("div",{className:"flex flex-col items-center justify-center text-muted-foreground",children:[y(s.type),o.jsx("p",{className:"mt-4 text-center",children:"Preview not available for this file type."}),o.jsxs("p",{className:"text-sm text-center mt-2",children:["File: ",s.name," (",Fi(s.size),")"]})]})]}),o.jsxs(ju,{className:"flex justify-between",children:[o.jsxs("div",{className:"flex gap-2",children:[o.jsxs(X,{variant:"outline",onClick:()=>window.open(s.url,"_blank"),children:[o.jsx(vr,{className:"h-4 w-4 mr-2"}),"Open in New Tab"]}),o.jsxs(X,{variant:"outline",onClick:()=>{$i(s.url,s.originalName)},children:[o.jsx(Ys,{className:"h-4 w-4 mr-2"}),"Download File"]})]}),o.jsxs(X,{variant:"outline",onClick:()=>a(null),children:[o.jsx(Wt,{className:"h-4 w-4 mr-2"}),"Close"]})]})]})})]})},cN=({grievance:e,onAddComment:t})=>{const[r,n]=f.useState("all"),[s,a]=f.useState("");if(!e)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(tn,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"No grievance data available"})]})});const i=async()=>{if(!(!s.trim()||s.length>500))try{await t(s.trim())&&a("")}catch(m){console.error("Failed to add comment:",m)}},l=(()=>{if(!e.comments)return[];switch(r){case"announcements":return e.comments.filter(m=>m.isInternal&&m.userId?.role==="admin");case"admin":return e.comments.filter(m=>m.userId?.role==="admin"&&!m.isInternal);case"community":return e.comments.filter(m=>m.userId?.role==="user");default:return e.comments}})(),u=m=>m.isInternal&&m.userId?.role==="admin"?"border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20":m.userId?.role==="admin"?"border-l-4 border-l-purple-500 bg-purple-50/50 dark:bg-purple-950/20":"border-l-4 border-l-green-500 bg-green-50/50 dark:bg-green-950/20",d=m=>m.isInternal&&m.userId?.role==="admin"?o.jsx(Fa,{className:"h-4 w-4 text-blue-600"}):m.userId?.role==="admin"?o.jsx($a,{className:"h-4 w-4 text-purple-600"}):o.jsx(_e,{className:"h-4 w-4 text-green-600"});return o.jsx("div",{className:"p-6 bg-background/50 dark:bg-[#0D0D0D]/50 min-h-full",children:o.jsxs("div",{className:"flex gap-6 h-[500px]",children:[o.jsx("div",{className:"w-48 flex-shrink-0",children:o.jsxs("div",{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border border-border/30 rounded-lg p-4 shadow-sm",children:[o.jsx("h3",{className:"text-sm font-semibold text-foreground mb-3",children:"Filter Comments"}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("button",{onClick:()=>n("announcements"),className:`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${r==="announcements"?"bg-blue-100 text-blue-800 border border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800":"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,children:[o.jsx(Fa,{className:"h-4 w-4"}),o.jsx("span",{children:"Announcements"}),o.jsx(q,{variant:"secondary",className:"ml-auto text-xs",children:e.comments?.filter(m=>m.isInternal&&m.userId?.role==="admin")?.length||0})]}),o.jsxs("button",{onClick:()=>n("admin"),className:`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${r==="admin"?"bg-purple-100 text-purple-800 border border-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-800":"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,children:[o.jsx($a,{className:"h-4 w-4"}),o.jsx("span",{children:"Admin Messages"}),o.jsx(q,{variant:"secondary",className:"ml-auto text-xs",children:e.comments?.filter(m=>m.userId?.role==="admin"&&!m.isInternal)?.length||0})]}),o.jsxs("button",{onClick:()=>n("community"),className:`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${r==="community"?"bg-green-100 text-green-800 border border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800":"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,children:[o.jsx(Dm,{className:"h-4 w-4"}),o.jsx("span",{children:"Community"}),o.jsx(q,{variant:"secondary",className:"ml-auto text-xs",children:e.comments?.filter(m=>m.userId?.role==="user")?.length||0})]}),o.jsxs("button",{onClick:()=>n("all"),className:`w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${r==="all"?"bg-gray-100 text-gray-800 border border-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700":"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,children:[o.jsx(tn,{className:"h-4 w-4"}),o.jsx("span",{children:"All Comments"}),o.jsx(q,{variant:"secondary",className:"ml-auto text-xs",children:e.comments?.length||0})]})]})]})}),o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsx("div",{className:"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent bg-background/50 dark:bg-[#0D0D0D]/50 border border-border/30 rounded-lg p-4 shadow-sm",children:l.length>0?o.jsx("div",{className:"space-y-3",children:l.map((m,h)=>o.jsx(ae,{className:`${u(m)} shadow-sm`,children:o.jsx(de,{className:"p-3",children:o.jsxs("div",{className:"flex items-start gap-3",children:[o.jsx("div",{className:"flex-shrink-0",children:d(m)}),o.jsxs("div",{className:"flex-1 min-w-0",children:[o.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[o.jsx("span",{className:"font-medium text-sm",children:m.userId?.fullName||"Anonymous"}),m.userId?.role==="admin"&&o.jsxs(q,{variant:"outline",className:"text-xs",children:[o.jsx(qt,{className:"h-3 w-3 mr-1"}),"Admin"]}),m.isInternal&&o.jsx(q,{variant:"secondary",className:"text-xs",children:"Internal"})]}),o.jsx("p",{className:"text-sm text-foreground mb-2 leading-relaxed",children:m.message}),o.jsxs("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[o.jsx(Gt,{className:"h-3 w-3"}),o.jsx("span",{children:Ye(m.createdAt)})]})]})]})})},h))}):o.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[o.jsx("div",{className:"w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center mb-4",children:o.jsx(tn,{className:"h-8 w-8 text-muted-foreground"})}),o.jsx("h3",{className:"text-lg font-medium text-muted-foreground mb-2",children:"No comments found"}),o.jsx("p",{className:"text-sm text-muted-foreground max-w-sm",children:r==="all"?"No comments have been added to this grievance yet.":`No ${r} comments found. Try selecting a different category.`})]})}),typeof t=="function"&&o.jsx("div",{className:"mt-3 p-3 bg-background/50 dark:bg-[#0D0D0D]/50 border border-border dark:border-gray-800 rounded-lg",children:o.jsxs("div",{className:"space-y-3",children:[o.jsx(xr,{placeholder:"Add a comment...",value:s,onChange:m=>a(m.target.value),className:"min-h-[80px] resize-none bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70 transition-colors"}),o.jsxs("div",{className:"flex justify-between items-center",children:[o.jsxs("div",{className:"text-xs text-muted-foreground",children:[s.length,"/500 characters"]}),o.jsxs(X,{onClick:i,disabled:!s.trim()||s.length>500,className:"bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm transition-colors",size:"sm",children:[o.jsx(yr,{className:"h-4 w-4 mr-2"}),"Add Comment"]})]})]})})]})]})})};function Aa(e){const t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var lN=[" ","Enter","ArrowUp","ArrowDown"],dN=[" ","Enter"],_t="Select",[us,fs,uN]=fa(_t),[lr,pk]=Ne(_t,[uN,gt]),ms=gt(),[fN,bt]=lr(_t),[mN,hN]=lr(_t),ff=e=>{const{__scopeSelect:t,children:r,open:n,defaultOpen:s,onOpenChange:a,value:i,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:m,disabled:h,required:x,form:g}=e,p=ms(t),[b,v]=f.useState(null),[y,w]=f.useState(null),[S,j]=f.useState(!1),D=ir(u),[E,T]=Qe({prop:n,defaultProp:s??!1,onChange:a,caller:_t}),[N,_]=Qe({prop:i,defaultProp:c,onChange:l,caller:_t}),I=f.useRef(null),H=b?g||!!b.closest("form"):!0,[A,$]=f.useState(new Set),V=Array.from(A).map(F=>F.props.value).join(";");return o.jsx(Vn,{...p,children:o.jsxs(fN,{required:x,scope:t,trigger:b,onTriggerChange:v,valueNode:y,onValueNodeChange:w,valueNodeHasChildren:S,onValueNodeHasChildrenChange:j,contentId:Ie(),value:N,onValueChange:_,open:E,onOpenChange:T,dir:D,triggerPointerDownPosRef:I,disabled:h,children:[o.jsx(us.Provider,{scope:t,children:o.jsx(mN,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(F=>{$(z=>new Set(z).add(F))},[]),onNativeOptionRemove:f.useCallback(F=>{$(z=>{const C=new Set(z);return C.delete(F),C})},[]),children:r})}),H?o.jsxs(Mf,{"aria-hidden":!0,required:x,tabIndex:-1,name:d,autoComplete:m,value:N,onChange:F=>_(F.target.value),disabled:h,form:g,children:[N===void 0?o.jsx("option",{value:""}):null,Array.from(A)]},V):null]})})};ff.displayName=_t;var mf="SelectTrigger",hf=f.forwardRef((e,t)=>{const{__scopeSelect:r,disabled:n=!1,...s}=e,a=ms(r),i=bt(mf,r),c=i.disabled||n,l=G(t,i.onTriggerChange),u=fs(r),d=f.useRef("touch"),[m,h,x]=Ff(p=>{const b=u().filter(w=>!w.disabled),v=b.find(w=>w.value===i.value),y=$f(b,p,v);y!==void 0&&i.onValueChange(y.value)}),g=p=>{c||(i.onOpenChange(!0),x()),p&&(i.triggerPointerDownPosRef.current={x:Math.round(p.pageX),y:Math.round(p.pageY)})};return o.jsx(Or,{asChild:!0,...a,children:o.jsx(U.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":Lf(i.value)?"":void 0,...s,ref:l,onClick:P(s.onClick,p=>{p.currentTarget.focus(),d.current!=="mouse"&&g(p)}),onPointerDown:P(s.onPointerDown,p=>{d.current=p.pointerType;const b=p.target;b.hasPointerCapture(p.pointerId)&&b.releasePointerCapture(p.pointerId),p.button===0&&p.ctrlKey===!1&&p.pointerType==="mouse"&&(g(p),p.preventDefault())}),onKeyDown:P(s.onKeyDown,p=>{const b=m.current!=="";!(p.ctrlKey||p.altKey||p.metaKey)&&p.key.length===1&&h(p.key),!(b&&p.key===" ")&&lN.includes(p.key)&&(g(),p.preventDefault())})})})});hf.displayName=mf;var pf="SelectValue",gf=f.forwardRef((e,t)=>{const{__scopeSelect:r,className:n,style:s,children:a,placeholder:i="",...c}=e,l=bt(pf,r),{onValueNodeHasChildrenChange:u}=l,d=a!==void 0,m=G(t,l.onValueNodeChange);return fe(()=>{u(d)},[u,d]),o.jsx(U.span,{...c,ref:m,style:{pointerEvents:"none"},children:Lf(l.value)?o.jsx(o.Fragment,{children:i}):a})});gf.displayName=pf;var pN="SelectIcon",xf=f.forwardRef((e,t)=>{const{__scopeSelect:r,children:n,...s}=e;return o.jsx(U.span,{"aria-hidden":!0,...s,ref:t,children:n||"▼"})});xf.displayName=pN;var gN="SelectPortal",bf=e=>o.jsx(Ar,{asChild:!0,...e});bf.displayName=gN;var At="SelectContent",vf=f.forwardRef((e,t)=>{const r=bt(At,e.__scopeSelect),[n,s]=f.useState();if(fe(()=>{s(new DocumentFragment)},[]),!r.open){const a=n;return a?Rr.createPortal(o.jsx(yf,{scope:e.__scopeSelect,children:o.jsx(us.Slot,{scope:e.__scopeSelect,children:o.jsx("div",{children:e.children})})}),a):null}return o.jsx(wf,{...e,ref:t})});vf.displayName=At;var Fe=10,[yf,vt]=lr(At),xN="SelectContentImpl",bN=mt("SelectContent.RemoveScroll"),wf=f.forwardRef((e,t)=>{const{__scopeSelect:r,position:n="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:a,onPointerDownOutside:i,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:x,sticky:g,hideWhenDetached:p,avoidCollisions:b,...v}=e,y=bt(At,r),[w,S]=f.useState(null),[j,D]=f.useState(null),E=G(t,O=>S(O)),[T,N]=f.useState(null),[_,I]=f.useState(null),H=fs(r),[A,$]=f.useState(!1),V=f.useRef(!1);f.useEffect(()=>{if(w)return Fn(w)},[w]),Mn();const F=f.useCallback(O=>{const[ee,...le]=H().map(oe=>oe.ref.current),[Z]=le.slice(-1),te=document.activeElement;for(const oe of O)if(oe===te||(oe?.scrollIntoView({block:"nearest"}),oe===ee&&j&&(j.scrollTop=0),oe===Z&&j&&(j.scrollTop=j.scrollHeight),oe?.focus(),document.activeElement!==te))return},[H,j]),z=f.useCallback(()=>F([T,w]),[F,T,w]);f.useEffect(()=>{A&&z()},[A,z]);const{onOpenChange:C,triggerPointerDownPosRef:R}=y;f.useEffect(()=>{if(w){let O={x:0,y:0};const ee=Z=>{O={x:Math.abs(Math.round(Z.pageX)-(R.current?.x??0)),y:Math.abs(Math.round(Z.pageY)-(R.current?.y??0))}},le=Z=>{O.x<=10&&O.y<=10?Z.preventDefault():w.contains(Z.target)||C(!1),document.removeEventListener("pointermove",ee),R.current=null};return R.current!==null&&(document.addEventListener("pointermove",ee),document.addEventListener("pointerup",le,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ee),document.removeEventListener("pointerup",le,{capture:!0})}}},[w,C,R]),f.useEffect(()=>{const O=()=>C(!1);return window.addEventListener("blur",O),window.addEventListener("resize",O),()=>{window.removeEventListener("blur",O),window.removeEventListener("resize",O)}},[C]);const[K,he]=Ff(O=>{const ee=H().filter(te=>!te.disabled),le=ee.find(te=>te.ref.current===document.activeElement),Z=$f(ee,O,le);Z&&setTimeout(()=>Z.ref.current.focus())}),Se=f.useCallback((O,ee,le)=>{const Z=!V.current&&!le;(y.value!==void 0&&y.value===ee||Z)&&(N(O),Z&&(V.current=!0))},[y.value]),se=f.useCallback(()=>w?.focus(),[w]),re=f.useCallback((O,ee,le)=>{const Z=!V.current&&!le;(y.value!==void 0&&y.value===ee||Z)&&I(O)},[y.value]),je=n==="popper"?Ro:Nf,be=je===Ro?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:m,collisionBoundary:h,collisionPadding:x,sticky:g,hideWhenDetached:p,avoidCollisions:b}:{};return o.jsx(yf,{scope:r,content:w,viewport:j,onViewportChange:D,itemRefCallback:Se,selectedItem:T,onItemLeave:se,itemTextRefCallback:re,focusSelectedItem:z,selectedItemText:_,position:n,isPositioned:A,searchRef:K,children:o.jsx(Dr,{as:bN,allowPinchZoom:!0,children:o.jsx(_r,{asChild:!0,trapped:y.open,onMountAutoFocus:O=>{O.preventDefault()},onUnmountAutoFocus:P(s,O=>{y.trigger?.focus({preventScroll:!0}),O.preventDefault()}),children:o.jsx(tr,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:O=>O.preventDefault(),onDismiss:()=>y.onOpenChange(!1),children:o.jsx(je,{role:"listbox",id:y.contentId,"data-state":y.open?"open":"closed",dir:y.dir,onContextMenu:O=>O.preventDefault(),...v,...be,onPlaced:()=>$(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:P(v.onKeyDown,O=>{const ee=O.ctrlKey||O.altKey||O.metaKey;if(O.key==="Tab"&&O.preventDefault(),!ee&&O.key.length===1&&he(O.key),["ArrowUp","ArrowDown","Home","End"].includes(O.key)){let Z=H().filter(te=>!te.disabled).map(te=>te.ref.current);if(["ArrowUp","End"].includes(O.key)&&(Z=Z.slice().reverse()),["ArrowUp","ArrowDown"].includes(O.key)){const te=O.target,oe=Z.indexOf(te);Z=Z.slice(oe+1)}setTimeout(()=>F(Z)),O.preventDefault()}})})})})})})});wf.displayName=xN;var vN="SelectItemAlignedPosition",Nf=f.forwardRef((e,t)=>{const{__scopeSelect:r,onPlaced:n,...s}=e,a=bt(At,r),i=vt(At,r),[c,l]=f.useState(null),[u,d]=f.useState(null),m=G(t,E=>d(E)),h=fs(r),x=f.useRef(!1),g=f.useRef(!0),{viewport:p,selectedItem:b,selectedItemText:v,focusSelectedItem:y}=i,w=f.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&u&&p&&b&&v){const E=a.trigger.getBoundingClientRect(),T=u.getBoundingClientRect(),N=a.valueNode.getBoundingClientRect(),_=v.getBoundingClientRect();if(a.dir!=="rtl"){const te=_.left-T.left,oe=N.left-te,De=E.left-oe,yt=E.width+De,gs=Math.max(yt,T.width),xs=window.innerWidth-Fe,bs=To(oe,[Fe,Math.max(Fe,xs-gs)]);c.style.minWidth=yt+"px",c.style.left=bs+"px"}else{const te=T.right-_.right,oe=window.innerWidth-N.right-te,De=window.innerWidth-E.right-oe,yt=E.width+De,gs=Math.max(yt,T.width),xs=window.innerWidth-Fe,bs=To(oe,[Fe,Math.max(Fe,xs-gs)]);c.style.minWidth=yt+"px",c.style.right=bs+"px"}const I=h(),H=window.innerHeight-Fe*2,A=p.scrollHeight,$=window.getComputedStyle(u),V=parseInt($.borderTopWidth,10),F=parseInt($.paddingTop,10),z=parseInt($.borderBottomWidth,10),C=parseInt($.paddingBottom,10),R=V+F+A+C+z,K=Math.min(b.offsetHeight*5,R),he=window.getComputedStyle(p),Se=parseInt(he.paddingTop,10),se=parseInt(he.paddingBottom,10),re=E.top+E.height/2-Fe,je=H-re,be=b.offsetHeight/2,O=b.offsetTop+be,ee=V+F+O,le=R-ee;if(ee<=re){const te=I.length>0&&b===I[I.length-1].ref.current;c.style.bottom="0px";const oe=u.clientHeight-p.offsetTop-p.offsetHeight,De=Math.max(je,be+(te?se:0)+oe+z),yt=ee+De;c.style.height=yt+"px"}else{const te=I.length>0&&b===I[0].ref.current;c.style.top="0px";const De=Math.max(re,V+p.offsetTop+(te?Se:0)+be)+le;c.style.height=De+"px",p.scrollTop=ee-re+p.offsetTop}c.style.margin=`${Fe}px 0`,c.style.minHeight=K+"px",c.style.maxHeight=H+"px",n?.(),requestAnimationFrame(()=>x.current=!0)}},[h,a.trigger,a.valueNode,c,u,p,b,v,a.dir,n]);fe(()=>w(),[w]);const[S,j]=f.useState();fe(()=>{u&&j(window.getComputedStyle(u).zIndex)},[u]);const D=f.useCallback(E=>{E&&g.current===!0&&(w(),y?.(),g.current=!1)},[w,y]);return o.jsx(wN,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:x,onScrollButtonChange:D,children:o.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:o.jsx(U.div,{...s,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});Nf.displayName=vN;var yN="SelectPopperPosition",Ro=f.forwardRef((e,t)=>{const{__scopeSelect:r,align:n="start",collisionPadding:s=Fe,...a}=e,i=ms(r);return o.jsx(zn,{...i,...a,ref:t,align:n,collisionPadding:s,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Ro.displayName=yN;var[wN,Da]=lr(At,{}),_o="SelectViewport",Sf=f.forwardRef((e,t)=>{const{__scopeSelect:r,nonce:n,...s}=e,a=vt(_o,r),i=Da(_o,r),c=G(t,a.onViewportChange),l=f.useRef(0);return o.jsxs(o.Fragment,{children:[o.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:n}),o.jsx(us.Slot,{scope:r,children:o.jsx(U.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:P(s.onScroll,u=>{const d=u.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:h}=i;if(h?.current&&m){const x=Math.abs(l.current-d.scrollTop);if(x>0){const g=window.innerHeight-Fe*2,p=parseFloat(m.style.minHeight),b=parseFloat(m.style.height),v=Math.max(p,b);if(v<g){const y=v+x,w=Math.min(g,y),S=y-w;m.style.height=w+"px",m.style.bottom==="0px"&&(d.scrollTop=S>0?S:0,m.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});Sf.displayName=_o;var kf="SelectGroup",[NN,SN]=lr(kf),kN=f.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,s=Ie();return o.jsx(NN,{scope:r,id:s,children:o.jsx(U.div,{role:"group","aria-labelledby":s,...n,ref:t})})});kN.displayName=kf;var jf="SelectLabel",Cf=f.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,s=SN(jf,r);return o.jsx(U.div,{id:s.id,...n,ref:t})});Cf.displayName=jf;var Dn="SelectItem",[jN,Tf]=lr(Dn),Ef=f.forwardRef((e,t)=>{const{__scopeSelect:r,value:n,disabled:s=!1,textValue:a,...i}=e,c=bt(Dn,r),l=vt(Dn,r),u=c.value===n,[d,m]=f.useState(a??""),[h,x]=f.useState(!1),g=G(t,y=>l.itemRefCallback?.(y,n,s)),p=Ie(),b=f.useRef("touch"),v=()=>{s||(c.onValueChange(n),c.onOpenChange(!1))};if(n==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return o.jsx(jN,{scope:r,value:n,disabled:s,textId:p,isSelected:u,onItemTextChange:f.useCallback(y=>{m(w=>w||(y?.textContent??"").trim())},[]),children:o.jsx(us.ItemSlot,{scope:r,value:n,disabled:s,textValue:d,children:o.jsx(U.div,{role:"option","aria-labelledby":p,"data-highlighted":h?"":void 0,"aria-selected":u&&h,"data-state":u?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...i,ref:g,onFocus:P(i.onFocus,()=>x(!0)),onBlur:P(i.onBlur,()=>x(!1)),onClick:P(i.onClick,()=>{b.current!=="mouse"&&v()}),onPointerUp:P(i.onPointerUp,()=>{b.current==="mouse"&&v()}),onPointerDown:P(i.onPointerDown,y=>{b.current=y.pointerType}),onPointerMove:P(i.onPointerMove,y=>{b.current=y.pointerType,s?l.onItemLeave?.():b.current==="mouse"&&y.currentTarget.focus({preventScroll:!0})}),onPointerLeave:P(i.onPointerLeave,y=>{y.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:P(i.onKeyDown,y=>{l.searchRef?.current!==""&&y.key===" "||(dN.includes(y.key)&&v(),y.key===" "&&y.preventDefault())})})})})});Ef.displayName=Dn;var gr="SelectItemText",Rf=f.forwardRef((e,t)=>{const{__scopeSelect:r,className:n,style:s,...a}=e,i=bt(gr,r),c=vt(gr,r),l=Tf(gr,r),u=hN(gr,r),[d,m]=f.useState(null),h=G(t,v=>m(v),l.onItemTextChange,v=>c.itemTextRefCallback?.(v,l.value,l.disabled)),x=d?.textContent,g=f.useMemo(()=>o.jsx("option",{value:l.value,disabled:l.disabled,children:x},l.value),[l.disabled,l.value,x]),{onNativeOptionAdd:p,onNativeOptionRemove:b}=u;return fe(()=>(p(g),()=>b(g)),[p,b,g]),o.jsxs(o.Fragment,{children:[o.jsx(U.span,{id:l.textId,...a,ref:h}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Rr.createPortal(a.children,i.valueNode):null]})});Rf.displayName=gr;var _f="SelectItemIndicator",Af=f.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e;return Tf(_f,r).isSelected?o.jsx(U.span,{"aria-hidden":!0,...n,ref:t}):null});Af.displayName=_f;var Ao="SelectScrollUpButton",Df=f.forwardRef((e,t)=>{const r=vt(Ao,e.__scopeSelect),n=Da(Ao,e.__scopeSelect),[s,a]=f.useState(!1),i=G(t,n.onScrollButtonChange);return fe(()=>{if(r.viewport&&r.isPositioned){let c=function(){const u=l.scrollTop>0;a(u)};const l=r.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[r.viewport,r.isPositioned]),s?o.jsx(Of,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=r;c&&l&&(c.scrollTop=c.scrollTop-l.offsetHeight)}}):null});Df.displayName=Ao;var Do="SelectScrollDownButton",Pf=f.forwardRef((e,t)=>{const r=vt(Do,e.__scopeSelect),n=Da(Do,e.__scopeSelect),[s,a]=f.useState(!1),i=G(t,n.onScrollButtonChange);return fe(()=>{if(r.viewport&&r.isPositioned){let c=function(){const u=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<u;a(d)};const l=r.viewport;return c(),l.addEventListener("scroll",c),()=>l.removeEventListener("scroll",c)}},[r.viewport,r.isPositioned]),s?o.jsx(Of,{...e,ref:i,onAutoScroll:()=>{const{viewport:c,selectedItem:l}=r;c&&l&&(c.scrollTop=c.scrollTop+l.offsetHeight)}}):null});Pf.displayName=Do;var Of=f.forwardRef((e,t)=>{const{__scopeSelect:r,onAutoScroll:n,...s}=e,a=vt("SelectScrollButton",r),i=f.useRef(null),c=fs(r),l=f.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return f.useEffect(()=>()=>l(),[l]),fe(()=>{c().find(d=>d.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[c]),o.jsx(U.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:P(s.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(n,50))}),onPointerMove:P(s.onPointerMove,()=>{a.onItemLeave?.(),i.current===null&&(i.current=window.setInterval(n,50))}),onPointerLeave:P(s.onPointerLeave,()=>{l()})})}),CN="SelectSeparator",If=f.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e;return o.jsx(U.div,{"aria-hidden":!0,...n,ref:t})});If.displayName=CN;var Po="SelectArrow",TN=f.forwardRef((e,t)=>{const{__scopeSelect:r,...n}=e,s=ms(r),a=bt(Po,r),i=vt(Po,r);return a.open&&i.position==="popper"?o.jsx(Wn,{...s,...n,ref:t}):null});TN.displayName=Po;var EN="SelectBubbleInput",Mf=f.forwardRef(({__scopeSelect:e,value:t,...r},n)=>{const s=f.useRef(null),a=G(n,s),i=Aa(t);return f.useEffect(()=>{const c=s.current;if(!c)return;const l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==t&&d){const m=new Event("change",{bubbles:!0});d.call(c,t),c.dispatchEvent(m)}},[i,t]),o.jsx(U.select,{...r,style:{...Nl,...r.style},ref:a,defaultValue:t})});Mf.displayName=EN;function Lf(e){return e===""||e===void 0}function Ff(e){const t=pe(e),r=f.useRef(""),n=f.useRef(0),s=f.useCallback(i=>{const c=r.current+i;t(c),function l(u){r.current=u,window.clearTimeout(n.current),u!==""&&(n.current=window.setTimeout(()=>l(""),1e3))}(c)},[t]),a=f.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,s,a]}function $f(e,t,r){const s=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,a=r?e.indexOf(r):-1;let i=RN(e,Math.max(a,0));s.length===1&&(i=i.filter(u=>u!==r));const l=i.find(u=>u.textValue.toLowerCase().startsWith(s.toLowerCase()));return l!==r?l:void 0}function RN(e,t){return e.map((r,n)=>e[(t+n)%e.length])}var _N=ff,Uf=hf,AN=gf,DN=xf,PN=bf,Bf=vf,ON=Sf,Hf=Cf,Vf=Ef,IN=Rf,MN=Af,zf=Df,Wf=Pf,Gf=If;const Ji=_N,Qi=AN,Oo=f.forwardRef(({className:e,children:t,...r},n)=>o.jsxs(Uf,{ref:n,className:B("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,o.jsx(DN,{asChild:!0,children:o.jsx(Ct,{className:"h-4 w-4 opacity-50"})})]}));Oo.displayName=Uf.displayName;const qf=f.forwardRef(({className:e,...t},r)=>o.jsx(zf,{ref:r,className:B("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(gn,{className:"h-4 w-4"})}));qf.displayName=zf.displayName;const Kf=f.forwardRef(({className:e,...t},r)=>o.jsx(Wf,{ref:r,className:B("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(Ct,{className:"h-4 w-4"})}));Kf.displayName=Wf.displayName;const Io=f.forwardRef(({className:e,children:t,position:r="popper",...n},s)=>o.jsx(PN,{children:o.jsxs(Bf,{ref:s,className:B("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[o.jsx(qf,{}),o.jsx(ON,{className:B("p-1",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),o.jsx(Kf,{})]})}));Io.displayName=Bf.displayName;const LN=f.forwardRef(({className:e,...t},r)=>o.jsx(Hf,{ref:r,className:B("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));LN.displayName=Hf.displayName;const pn=f.forwardRef(({className:e,children:t,...r},n)=>o.jsxs(Vf,{ref:n,className:B("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[o.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:o.jsx(MN,{children:o.jsx(sc,{className:"h-4 w-4"})})}),o.jsx(IN,{children:t})]}));pn.displayName=Vf.displayName;const FN=f.forwardRef(({className:e,...t},r)=>o.jsx(Gf,{ref:r,className:B("-mx-1 my-1 h-px bg-muted",e),...t}));FN.displayName=Gf.displayName;function $N(e){if(typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}Array(12).fill(0);let Mo=1;class UN{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{const r=this.subscribers.indexOf(t);this.subscribers.splice(r,1)}),this.publish=t=>{this.subscribers.forEach(r=>r(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var r;const{message:n,...s}=t,a=typeof t?.id=="number"||((r=t.id)==null?void 0:r.length)>0?t.id:Mo++,i=this.toasts.find(l=>l.id===a),c=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(a)&&this.dismissedToasts.delete(a),i?this.toasts=this.toasts.map(l=>l.id===a?(this.publish({...l,...t,id:a,title:n}),{...l,...t,id:a,dismissible:c,title:n}):l):this.addToast({title:n,...s,dismissible:c,id:a}),a},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(r=>r({id:t,dismiss:!0})))):this.toasts.forEach(r=>{this.subscribers.forEach(n=>n({id:r.id,dismiss:!0}))}),t),this.message=(t,r)=>this.create({...r,message:t}),this.error=(t,r)=>this.create({...r,message:t,type:"error"}),this.success=(t,r)=>this.create({...r,type:"success",message:t}),this.info=(t,r)=>this.create({...r,type:"info",message:t}),this.warning=(t,r)=>this.create({...r,type:"warning",message:t}),this.loading=(t,r)=>this.create({...r,type:"loading",message:t}),this.promise=(t,r)=>{if(!r)return;let n;r.loading!==void 0&&(n=this.create({...r,promise:t,type:"loading",message:r.loading,description:typeof r.description!="function"?r.description:void 0}));const s=Promise.resolve(t instanceof Function?t():t);let a=n!==void 0,i;const c=s.then(async u=>{if(i=["resolve",u],Ee.isValidElement(u))a=!1,this.create({id:n,type:"default",message:u});else if(HN(u)&&!u.ok){a=!1;const m=typeof r.error=="function"?await r.error(`HTTP error! status: ${u.status}`):r.error,h=typeof r.description=="function"?await r.description(`HTTP error! status: ${u.status}`):r.description,g=typeof m=="object"&&!Ee.isValidElement(m)?m:{message:m};this.create({id:n,type:"error",description:h,...g})}else if(u instanceof Error){a=!1;const m=typeof r.error=="function"?await r.error(u):r.error,h=typeof r.description=="function"?await r.description(u):r.description,g=typeof m=="object"&&!Ee.isValidElement(m)?m:{message:m};this.create({id:n,type:"error",description:h,...g})}else if(r.success!==void 0){a=!1;const m=typeof r.success=="function"?await r.success(u):r.success,h=typeof r.description=="function"?await r.description(u):r.description,g=typeof m=="object"&&!Ee.isValidElement(m)?m:{message:m};this.create({id:n,type:"success",description:h,...g})}}).catch(async u=>{if(i=["reject",u],r.error!==void 0){a=!1;const d=typeof r.error=="function"?await r.error(u):r.error,m=typeof r.description=="function"?await r.description(u):r.description,x=typeof d=="object"&&!Ee.isValidElement(d)?d:{message:d};this.create({id:n,type:"error",description:m,...x})}}).finally(()=>{a&&(this.dismiss(n),n=void 0),r.finally==null||r.finally.call(r)}),l=()=>new Promise((u,d)=>c.then(()=>i[0]==="reject"?d(i[1]):u(i[1])).catch(d));return typeof n!="string"&&typeof n!="number"?{unwrap:l}:Object.assign(n,{unwrap:l})},this.custom=(t,r)=>{const n=r?.id||Mo++;return this.create({jsx:t(n),id:n,...r}),n},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const Pe=new UN,BN=(e,t)=>{const r=t?.id||Mo++;return Pe.addToast({title:e,...t,id:r}),r},HN=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",VN=BN,zN=()=>Pe.toasts,WN=()=>Pe.getActiveToasts(),Yf=Object.assign(VN,{success:Pe.success,info:Pe.info,warning:Pe.warning,error:Pe.error,custom:Pe.custom,message:Pe.message,promise:Pe.promise,dismiss:Pe.dismiss,loading:Pe.loading},{getHistory:zN,getToasts:WN});$N("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");const ve={INVALID_TRANSITION:"This status change is not allowed from the current status.",INSUFFICIENT_PERMISSIONS:"You do not have permission to perform this action.",REASON_REQUIRED:"A reason is required for this status change.",REASON_TOO_SHORT:"Reason must be at least 10 characters long.",REASON_TOO_LONG:"Reason cannot exceed 500 characters.",NETWORK_ERROR:"Network error. Please check your connection and try again.",SERVER_ERROR:"Server error. Please try again later.",TIMEOUT_ERROR:"Request timed out. Please try again.",VALIDATION_FAILED:"Please check your input and try again.",GRIEVANCE_NOT_FOUND:"Grievance not found or has been deleted.",CONCURRENT_MODIFICATION:"This grievance was modified by another user. Please refresh and try again.",UNKNOWN_ERROR:"An unexpected error occurred. Please try again."},Zr={SLA_ALREADY_BREACHED:"This grievance has exceeded its SLA deadline.",REOPENING_CLOSED:"Reopening a closed grievance may affect SLA calculations.",CANCELLING_IN_PROGRESS:"Cancelling an in-progress grievance will stop all current work.",REJECTING_LATE_STAGE:"Rejecting at this stage may require additional approvals."},Zi={STATUS_UPDATED:"Status updated successfully.",PRIORITY_UPDATED:"Priority updated successfully.",ASSIGNMENT_UPDATED:"Assignment updated successfully.",NOTE_ADDED:"Note added successfully.",COMMENT_ADDED:"Comment added successfully.",ATTACHMENT_UPLOADED:"Attachment uploaded successfully.",GRIEVANCE_SAVED:"Grievance saved successfully.",STATUS_SUBMITTED:"Grievance submitted successfully and is now under review.",STATUS_PENDING:"Grievance moved to initial review phase. You will be notified of progress.",STATUS_DESK_1:"Grievance is now under initial verification by our team.",STATUS_DESK_2:"Grievance moved to fact-checking phase for detailed investigation.",STATUS_DESK_3:"Grievance is in final review stage before resolution.",STATUS_OFFICER:"Grievance assigned to field officer for on-site investigation.",STATUS_IN_PROGRESS:"Field investigation has begun. Updates will be provided regularly.",STATUS_RESOLVED:"Grievance has been successfully resolved. Thank you for your patience.",STATUS_CLOSED:"Grievance case has been officially closed.",STATUS_REJECTED:"Grievance has been rejected. Please check the reason provided.",STATUS_CANCELLED:"Grievance has been cancelled as requested.",STATUS_REOPENED:"Grievance has been reopened for further review and investigation."},GN={submitted:["Initial submission for review","Grievance submitted by citizen","New complaint received"],pending:["Moving to initial review phase","Assigned for preliminary assessment","Queued for officer review"],desk_1:["Initial verification in progress","Document verification started","Basic compliance check initiated"],desk_2:["Fact-checking and investigation phase","Detailed analysis in progress","Evidence collection and verification"],desk_3:["Final review and decision phase","Senior officer review initiated","Preparing resolution recommendations"],officer:["Assigned to field officer for investigation","On-site inspection required","Field verification needed"],in_progress:["Field investigation in progress","Active resolution efforts underway","Implementation of corrective measures"],resolved:["Issue successfully resolved","Corrective action completed","Satisfactory resolution achieved"],closed:["Case officially closed","Resolution confirmed and documented","No further action required"],rejected:["Insufficient evidence provided","Does not meet criteria for action","Outside jurisdiction or scope"],cancelled:["Cancelled at citizen request","Duplicate complaint identified","Issue resolved through other means"],reopened:["New evidence provided","Previous resolution inadequate","Citizen requested review of closure"]},qN=(e,t,r,n)=>{if(r==="user"&&!["cancelled","reopened"].includes(t))return{isValid:!1,error:ve.INSUFFICIENT_PERMISSIONS};if({resolved:["submitted","pending","desk_1","desk_2"],closed:["submitted","pending","desk_1","desk_2","desk_3"],cancelled:["resolved","closed"],rejected:["resolved","closed"]}[e]?.includes(t))return{isValid:!1,error:ve.INVALID_TRANSITION};let a;return t==="reopened"&&["closed","resolved"].includes(e)?a=Zr.REOPENING_CLOSED:t==="cancelled"&&["in_progress","officer"].includes(e)?a=Zr.CANCELLING_IN_PROGRESS:t==="rejected"&&["desk_3","officer"].includes(e)&&(a=Zr.REJECTING_LATE_STAGE),n?.slaMetrics?.isOverdue&&["resolved","closed"].includes(t)&&(a=a||Zr.SLA_ALREADY_BREACHED),{isValid:!0,warning:a}},KN=(e,t,r=!0)=>r&&(!e||e.trim().length===0)?{isValid:!1,error:ve.REASON_REQUIRED}:e&&e.trim().length<10?{isValid:!1,error:ve.REASON_TOO_SHORT}:e&&e.length>500?{isValid:!1,error:ve.REASON_TOO_LONG}:{isValid:!0},YN=e=>{const t=`STATUS_${e.toUpperCase()}`;return Zi[t]||Zi.STATUS_UPDATED},ec=e=>GN[e]||[],XN=e=>{if(!navigator.onLine)return{type:"NETWORK",message:ve.NETWORK_ERROR};if(e.name==="TimeoutError"||e.code==="TIMEOUT")return{type:"TIMEOUT",message:ve.TIMEOUT_ERROR};if(e.response){const t=e.response.status,r=e.response.data;switch(t){case 400:return{type:"VALIDATION",message:r?.message||ve.VALIDATION_FAILED,code:r?.code};case 401:case 403:return{type:"PERMISSION",message:r?.message||ve.INSUFFICIENT_PERMISSIONS,code:r?.code};case 404:return{type:"VALIDATION",message:ve.GRIEVANCE_NOT_FOUND,code:"NOT_FOUND"};case 409:return{type:"VALIDATION",message:ve.CONCURRENT_MODIFICATION,code:"CONFLICT"};case 500:default:return{type:"SERVER",message:r?.message||ve.SERVER_ERROR,code:r?.code}}}if(e.message){if(e.message.includes("transition"))return{type:"VALIDATION",message:ve.INVALID_TRANSITION};if(e.message.includes("permission"))return{type:"PERMISSION",message:ve.INSUFFICIENT_PERMISSIONS};if(e.message.includes("reason"))return{type:"VALIDATION",message:ve.REASON_REQUIRED}}return{type:"UNKNOWN",message:e.message||ve.UNKNOWN_ERROR}},en=(e,t)=>{const r=XN(e),n=t?`${t}: `:"";Yf.error(`${n}${r.message}`,{duration:5e3,action:r.type==="NETWORK"?{label:"Retry",onClick:()=>window.location.reload()}:void 0}),console.error(`[${r.type}] ${n}${r.message}`,e)},JN=e=>{Yf.success(e,{duration:3e3})},QN=()=>{const{user:e}=st();return{hasRole:t=>!e||!e.role?!1:(Array.isArray(t)?t:[t]).includes(e.role),hasMinimumRole:t=>{if(!e||!e.role)return!1;const r={super_admin:100,admin:80,officer:60,moderator:40,user:20,guest:10},n=r[e.role]||0,s=r[t]||0;return n>=s},canAccess:(t,r)=>{if(!e||!e.role)return!1;const n={"grievance:create":["user","moderator","officer","admin","super_admin"],"grievance:view_all":["officer","admin","super_admin"],"stats:view_detailed":["officer","admin","super_admin"],"admin:access":["admin","super_admin"],"system:manage":["super_admin"]},s=`${t}:${r}`,a=n[s];return a?a.includes(e.role):!1},user:e,isAuthenticated:!!e}},Lo=[{value:"submitted",label:"Submission Confirmed",icon:ot,color:"bg-green-500"},{value:"pending",label:"Initial Review",icon:yr,color:"bg-yellow-500"},{value:"desk_1",label:"Initial Verification",icon:On,color:"bg-blue-500"},{value:"desk_2",label:"Fact Checking",icon:qt,color:"bg-blue-500"},{value:"desk_3",label:"Administrative Review",icon:$o,color:"bg-blue-500"},{value:"officer",label:"Officer Assignment",icon:kt,color:"bg-purple-500"},{value:"in_progress",label:"Investigation",icon:Fo,color:"bg-green-500"},{value:"resolved",label:"Resolved",icon:ot,color:"bg-green-500"},{value:"closed",label:"Closed",icon:Er,color:"bg-gray-500"},{value:"rejected",label:"Rejected",icon:Je,color:"bg-red-500"},{value:"cancelled",label:"Cancelled",icon:Je,color:"bg-red-500"},{value:"reopened",label:"Reopened",icon:Kt,color:"bg-orange-500"}],ZN=e=>{const r={submitted:["pending","cancelled"],pending:["desk_1","rejected","cancelled"],desk_1:["desk_2","rejected","cancelled"],desk_2:["desk_3","rejected","cancelled"],desk_3:["officer","rejected","cancelled"],officer:["in_progress","rejected","cancelled"],in_progress:["resolved","rejected","cancelled"],resolved:["closed","reopened"],closed:["reopened"],rejected:["reopened"],cancelled:["reopened"],reopened:["desk_1","in_progress"]}[e]||[];return Lo.filter(n=>r.includes(n.value))},eS=(e,t,r,n)=>{const s=ZN(e);if(t==="user"){const a=["cancelled","reopened"];let i=s.filter(c=>a.includes(c.value));if(r&&n){if(r.userId!==n._id)return[];i.some(c=>c.value==="cancelled")&&(["submitted","pending","desk_1","desk_2","desk_3"].includes(e)||(i=i.filter(l=>l.value!=="cancelled"))),i.some(c=>c.value==="reopened")&&(["resolved","closed","rejected"].includes(e)||(i=i.filter(l=>l.value!=="reopened")))}return i}return t==="officer"||t==="admin"?s:[]},tS=[{value:"low",label:"Low Priority",color:"text-green-600"},{value:"medium",label:"Medium Priority",color:"text-yellow-600"},{value:"high",label:"High Priority",color:"text-orange-600"},{value:"urgent",label:"Urgent Priority",color:"text-red-600"}],rS=[{_id:"admin1",fullName:"Admin User",role:"admin",gmail:"<EMAIL>"},{_id:"officer1",fullName:"Officer Smith",role:"officer",gmail:"<EMAIL>"},{_id:"officer2",fullName:"Officer Johnson",role:"officer",gmail:"<EMAIL>"}],nS=({grievance:e,onStatusChange:t,onPriorityChange:r,onAssignmentChange:n,onAddNote:s,availableUsers:a=[]})=>{const{user:i}=QN(),[c,l]=f.useState(e?.status||""),[u,d]=f.useState(e?.priority||""),[m,h]=f.useState(e?.assignedTo?._id||"unassigned"),[x,g]=f.useState(""),[p,b]=f.useState(""),[v,y]=f.useState(""),[w,S]=f.useState(!1);if(!e)return o.jsx("div",{className:"flex items-center justify-center h-64",children:o.jsxs("div",{className:"text-center",children:[o.jsx(kt,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"No grievance data available"})]})});const j=Ca(e.status),D=ja(e.priority),E=eS(e.status,i?.role||"user",e,i);f.useEffect(()=>{console.log("🔄 ActionsTab: Grievance data changed, resetting form"),console.log("📊 Current grievance status:",e.status),l(""),g("")},[e._id,e.status]);const T=async()=>{if(!c||c===e.status)return;console.log("🔄 ActionsTab: Starting status update validation"),console.log("📊 Current status:",e.status),console.log("📊 New status:",c),console.log("📊 User role:",i?.role);const A=qN(e.status,c,i?.role||"user",e);if(!A.isValid){console.error("❌ ActionsTab: Status transition validation failed:",A.error),en(new Error(A.error||"Invalid status transition"),"Status Update");return}if(A.warning&&(console.warn("⚠️ ActionsTab: Status transition warning:",A.warning),!window.confirm(`${A.warning}

Do you want to continue?`))){console.log("ℹ️ ActionsTab: Status update cancelled by user");return}const $=KN(x,c,["rejected","cancelled","reopened"].includes(c));if(!$.isValid){console.error("❌ ActionsTab: Reason validation failed:",$.error),en(new Error($.error||"Invalid reason"),"Status Update");return}console.log("✅ ActionsTab: All validations passed, proceeding with status update"),S(!0);try{if(t)if(console.log("🔄 ActionsTab: Calling onStatusChange handler"),await t(c,x)){console.log("✅ ActionsTab: Status update successful"),l(""),g("");const F=YN(c);JN(F)}else console.error("❌ ActionsTab: Status update failed (returned false)"),en(new Error("Status update failed"),"Status Update")}catch(V){console.error("❌ ActionsTab: Status update error:",V),en(V,"Status Update")}finally{S(!1)}},N=async()=>{if(!(!u||u===e.priority)){S(!0);try{r&&await r(u,x),g("")}catch(A){console.error("Failed to update priority:",A)}finally{S(!1)}}},_=async()=>{if(!(!m||m===e?.assignedTo?._id)){S(!0);try{n&&await n(m==="unassigned"?"":m,p),b("")}catch(A){console.error("Failed to update assignment:",A)}finally{S(!1)}}},I=async()=>{if(v.trim()){S(!0);try{s&&await s(v),y("")}catch(A){console.error("Failed to add note:",A)}finally{S(!1)}}},H=A=>{const $=Lo.find(F=>F.value===A);if(!$)return o.jsx(ie,{className:"h-4 w-4"});const V=$.icon;return o.jsx(V,{className:"h-4 w-4"})};return o.jsxs("div",{className:"space-y-6 p-6 bg-background/50 dark:bg-[#0D0D0D]/50 min-h-full",children:[o.jsxs("div",{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm rounded-lg p-4",children:[o.jsx("div",{className:"flex items-center gap-2 mb-3",children:o.jsxs("div",{className:"flex items-center gap-2 text-sm font-medium",children:[o.jsx("div",{className:"w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white text-xl shadow-lg",children:j.icon}),"Current Status - ",j.title]})}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"flex items-center justify-between p-4 bg-muted/30 rounded-lg border",children:[o.jsx("div",{className:"flex items-center gap-4",children:o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white text-lg shadow-lg",children:j.icon}),o.jsxs("div",{children:[o.jsx(q,{className:"bg-blue-100 text-blue-800 text-base px-4 py-2 mb-1",children:j.title}),o.jsx("p",{className:"text-xs text-muted-foreground",children:j.description})]})]})}),o.jsxs("div",{className:"text-right",children:[o.jsxs("div",{className:"text-sm font-medium text-primary",children:["Step ",j.step,"/8"]}),o.jsxs("div",{className:"text-xs text-muted-foreground",children:["Est: ",j.estTime]})]})]}),o.jsx("div",{className:"flex items-center justify-between p-4 bg-muted/30 rounded-lg border",children:o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx("div",{className:"w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center text-white text-lg shadow-lg",children:D.icon}),o.jsxs("div",{children:[o.jsx(q,{className:`${D.color} text-base px-4 py-2 mb-1`,children:D.label}),o.jsx("p",{className:"text-xs text-muted-foreground",children:D.description})]})]})})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm",children:[o.jsxs(ge,{className:"pb-3",children:[o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(kt,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),"Status Management"]}),o.jsxs("div",{className:"text-sm text-muted-foreground",children:["Current: ",o.jsx("span",{className:"font-medium text-foreground",children:j.title}),E.length>0&&o.jsxs("span",{className:"ml-2",children:["• ",E.length," transition",E.length>1?"s":""," available"]})]})]}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"space-y-3",children:[o.jsxs("label",{className:"text-sm font-medium",children:["Available Status Transitions",E.length===0&&o.jsx("span",{className:"text-xs text-muted-foreground ml-2",children:"(No transitions available)"})]}),E.length>0?o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:E.map(A=>o.jsxs(X,{variant:c===A.value?"default":"outline",onClick:()=>l(A.value),className:`h-auto p-4 flex flex-col items-center gap-2 transition-all duration-200 hover:scale-105 ${c===A.value?"bg-primary text-primary-foreground shadow-lg":"bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"}`,children:[o.jsx("div",{className:"text-lg",children:H(A.value)}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"font-medium text-sm",children:A.label}),o.jsx("div",{className:"text-xs opacity-70",children:A.value==="rejected"||A.value==="cancelled"?"Terminal":A.value==="reopened"?"Restart":"Continue"})]})]},A.value))}):o.jsxs("div",{className:"text-center py-8 text-muted-foreground",children:[o.jsx(kt,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),o.jsx("p",{className:"text-sm",children:"No status transitions available"})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-sm font-medium",children:"Change Priority"}),o.jsxs(Ji,{value:u,onValueChange:d,children:[o.jsx(Oo,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 transition-colors",children:o.jsx(Qi,{placeholder:"Select new priority"})}),o.jsx(Io,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg",children:tS.map(A=>o.jsx(pn,{value:A.value,className:"hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70",children:o.jsx("span",{className:A.color,children:A.label})},A.value))})]})]}),E.length>0&&o.jsxs("div",{className:"p-3 bg-muted/20 rounded-lg border border-border/30",children:[o.jsx("div",{className:"text-xs font-medium text-muted-foreground mb-2",children:"Available Transitions:"}),o.jsx("div",{className:"flex flex-wrap gap-2",children:E.map(A=>o.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[H(A.value),o.jsx("span",{className:"text-muted-foreground",children:A.label})]},A.value))})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsxs("label",{className:"text-sm font-medium",children:["Action Note",["rejected","cancelled","reopened"].includes(c)&&o.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),c&&ec(c).length>0&&o.jsxs("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-xs text-muted-foreground",children:"Quick Templates:"}),o.jsx("div",{className:"flex flex-wrap gap-1",children:ec(c).map((A,$)=>o.jsx(X,{type:"button",variant:"outline",size:"sm",className:"text-xs h-6 px-2 bg-background/30 hover:bg-background/50",onClick:()=>g(A),children:A},$))})]}),o.jsx(xr,{placeholder:c?`Add a note about changing status to ${c}...`:"Add a note about this action...",value:x,onChange:A=>g(A.target.value),className:"min-h-[80px] bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70 transition-colors",required:["rejected","cancelled","reopened"].includes(c)}),o.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[o.jsxs("span",{children:[x.length,"/500 characters",x.length>500&&o.jsx("span",{className:"text-red-500 ml-1",children:"Too long"})]}),["rejected","cancelled","reopened"].includes(c)&&x.length<10&&o.jsx("span",{className:"text-red-500",children:"Minimum 10 characters required"})]})]}),o.jsxs("div",{className:"flex gap-2",children:[o.jsxs(X,{onClick:T,disabled:!c||c===e.status||w||E.length===0,className:"flex-1 bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm transition-colors",children:[o.jsx(Pm,{className:"h-4 w-4 mr-2"}),c&&c!==e.status?`Move to ${Lo.find(A=>A.value===c)?.label||"Next Status"}`:"Update Status"]}),o.jsxs(X,{onClick:N,disabled:!u||u===e.priority||w,variant:"outline",className:"flex-1 bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 shadow-sm transition-colors",children:[o.jsx(br,{className:"h-4 w-4 mr-2"}),"Update Priority"]})]})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm",children:[o.jsx(ge,{className:"pb-3",children:o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(Ua,{className:"h-5 w-5 text-green-600 dark:text-green-400"}),"Assignment Management"]})}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-sm font-medium",children:"Assign To"}),o.jsxs(Ji,{value:m,onValueChange:h,children:[o.jsx(Oo,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 transition-colors",children:o.jsx(Qi,{placeholder:"Select assignee"})}),o.jsxs(Io,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg",children:[o.jsx(pn,{value:"unassigned",className:"hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70",children:"Unassigned"}),(a.length>0?a:rS).map(A=>o.jsx(pn,{value:A._id,className:"hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70",children:o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(_e,{className:"h-4 w-4"}),o.jsxs("span",{children:[A.fullName," (",A.role,")"]})]})},A._id))]})]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-sm font-medium",children:"Assignment Note (Optional)"}),o.jsx(xr,{placeholder:"Add a note about this assignment...",value:p,onChange:A=>b(A.target.value),className:"min-h-[60px] bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70 transition-colors"})]}),o.jsxs(X,{onClick:_,disabled:!m||m!=="unassigned"&&m===e?.assignedTo?._id||m==="unassigned"&&!e?.assignedTo||w,className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm transition-colors",children:[o.jsx(Ua,{className:"h-4 w-4 mr-2"}),"Update Assignment"]})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm",children:[o.jsx(ge,{className:"pb-3",children:o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(yr,{className:"h-5 w-5 text-purple-600 dark:text-purple-400"}),"Add Note"]})}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"space-y-2",children:[o.jsx("label",{className:"text-sm font-medium",children:"Note"}),o.jsx(xr,{placeholder:"Add a note to this grievance...",value:v,onChange:A=>y(A.target.value),className:"min-h-[100px] bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70 focus:bg-background/70 dark:focus:bg-[#0D0D0D]/70 transition-colors"})]}),o.jsxs(X,{onClick:I,disabled:!v.trim()||w,className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm transition-colors",children:[o.jsx(yr,{className:"h-4 w-4 mr-2"}),"Add Note"]})]})]}),o.jsxs(ae,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-sm",children:[o.jsx(ge,{className:"pb-3",children:o.jsxs(ye,{className:"flex items-center gap-2 text-lg",children:[o.jsx(_e,{className:"h-5 w-5 text-orange-600 dark:text-orange-400"}),"Current Assignment"]})}),o.jsxs(de,{className:"space-y-4",children:[o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Assigned To"}),o.jsx("p",{className:"text-sm font-medium",children:e.assignedTo?.fullName||"Unassigned"})]}),o.jsxs("div",{children:[o.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Role"}),o.jsx("p",{className:"text-sm font-medium",children:e.assignedTo?.role||"N/A"})]})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Assigned Date"}),o.jsxs("p",{className:"text-sm flex items-center gap-1",children:[o.jsx(Gt,{className:"h-3 w-3"}),e.assignedAt?Ye(e.assignedAt):"Not assigned"]})]}),o.jsxs("div",{children:[o.jsx("label",{className:"text-sm font-medium text-muted-foreground",children:"Last Updated"}),o.jsxs("p",{className:"text-sm flex items-center gap-1",children:[o.jsx(ie,{className:"h-3 w-3"}),Ye(e.lastUpdatedAt||e.submittedAt)]})]})]})]})]})]})},sS=({grievanceId:e,isOpen:t,onClose:r})=>{const[n,s]=f.useState("details"),[a,i]=f.useState(!0),[c,l]=f.useState(null),[u,d]=f.useState([]),[m,h]=f.useState(!1),[x,g]=f.useState(new Set),{currentGrievance:p,getGrievanceById:b,changeStatus:v,addComment:y,changePriority:w,changeAssignment:S,addNote:j,removeAttachment:D,uploadAttachment:E}=hn(),T=f.useCallback(async(C=!1)=>{try{i(!0),l(null),console.log("🔄 Loading grievance data for ID:",e,C?"(FORCE REFRESH)":""),C&&console.log("🗑️ Clearing cached data for fresh fetch"),await b(e,C),console.log("✅ Grievance data loaded, current status:",p?.status),console.log("📊 Status history entries:",p?.statusHistory?.length||0),console.log("📋 Previous timelines:",p?.previousTimelines?.length||0)}catch(R){console.error("❌ Error loading grievance data:",R),l(R instanceof Error?R.message:"Failed to load grievance")}finally{i(!1)}},[b,e]);f.useEffect(()=>{t&&e&&T()},[t,e,T]),f.useEffect(()=>{p&&(console.log("🔄 Grievance data changed, refreshing timeline..."),console.log("📊 New grievance data:",{id:p._id,status:p.status,statusHistoryCount:p.statusHistory?.length||0,previousTimelinesCount:p.previousTimelines?.length||0}),I())},[p]);const N=C=>{h(C)},_=C=>{const R=new Set(x);R.has(C)?R.delete(C):R.add(C),g(R)},I=()=>{if(p){console.log("🔄 Refreshing timeline for grievance:",p._id,"Status:",p.status),console.log("📊 Input data - Status History:",p.statusHistory?.length||0),console.log("📋 Input data - Previous Timelines:",p.previousTimelines?.length||0);const R=new Co().generateTimeline(p);console.log("📊 Generated timeline:",R),d([R]),console.log("✅ Timeline refreshed and set")}else console.warn("⚠️ No currentGrievance data available for timeline refresh")},H=async(C,R)=>{try{console.log("🔄 Starting status change:",{newStatus:C,note:R});const K=await v(e,C,R);return K&&console.log("✅ Status change successful - store and timeline updated automatically"),K}catch(K){return console.error("❌ Failed to update status:",K),!1}},A=async C=>{try{const R=await y(e,C);return R&&(I(),await T()),R}catch(R){return console.error("Failed to add comment:",R),!1}},$=async C=>{try{return await D(e,C),await T(),!0}catch(R){return console.error("Failed to remove attachment:",R),!1}},V=C=>{console.log("Preview file:",C)},z=p?{attachments:p.attachments?.length||0,comments:p.comments?.length||0,timeline:u?.length||0}:{attachments:0,comments:0,timeline:0};return a?o.jsx(pr,{open:t,onOpenChange:r,children:o.jsxs(Ut,{className:"max-w-6xl h-[90vh] p-0",children:[o.jsx(Bt,{className:"sr-only",children:"Loading Grievance"}),o.jsx("div",{className:"flex items-center justify-center h-full",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Om,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"Loading grievance..."})]})})]})}):c?o.jsx(pr,{open:t,onOpenChange:r,children:o.jsxs(Ut,{className:"max-w-6xl h-[90vh] p-0",children:[o.jsx(Bt,{className:"sr-only",children:"Grievance Error"}),o.jsx("div",{className:"flex items-center justify-center h-full",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Wt,{className:"h-8 w-8 text-destructive mx-auto mb-4"}),o.jsx("p",{className:"text-destructive mb-4",children:c}),o.jsx(X,{onClick:()=>T(!0),children:"Try Again"})]})})]})}):p?o.jsx(pr,{open:t,onOpenChange:r,children:o.jsxs(Ut,{className:"max-w-6xl h-[90vh] p-0 flex flex-col bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsx(Bt,{className:"sr-only",children:p?.title||"Grievance Details"}),o.jsxs("div",{className:"flex flex-col h-full min-h-0 bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsx(Dw,{grievance:p,showActions:!0,compact:!1}),o.jsx("div",{className:"flex-1 min-h-0 overflow-hidden bg-background/50 dark:bg-[#0D0D0D]/50",children:o.jsxs(Yu,{value:n,onValueChange:s,className:"h-full flex flex-col bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsxs(ka,{className:"grid w-full grid-cols-5 rounded-none border-b bg-background/50 dark:bg-[#0D0D0D]/50 shadow-sm",children:[o.jsxs(dt,{value:"details",className:"flex items-center gap-2",children:[o.jsx(Ue,{className:"h-4 w-4"}),"Details"]}),o.jsxs(dt,{value:"timeline",className:"flex items-center gap-2",children:[o.jsx(ie,{className:"h-4 w-4"}),"Timeline",z.timeline>0&&o.jsx(q,{variant:"secondary",className:"ml-1 text-xs",children:z.timeline})]}),o.jsxs(dt,{value:"attachments",className:"flex items-center gap-2",children:[o.jsx(Im,{className:"h-4 w-4"}),"Attachments",z.attachments>0&&o.jsx(q,{variant:"secondary",className:"ml-1 text-xs",children:z.attachments})]}),o.jsxs(dt,{value:"comments",className:"flex items-center gap-2",children:[o.jsx(tn,{className:"h-4 w-4"}),"Comments",z.comments>0&&o.jsx(q,{variant:"secondary",className:"ml-1 text-xs",children:z.comments})]}),o.jsxs(dt,{value:"actions",className:"flex items-center gap-2",children:[o.jsx(kt,{className:"h-4 w-4"}),"Actions"]})]}),o.jsxs("div",{className:"flex-1 min-h-0 overflow-hidden bg-background/50 dark:bg-[#0D0D0D]/50",children:[o.jsx(ut,{value:"details",className:"h-full overflow-y-auto m-0 p-0 bg-background/50 dark:bg-[#0D0D0D]/50 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsx(Pw,{grievance:p})}),o.jsx(ut,{value:"timeline",className:"h-full overflow-y-auto m-0 p-0 bg-background/50 dark:bg-[#0D0D0D]/50 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsx(qw,{grievance:p,timeline:u,showPreviousTimeline:m,expandedTimelines:x,onTogglePreviousTimeline:N,onToggleTimelineExpansion:_,onTimelineEntryClick:C=>{console.log("Timeline entry clicked:",C)}})}),o.jsx(ut,{value:"attachments",className:"h-full overflow-y-auto m-0 p-0 bg-background/50 dark:bg-[#0D0D0D]/50 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsx(iN,{grievance:p,onFileRemove:$,onFilePreview:V,onFileUpload:async C=>p?await E(p._id,C):!1})}),o.jsx(ut,{value:"comments",className:"h-full overflow-y-auto m-0 p-0 bg-background/50 dark:bg-[#0D0D0D]/50 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsx(cN,{grievance:p,onAddComment:A})}),o.jsx(ut,{value:"actions",className:"h-full overflow-y-auto m-0 p-0 bg-background/50 dark:bg-[#0D0D0D]/50 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent",children:o.jsx(nS,{grievance:p,onStatusChange:H,onPriorityChange:async(C,R)=>{if(!p)return!1;try{const K=await w(p._id,C,R);return K&&I(),K}catch(K){return console.error("Failed to change priority:",K),!1}},onAssignmentChange:async(C,R)=>{if(!p)return!1;try{const K=await S(p._id,C,R);return K&&I(),K}catch(K){return console.error("Failed to change assignment:",K),!1}},onAddNote:async C=>{if(!p)return!1;try{const R=await j(p._id,C);return R&&I(),R}catch(R){return console.error("Failed to add note:",R),!1}}})})]})]})})]})]})}):o.jsx(pr,{open:t,onOpenChange:r,children:o.jsxs(Ut,{className:"max-w-6xl h-[90vh] p-0",children:[o.jsx(Bt,{className:"sr-only",children:"Grievance Not Found"}),o.jsx("div",{className:"flex items-center justify-center h-full",children:o.jsxs("div",{className:"text-center",children:[o.jsx(Ue,{className:"h-8 w-8 text-muted-foreground mx-auto mb-4"}),o.jsx("p",{className:"text-muted-foreground",children:"Grievance not found"})]})})]})})},gk=({grievanceId:e})=>{const[t,r]=f.useState(!1);return o.jsxs(o.Fragment,{children:[o.jsxs(X,{variant:"outline",size:"sm",onClick:()=>r(!0),className:"h-8 px-2",children:[o.jsx(Ue,{className:"h-4 w-4 mr-1"}),"View"]}),o.jsx(sS,{grievanceId:e,isOpen:t,onClose:()=>r(!1)})]})};var hs="Switch",[oS,xk]=Ne(hs),[aS,iS]=oS(hs),Xf=f.forwardRef((e,t)=>{const{__scopeSwitch:r,name:n,checked:s,defaultChecked:a,required:i,disabled:c,value:l="on",onCheckedChange:u,form:d,...m}=e,[h,x]=f.useState(null),g=G(t,w=>x(w)),p=f.useRef(!1),b=h?d||!!h.closest("form"):!0,[v,y]=Qe({prop:s,defaultProp:a??!1,onChange:u,caller:hs});return o.jsxs(aS,{scope:r,checked:v,disabled:c,children:[o.jsx(U.button,{type:"button",role:"switch","aria-checked":v,"aria-required":i,"data-state":em(v),"data-disabled":c?"":void 0,disabled:c,value:l,...m,ref:g,onClick:P(e.onClick,w=>{y(S=>!S),b&&(p.current=w.isPropagationStopped(),p.current||w.stopPropagation())})}),b&&o.jsx(Zf,{control:h,bubbles:!p.current,name:n,value:l,checked:v,required:i,disabled:c,form:d,style:{transform:"translateX(-100%)"}})]})});Xf.displayName=hs;var Jf="SwitchThumb",Qf=f.forwardRef((e,t)=>{const{__scopeSwitch:r,...n}=e,s=iS(Jf,r);return o.jsx(U.span,{"data-state":em(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:t})});Qf.displayName=Jf;var cS="SwitchBubbleInput",Zf=f.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:n=!0,...s},a)=>{const i=f.useRef(null),c=G(i,a),l=Aa(r),u=Zo(t);return f.useEffect(()=>{const d=i.current;if(!d)return;const m=window.HTMLInputElement.prototype,x=Object.getOwnPropertyDescriptor(m,"checked").set;if(l!==r&&x){const g=new Event("click",{bubbles:n});x.call(d,r),d.dispatchEvent(g)}},[l,r,n]),o.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:c,style:{...s.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Zf.displayName=cS;function em(e){return e?"checked":"unchecked"}var bk=Xf,vk=Qf,Pa="Radio",[lS,tm]=Ne(Pa),[dS,uS]=lS(Pa),rm=f.forwardRef((e,t)=>{const{__scopeRadio:r,name:n,checked:s=!1,required:a,disabled:i,value:c="on",onCheck:l,form:u,...d}=e,[m,h]=f.useState(null),x=G(t,b=>h(b)),g=f.useRef(!1),p=m?u||!!m.closest("form"):!0;return o.jsxs(dS,{scope:r,checked:s,disabled:i,children:[o.jsx(U.button,{type:"button",role:"radio","aria-checked":s,"data-state":am(s),"data-disabled":i?"":void 0,disabled:i,value:c,...d,ref:x,onClick:P(e.onClick,b=>{s||l?.(),p&&(g.current=b.isPropagationStopped(),g.current||b.stopPropagation())})}),p&&o.jsx(om,{control:m,bubbles:!g.current,name:n,value:c,checked:s,required:a,disabled:i,form:u,style:{transform:"translateX(-100%)"}})]})});rm.displayName=Pa;var nm="RadioIndicator",sm=f.forwardRef((e,t)=>{const{__scopeRadio:r,forceMount:n,...s}=e,a=uS(nm,r);return o.jsx(xe,{present:n||a.checked,children:o.jsx(U.span,{"data-state":am(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})})});sm.displayName=nm;var fS="RadioBubbleInput",om=f.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:n=!0,...s},a)=>{const i=f.useRef(null),c=G(i,a),l=Aa(r),u=Zo(t);return f.useEffect(()=>{const d=i.current;if(!d)return;const m=window.HTMLInputElement.prototype,x=Object.getOwnPropertyDescriptor(m,"checked").set;if(l!==r&&x){const g=new Event("click",{bubbles:n});x.call(d,r),d.dispatchEvent(g)}},[l,r,n]),o.jsx(U.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:c,style:{...s.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});om.displayName=fS;function am(e){return e?"checked":"unchecked"}var mS=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],ps="RadioGroup",[hS,yk]=Ne(ps,[cr,tm]),im=cr(),cm=tm(),[pS,gS]=hS(ps),lm=f.forwardRef((e,t)=>{const{__scopeRadioGroup:r,name:n,defaultValue:s,value:a,required:i=!1,disabled:c=!1,orientation:l,dir:u,loop:d=!0,onValueChange:m,...h}=e,x=im(r),g=ir(u),[p,b]=Qe({prop:a,defaultProp:s??null,onChange:m,caller:ps});return o.jsx(pS,{scope:r,name:n,required:i,disabled:c,value:p,onValueChange:b,children:o.jsx(ma,{asChild:!0,...x,orientation:l,dir:g,loop:d,children:o.jsx(U.div,{role:"radiogroup","aria-required":i,"aria-orientation":l,"data-disabled":c?"":void 0,dir:g,...h,ref:t})})})});lm.displayName=ps;var dm="RadioGroupItem",um=f.forwardRef((e,t)=>{const{__scopeRadioGroup:r,disabled:n,...s}=e,a=gS(dm,r),i=a.disabled||n,c=im(r),l=cm(r),u=f.useRef(null),d=G(t,u),m=a.value===s.value,h=f.useRef(!1);return f.useEffect(()=>{const x=p=>{mS.includes(p.key)&&(h.current=!0)},g=()=>h.current=!1;return document.addEventListener("keydown",x),document.addEventListener("keyup",g),()=>{document.removeEventListener("keydown",x),document.removeEventListener("keyup",g)}},[]),o.jsx(ha,{asChild:!0,...c,focusable:!i,active:m,children:o.jsx(rm,{disabled:i,required:a.required,checked:m,...l,...s,name:a.name,ref:d,onCheck:()=>a.onValueChange(s.value),onKeyDown:P(x=>{x.key==="Enter"&&x.preventDefault()}),onFocus:P(s.onFocus,()=>{h.current&&u.current?.click()})})})});um.displayName=dm;var xS="RadioGroupIndicator",fm=f.forwardRef((e,t)=>{const{__scopeRadioGroup:r,...n}=e,s=cm(r);return o.jsx(sm,{...s,...n,ref:t})});fm.displayName=xS;var wk=lm,Nk=um,Sk=fm,Oa="Progress",Ia=100,[bS,kk]=Ne(Oa),[vS,yS]=bS(Oa),mm=f.forwardRef((e,t)=>{const{__scopeProgress:r,value:n=null,max:s,getValueLabel:a=wS,...i}=e;(s||s===0)&&!tc(s)&&console.error(NS(`${s}`,"Progress"));const c=tc(s)?s:Ia;n!==null&&!rc(n,c)&&console.error(SS(`${n}`,"Progress"));const l=rc(n,c)?n:null,u=Pn(l)?a(l,c):void 0;return o.jsx(vS,{scope:r,value:l,max:c,children:o.jsx(U.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":Pn(l)?l:void 0,"aria-valuetext":u,role:"progressbar","data-state":gm(l,c),"data-value":l??void 0,"data-max":c,...i,ref:t})})});mm.displayName=Oa;var hm="ProgressIndicator",pm=f.forwardRef((e,t)=>{const{__scopeProgress:r,...n}=e,s=yS(hm,r);return o.jsx(U.div,{"data-state":gm(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...n,ref:t})});pm.displayName=hm;function wS(e,t){return`${Math.round(e/t*100)}%`}function gm(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function Pn(e){return typeof e=="number"}function tc(e){return Pn(e)&&!isNaN(e)&&e>0}function rc(e,t){return Pn(e)&&!isNaN(e)&&e<=t&&e>=0}function NS(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Ia}\`.`}function SS(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Ia} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var jk=mm,Ck=pm;export{rg as $,ik as A,X as B,IS as C,pr as D,Ut as E,ku as F,gk as G,Bt as H,Q as I,Bm as J,wk as K,ne as L,Nk as M,Sk as N,jk as O,DS as P,Ck as Q,PS as R,Ji as S,OS as T,xr as U,Eo as V,Yy as W,Jc as X,tg as Y,Qc as Z,TS as _,uk as a,Bo as a0,Zc as a1,el as a2,Zp as a3,Sc as a4,rk as a5,nk as a6,sk as a7,zi as a8,Wi as a9,jo as aa,$t as ab,Ky as ac,ew as ad,ac as ae,Th as b,B as c,fk as d,lk as e,dk as f,hn as g,ae as h,ge as i,ye as j,de as k,Oo as l,Qi as m,Io as n,pn as o,q as p,Fi as q,bk as r,vk as s,Ge as t,st as u,pd as v,Yu as w,ka as x,dt as y,ut as z};
