const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/page-components-1uWkaYuZ.js","assets/ui-vendor-CJ9vBRYM.js","assets/react-vendor-DavUf6mE.js","assets/grievance-components-D9xcC4N3.js","assets/state-vendor-CoPsor3D.js","assets/form-vendor-iDrVnY0w.js","assets/page-components-D3mk0fMs.css"])))=>i.map(i=>d[i]);
import{r,j as e,X as le,at as ts,q as je,au as rs,p as Ne,y as ne,H as ds,f as ls,U as ns,av as is,aw as os,ax as cs,ae as oe,J as U,ay as ms,T as V,o as xs,az as us,a8 as hs,h as gs,aA as bs,a5 as ps,ac as fs,b as me,u as js,v as Ns,aB as vs,g as F,C as G,i as ws,s as O,E as B,d as I,$}from"./ui-vendor-CJ9vBRYM.js";import{a as ys,g as ks}from"./react-vendor-DavUf6mE.js";import{X as ve,c as g,Y as Ss,Z as we,$ as Cs,a0 as ye,a1 as ke,a2 as Se,a3 as Ds,B as x,a4 as Rs,J as H,a5 as Ce,a6 as De,a7 as Re,u as W,a8 as Ps,a9 as As,aa as Es,ab as xe,L as D,I as ie,h as y,i as z,j as ae,ac as Pe,k as _,D as Ms,E as Ts,F as Is,H as zs,ad as _s,p as L,U as Ls,w as Fs,x as Gs,y as ue,z as he,ae as te,W as Os}from"./grievance-components-D9xcC4N3.js";import{T as K,I as P,a as J,b as Z,c as Q,S as ge,A,f as be,P as Ae,d as Ee,e as Me,C as Bs,u as Vs,g as qs}from"./page-components-1uWkaYuZ.js";import"./state-vendor-CoPsor3D.js";import"./form-vendor-iDrVnY0w.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))l(d);new MutationObserver(d=>{for(const n of d)if(n.type==="childList")for(const c of n.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&l(c)}).observe(document,{childList:!0,subtree:!0});function t(d){const n={};return d.integrity&&(n.integrity=d.integrity),d.referrerPolicy&&(n.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?n.credentials="include":d.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function l(d){if(d.ep)return;d.ep=!0;const n=t(d);fetch(d.href,n)}})();var Y={},pe;function $s(){if(pe)return Y;pe=1;var s=ys();return Y.createRoot=s.createRoot,Y.hydrateRoot=s.hydrateRoot,Y}var Us=$s();const Hs=ks(Us),de=768;function Ws(){const[s,a]=r.useState(void 0);return r.useEffect(()=>{const t=window.matchMedia(`(max-width: ${de-1}px)`),l=()=>{a(window.innerWidth<de)};return t.addEventListener("change",l),a(window.innerWidth<de),()=>t.removeEventListener("change",l)},[]),!!s}const Ys=Ds,Ks=Ss,Te=r.forwardRef(({className:s,...a},t)=>e.jsx(ve,{className:g("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...a,ref:t}));Te.displayName=ve.displayName;const Js=ye("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Ie=r.forwardRef(({side:s="right",className:a,children:t,...l},d)=>e.jsxs(Ks,{children:[e.jsx(Te,{}),e.jsxs(we,{ref:d,className:g(Js({side:s}),a),...l,children:[e.jsxs(Cs,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[e.jsx(le,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]}),t]})]}));Ie.displayName=we.displayName;const ze=({className:s,...a})=>e.jsx("div",{className:g("flex flex-col space-y-2 text-center sm:text-left",s),...a});ze.displayName="SheetHeader";const _e=r.forwardRef(({className:s,...a},t)=>e.jsx(ke,{ref:t,className:g("text-lg font-semibold text-foreground",s),...a}));_e.displayName=ke.displayName;const Le=r.forwardRef(({className:s,...a},t)=>e.jsx(Se,{ref:t,className:g("text-sm text-muted-foreground",s),...a}));Le.displayName=Se.displayName;const Zs="sidebar_state",Qs=60*60*24*7,Xs="16rem",ea="18rem",sa="3rem",aa="b",Fe=r.createContext(null);function re(){const s=r.useContext(Fe);if(!s)throw new Error("useSidebar must be used within a SidebarProvider.");return s}const Ge=r.forwardRef(({defaultOpen:s=!0,open:a,onOpenChange:t,className:l,style:d,children:n,...c},h)=>{const m=Ws(),[u,i]=r.useState(!1),[o,b]=r.useState(s),p=a??o,w=r.useCallback(j=>{const k=typeof j=="function"?j(p):j;t?t(k):b(k),document.cookie=`${Zs}=${k}; path=/; max-age=${Qs}`},[t,p]),N=r.useCallback(()=>m?i(j=>!j):w(j=>!j),[m,w,i]);r.useEffect(()=>{const j=k=>{k.key===aa&&(k.metaKey||k.ctrlKey)&&(k.preventDefault(),N())};return window.addEventListener("keydown",j),()=>window.removeEventListener("keydown",j)},[N]);const T=p?"expanded":"collapsed",R=r.useMemo(()=>({state:T,open:p,setOpen:w,isMobile:m,openMobile:u,setOpenMobile:i,toggleSidebar:N}),[T,p,w,m,u,i,N]);return e.jsx(Fe.Provider,{value:R,children:e.jsx(K,{delayDuration:0,children:e.jsx("div",{style:{"--sidebar-width":Xs,"--sidebar-width-icon":sa,...d},className:g("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",l),ref:h,...c,children:n})})})});Ge.displayName="SidebarProvider";const Oe=r.forwardRef(({side:s="left",variant:a="sidebar",collapsible:t="offcanvas",className:l,children:d,...n},c)=>{const{isMobile:h,state:m,openMobile:u,setOpenMobile:i}=re();return t==="none"?e.jsx("div",{className:g("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",l),ref:c,...n,children:d}):h?e.jsx(Ys,{open:u,onOpenChange:i,...n,children:e.jsxs(Ie,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":ea},side:s,children:[e.jsxs(ze,{className:"sr-only",children:[e.jsx(_e,{children:"Sidebar"}),e.jsx(Le,{children:"Displays the mobile sidebar."})]}),e.jsx("div",{className:"flex h-full w-full flex-col",children:d})]})}):e.jsxs("div",{ref:c,className:"group peer hidden text-sidebar-foreground md:block","data-state":m,"data-collapsible":m==="collapsed"?t:"","data-variant":a,"data-side":s,children:[e.jsx("div",{className:g("relative w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",a==="floating"||a==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),e.jsx("div",{className:g("fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex",s==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",a==="floating"||a==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",l),...n,children:e.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:d})})]})});Oe.displayName="Sidebar";const Be=r.forwardRef(({className:s,onClick:a,...t},l)=>{const{toggleSidebar:d}=re();return e.jsxs(x,{ref:l,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:g("h-7 w-7",s),onClick:n=>{a?.(n),d()},...t,children:[e.jsx(ts,{}),e.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});Be.displayName="SidebarTrigger";const ta=r.forwardRef(({className:s,...a},t)=>{const{toggleSidebar:l}=re();return e.jsx("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:l,title:"Toggle Sidebar",className:g("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",s),...a})});ta.displayName="SidebarRail";const ra=r.forwardRef(({className:s,...a},t)=>e.jsx("main",{ref:t,className:g("relative flex w-full flex-1 flex-col bg-background","md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",s),...a}));ra.displayName="SidebarInset";const da=r.forwardRef(({className:s,...a},t)=>e.jsx(P,{ref:t,"data-sidebar":"input",className:g("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",s),...a}));da.displayName="SidebarInput";const Ve=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"header",className:g("flex flex-col gap-2 p-2",s),...a}));Ve.displayName="SidebarHeader";const qe=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"footer",className:g("flex flex-col gap-2 p-2",s),...a}));qe.displayName="SidebarFooter";const la=r.forwardRef(({className:s,...a},t)=>e.jsx(Rs,{ref:t,"data-sidebar":"separator",className:g("mx-2 w-auto bg-sidebar-border",s),...a}));la.displayName="SidebarSeparator";const $e=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"content",className:g("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",s),...a}));$e.displayName="SidebarContent";const Ue=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"group",className:g("relative flex w-full min-w-0 flex-col p-2",s),...a}));Ue.displayName="SidebarGroup";const He=r.forwardRef(({className:s,asChild:a=!1,...t},l)=>{const d=a?H:"div";return e.jsx(d,{ref:l,"data-sidebar":"group-label",className:g("flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",s),...t})});He.displayName="SidebarGroupLabel";const na=r.forwardRef(({className:s,asChild:a=!1,...t},l)=>{const d=a?H:"button";return e.jsx(d,{ref:l,"data-sidebar":"group-action",className:g("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",s),...t})});na.displayName="SidebarGroupAction";const We=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"group-content",className:g("w-full text-sm",s),...a}));We.displayName="SidebarGroupContent";const X=r.forwardRef(({className:s,...a},t)=>e.jsx("ul",{ref:t,"data-sidebar":"menu",className:g("flex w-full min-w-0 flex-col gap-1",s),...a}));X.displayName="SidebarMenu";const ee=r.forwardRef(({className:s,...a},t)=>e.jsx("li",{ref:t,"data-sidebar":"menu-item",className:g("group/menu-item relative",s),...a}));ee.displayName="SidebarMenuItem";const ia=ye("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),se=r.forwardRef(({asChild:s=!1,isActive:a=!1,variant:t="default",size:l="default",tooltip:d,className:n,...c},h)=>{const m=s?H:"button",{isMobile:u,state:i}=re(),o=e.jsx(m,{ref:h,"data-sidebar":"menu-button","data-size":l,"data-active":a,className:g(ia({variant:t,size:l}),n),...c});return d?(typeof d=="string"&&(d={children:d}),e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:o}),e.jsx(Q,{side:"right",align:"center",hidden:i!=="collapsed"||u,...d})]})):o});se.displayName="SidebarMenuButton";const oa=r.forwardRef(({className:s,asChild:a=!1,showOnHover:t=!1,...l},d)=>{const n=a?H:"button";return e.jsx(n,{ref:d,"data-sidebar":"menu-action",className:g("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",s),...l})});oa.displayName="SidebarMenuAction";const ca=r.forwardRef(({className:s,...a},t)=>e.jsx("div",{ref:t,"data-sidebar":"menu-badge",className:g("pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",s),...a}));ca.displayName="SidebarMenuBadge";const ma=r.forwardRef(({className:s,showIcon:a=!1,...t},l)=>{const d=r.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return e.jsxs("div",{ref:l,"data-sidebar":"menu-skeleton",className:g("flex h-8 items-center gap-2 rounded-md px-2",s),...t,children:[a&&e.jsx(ge,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),e.jsx(ge,{className:"h-4 max-w-[--skeleton-width] flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":d}})]})});ma.displayName="SidebarMenuSkeleton";const xa=r.forwardRef(({className:s,...a},t)=>e.jsx("ul",{ref:t,"data-sidebar":"menu-sub",className:g("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",s),...a}));xa.displayName="SidebarMenuSub";const ua=r.forwardRef(({...s},a)=>e.jsx("li",{ref:a,...s}));ua.displayName="SidebarMenuSubItem";const ha=r.forwardRef(({asChild:s=!1,size:a="md",isActive:t,className:l,...d},n)=>{const c=s?H:"a";return e.jsx(c,{ref:n,"data-sidebar":"menu-sub-button","data-size":a,"data-active":t,className:g("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",a==="sm"&&"text-xs",a==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",l),...d})});ha.displayName="SidebarMenuSubButton";const Ye=r.forwardRef(({className:s,...a},t)=>e.jsx(Ce,{ref:t,className:g("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...a}));Ye.displayName=Ce.displayName;const Ke=r.forwardRef(({className:s,...a},t)=>e.jsx(De,{ref:t,className:g("aspect-square h-full w-full",s),...a}));Ke.displayName=De.displayName;const Je=r.forwardRef(({className:s,...a},t)=>e.jsx(Re,{ref:t,className:g("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...a}));Je.displayName=Re.displayName;const ga={navMain:[{title:"Dashboard",url:"/dashboard",icon:rs},{title:"Grievances",url:"/grievances",icon:Ne},{title:"Chat",url:"/chat",icon:ne},{title:"Settings",url:"/settings",icon:ds}]};function ba({setCurrentPage:s}){const{logout:a,user:t}=W();return e.jsxs(Oe,{collapsible:"icon",className:"bg-sidebar dark:bg-[#171717]",children:[e.jsx(Ve,{children:e.jsx(X,{children:e.jsx(ee,{children:e.jsx(se,{size:"lg",asChild:!0,children:e.jsxs("a",{href:"/",children:[e.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground",children:e.jsx(je,{className:"size-4"})}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[e.jsx("span",{className:"truncate font-semibold",children:"CivicAssist"}),e.jsx("span",{className:"truncate text-xs",children:"Grievance System"})]})]})})})})}),e.jsx($e,{children:e.jsxs(Ue,{children:[e.jsx(He,{children:"Navigation"}),e.jsx(We,{children:e.jsx(X,{children:ga.navMain.map(l=>e.jsx(ee,{children:e.jsxs(se,{tooltip:l.title,onClick:()=>{console.log("Clicked on:",l.title.toLowerCase()),s(l.title.toLowerCase()),console.log("Current page after click:",l.title.toLowerCase())},children:[e.jsx(l.icon,{}),e.jsx("span",{children:l.title})]})},l.title))})})]})}),e.jsx(qe,{children:e.jsx(X,{children:e.jsx(ee,{children:e.jsxs(Ps,{children:[e.jsx(As,{asChild:!0,children:e.jsxs(se,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground group transition-all duration-300 hover:w-full w-12 overflow-hidden",children:[e.jsxs("div",{className:"relative flex-shrink-0",children:[e.jsxs(Ye,{className:"size-8",children:[e.jsx(Ke,{src:t?.profilePhotoUrl||"",alt:t?.fullName||"User",onError:l=>{console.error("Failed to load profile image in sidebar"),l.currentTarget.style.display="none"}}),e.jsx(Je,{children:t?.fullName?.split(" ").map(l=>l[0]).join("").toUpperCase()||"U"})]}),e.jsx("span",{className:"border-background absolute -end-0.5 -bottom-0.5 size-3 rounded-full border-2 bg-emerald-500",children:e.jsx("span",{className:"sr-only",children:"Online"})})]}),e.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight opacity-0 group-hover:opacity-100 transition-opacity duration-300 ml-3",children:[e.jsx("span",{className:"truncate font-semibold",children:"John Doe"}),e.jsx("span",{className:"truncate text-xs",children:"<EMAIL>"})]}),e.jsx(ls,{className:"ml-auto size-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}),e.jsxs(Es,{className:"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",side:"bottom",align:"end",sideOffset:4,children:[e.jsxs(xe,{onClick:()=>s("profile"),className:"bg-background dark:bg-[#0D0D0D] hover:bg-muted",children:[e.jsx(ns,{className:"mr-2 size-4"}),"Account and Settings"]}),e.jsxs(xe,{onClick:()=>{a(),window.location.href="/login"},className:"bg-background dark:bg-[#0D0D0D] hover:bg-muted",children:[e.jsx(is,{className:"mr-2 size-4"}),"Sign out"]})]})]})})})})]})}const pa={theme:"system",setTheme:()=>null},Ze=r.createContext(pa);function fa({children:s,defaultTheme:a="dark",storageKey:t="vite-ui-theme",...l}){const[d,n]=r.useState(()=>localStorage.getItem(t)||a);r.useEffect(()=>{const h=window.document.documentElement;if(h.classList.remove("light","dark"),d==="system"){const m=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";h.classList.add(m);return}h.classList.add(d)},[d]);const c={theme:d,setTheme:h=>{localStorage.setItem(t,h),n(h)}};return e.jsx(Ze.Provider,{...l,value:c,children:s})}const ja=()=>{const s=r.useContext(Ze);if(s===void 0)throw new Error("useTheme must be used within a ThemeProvider");return s};function Qe(){const{theme:s,setTheme:a}=ja();return e.jsxs(x,{variant:"outline",size:"icon",onClick:()=>a(s==="light"?"dark":"light"),className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 hover:bg-muted",children:[e.jsx(os,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),e.jsx(cs,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}const Na=({size:s="md",text:a="Loading...",className:t="",minHeight:l="min-h-[400px]"})=>{const d={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"},n={sm:"text-xs",md:"text-sm",lg:"text-base"};return e.jsx("div",{className:`flex items-center justify-center ${l} ${t}`,children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`animate-spin rounded-full border-b-2 border-primary mx-auto mb-2 ${d[s]}`}),e.jsx("p",{className:`text-muted-foreground ${n[s]}`,children:a})]})})};function va({onNavigate:s}){const[a,t]=r.useState(""),[l,d]=r.useState(""),[n,c]=r.useState(!1),[h,m]=r.useState(!1),[u,i]=r.useState(""),[,o]=r.useState(null),{login:b,isLoading:p,error:w,clearError:N}=W(),T=async j=>{if(j.preventDefault(),!a||!l){o("other"),i("Please enter both email and password");return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)){o("other"),i("Please enter a valid email address");return}m(!1),i(""),o(null),N();try{await b({gmail:a,password:l})&&m(!0)}catch(E){console.error("Login error:",E);const v=E.response?.data?.message||E.message||"Login failed";v.includes("credentials")||v.includes("password")?o("credentials"):v.includes("verify")||v.includes("verification")?o("verification"):v.includes("locked")||v.includes("attempts")?o("locked"):o("other"),i(v)}},R=u||w;return e.jsxs("div",{className:"w-full max-w-md space-y-6 p-6 bg-card dark:bg-[#171717] rounded-xl shadow-lg border border-border dark:border-gray-800",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"Welcome Back"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Sign in to your account to continue"})]}),e.jsxs("form",{onSubmit:T,className:"space-y-4",children:[R&&e.jsx(A,{type:"error",message:R}),h&&e.jsx(A,{type:"success",message:"Login successful! Redirecting..."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"email",children:"Email"}),e.jsx(P,{id:"email",type:"email",placeholder:"Enter your email",value:a,onChange:j=>t(j.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D]",autoComplete:"email"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"password",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(P,{id:"password",type:n?"text":"password",placeholder:"Enter your password",value:l,onChange:j=>d(j.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D] pr-10",autoComplete:"current-password"}),e.jsx(x,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>c(!n),children:n?e.jsx(oe,{className:"h-4 w-4 text-muted-foreground"}):e.jsx(U,{className:"h-4 w-4 text-muted-foreground"})})]})]}),e.jsx(x,{type:"submit",className:"w-full bg-primary hover:bg-primary/90",disabled:p,children:p?"Signing in...":"Sign In"}),e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx(x,{variant:"link",className:"text-sm text-primary p-0 h-auto",onClick:()=>s("forgot-password"),children:"Forgot your password?"}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",e.jsx(x,{variant:"link",className:"text-primary p-0 h-auto",onClick:()=>s("register"),children:"Sign up"})]})]})]})]})}function Xe({password:s,onStrengthChange:a}){const[t,l]=r.useState(0),[d,n]=r.useState("");r.useEffect(()=>{(()=>{if(!s){l(0),n("");return}let m=0;s.length>=8&&(m+=1),s.length>=12&&(m+=1),/[A-Z]/.test(s)&&(m+=1),/[a-z]/.test(s)&&(m+=1),/[0-9]/.test(s)&&(m+=1),/[^A-Za-z0-9]/.test(s)&&(m+=1);const u=Math.min(4,Math.floor(m/1.5));switch(l(u),u){case 0:n("Too weak");break;case 1:n("Weak");break;case 2:n("Fair");break;case 3:n("Good");break;case 4:n("Strong");break;default:n("")}a&&a(u)})()},[s,a]);const c=()=>{switch(t){case 0:return"bg-gray-300 dark:bg-gray-700";case 1:return"bg-red-500";case 2:return"bg-orange-500";case 3:return"bg-yellow-500";case 4:return"bg-green-500";default:return"bg-gray-300 dark:bg-gray-700"}};return e.jsxs("div",{className:"mt-2",children:[e.jsx("div",{className:"flex gap-1 h-1 mb-1",children:[0,1,2,3].map(h=>e.jsx("div",{className:`h-full flex-1 rounded-full transition-colors ${h<t?c():"bg-gray-200 dark:bg-gray-800"}`},h))}),d&&e.jsx("p",{className:"text-xs text-muted-foreground",children:d})]})}function wa({onNavigate:s}){const[a,t]=r.useState(""),[l,d]=r.useState(""),[n,c]=r.useState(""),[h,m]=r.useState(""),[u,i]=r.useState(""),[o,b]=r.useState(""),[p,w]=r.useState(!1),[N,T]=r.useState(0),[R,j]=r.useState(!0),[k,E]=r.useState(!1),[v,f]=r.useState(null),{register:q,isLoading:M,error:S,clearError:es}=W(),ss=async C=>{if(C.preventDefault(),es(),E(!1),f(null),!a||!l||!u||!o||!n){f("other");return}if(u!==o){j(!1);return}if(!/^[0-9]{10}$/.test(n)){f("invalid_mobile");return}if(N<2){f("weak_password");return}await q({gmail:l,password:u,fullName:a,mobile:n,address:h||void 0})?E(!0):S&&(S.includes("already exists")||S.includes("duplicate")?f("user_exists"):f("other"))},as=C=>{b(C.target.value),j(C.target.value===u||C.target.value==="")};return e.jsxs("div",{className:"w-full max-w-md space-y-6 p-6 bg-card dark:bg-[#171717] rounded-xl shadow-lg border border-border dark:border-gray-800",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"Create Account"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Register to get started"})]}),e.jsxs("form",{onSubmit:ss,className:"space-y-4",children:[v==="user_exists"&&e.jsx(A,{type:"error",message:"User with this email already exists. Please use a different email or sign in."}),v==="weak_password"&&e.jsx(A,{type:"error",title:"Password does not meet requirements:",message:"",items:["Minimum 8 characters","Include at least one uppercase letter","Include at least one number"]}),v==="invalid_mobile"&&e.jsx(A,{type:"error",title:"Invalid mobile number:",message:"Mobile number must be exactly 10 digits (0-9 only)."}),v==="other"&&S&&e.jsx(A,{type:"error",message:S}),k&&e.jsx(A,{type:"success",message:"Registration successful! Redirecting to login..."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"fullName",children:"Full Name"}),e.jsx(P,{id:"fullName",type:"text",placeholder:"Enter your full name",value:a,onChange:C=>t(C.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"email",children:"Email"}),e.jsx(P,{id:"email",type:"email",placeholder:"Enter your email",value:l,onChange:C=>d(C.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"mobile",children:"Mobile Number"}),e.jsx(P,{id:"mobile",type:"tel",placeholder:"Enter 10-digit mobile number (e.g., 9876543210)",value:n,onChange:C=>{const ce=C.target.value.replace(/\D/g,"").slice(0,10);c(ce)},maxLength:10,required:!0,className:"bg-background dark:bg-[#0D0D0D]"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Enter exactly 10 digits without spaces or special characters"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"address",children:"Address (Optional)"}),e.jsx(P,{id:"address",type:"text",placeholder:"Enter your address",value:h,onChange:C=>m(C.target.value),className:"bg-background dark:bg-[#0D0D0D]"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"password",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(P,{id:"password",type:p?"text":"password",placeholder:"Create a strong password",value:u,onChange:C=>i(C.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D] pr-10"}),e.jsx("button",{type:"button",onClick:()=>w(!p),className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground",children:p?e.jsx(oe,{size:16}):e.jsx(U,{size:16})})]}),e.jsx(Xe,{password:u,onStrengthChange:T})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"confirmPassword",children:"Confirm Password"}),e.jsx(P,{id:"confirmPassword",type:p?"text":"password",placeholder:"Confirm your password",value:o,onChange:as,required:!0,className:`bg-background dark:bg-[#0D0D0D] ${R?"":"border-destructive"}`}),!R&&e.jsx("p",{className:"text-xs text-destructive mt-1",children:"Passwords do not match"})]}),e.jsx(x,{type:"submit",className:"w-full bg-primary hover:bg-primary/90",disabled:M||!R||N<2,children:M?"Creating account...":"Create Account"})]}),e.jsxs("div",{className:"text-center text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:"Already have an account? "}),e.jsx(x,{variant:"link",className:"text-primary p-0 h-auto",onClick:()=>s("login"),children:"Sign in"})]})]})}function ya({onNavigate:s}){const[a,t]=r.useState(""),[l,d]=r.useState(""),[n,c]=r.useState(!1),[h,m]=r.useState(""),[u,i]=r.useState(!1),[o,b]=r.useState("email"),p=async w=>{w.preventDefault(),m(""),i(!1),c(!0);try{let N;o==="email"?N=await ie.post("/auth/forgot-password",{gmail:a}):N=await ie.patch("/auth/direct-password-change",{gmail:a,newPassword:l}),N.data.success?i(!0):m(N.data.message||`Failed to ${o==="email"?"send reset link":"change password"}`)}catch(N){m(N.response?.data?.message||"An error occurred. Please try again.")}finally{c(!1)}};return e.jsxs("div",{className:"w-full max-w-md space-y-6 p-6 bg-card dark:bg-[#171717] rounded-xl shadow-lg border border-border dark:border-gray-800",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"Forgot Password"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:o==="email"?"Enter your email to receive a password reset link":"Enter your email and new password to change it directly"})]}),e.jsxs("div",{className:"flex space-x-2 p-1 bg-muted rounded-lg",children:[e.jsx("button",{type:"button",onClick:()=>b("email"),className:`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${o==="email"?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:"Email Reset Link"}),e.jsx("button",{type:"button",onClick:()=>b("direct"),className:`flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors ${o==="direct"?"bg-background text-foreground shadow-sm":"text-muted-foreground hover:text-foreground"}`,children:"Direct Change"})]}),e.jsxs("form",{onSubmit:p,className:"space-y-4",children:[h&&e.jsx(A,{type:"error",message:h}),u&&e.jsx(A,{type:"success",message:o==="email"?"If an account exists with this email, a password reset link has been sent. Please check your inbox.":"Password changed successfully! You can now login with your new password."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"email",children:"Email"}),e.jsx(P,{id:"email",type:"email",placeholder:"Enter your email",value:a,onChange:w=>t(w.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D]"})]}),o==="direct"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"newPassword",children:"New Password"}),e.jsx(P,{id:"newPassword",type:"password",placeholder:"Enter your new password",value:l,onChange:w=>d(w.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D]"})]}),e.jsx(x,{type:"submit",className:"w-full bg-primary hover:bg-primary/90",disabled:n,children:n?o==="email"?"Sending...":"Changing...":o==="email"?"Send Reset Link":"Change Password"}),e.jsx("div",{className:"text-center",children:e.jsx(x,{variant:"link",className:"text-sm text-primary p-0 h-auto",onClick:()=>s("login"),children:"Back to Login"})})]})]})}function ka({onNavigate:s,token:a}){const[t,l]=r.useState(a||""),[d,n]=r.useState(""),[c,h]=r.useState(""),[m,u]=r.useState(!1),[i,o]=r.useState(0),[b,p]=r.useState(!0),[w,N]=r.useState(!1),[T,R]=r.useState(""),[j,k]=r.useState(!1),[E,v]=r.useState(null);r.useEffect(()=>{if(!a){const S=new URLSearchParams(window.location.search).get("token");S&&l(S)}},[a]);const f=async M=>{if(M.preventDefault(),R(""),k(!1),v(null),d!==c){p(!1);return}if(i<2){v("weak_password");return}N(!0);try{const S=await ie.post("/auth/reset-password",{token:t,newPassword:d});S.data.success?k(!0):(R(S.data.message||"Failed to reset password"),S.data.message?.includes("token")?v("token_invalid"):v("other"))}catch(S){R(S.response?.data?.message||"An error occurred. Please try again."),S.response?.data?.message?.includes("token")?v("token_invalid"):v("other")}finally{N(!1)}},q=M=>{h(M.target.value),p(M.target.value===d||M.target.value==="")};return e.jsxs("div",{className:"w-full max-w-md space-y-6 p-6 bg-card dark:bg-[#171717] rounded-xl shadow-lg border border-border dark:border-gray-800",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:"Reset Password"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Create a new password for your account"})]}),e.jsxs("form",{onSubmit:f,className:"space-y-4",children:[E==="weak_password"&&e.jsx(A,{type:"error",title:"Password does not meet requirements:",message:"",items:["Minimum 8 characters","Include at least one uppercase letter","Include at least one number"]}),E==="token_invalid"&&e.jsx(A,{type:"error",message:"Invalid or expired reset token. Please request a new password reset link."}),E==="other"&&T&&e.jsx(A,{type:"error",message:T}),j&&e.jsx(A,{type:"success",message:"Your password has been reset successfully! Redirecting to login..."}),!t&&e.jsx(A,{type:"error",message:"No reset token found. Please use the link from your email."}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"password",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(P,{id:"password",type:m?"text":"password",placeholder:"Create a strong password",value:d,onChange:M=>n(M.target.value),required:!0,className:"bg-background dark:bg-[#0D0D0D] pr-10"}),e.jsx("button",{type:"button",onClick:()=>u(!m),className:"absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground",children:m?e.jsx(oe,{size:16}):e.jsx(U,{size:16})})]}),e.jsx(Xe,{password:d,onStrengthChange:o})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(D,{htmlFor:"confirmPassword",children:"Confirm Password"}),e.jsx(P,{id:"confirmPassword",type:m?"text":"password",placeholder:"Confirm your password",value:c,onChange:q,required:!0,className:`bg-background dark:bg-[#0D0D0D] ${b?"":"border-destructive"}`}),!b&&e.jsx("p",{className:"text-xs text-destructive mt-1",children:"Passwords do not match"})]}),e.jsx(x,{type:"submit",className:"w-full bg-primary hover:bg-primary/90",disabled:w||!b||i<2||!t,children:w?"Resetting...":"Reset Password"}),e.jsx("div",{className:"text-center",children:e.jsxs(x,{variant:"link",className:"text-sm text-primary p-0 h-auto flex items-center justify-center mx-auto",onClick:()=>s("login"),children:[e.jsx(ms,{size:16,className:"mr-1"}),"Back to Login"]})})]})]})}function Sa({children:s,title:a,subtitle:t}){return e.jsxs("div",{className:"min-h-screen flex flex-col bg-background dark:bg-[#0D0D0D]",children:[e.jsxs("header",{className:"w-full py-4 px-6 flex items-center justify-between border-b border-border dark:border-gray-800",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground",children:e.jsx(je,{className:"size-4"})}),e.jsx("span",{className:"font-semibold text-foreground",children:"CivicAssist"})]}),e.jsx(Qe,{})]}),e.jsx("main",{className:"flex-1 flex items-center justify-center p-6",children:e.jsxs("div",{className:"w-full max-w-md",children:[(a||t)&&e.jsxs("div",{className:"text-center mb-6",children:[a&&e.jsx("h1",{className:"text-3xl font-bold text-foreground",children:a}),t&&e.jsx("p",{className:"text-muted-foreground mt-2",children:t})]}),s]})}),e.jsx("footer",{className:"py-4 px-6 text-center border-t border-border dark:border-gray-800",children:e.jsxs("p",{className:"text-sm text-muted-foreground",children:["© ",new Date().getFullYear()," CivicAssist. All rights reserved."]})})]})}function Ca({children:s}){const{isAuthenticated:a,initialize:t}=W(),[l,d]=r.useState(!0),[n,c]=r.useState("login"),[h,m]=r.useState("");if(r.useEffect(()=>{(async()=>{await t(),d(!1)})()},[t]),l)return e.jsx("div",{className:"flex items-center justify-center h-screen",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})});const u=(i,o="")=>{c(i),o&&m(o)};if(!a){let i,o="",b="";switch(n){case"register":i=e.jsx(wa,{onNavigate:u}),o="Create Account",b="Join CivicAssist today";break;case"forgot-password":i=e.jsx(ya,{onNavigate:u}),o="Forgot Password",b="Reset your password";break;case"reset-password":i=e.jsx(ka,{token:h,onNavigate:u}),o="Reset Password",b="Create a new password";break;default:i=e.jsx(va,{onNavigate:u}),o="Welcome Back",b="Sign in to access your account"}return e.jsx(Sa,{title:o,subtitle:b,children:i})}return e.jsx(e.Fragment,{children:s})}class fe extends r.Component{constructor(a){super(a),this.state={hasError:!1}}static getDerivedStateFromError(a){return{hasError:!0,error:a}}componentDidCatch(a,t){console.error("Error Boundary caught an error:",a,t),this.setState({error:a,errorInfo:t})}handleReset=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})};render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-screen flex items-center justify-center p-4 bg-background",children:e.jsxs(y,{className:"w-full max-w-md",children:[e.jsxs(z,{className:"text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20",children:e.jsx(V,{className:"h-6 w-6 text-red-600 dark:text-red-400"})}),e.jsx(ae,{className:"text-xl font-semibold",children:"Something went wrong"}),e.jsx(Pe,{children:"An unexpected error occurred. Please try refreshing the page."})]}),e.jsxs(_,{className:"space-y-4",children:[e.jsxs(x,{onClick:this.handleReset,className:"w-full",variant:"default",children:[e.jsx(xs,{className:"mr-2 h-4 w-4"}),"Try Again"]}),e.jsx(x,{onClick:()=>window.location.reload(),className:"w-full",variant:"outline",children:"Refresh Page"}),!1]})]})}):this.props.children}}function Da({setCurrentPage:s}){const[a,t]=r.useState(!1),[l,d]=r.useState(""),[n,c]=r.useState(null),[h,m]=r.useState(""),[u,i]=r.useState(!1),[o,b]=r.useState(!1),[p,w]=r.useState(void 0),[N,T]=r.useState(!1),[R,j]=r.useState(!1),E=[{id:"GRV-2025-0009",title:"Street Light Not Working",status:"submitted",category:"Infrastructure",priority:"urgent",submittedDate:"2025-01-04",description:"The street light on Main Street has been non-functional for 3 days, causing safety concerns for pedestrians.",location:"Main Street, Block A"},{id:"GRV-2025-0008",title:"Garbage Collection Issue",status:"in-review",category:"Sanitation",priority:"high",submittedDate:"2025-01-03",description:"Garbage has not been collected from our area for over a week, leading to hygiene issues.",location:"Residential Area, Sector 5"},{id:"GRV-2025-0007",title:"Road Pothole Repair",status:"resolved",category:"Infrastructure",priority:"medium",submittedDate:"2025-01-02",description:"Large pothole on the main road causing vehicle damage and traffic issues.",location:"Highway Road, KM 15"}].filter(f=>{const q=f.id.toLowerCase().includes(l.toLowerCase())||f.title.toLowerCase().includes(l.toLowerCase()),M=!p||f.submittedDate===be(p,"yyyy-MM-dd");return q&&M}),v=()=>{i(!0),setTimeout(()=>{i(!1),b(!0),setTimeout(()=>{b(!1),t(!1),c(null),m(""),d("")},2e3)},1e3)};return e.jsxs(y,{className:"bg-card dark:bg-[#171717] rounded-xl shadow-lg",children:[e.jsxs(z,{className:"flex flex-row items-center gap-3 pb-6",children:[e.jsx("div",{className:"p-2 rounded-lg bg-purple-600/20",children:e.jsx(us,{className:"h-5 w-5 text-purple-400"})}),e.jsx(ae,{className:"text-xl font-semibold text-foreground",children:"Quick Actions"})]}),e.jsxs(_,{className:"flex flex-col gap-3 pt-0 pb-6",children:[e.jsxs(x,{onClick:()=>s("file-grievance"),className:"w-full py-3 text-sm font-medium bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[e.jsx(Ne,{className:"mr-2 h-4 w-4"}),"File Grievance"]}),e.jsxs(x,{onClick:()=>s("chat"),variant:"secondary",className:"w-full py-3 text-sm font-medium bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-all duration-200 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),"AI Chat"]}),e.jsxs(x,{onClick:()=>s("grievances"),className:"w-full py-3 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),"View All Grievances"]}),e.jsxs(x,{onClick:()=>t(!0),className:"w-full py-3 text-sm font-medium bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[e.jsx(ne,{className:"mr-2 h-4 w-4"}),"Submit Remark"]})]}),e.jsx(Ms,{open:a,onOpenChange:t,children:e.jsxs(Ts,{className:"bg-card dark:bg-[#171717] max-w-4xl max-h-[90vh] overflow-y-auto",children:[e.jsxs(Is,{children:[e.jsx(zs,{className:"text-foreground text-xl",children:"Submit Remark"}),e.jsx(_s,{className:"text-muted-foreground",children:"Select a grievance to add your remark with detailed information"})]}),o?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-green-500 text-6xl mb-4",children:"✓"}),e.jsx("p",{className:"text-foreground font-medium text-lg",children:"Remark submitted successfully!"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx(D,{htmlFor:"search",children:"Search Grievance"}),e.jsxs("div",{className:"relative mt-1",children:[e.jsx(P,{id:"search",className:"pl-9 bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800 text-foreground dark:text-white placeholder:text-muted-foreground focus:bg-card dark:focus:bg-[#0D0D0D] [&:-webkit-autofill]:bg-card [&:-webkit-autofill]:dark:bg-[#0D0D0D] [&:-webkit-autofill]:text-foreground [&:-webkit-autofill]:dark:text-white",placeholder:"Search by ID or title...",value:l,onChange:f=>d(f.target.value)}),e.jsx(hs,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"})]})]}),e.jsxs("div",{children:[e.jsx(D,{children:"Filter by Date"}),e.jsxs("div",{className:"flex gap-2 mt-1",children:[e.jsxs(Ae,{open:R,onOpenChange:j,children:[e.jsx(Ee,{asChild:!0,children:e.jsxs(x,{variant:"outline","data-empty":!p,className:"data-[empty=true]:text-muted-foreground w-full sm:w-auto justify-start text-left font-normal bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800 text-foreground dark:text-white hover:bg-card dark:hover:bg-[#0D0D0D]",children:[e.jsx(gs,{className:"mr-2 h-4 w-4"}),p?be(p,"PPP"):e.jsx("span",{children:"Pick a date"})]})}),e.jsx(Me,{className:"w-auto p-0 bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",align:"start",children:e.jsx(Bs,{mode:"single",selected:p,onSelect:f=>{w(f),j(!1)},className:"bg-card dark:bg-[#0D0D0D] text-foreground dark:text-white [&_button]:bg-card [&_button]:dark:bg-[#0D0D0D] [&_button]:text-foreground [&_button]:dark:text-white [&_button:hover]:bg-muted [&_button:hover]:dark:bg-zinc-800"})})]}),p&&e.jsx(x,{variant:"outline",size:"sm",onClick:()=>w(void 0),children:e.jsx(le,{className:"h-4 w-4"})})]})]})]}),(l||p)&&e.jsx("div",{className:"max-h-48 overflow-y-auto border rounded-md",children:E.map(f=>e.jsxs("div",{className:"p-3 hover:bg-muted cursor-pointer border-b last:border-b-0",onClick:()=>{c(f),d("")},children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("div",{className:"font-medium text-sm",children:f.id}),e.jsx(L,{className:`text-xs ${f.status==="submitted"?"bg-blue-600":f.status==="in-review"?"bg-yellow-600":"bg-green-600"}`,children:f.status})]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:f.title}),e.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:f.submittedDate})]},f.id))}),n&&e.jsx(y,{className:"bg-background/50 dark:bg-black/20 border",children:e.jsx("div",{className:"p-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ae,{className:"text-sm font-semibold",children:n.id}),e.jsx(L,{className:`text-xs px-2 py-0.5 ${n.status==="submitted"?"bg-blue-600":n.status==="in-review"?"bg-yellow-600":"bg-green-600"}`,children:n.status})]}),e.jsx("p",{className:"text-sm font-medium text-foreground mt-1",children:n.title}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["Submitted on ",n.submittedDate]}),!N&&e.jsxs("div",{className:"mt-3 space-y-2",children:[e.jsx("p",{className:"text-xs text-muted-foreground",children:n.description}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Category:"}),e.jsx("p",{className:"text-muted-foreground",children:n.category})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Priority:"}),e.jsx("p",{className:"text-muted-foreground",children:n.priority})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Status:"}),e.jsx("p",{className:"text-muted-foreground",children:n.status})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Date:"}),e.jsx("p",{className:"text-muted-foreground",children:n.submittedDate})]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-xs",children:"Location:"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:n.location})]})]})]}),e.jsxs("div",{className:"flex gap-1 ml-2 flex-shrink-0",children:[e.jsx(x,{variant:"ghost",size:"sm",onClick:()=>T(!N),children:e.jsx(bs,{className:"h-4 w-4"})}),e.jsx(x,{variant:"ghost",size:"sm",onClick:()=>{c(null),T(!1)},children:e.jsx(le,{className:"h-4 w-4"})})]})]})})}),e.jsxs("div",{children:[e.jsx(D,{htmlFor:"remark",children:"Your Remark"}),e.jsx(Ls,{id:"remark",placeholder:"Enter your detailed remark...",value:h,onChange:f=>m(f.target.value),className:"mt-1 min-h-[120px] bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800 text-foreground dark:text-white placeholder:text-muted-foreground focus:bg-card dark:focus:bg-[#0D0D0D] [&:-webkit-autofill]:bg-card [&:-webkit-autofill]:dark:bg-[#0D0D0D] [&:-webkit-autofill]:text-foreground [&:-webkit-autofill]:dark:text-white"})]}),e.jsxs(x,{onClick:v,disabled:!n||!h||u,className:"w-full",children:[u?e.jsx(ps,{className:"mr-2 h-4 w-4 animate-spin"}):null,"Submit Remark"]})]})]})})]})}const Ra=[{id:1,user:"Admin Team",action:"updated status of",target:"Grievance #GRV-2025-0009",timestamp:"15 minutes ago",unread:!0},{id:2,user:"System",action:"generated report for",target:"Monthly Summary",timestamp:"45 minutes ago",unread:!0},{id:3,user:"Support Team",action:"replied to your remark in",target:"Street Light Issue",timestamp:"4 hours ago",unread:!1},{id:4,user:"AI Assistant",action:"completed analysis of",target:"Traffic Signal Report",timestamp:"12 hours ago",unread:!1},{id:5,user:"Department Head",action:"approved your",target:"Budget Request #BDG-001",timestamp:"2 days ago",unread:!1}];function Pa(){const[s,a]=r.useState(Ra),[t,l]=r.useState("new"),d=s.filter(i=>i.unread),n=s.filter(i=>!i.unread),c=d.length,h=i=>{a(o=>o.map(b=>b.id===i?{...b,unread:!1}:b))},m=()=>{a(i=>i.map(o=>({...o,unread:!1}))),setTimeout(()=>l("seen"),600)},u=i=>{i&&c>0&&l("new")};return e.jsxs(Ae,{onOpenChange:u,children:[e.jsx(Ee,{asChild:!0,children:e.jsxs(x,{size:"icon",variant:"outline",className:"relative bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 hover:bg-muted","aria-label":"Open notifications",children:[e.jsx(fs,{size:16,"aria-hidden":"true"}),c>0&&e.jsx(L,{className:"absolute -top-2 left-full min-w-5 -translate-x-1/2 px-1",children:c>99?"99+":c})]})}),e.jsx(Me,{className:"w-80 p-0 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:e.jsxs(Fs,{value:t,onValueChange:l,className:"w-full",children:[e.jsx("div",{className:"p-3 border-b",children:e.jsxs(Gs,{className:"grid w-full grid-cols-2",children:[e.jsxs(ue,{value:"new",className:"text-sm",children:["New ",c>0&&`(${c})`]}),e.jsx(ue,{value:"seen",className:"text-sm",children:"Seen"})]})}),e.jsx(he,{value:"new",className:"mt-0",children:e.jsxs("div",{className:"max-h-80 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-border [&::-webkit-scrollbar-thumb]:rounded-full",children:[c>0&&e.jsx("div",{className:"flex justify-end p-3 border-b",children:e.jsxs(x,{variant:"ghost",size:"sm",onClick:m,className:"text-xs",children:[e.jsx(me,{className:"mr-1 h-3 w-3"}),"Read All"]})}),e.jsx(js,{children:d.map(i=>e.jsx(Ns.div,{initial:{opacity:1,height:"auto"},exit:{opacity:0,height:0,scale:.95},transition:{duration:.5,ease:"easeInOut"},className:"border-b last:border-b-0",children:e.jsxs("div",{className:"flex items-start gap-3 p-3 hover:bg-muted/50",children:[e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("p",{className:"text-sm",children:[e.jsx("span",{className:"font-medium",children:i.user})," ",i.action," ",e.jsx("span",{className:"font-medium",children:i.target})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:i.timestamp})]}),e.jsx(x,{size:"sm",variant:"ghost",onClick:()=>h(i.id),className:"h-6 w-6 p-0",children:e.jsx(U,{className:"h-3 w-3"})})]})},i.id))}),c===0&&e.jsx("div",{className:"p-6 text-center text-sm text-muted-foreground",children:"No new notifications"})]})}),e.jsx(he,{value:"seen",className:"mt-0",children:e.jsxs("div",{className:"max-h-80 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-border [&::-webkit-scrollbar-thumb]:rounded-full",children:[n.map(i=>e.jsx("div",{className:"border-b last:border-b-0",children:e.jsxs("div",{className:"flex items-start gap-3 p-3 hover:bg-muted/50",children:[e.jsxs("div",{className:"flex-1 space-y-1",children:[e.jsxs("p",{className:"text-sm opacity-75",children:[e.jsx("span",{className:"font-medium",children:i.user})," ",i.action," ",e.jsx("span",{className:"font-medium",children:i.target})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:i.timestamp})]}),e.jsx("div",{className:"h-6 w-6 flex items-center justify-center",children:e.jsx(me,{className:"h-3 w-3 text-muted-foreground"})})]})},i.id)),n.length===0&&e.jsx("div",{className:"p-6 text-center text-sm text-muted-foreground",children:"No seen notifications"})]})})]})})]})}const Aa=r.lazy(()=>te(()=>import("./page-components-1uWkaYuZ.js").then(s=>s.h),__vite__mapDeps([0,1,2,3,4,5,6])).then(s=>({default:s.GrievancesPage}))),Ea=r.lazy(()=>te(()=>import("./page-components-1uWkaYuZ.js").then(s=>s.p),__vite__mapDeps([0,1,2,3,4,5,6])).then(s=>({default:s.ProfileSettings}))),Ma=r.lazy(()=>te(()=>import("./page-components-1uWkaYuZ.js").then(s=>s.i),__vite__mapDeps([0,1,2,3,4,5,6])).then(s=>({default:s.ChatPage}))),Ta=r.lazy(()=>te(()=>import("./page-components-1uWkaYuZ.js").then(s=>s.j),__vite__mapDeps([0,1,2,3,4,5,6])).then(s=>({default:s.GrievanceForm})));function Ia(){const[s,a]=r.useState("dashboard"),[t,l]=r.useState(null),[d,n]=r.useTransition(),{isOpen:c,openCanvas:h}=Vs(),{user:m}=W(),u=b=>{n(()=>{a(b)})},i=b=>{l(t===b?null:b)},o=()=>s==="grievances"?e.jsx(Aa,{}):s==="chat"?e.jsx(Ma,{}):s==="file-grievance"?e.jsx(Ta,{}):s==="profile"||s==="settings"?e.jsx(Ea,{}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6",children:[e.jsxs(y,{className:"bg-card dark:bg-[#171717] p-4 md:p-6 rounded-xl shadow-lg h-[160px] sm:h-[180px] flex flex-col justify-between overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-start gap-2 min-h-0",children:[e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-muted-foreground truncate flex-1",children:"Total Grievances"}),e.jsx("span",{className:"text-xs sm:text-sm font-semibold bg-green-500/20 text-green-400 px-2 py-1 rounded-full whitespace-nowrap flex-shrink-0",children:"+12.5% ↗"})]}),e.jsx("div",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground truncate",children:"1,247"}),e.jsxs("div",{className:"min-h-0",children:[e.jsx("p",{className:"text-xs sm:text-sm font-semibold text-foreground truncate",children:"Trending up this month"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground truncate",children:"Cases submitted in last 30 days"})]})]}),e.jsxs(y,{className:"bg-card dark:bg-[#171717] p-4 md:p-6 rounded-xl shadow-lg h-[160px] sm:h-[180px] flex flex-col justify-between overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-start gap-2 min-h-0",children:[e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-muted-foreground truncate flex-1",children:"Resolved"}),e.jsx("span",{className:"text-xs sm:text-sm font-semibold bg-green-500/20 text-green-400 px-2 py-1 rounded-full whitespace-nowrap flex-shrink-0",children:"+8.2% ↗"})]}),e.jsx("div",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground truncate",children:"892"}),e.jsxs("div",{className:"min-h-0",children:[e.jsx("p",{className:"text-xs sm:text-sm font-semibold text-foreground truncate",children:"Strong resolution rate"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground truncate",children:"71.5% of total cases resolved"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6",children:[e.jsxs(y,{className:"bg-card dark:bg-[#171717] p-4 md:p-6 rounded-xl shadow-lg h-[160px] sm:h-[180px] flex flex-col justify-between overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-start gap-2 min-h-0",children:[e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-muted-foreground truncate flex-1",children:"In Progress"}),e.jsx("span",{className:"text-xs sm:text-sm font-semibold bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full whitespace-nowrap flex-shrink-0",children:"18.8%"})]}),e.jsx("div",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground truncate",children:"234"}),e.jsxs("div",{className:"min-h-0",children:[e.jsx("p",{className:"text-xs sm:text-sm font-semibold text-foreground truncate",children:"Active cases pending"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground truncate",children:"Currently under review"})]})]}),e.jsxs(y,{className:"bg-card dark:bg-[#171717] p-4 md:p-6 rounded-xl shadow-lg h-[160px] sm:h-[180px] flex flex-col justify-between overflow-hidden",children:[e.jsxs("div",{className:"flex justify-between items-start gap-2 min-h-0",children:[e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-muted-foreground truncate flex-1",children:"Avg Resolution"}),e.jsx("span",{className:"text-xs sm:text-sm font-semibold bg-red-500/20 text-red-400 px-2 py-1 rounded-full whitespace-nowrap flex-shrink-0",children:"-15% ↘"})]}),e.jsx("div",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground truncate",children:"2.4 days"}),e.jsxs("div",{className:"min-h-0",children:[e.jsx("p",{className:"text-xs sm:text-sm font-semibold text-foreground truncate",children:"Faster response time"}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground truncate",children:"Average time to resolve cases"})]})]})]}),e.jsx(Da,{setCurrentPage:u})]}),e.jsx("div",{children:e.jsxs(y,{className:"bg-card dark:bg-[#171717] rounded-xl shadow-lg max-h-[85vh] flex flex-col",children:[e.jsxs(z,{className:"pb-6 flex-shrink-0",children:[e.jsx(ae,{className:"text-xl font-semibold text-foreground",children:"Recent Activity"}),e.jsx(Pe,{className:"text-muted-foreground",children:"Your latest grievances and system updates"})]}),e.jsx(_,{className:"pb-6 flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-muted [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-muted-foreground/50",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(0),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"📋"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Grievance Status Updated"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Your grievance status changed to 'In Review'"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"2 hours ago"}),t===0?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===0&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-20240115-0001"}),e.jsx(L,{className:"bg-yellow-600 text-white text-xs px-2 py-0.5",children:"In Review"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(ws,{className:"h-3 w-3"}),e.jsx("span",{children:"Jan 15, 2024"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Street Light Not Working"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-blue-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Infrastructure"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3 text-red-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Urgent"})]})]})]}),e.jsxs("div",{className:"bg-background/50 dark:bg-black/20 rounded-md p-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-muted-foreground",children:"Assigned to:"}),e.jsx("span",{className:"font-medium text-foreground",children:"Infrastructure Team"})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs mt-1",children:[e.jsx("span",{className:"text-muted-foreground",children:"Est. Resolution:"}),e.jsx("span",{className:"font-medium text-foreground",children:"3-5 business days"})]})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"Open Grievance"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(1),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"✅"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Grievance Resolved"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Your grievance has been resolved"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"1 day ago"}),t===1?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===1&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-20240114-0003"}),e.jsx(L,{className:"bg-green-600 text-white text-xs px-2 py-0.5",children:"Resolved"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(I,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{children:"2 days"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Garbage Collection Issue"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Sanitation"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3 text-orange-400"}),e.jsx("span",{className:"text-muted-foreground",children:"High"})]})]})]}),e.jsxs("div",{className:"bg-green-500/10 border border-green-500/20 rounded-md p-2",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[e.jsx(I,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{className:"font-medium text-green-400",children:"Resolution Complete"})]}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Collection schedule has been restored to normal operations"})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Resolution Details"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(2),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"💬"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"New Response from Admin"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Admin added a response to your grievance"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"30 minutes ago"}),t===2?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===2&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-20240115-0001"}),e.jsx(L,{className:"bg-blue-600 text-white text-xs px-2 py-0.5",children:"Response"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx($,{className:"h-3 w-3 text-blue-400"}),e.jsx("span",{children:"30 min ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx(O,{className:"h-3 w-3 text-white"})}),e.jsx("span",{className:"text-sm font-medium text-foreground",children:"Infrastructure Team"})]}),e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Street Light Inspection Update"})]}),e.jsx("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-md p-3",children:e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:'"We have scheduled an inspection for tomorrow morning at 9:00 AM. Our technical team will assess the street light issue and provide an estimated repair timeline. You will receive updates via SMS and email."'})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(x,{size:"sm",className:"flex-1 bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Conversation"]}),e.jsxs(x,{size:"sm",variant:"outline",className:"flex-1",children:[e.jsx($,{className:"mr-2 h-3 w-3"}),"Reply"]})]})]})})]})})]}),e.jsx("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"🔔"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Profile Updated"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"You updated your profile information"})]})]}),e.jsx("span",{className:"text-xs text-muted-foreground",children:"3 hours ago"})]})}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(4),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"📞"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Phone Call Scheduled"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Follow-up call arranged with department head"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"4 hours ago"}),t===4?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===4&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-2025-0004"}),e.jsx(L,{className:"bg-purple-600 text-white text-xs px-2 py-0.5",children:"Call Scheduled"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx($,{className:"h-3 w-3 text-purple-400"}),e.jsx("span",{children:"4 hrs ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Public Park Lighting Issue"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-purple-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Recreation"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3 text-yellow-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Medium"})]})]})]}),e.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx($,{className:"h-3 w-3 text-purple-400"}),e.jsx("span",{className:"font-medium text-purple-400",children:"Call Arranged"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"Department head will call you tomorrow at 2:00 PM to discuss the park lighting repairs and timeline for completion."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Call Details"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(5),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"📊"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Survey Completed"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Site inspection and damage assessment finished"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"6 hours ago"}),t===5?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===5&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-2025-0003"}),e.jsx(L,{className:"bg-teal-600 text-white text-xs px-2 py-0.5",children:"Survey Done"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(I,{className:"h-3 w-3 text-teal-400"}),e.jsx("span",{children:"6 hrs ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Drainage System Blockage"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-teal-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Infrastructure"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3 text-orange-400"}),e.jsx("span",{className:"text-muted-foreground",children:"High"})]})]})]}),e.jsxs("div",{className:"bg-teal-500/10 border border-teal-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx(I,{className:"h-3 w-3 text-teal-400"}),e.jsx("span",{className:"font-medium text-teal-400",children:"Assessment Complete"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"Technical team has completed the site survey. Blockage severity confirmed as high priority. Repair work will begin within 48 hours."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Survey Report"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(6),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"📝"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Report Generated"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Monthly grievance summary report created"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"8 hours ago"}),t===6?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===6&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"RPT-2025-001"}),e.jsx(L,{className:"bg-indigo-600 text-white text-xs px-2 py-0.5",children:"Report"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx($,{className:"h-3 w-3 text-indigo-400"}),e.jsx("span",{children:"8 hrs ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"January 2025 Summary"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-indigo-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Analytics"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(I,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Complete"})]})]})]}),e.jsxs("div",{className:"bg-indigo-500/10 border border-indigo-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx(I,{className:"h-3 w-3 text-indigo-400"}),e.jsx("span",{className:"font-medium text-indigo-400",children:"Report Ready"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"Your monthly grievance activity report is now available. It includes resolution statistics, response times, and department performance metrics."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"Download Report"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(7),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"🔔"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Reminder Set"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Follow-up reminder scheduled for pending grievance"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"10 hours ago"}),t===7?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===7&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"GRV-2025-0002"}),e.jsx(L,{className:"bg-amber-600 text-white text-xs px-2 py-0.5",children:"Reminder"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(V,{className:"h-3 w-3 text-amber-400"}),e.jsx("span",{children:"10 hrs ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Bus Stop Shelter Repair"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-amber-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Transportation"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(V,{className:"h-3 w-3 text-yellow-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Medium"})]})]})]}),e.jsxs("div",{className:"bg-amber-500/10 border border-amber-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx(V,{className:"h-3 w-3 text-amber-400"}),e.jsx("span",{className:"font-medium text-amber-400",children:"Follow-up Scheduled"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"Automatic reminder set for tomorrow to check progress on bus shelter repair. You'll receive a notification if no updates are provided."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Reminder"]})]})})]})})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(8),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"💰"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Budget Approved"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Funding allocated for infrastructure improvements"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"12 hours ago"}),t===8?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===8&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"BDG-2025-001"}),e.jsx(L,{className:"bg-emerald-600 text-white text-xs px-2 py-0.5",children:"Approved"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(I,{className:"h-3 w-3 text-emerald-400"}),e.jsx("span",{children:"12 hrs ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Q1 Infrastructure Budget"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-emerald-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Finance"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(I,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Approved"})]})]})]}),e.jsxs("div",{className:"bg-emerald-500/10 border border-emerald-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx(I,{className:"h-3 w-3 text-emerald-400"}),e.jsx("span",{className:"font-medium text-emerald-400",children:"Funding Secured"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"Budget approval received for road repairs, street lighting upgrades, and drainage improvements. Work orders will be issued within 48 hours."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Budget Details"]})]})})]})})]}),e.jsx("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"📧"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"Email Notification"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Weekly digest sent to your registered email"})]})]}),e.jsx("span",{className:"text-xs text-muted-foreground",children:"1 day ago"})]})}),e.jsxs("div",{className:"border border-border rounded-lg p-4 bg-background/50 dark:bg-black/20 hover:bg-background/70 dark:hover:bg-black/30 transition-colors",children:[e.jsxs("div",{className:"flex items-center justify-between cursor-pointer",onClick:()=>i(9),children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-lg",children:"🔄"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-foreground",children:"System Update"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Platform maintenance completed successfully"})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-xs text-muted-foreground",children:"1 day ago"}),t===9?e.jsx(F,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})]}),t===9&&e.jsx("div",{className:"mt-3 pt-3 border-t border-border",children:e.jsxs(y,{className:"bg-background/30 dark:bg-black/40 border-border/50",children:[e.jsx(z,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-semibold text-foreground",children:"SYS-2025-001"}),e.jsx(L,{className:"bg-cyan-600 text-white text-xs px-2 py-0.5",children:"System"})]}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[e.jsx(I,{className:"h-3 w-3 text-cyan-400"}),e.jsx("span",{children:"1 day ago"})]})]})}),e.jsx(_,{className:"pt-0 pb-3",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm text-foreground mb-1",children:"Platform Maintenance v2.1.5"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(O,{className:"h-3 w-3 text-cyan-400"}),e.jsx("span",{className:"text-muted-foreground",children:"System"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(I,{className:"h-3 w-3 text-green-400"}),e.jsx("span",{className:"text-muted-foreground",children:"Complete"})]})]})]}),e.jsxs("div",{className:"bg-cyan-500/10 border border-cyan-500/20 rounded-md p-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-xs mb-2",children:[e.jsx(I,{className:"h-3 w-3 text-cyan-400"}),e.jsx("span",{className:"font-medium text-cyan-400",children:"Update Complete"})]}),e.jsx("p",{className:"text-xs text-foreground leading-relaxed",children:"System maintenance has been completed successfully. New features include improved search functionality, enhanced security measures, and better mobile responsiveness."})]}),e.jsxs(x,{size:"sm",className:"w-full bg-blue-600 hover:bg-blue-700 text-white",children:[e.jsx(B,{className:"mr-2 h-3 w-3"}),"View Release Notes"]})]})})]})})]})]})}),e.jsx(Os,{className:"flex-shrink-0",children:e.jsx("button",{className:"text-sm text-blue-400 hover:text-blue-300",children:"View All Activities"})})]})})]})});return e.jsx(fe,{children:e.jsx(fa,{defaultTheme:"dark",storageKey:"vite-ui-theme",children:e.jsx(qs,{children:e.jsx(Ca,{children:e.jsxs(Ge,{children:[e.jsx(ba,{setCurrentPage:u}),e.jsxs("main",{className:"flex-1 p-4 bg-background dark:bg-[#0D0D0D] min-h-screen relative overflow-hidden min-w-0 contain-layout",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(Be,{}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-foreground",children:s==="dashboard"?"Dashboard":s==="grievances"?"Grievances":s==="chat"?"Chat":s==="file-grievance"?"File New Grievance":s==="profile"||s==="settings"?"Profile Settings":"Dashboard"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s==="dashboard"?`Welcome ${m?.fullName||"User"}`:s==="grievances"?"Check out your grievances and track their progress!":s==="chat"?"":s==="file-grievance"?"Submit a new grievance with our step-by-step form":s==="profile"||s==="settings"?"Manage your account and preferences":`Welcome ${m?.fullName||"User"}`})]})]}),e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:"flex items-center gap-2 bg-background dark:bg-[#0D0D0D] border border-border dark:border-gray-800 rounded-lg p-2",children:[e.jsx(K,{children:e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:e.jsx("div",{children:e.jsx(Pa,{})})}),e.jsx(Q,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:e.jsx("p",{children:"Notifications"})})]})}),e.jsx(K,{children:e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:e.jsx("div",{children:e.jsx(Qe,{})})}),e.jsx(Q,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:e.jsx("p",{children:"Toggle Theme"})})]})}),!c&&s==="chat"&&e.jsx(K,{children:e.jsxs(J,{children:[e.jsx(Z,{asChild:!0,children:e.jsx("button",{onClick:()=>h(),className:"inline-flex items-center justify-center w-8 h-8 rounded-md text-foreground transition-colors hover:bg-muted",children:e.jsx(vs,{className:"h-4 w-4"})})}),e.jsx(Q,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:e.jsx("p",{children:"Open Canvas"})})]})})]})})]}),e.jsx(fe,{fallback:e.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:e.jsxs("div",{className:"text-center",children:[e.jsx(V,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),e.jsx("p",{className:"text-muted-foreground",children:"Failed to load page component"}),e.jsx(x,{onClick:()=>window.location.reload(),className:"mt-4",variant:"outline",children:"Refresh Page"})]})}),children:e.jsx(r.Suspense,{fallback:e.jsx(Na,{size:"lg"}),children:e.jsx("div",{className:d?"opacity-50 transition-opacity":"",children:o()})})}),d&&e.jsx("div",{className:"fixed top-4 right-4 z-50",children:e.jsxs("div",{className:"bg-background border rounded-lg px-3 py-2 shadow-lg flex items-center gap-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Loading..."})]})})]})]})})})})})}Hs.createRoot(document.getElementById("root")).render(e.jsx(Ia,{}));
