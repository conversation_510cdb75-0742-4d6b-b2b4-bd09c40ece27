import{r as p,j as s,n as Hr,d as Kt,R,a7 as Hn,C as Gn,g as yo,o as jo,a8 as Gr,X as De,h as Yr,a9 as No,aa as Do,a6 as Qt,N as Co,i as In,ab as So,v as Se,U as ko,L as Mt,ac as Ze,q as bt,H as qr,b as Mo,ad as Ro,a5 as Et,G as _o,l as wt,ae as dn,J as Be,af as ar,M as It,ag as Po,D as gt,ah as Ur,ai as Fo,aj as Eo,u as On,ak as Io,al as cn,am as ir,an as Oo,ao as To,W as Ao,p as Ke,ap as Wo,aq as $o,Z as zo,Y as Lo,_ as Vo,c as Bo,Q as Ho,ar as Go,as as Yo,T as qo}from"./ui-vendor-CJ9vBRYM.js";import{c as E,C as Zr,P as Uo,R as Zo,T as Xo,b as lr,B as O,a as <PERSON>,d as Xr,e as Qo,f as Jo,u as $e,g as Yn,h as Ee,i as at,j as Rt,k as Le,S as Qe,l as Je,m as et,n as tt,o as U,p as ft,G as ea,v as Kr,q as ta,r as Qr,s as na,_ as ra,t as he,w as sa,x as oa,y as yt,z as jt,L as Me,D as lt,A as Ut,E as dt,F as ct,H as ut,I as ht,J as aa,K as Jr,M as es,N as ia,O as ts,Q as la,U as da,V as un,W as ca}from"./grievance-components-D9xcC4N3.js";import{g as ua}from"./react-vendor-DavUf6mE.js";import{c as ma}from"./state-vendor-CoPsor3D.js";import{o as dr,s as cr,p as fa,a as ga,$ as ha,b as ns,F as pa,C as xa,u as va,c as ba,d as Vt,_ as mn,e as Pe,f as ur,g as wa,n as mr}from"./form-vendor-iDrVnY0w.js";const se=p.forwardRef(({className:e,type:n,value:t,...r},o)=>{const a=t!==void 0?t:"";return s.jsx("input",{type:n,value:a,className:E("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:o,...r})});se.displayName="Input";function ot({className:e,...n}){return s.jsx("div",{className:E("animate-pulse rounded-md bg-primary/10",e),...n})}const fn=Uo,gn=Zo,hn=Xo,Zt=p.forwardRef(({className:e,sideOffset:n=4,...t},r)=>s.jsx(Zr,{ref:r,sideOffset:n,className:E("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));Zt.displayName=Zr.displayName;const ya=`
<h1>Formal Grievance: Project Phoenix</h1>
<p><strong>Date:</strong> January 8, 2025</p>
<p>&nbsp;</p>
<h2>Submitted By</h2>
<div class="info-group">
  <p><strong>Name:</strong> Alex Doe</p>
  <p><strong>Department:</strong> Engineering</p>
  <p><strong>Email:</strong> <EMAIL></p>
</div>
<p>&nbsp;</p>
<h2>Subject: Grievance Regarding Project 'Phoenix' Scope Creep and Inadequate Resource Allocation</h2>
<p>&nbsp;</p>
<h2>1. Introduction</h2>
<p>This document outlines a formal grievance concerning the management and execution of Project 'Phoenix'. The project, initially scoped for a three-month development cycle, has been subjected to significant and undocumented scope creep, leading to an unsustainable work environment and jeopardizing the project's success.</p>
<p>&nbsp;</p>
<h2>2. Details of the Grievance</h2>
<p>The primary issues are as follows:</p>
<ul>
    <li><strong>Uncontrolled Scope Creep:</strong> Since the project's inception, numerous features and functionality changes have been verbally requested by stakeholders without following the established change request protocol. These changes have collectively expanded the project's complexity by an estimated 50%.</li>
    <li><strong>Inadequate Resource Allocation:</strong> The development team consists of three engineers, which was sufficient for the original scope. However, the current workload requires at least two additional engineers to meet the revised, unstated deadlines.</li>
    <li><strong>Unrealistic Deadlines:</strong> Despite the expanded scope, the original deadline has not been adjusted. This has resulted in the team consistently working overtime, including weekends, leading to burnout and a decline in morale.</li>
</ul>
<p>&nbsp;</p>
<h2>3. Impact on Team and Project</h2>
<p>The current situation has had several negative impacts:</p>
<ul>
    <li>Decreased code quality and an increase in bugs due to rushed development.</li>
    <li>High levels of stress and fatigue within the team, impacting productivity and innovation.</li>
    <li>A high risk of missing the project deadline, which will have financial repercussions for the company.</li>
</ul>
<p>&nbsp;</p>
<h2>4. Desired Resolution</h2>
<p>To rectify this situation and ensure the successful delivery of Project 'Phoenix', I propose the following actions:</p>
<ol>
    <li>An immediate freeze on all new feature requests until the current scope is formally documented and agreed upon by all stakeholders.</li>
    <li>A formal review of the project timeline and a realistic adjustment of the delivery date based on the approved scope.</li>
    <li>The allocation of at least one additional senior engineer to the project team to manage the increased complexity.</li>
    <li>Re-commitment from all stakeholders to adhere to the formal change management process for any future modifications.</li>
</ol>
<p>&nbsp;</p>
<p>I am confident that with these adjustments, we can bring Project 'Phoenix' back on track and deliver a high-quality product. I am available to discuss this matter further at your earliest convenience.</p>
`,At=ma(e=>({isOpen:!1,content:"",width:50,openCanvas:n=>e({isOpen:!0,content:n||ya}),closeCanvas:()=>e({isOpen:!1,content:""}),setWidth:n=>e({width:Math.max(30,Math.min(70,n))})}));function Bt({type:e,title:n,message:t,items:r,className:o}){const a=e==="error";return s.jsx("div",{className:`rounded-md border ${a?"border-red-500/50 text-red-600 bg-red-50 dark:bg-red-950/20":"border-green-500/50 text-green-600 bg-green-50 dark:bg-green-950/20"} px-4 py-3 ${o||""}`,children:s.jsxs("div",{className:"flex gap-3",children:[a?s.jsx(Hr,{className:"mt-0.5 shrink-0 opacity-60",size:16,"aria-hidden":"true"}):s.jsx(Kt,{className:"mt-0.5 shrink-0 opacity-80",size:16,"aria-hidden":"true"}),s.jsxs("div",{className:"grow space-y-1",children:[n&&s.jsx("p",{className:"text-sm font-medium",children:n}),s.jsx("p",{className:"text-sm",children:t}),r&&r.length>0&&s.jsx("ul",{className:"list-inside list-disc text-sm opacity-80",children:r.map((i,l)=>s.jsx("li",{children:i},l))})]})]})})}function ja({message:e,type:n,duration:t=3e3,onClose:r}){const[o,a]=p.useState(!0);if(p.useEffect(()=>{const d=setTimeout(()=>{a(!1),r&&r()},t);return()=>clearTimeout(d)},[t,r]),!o)return null;const i={success:"bg-green-500/90",error:"bg-red-500/90",info:"bg-blue-500/90"}[n],l={success:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),error:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),info:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}[n];return s.jsx("div",{className:"fixed top-4 right-4 z-50 animate-fade-in",children:s.jsxs("div",{className:`${i} text-white px-4 py-3 rounded-md shadow-lg flex items-center`,children:[s.jsx("div",{className:"mr-2",children:l}),s.jsx("div",{children:e}),s.jsx("button",{onClick:()=>{a(!1),r&&r()},className:"ml-4 text-white hover:text-gray-200",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})}const rs=p.createContext(void 0);function $u({children:e}){const[n,t]=p.useState([]);let r=0;const o=(i,l,d=3e3)=>{const c=r++;t(u=>[...u,{id:c,message:i,type:l,duration:d}])},a=i=>{t(l=>l.filter(d=>d.id!==i))};return s.jsxs(rs.Provider,{value:{showToast:o},children:[e,s.jsx("div",{className:"toast-container",children:n.map(i=>s.jsx(ja,{message:i.message,type:i.type,duration:i.duration,onClose:()=>a(i.id)},i.id))})]})}function Na(){const e=p.useContext(rs);if(e===void 0)throw new Error("useToast must be used within a ToastProvider");return e}const Da={},_t={};function Pt(e,n){try{const r=(Da[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(n).split("GMT")[1]||"";return r in _t?_t[r]:fr(r,r.split(":"))}catch{if(e in _t)return _t[e];const t=e?.match(Ca);return t?fr(e,t.slice(1)):NaN}}const Ca=/([+-]\d\d):?(\d\d)?/;function fr(e,n){const t=+n[0],r=+(n[1]||0);return _t[e]=t>0?t*60+r:t*60-r}class We extends Date{constructor(...n){super(),n.length>1&&typeof n[n.length-1]=="string"&&(this.timeZone=n.pop()),this.internal=new Date,isNaN(Pt(this.timeZone,this))?this.setTime(NaN):n.length?typeof n[0]=="number"&&(n.length===1||n.length===2&&typeof n[1]!="number")?this.setTime(n[0]):typeof n[0]=="string"?this.setTime(+new Date(n[0])):n[0]instanceof Date?this.setTime(+n[0]):(this.setTime(+new Date(...n)),ss(this),Tn(this)):this.setTime(Date.now())}static tz(n,...t){return t.length?new We(...t,n):new We(Date.now(),n)}withTimeZone(n){return new We(+this,n)}getTimezoneOffset(){return-Pt(this.timeZone,this)}setTime(n){return Date.prototype.setTime.apply(this,arguments),Tn(this),+this}[Symbol.for("constructDateFrom")](n){return new We(+new Date(n),this.timeZone)}}const gr=/^(get|set)(?!UTC)/;Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!gr.test(e))return;const n=e.replace(gr,"$1UTC");We.prototype[n]&&(e.startsWith("get")?We.prototype[e]=function(){return this.internal[n]()}:(We.prototype[e]=function(){return Date.prototype[n].apply(this.internal,arguments),Sa(this),+this},We.prototype[n]=function(){return Date.prototype[n].apply(this,arguments),Tn(this),+this}))});function Tn(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function Sa(e){Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate()),Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds()),ss(e)}function ss(e){const n=Pt(e.timeZone,e),t=new Date(+e);t.setUTCHours(t.getUTCHours()-1);const r=-new Date(+e).getTimezoneOffset(),o=-new Date(+t).getTimezoneOffset(),a=r-o,i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();a&&i&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+a);const l=r-n;l&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);const d=Pt(e.timeZone,e),u=-new Date(+e).getTimezoneOffset()-d,h=d!==n,g=u-l;if(h&&g){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+g);const f=Pt(e.timeZone,e),x=d-f;x&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+x),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+x))}}class pe extends We{static tz(n,...t){return t.length?new pe(...t,n):new pe(Date.now(),n)}toISOString(){const[n,t,r]=this.tzComponents(),o=`${n}${t}:${r}`;return this.internal.toISOString().slice(0,-1)+o}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){const[n,t,r,o]=this.internal.toUTCString().split(" ");return`${n?.slice(0,-1)} ${r} ${t} ${o}`}toTimeString(){const n=this.internal.toUTCString().split(" ")[4],[t,r,o]=this.tzComponents();return`${n} GMT${t}${r}${o} (${ka(this.timeZone,this)})`}toLocaleString(n,t){return Date.prototype.toLocaleString.call(this,n,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(n,t){return Date.prototype.toLocaleDateString.call(this,n,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(n,t){return Date.prototype.toLocaleTimeString.call(this,n,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){const n=this.getTimezoneOffset(),t=n>0?"-":"+",r=String(Math.floor(Math.abs(n)/60)).padStart(2,"0"),o=String(Math.abs(n)%60).padStart(2,"0");return[t,r,o]}withTimeZone(n){return new pe(+this,n)}[Symbol.for("constructDateFrom")](n){return new pe(+new Date(n),this.timeZone)}}function ka(e,n){return new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(n).slice(12)}var I;(function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"})(I||(I={}));var te;(function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"})(te||(te={}));var Ie;(function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"})(Ie||(Ie={}));var Ce;(function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"})(Ce||(Ce={}));const os=6048e5,Ma=864e5,hr=Symbol.for("constructDateFrom");function oe(e,n){return typeof e=="function"?e(n):e&&typeof e=="object"&&hr in e?e[hr](n):e instanceof Date?new e.constructor(n):new Date(n)}function K(e,n){return oe(n||e,e)}function as(e,n,t){const r=K(e,t?.in);return isNaN(n)?oe(e,NaN):(n&&r.setDate(r.getDate()+n),r)}function is(e,n,t){const r=K(e,t?.in);if(isNaN(n))return oe(e,NaN);if(!n)return r;const o=r.getDate(),a=oe(e,r.getTime());a.setMonth(r.getMonth()+n+1,0);const i=a.getDate();return o>=i?a:(r.setFullYear(a.getFullYear(),a.getMonth(),o),r)}let Ra={};function Wt(){return Ra}function pt(e,n){const t=Wt(),r=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,o=K(e,n?.in),a=o.getDay(),i=(a<r?7:0)+a-r;return o.setDate(o.getDate()-i),o.setHours(0,0,0,0),o}function Ot(e,n){return pt(e,{...n,weekStartsOn:1})}function ls(e,n){const t=K(e,n?.in),r=t.getFullYear(),o=oe(t,0);o.setFullYear(r+1,0,4),o.setHours(0,0,0,0);const a=Ot(o),i=oe(t,0);i.setFullYear(r,0,4),i.setHours(0,0,0,0);const l=Ot(i);return t.getTime()>=a.getTime()?r+1:t.getTime()>=l.getTime()?r:r-1}function pr(e){const n=K(e),t=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return t.setUTCFullYear(n.getFullYear()),+e-+t}function vt(e,...n){const t=oe.bind(null,n.find(r=>typeof r=="object"));return n.map(t)}function Tt(e,n){const t=K(e,n?.in);return t.setHours(0,0,0,0),t}function ds(e,n,t){const[r,o]=vt(t?.in,e,n),a=Tt(r),i=Tt(o),l=+a-pr(a),d=+i-pr(i);return Math.round((l-d)/Ma)}function _a(e,n){const t=ls(e,n),r=oe(e,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),Ot(r)}function Pa(e,n,t){return as(e,n*7,t)}function Fa(e,n,t){return is(e,n*12,t)}function Ea(e,n){let t,r=n?.in;return e.forEach(o=>{!r&&typeof o=="object"&&(r=oe.bind(null,o));const a=K(o,r);(!t||t<a||isNaN(+a))&&(t=a)}),oe(r,t||NaN)}function Ia(e,n){let t,r=n?.in;return e.forEach(o=>{!r&&typeof o=="object"&&(r=oe.bind(null,o));const a=K(o,r);(!t||t>a||isNaN(+a))&&(t=a)}),oe(r,t||NaN)}function Oa(e,n,t){const[r,o]=vt(t?.in,e,n);return+Tt(r)==+Tt(o)}function cs(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function Ta(e){return!(!cs(e)&&typeof e!="number"||isNaN(+K(e)))}function Aa(e,n,t){const[r,o]=vt(t?.in,e,n),a=r.getFullYear()-o.getFullYear(),i=r.getMonth()-o.getMonth();return a*12+i}function Wa(e,n){const t=K(e,n?.in),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(23,59,59,999),t}function $a(e,n){const[t,r]=vt(e,n.start,n.end);return{start:t,end:r}}function za(e,n){const{start:t,end:r}=$a(n?.in,e);let o=+t>+r;const a=o?+t:+r,i=o?r:t;i.setHours(0,0,0,0),i.setDate(1);let l=1;const d=[];for(;+i<=a;)d.push(oe(t,i)),i.setMonth(i.getMonth()+l);return o?d.reverse():d}function La(e,n){const t=K(e,n?.in);return t.setDate(1),t.setHours(0,0,0,0),t}function Va(e,n){const t=K(e,n?.in),r=t.getFullYear();return t.setFullYear(r+1,0,0),t.setHours(23,59,59,999),t}function us(e,n){const t=K(e,n?.in);return t.setFullYear(t.getFullYear(),0,1),t.setHours(0,0,0,0),t}function ms(e,n){const t=Wt(),r=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??t.weekStartsOn??t.locale?.options?.weekStartsOn??0,o=K(e,n?.in),a=o.getDay(),i=(a<r?-7:0)+6-(a-r);return o.setDate(o.getDate()+i),o.setHours(23,59,59,999),o}function Ba(e,n){return ms(e,{...n,weekStartsOn:1})}const Ha={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Ga=(e,n,t)=>{let r;const o=Ha[e];return typeof o=="string"?r=o:n===1?r=o.one:r=o.other.replace("{{count}}",n.toString()),t?.addSuffix?t.comparison&&t.comparison>0?"in "+r:r+" ago":r};function pn(e){return(n={})=>{const t=n.width?String(n.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}const Ya={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},qa={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ua={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Za={date:pn({formats:Ya,defaultWidth:"full"}),time:pn({formats:qa,defaultWidth:"full"}),dateTime:pn({formats:Ua,defaultWidth:"full"})},Xa={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Ka=(e,n,t,r)=>Xa[e];function Nt(e){return(n,t)=>{const r=t?.context?String(t.context):"standalone";let o;if(r==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,l=t?.width?String(t.width):i;o=e.formattingValues[l]||e.formattingValues[i]}else{const i=e.defaultWidth,l=t?.width?String(t.width):e.defaultWidth;o=e.values[l]||e.values[i]}const a=e.argumentCallback?e.argumentCallback(n):n;return o[a]}}const Qa={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Ja={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ei={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},ti={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ni={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},ri={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},si=(e,n)=>{const t=Number(e),r=t%100;if(r>20||r<10)switch(r%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},oi={ordinalNumber:si,era:Nt({values:Qa,defaultWidth:"wide"}),quarter:Nt({values:Ja,defaultWidth:"wide",argumentCallback:e=>e-1}),month:Nt({values:ei,defaultWidth:"wide"}),day:Nt({values:ti,defaultWidth:"wide"}),dayPeriod:Nt({values:ni,defaultWidth:"wide",formattingValues:ri,defaultFormattingWidth:"wide"})};function Dt(e){return(n,t={})=>{const r=t.width,o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],a=n.match(o);if(!a)return null;const i=a[0],l=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?ii(l,h=>h.test(i)):ai(l,h=>h.test(i));let c;c=e.valueCallback?e.valueCallback(d):d,c=t.valueCallback?t.valueCallback(c):c;const u=n.slice(i.length);return{value:c,rest:u}}}function ai(e,n){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&n(e[t]))return t}function ii(e,n){for(let t=0;t<e.length;t++)if(n(e[t]))return t}function li(e){return(n,t={})=>{const r=n.match(e.matchPattern);if(!r)return null;const o=r[0],a=n.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];i=t.valueCallback?t.valueCallback(i):i;const l=n.slice(o.length);return{value:i,rest:l}}}const di=/^(\d+)(th|st|nd|rd)?/i,ci=/\d+/i,ui={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},mi={any:[/^b/i,/^(a|c)/i]},fi={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},gi={any:[/1/i,/2/i,/3/i,/4/i]},hi={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},pi={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},xi={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},vi={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},bi={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},wi={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},yi={ordinalNumber:li({matchPattern:di,parsePattern:ci,valueCallback:e=>parseInt(e,10)}),era:Dt({matchPatterns:ui,defaultMatchWidth:"wide",parsePatterns:mi,defaultParseWidth:"any"}),quarter:Dt({matchPatterns:fi,defaultMatchWidth:"wide",parsePatterns:gi,defaultParseWidth:"any",valueCallback:e=>e+1}),month:Dt({matchPatterns:hi,defaultMatchWidth:"wide",parsePatterns:pi,defaultParseWidth:"any"}),day:Dt({matchPatterns:xi,defaultMatchWidth:"wide",parsePatterns:vi,defaultParseWidth:"any"}),dayPeriod:Dt({matchPatterns:bi,defaultMatchWidth:"any",parsePatterns:wi,defaultParseWidth:"any"})},qn={code:"en-US",formatDistance:Ga,formatLong:Za,formatRelative:Ka,localize:oi,match:yi,options:{weekStartsOn:0,firstWeekContainsDate:1}};function ji(e,n){const t=K(e,n?.in);return ds(t,us(t))+1}function fs(e,n){const t=K(e,n?.in),r=+Ot(t)-+_a(t);return Math.round(r/os)+1}function gs(e,n){const t=K(e,n?.in),r=t.getFullYear(),o=Wt(),a=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1,i=oe(n?.in||e,0);i.setFullYear(r+1,0,a),i.setHours(0,0,0,0);const l=pt(i,n),d=oe(n?.in||e,0);d.setFullYear(r,0,a),d.setHours(0,0,0,0);const c=pt(d,n);return+t>=+l?r+1:+t>=+c?r:r-1}function Ni(e,n){const t=Wt(),r=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??t.firstWeekContainsDate??t.locale?.options?.firstWeekContainsDate??1,o=gs(e,n),a=oe(n?.in||e,0);return a.setFullYear(o,0,r),a.setHours(0,0,0,0),pt(a,n)}function hs(e,n){const t=K(e,n?.in),r=+pt(t,n)-+Ni(t,n);return Math.round(r/os)+1}function X(e,n){const t=e<0?"-":"",r=Math.abs(e).toString().padStart(n,"0");return t+r}const Xe={y(e,n){const t=e.getFullYear(),r=t>0?t:1-t;return X(n==="yy"?r%100:r,n.length)},M(e,n){const t=e.getMonth();return n==="M"?String(t+1):X(t+1,2)},d(e,n){return X(e.getDate(),n.length)},a(e,n){const t=e.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.toUpperCase();case"aaa":return t;case"aaaaa":return t[0];case"aaaa":default:return t==="am"?"a.m.":"p.m."}},h(e,n){return X(e.getHours()%12||12,n.length)},H(e,n){return X(e.getHours(),n.length)},m(e,n){return X(e.getMinutes(),n.length)},s(e,n){return X(e.getSeconds(),n.length)},S(e,n){const t=n.length,r=e.getMilliseconds(),o=Math.trunc(r*Math.pow(10,t-3));return X(o,n.length)}},mt={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},xr={G:function(e,n,t){const r=e.getFullYear()>0?1:0;switch(n){case"G":case"GG":case"GGG":return t.era(r,{width:"abbreviated"});case"GGGGG":return t.era(r,{width:"narrow"});case"GGGG":default:return t.era(r,{width:"wide"})}},y:function(e,n,t){if(n==="yo"){const r=e.getFullYear(),o=r>0?r:1-r;return t.ordinalNumber(o,{unit:"year"})}return Xe.y(e,n)},Y:function(e,n,t,r){const o=gs(e,r),a=o>0?o:1-o;if(n==="YY"){const i=a%100;return X(i,2)}return n==="Yo"?t.ordinalNumber(a,{unit:"year"}):X(a,n.length)},R:function(e,n){const t=ls(e);return X(t,n.length)},u:function(e,n){const t=e.getFullYear();return X(t,n.length)},Q:function(e,n,t){const r=Math.ceil((e.getMonth()+1)/3);switch(n){case"Q":return String(r);case"QQ":return X(r,2);case"Qo":return t.ordinalNumber(r,{unit:"quarter"});case"QQQ":return t.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return t.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return t.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,n,t){const r=Math.ceil((e.getMonth()+1)/3);switch(n){case"q":return String(r);case"qq":return X(r,2);case"qo":return t.ordinalNumber(r,{unit:"quarter"});case"qqq":return t.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return t.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return t.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,n,t){const r=e.getMonth();switch(n){case"M":case"MM":return Xe.M(e,n);case"Mo":return t.ordinalNumber(r+1,{unit:"month"});case"MMM":return t.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return t.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return t.month(r,{width:"wide",context:"formatting"})}},L:function(e,n,t){const r=e.getMonth();switch(n){case"L":return String(r+1);case"LL":return X(r+1,2);case"Lo":return t.ordinalNumber(r+1,{unit:"month"});case"LLL":return t.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return t.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return t.month(r,{width:"wide",context:"standalone"})}},w:function(e,n,t,r){const o=hs(e,r);return n==="wo"?t.ordinalNumber(o,{unit:"week"}):X(o,n.length)},I:function(e,n,t){const r=fs(e);return n==="Io"?t.ordinalNumber(r,{unit:"week"}):X(r,n.length)},d:function(e,n,t){return n==="do"?t.ordinalNumber(e.getDate(),{unit:"date"}):Xe.d(e,n)},D:function(e,n,t){const r=ji(e);return n==="Do"?t.ordinalNumber(r,{unit:"dayOfYear"}):X(r,n.length)},E:function(e,n,t){const r=e.getDay();switch(n){case"E":case"EE":case"EEE":return t.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return t.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return t.day(r,{width:"short",context:"formatting"});case"EEEE":default:return t.day(r,{width:"wide",context:"formatting"})}},e:function(e,n,t,r){const o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(n){case"e":return String(a);case"ee":return X(a,2);case"eo":return t.ordinalNumber(a,{unit:"day"});case"eee":return t.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return t.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return t.day(o,{width:"short",context:"formatting"});case"eeee":default:return t.day(o,{width:"wide",context:"formatting"})}},c:function(e,n,t,r){const o=e.getDay(),a=(o-r.weekStartsOn+8)%7||7;switch(n){case"c":return String(a);case"cc":return X(a,n.length);case"co":return t.ordinalNumber(a,{unit:"day"});case"ccc":return t.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return t.day(o,{width:"narrow",context:"standalone"});case"cccccc":return t.day(o,{width:"short",context:"standalone"});case"cccc":default:return t.day(o,{width:"wide",context:"standalone"})}},i:function(e,n,t){const r=e.getDay(),o=r===0?7:r;switch(n){case"i":return String(o);case"ii":return X(o,n.length);case"io":return t.ordinalNumber(o,{unit:"day"});case"iii":return t.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return t.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return t.day(r,{width:"short",context:"formatting"});case"iiii":default:return t.day(r,{width:"wide",context:"formatting"})}},a:function(e,n,t){const o=e.getHours()/12>=1?"pm":"am";switch(n){case"a":case"aa":return t.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return t.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return t.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return t.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(e,n,t){const r=e.getHours();let o;switch(r===12?o=mt.noon:r===0?o=mt.midnight:o=r/12>=1?"pm":"am",n){case"b":case"bb":return t.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return t.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return t.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return t.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(e,n,t){const r=e.getHours();let o;switch(r>=17?o=mt.evening:r>=12?o=mt.afternoon:r>=4?o=mt.morning:o=mt.night,n){case"B":case"BB":case"BBB":return t.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return t.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return t.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(e,n,t){if(n==="ho"){let r=e.getHours()%12;return r===0&&(r=12),t.ordinalNumber(r,{unit:"hour"})}return Xe.h(e,n)},H:function(e,n,t){return n==="Ho"?t.ordinalNumber(e.getHours(),{unit:"hour"}):Xe.H(e,n)},K:function(e,n,t){const r=e.getHours()%12;return n==="Ko"?t.ordinalNumber(r,{unit:"hour"}):X(r,n.length)},k:function(e,n,t){let r=e.getHours();return r===0&&(r=24),n==="ko"?t.ordinalNumber(r,{unit:"hour"}):X(r,n.length)},m:function(e,n,t){return n==="mo"?t.ordinalNumber(e.getMinutes(),{unit:"minute"}):Xe.m(e,n)},s:function(e,n,t){return n==="so"?t.ordinalNumber(e.getSeconds(),{unit:"second"}):Xe.s(e,n)},S:function(e,n){return Xe.S(e,n)},X:function(e,n,t){const r=e.getTimezoneOffset();if(r===0)return"Z";switch(n){case"X":return br(r);case"XXXX":case"XX":return it(r);case"XXXXX":case"XXX":default:return it(r,":")}},x:function(e,n,t){const r=e.getTimezoneOffset();switch(n){case"x":return br(r);case"xxxx":case"xx":return it(r);case"xxxxx":case"xxx":default:return it(r,":")}},O:function(e,n,t){const r=e.getTimezoneOffset();switch(n){case"O":case"OO":case"OOO":return"GMT"+vr(r,":");case"OOOO":default:return"GMT"+it(r,":")}},z:function(e,n,t){const r=e.getTimezoneOffset();switch(n){case"z":case"zz":case"zzz":return"GMT"+vr(r,":");case"zzzz":default:return"GMT"+it(r,":")}},t:function(e,n,t){const r=Math.trunc(+e/1e3);return X(r,n.length)},T:function(e,n,t){return X(+e,n.length)}};function vr(e,n=""){const t=e>0?"-":"+",r=Math.abs(e),o=Math.trunc(r/60),a=r%60;return a===0?t+String(o):t+String(o)+n+X(a,2)}function br(e,n){return e%60===0?(e>0?"-":"+")+X(Math.abs(e)/60,2):it(e,n)}function it(e,n=""){const t=e>0?"-":"+",r=Math.abs(e),o=X(Math.trunc(r/60),2),a=X(r%60,2);return t+o+n+a}const wr=(e,n)=>{switch(e){case"P":return n.date({width:"short"});case"PP":return n.date({width:"medium"});case"PPP":return n.date({width:"long"});case"PPPP":default:return n.date({width:"full"})}},ps=(e,n)=>{switch(e){case"p":return n.time({width:"short"});case"pp":return n.time({width:"medium"});case"ppp":return n.time({width:"long"});case"pppp":default:return n.time({width:"full"})}},Di=(e,n)=>{const t=e.match(/(P+)(p+)?/)||[],r=t[1],o=t[2];if(!o)return wr(e,n);let a;switch(r){case"P":a=n.dateTime({width:"short"});break;case"PP":a=n.dateTime({width:"medium"});break;case"PPP":a=n.dateTime({width:"long"});break;case"PPPP":default:a=n.dateTime({width:"full"});break}return a.replace("{{date}}",wr(r,n)).replace("{{time}}",ps(o,n))},Ci={p:ps,P:Di},Si=/^D+$/,ki=/^Y+$/,Mi=["D","DD","YY","YYYY"];function Ri(e){return Si.test(e)}function _i(e){return ki.test(e)}function Pi(e,n,t){const r=Fi(e,n,t);if(console.warn(r),Mi.includes(e))throw new RangeError(r)}function Fi(e,n,t){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${n}\`) for formatting ${r} to the input \`${t}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Ei=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Ii=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Oi=/^'([^]*?)'?$/,Ti=/''/g,Ai=/[a-zA-Z]/;function Un(e,n,t){const r=Wt(),o=t?.locale??r.locale??qn,a=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,i=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,l=K(e,t?.in);if(!Ta(l))throw new RangeError("Invalid time value");let d=n.match(Ii).map(u=>{const h=u[0];if(h==="p"||h==="P"){const g=Ci[h];return g(u,o.formatLong)}return u}).join("").match(Ei).map(u=>{if(u==="''")return{isToken:!1,value:"'"};const h=u[0];if(h==="'")return{isToken:!1,value:Wi(u)};if(xr[h])return{isToken:!0,value:u};if(h.match(Ai))throw new RangeError("Format string contains an unescaped latin alphabet character `"+h+"`");return{isToken:!1,value:u}});o.localize.preprocessor&&(d=o.localize.preprocessor(l,d));const c={firstWeekContainsDate:a,weekStartsOn:i,locale:o};return d.map(u=>{if(!u.isToken)return u.value;const h=u.value;(!t?.useAdditionalWeekYearTokens&&_i(h)||!t?.useAdditionalDayOfYearTokens&&Ri(h))&&Pi(h,n,String(e));const g=xr[h[0]];return g(l,h,o.localize,c)}).join("")}function Wi(e){const n=e.match(Oi);return n?n[1].replace(Ti,"'"):e}function $i(e,n){const t=K(e,n?.in),r=t.getFullYear(),o=t.getMonth(),a=oe(t,0);return a.setFullYear(r,o+1,0),a.setHours(0,0,0,0),a.getDate()}function zi(e,n){return K(e,n?.in).getMonth()}function Li(e,n){return K(e,n?.in).getFullYear()}function Vi(e,n){return+K(e)>+K(n)}function Bi(e,n){return+K(e)<+K(n)}function Hi(e,n,t){const[r,o]=vt(t?.in,e,n);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}function Gi(e,n,t){const[r,o]=vt(t?.in,e,n);return r.getFullYear()===o.getFullYear()}function Yi(e,n,t){const r=K(e,t?.in),o=r.getFullYear(),a=r.getDate(),i=oe(e,0);i.setFullYear(o,n,15),i.setHours(0,0,0,0);const l=$i(i);return r.setMonth(n,Math.min(a,l)),r}function qi(e,n,t){const r=K(e,t?.in);return isNaN(+r)?oe(e,NaN):(r.setFullYear(n),r)}const yr=5,Ui=4;function Zi(e,n){const t=n.startOfMonth(e),r=t.getDay()>0?t.getDay():7,o=n.addDays(e,-r+1),a=n.addDays(o,yr*7-1);return n.getMonth(e)===n.getMonth(a)?yr:Ui}function xs(e,n){const t=n.startOfMonth(e),r=t.getDay();return r===1?t:r===0?n.addDays(t,-1*6):n.addDays(t,-1*(r-1))}function Xi(e,n){const t=xs(e,n),r=Zi(e,n);return n.addDays(t,r*7-1)}class Ye{constructor(n,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?pe.tz(this.options.timeZone):new this.Date,this.newDate=(r,o,a)=>this.overrides?.newDate?this.overrides.newDate(r,o,a):this.options.timeZone?new pe(r,o,a,this.options.timeZone):new Date(r,o,a),this.addDays=(r,o)=>this.overrides?.addDays?this.overrides.addDays(r,o):as(r,o),this.addMonths=(r,o)=>this.overrides?.addMonths?this.overrides.addMonths(r,o):is(r,o),this.addWeeks=(r,o)=>this.overrides?.addWeeks?this.overrides.addWeeks(r,o):Pa(r,o),this.addYears=(r,o)=>this.overrides?.addYears?this.overrides.addYears(r,o):Fa(r,o),this.differenceInCalendarDays=(r,o)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(r,o):ds(r,o),this.differenceInCalendarMonths=(r,o)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(r,o):Aa(r,o),this.eachMonthOfInterval=r=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(r):za(r),this.endOfBroadcastWeek=r=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(r):Xi(r,this),this.endOfISOWeek=r=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(r):Ba(r),this.endOfMonth=r=>this.overrides?.endOfMonth?this.overrides.endOfMonth(r):Wa(r),this.endOfWeek=(r,o)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(r,o):ms(r,this.options),this.endOfYear=r=>this.overrides?.endOfYear?this.overrides.endOfYear(r):Va(r),this.format=(r,o,a)=>{const i=this.overrides?.format?this.overrides.format(r,o,this.options):Un(r,o,this.options);return this.options.numerals&&this.options.numerals!=="latn"?this.replaceDigits(i):i},this.getISOWeek=r=>this.overrides?.getISOWeek?this.overrides.getISOWeek(r):fs(r),this.getMonth=(r,o)=>this.overrides?.getMonth?this.overrides.getMonth(r,this.options):zi(r,this.options),this.getYear=(r,o)=>this.overrides?.getYear?this.overrides.getYear(r,this.options):Li(r,this.options),this.getWeek=(r,o)=>this.overrides?.getWeek?this.overrides.getWeek(r,this.options):hs(r,this.options),this.isAfter=(r,o)=>this.overrides?.isAfter?this.overrides.isAfter(r,o):Vi(r,o),this.isBefore=(r,o)=>this.overrides?.isBefore?this.overrides.isBefore(r,o):Bi(r,o),this.isDate=r=>this.overrides?.isDate?this.overrides.isDate(r):cs(r),this.isSameDay=(r,o)=>this.overrides?.isSameDay?this.overrides.isSameDay(r,o):Oa(r,o),this.isSameMonth=(r,o)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(r,o):Hi(r,o),this.isSameYear=(r,o)=>this.overrides?.isSameYear?this.overrides.isSameYear(r,o):Gi(r,o),this.max=r=>this.overrides?.max?this.overrides.max(r):Ea(r),this.min=r=>this.overrides?.min?this.overrides.min(r):Ia(r),this.setMonth=(r,o)=>this.overrides?.setMonth?this.overrides.setMonth(r,o):Yi(r,o),this.setYear=(r,o)=>this.overrides?.setYear?this.overrides.setYear(r,o):qi(r,o),this.startOfBroadcastWeek=(r,o)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(r,this):xs(r,this),this.startOfDay=r=>this.overrides?.startOfDay?this.overrides.startOfDay(r):Tt(r),this.startOfISOWeek=r=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(r):Ot(r),this.startOfMonth=r=>this.overrides?.startOfMonth?this.overrides.startOfMonth(r):La(r),this.startOfWeek=(r,o)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(r,this.options):pt(r,this.options),this.startOfYear=r=>this.overrides?.startOfYear?this.overrides.startOfYear(r):us(r),this.options={locale:qn,...n},this.overrides=t}getDigitMap(){const{numerals:n="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:n}),r={};for(let o=0;o<10;o++)r[o.toString()]=t.format(o);return r}replaceDigits(n){const t=this.getDigitMap();return n.replace(/\d/g,r=>t[r]||r)}formatNumber(n){return this.replaceDigits(n.toString())}}const ze=new Ye;class vs{constructor(n,t,r=ze){this.date=n,this.displayMonth=t,this.outside=!!(t&&!r.isSameMonth(n,t)),this.dateLib=r}isEqualTo(n){return this.dateLib.isSameDay(n.date,this.date)&&this.dateLib.isSameMonth(n.displayMonth,this.displayMonth)}}class Ki{constructor(n,t){this.date=n,this.weeks=t}}class Qi{constructor(n,t){this.days=t,this.weekNumber=n}}function He(e,n,t=!1,r=ze){let{from:o,to:a}=e;const{differenceInCalendarDays:i,isSameDay:l}=r;return o&&a?(i(a,o)<0&&([o,a]=[a,o]),i(n,o)>=(t?1:0)&&i(a,n)>=(t?1:0)):!t&&a?l(a,n):!t&&o?l(o,n):!1}function bs(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function Zn(e){return!!(e&&typeof e=="object"&&"from"in e)}function ws(e){return!!(e&&typeof e=="object"&&"after"in e)}function ys(e){return!!(e&&typeof e=="object"&&"before"in e)}function js(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function Ns(e,n){return Array.isArray(e)&&e.every(n.isDate)}function Ge(e,n,t=ze){const r=Array.isArray(n)?n:[n],{isSameDay:o,differenceInCalendarDays:a,isAfter:i}=t;return r.some(l=>{if(typeof l=="boolean")return l;if(t.isDate(l))return o(e,l);if(Ns(l,t))return l.includes(e);if(Zn(l))return He(l,e,!1,t);if(js(l))return Array.isArray(l.dayOfWeek)?l.dayOfWeek.includes(e.getDay()):l.dayOfWeek===e.getDay();if(bs(l)){const d=a(l.before,e),c=a(l.after,e),u=d>0,h=c<0;return i(l.before,l.after)?h&&u:u||h}return ws(l)?a(e,l.after)>0:ys(l)?a(l.before,e)>0:typeof l=="function"?l(e):!1})}function Ji(e,n,t,r,o){const{disabled:a,hidden:i,modifiers:l,showOutsideDays:d,broadcastCalendar:c,today:u}=n,{isSameDay:h,isSameMonth:g,startOfMonth:f,isBefore:x,endOfMonth:w,isAfter:b}=o,y=t&&f(t),N=r&&w(r),j={[te.focused]:[],[te.outside]:[],[te.disabled]:[],[te.hidden]:[],[te.today]:[]},k={};for(const S of e){const{date:C,displayMonth:M}=S,_=!!(M&&!g(C,M)),F=!!(y&&x(C,y)),$=!!(N&&b(C,N)),z=!!(a&&Ge(C,a,o)),H=!!(i&&Ge(C,i,o))||F||$||!c&&!d&&_||c&&d===!1&&_,ae=h(C,u??o.today());_&&j.outside.push(S),z&&j.disabled.push(S),H&&j.hidden.push(S),ae&&j.today.push(S),l&&Object.keys(l).forEach(ie=>{const le=l?.[ie];le&&Ge(C,le,o)&&(k[ie]?k[ie].push(S):k[ie]=[S])})}return S=>{const C={[te.focused]:!1,[te.disabled]:!1,[te.hidden]:!1,[te.outside]:!1,[te.today]:!1},M={};for(const _ in j){const F=j[_];C[_]=F.some($=>$===S)}for(const _ in k)M[_]=k[_].some(F=>F===S);return{...C,...M}}}function el(e,n,t={}){return Object.entries(e).filter(([,o])=>o===!0).reduce((o,[a])=>(t[a]?o.push(t[a]):n[te[a]]?o.push(n[te[a]]):n[Ie[a]]&&o.push(n[Ie[a]]),o),[n[I.Day]])}function tl(e){return R.createElement("button",{...e})}function nl(e){return R.createElement("span",{...e})}function rl(e){const{size:n=24,orientation:t="left",className:r}=e;return R.createElement("svg",{className:r,width:n,height:n,viewBox:"0 0 24 24"},t==="up"&&R.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),t==="down"&&R.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),t==="left"&&R.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),t==="right"&&R.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function sl(e){const{day:n,modifiers:t,...r}=e;return R.createElement("td",{...r})}function ol(e){const{day:n,modifiers:t,...r}=e,o=R.useRef(null);return R.useEffect(()=>{t.focused&&o.current?.focus()},[t.focused]),R.createElement("button",{ref:o,...r})}function al(e){const{options:n,className:t,components:r,classNames:o,...a}=e,i=[o[I.Dropdown],t].join(" "),l=n?.find(({value:d})=>d===a.value);return R.createElement("span",{"data-disabled":a.disabled,className:o[I.DropdownRoot]},R.createElement(r.Select,{className:i,...a},n?.map(({value:d,label:c,disabled:u})=>R.createElement(r.Option,{key:d,value:d,disabled:u},c))),R.createElement("span",{className:o[I.CaptionLabel],"aria-hidden":!0},l?.label,R.createElement(r.Chevron,{orientation:"down",size:18,className:o[I.Chevron]})))}function il(e){return R.createElement("div",{...e})}function ll(e){return R.createElement("div",{...e})}function dl(e){const{calendarMonth:n,displayIndex:t,...r}=e;return R.createElement("div",{...r},e.children)}function cl(e){const{calendarMonth:n,displayIndex:t,...r}=e;return R.createElement("div",{...r})}function ul(e){return R.createElement("table",{...e})}function ml(e){return R.createElement("div",{...e})}const Ds=p.createContext(void 0);function $t(){const e=p.useContext(Ds);if(e===void 0)throw new Error("useDayPicker() must be used within a custom component.");return e}function fl(e){const{components:n}=$t();return R.createElement(n.Dropdown,{...e})}function gl(e){const{onPreviousClick:n,onNextClick:t,previousMonth:r,nextMonth:o,...a}=e,{components:i,classNames:l,labels:{labelPrevious:d,labelNext:c}}=$t(),u=p.useCallback(g=>{o&&t?.(g)},[o,t]),h=p.useCallback(g=>{r&&n?.(g)},[r,n]);return R.createElement("nav",{...a},R.createElement(i.PreviousMonthButton,{type:"button",className:l[I.PreviousMonthButton],tabIndex:r?void 0:-1,"aria-disabled":r?void 0:!0,"aria-label":d(r),onClick:h},R.createElement(i.Chevron,{disabled:r?void 0:!0,className:l[I.Chevron],orientation:"left"})),R.createElement(i.NextMonthButton,{type:"button",className:l[I.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":o?void 0:!0,"aria-label":c(o),onClick:u},R.createElement(i.Chevron,{disabled:o?void 0:!0,orientation:"right",className:l[I.Chevron]})))}function hl(e){const{components:n}=$t();return R.createElement(n.Button,{...e})}function pl(e){return R.createElement("option",{...e})}function xl(e){const{components:n}=$t();return R.createElement(n.Button,{...e})}function vl(e){const{rootRef:n,...t}=e;return R.createElement("div",{...t,ref:n})}function bl(e){return R.createElement("select",{...e})}function wl(e){const{week:n,...t}=e;return R.createElement("tr",{...t})}function yl(e){return R.createElement("th",{...e})}function jl(e){return R.createElement("thead",{"aria-hidden":!0},R.createElement("tr",{...e}))}function Nl(e){const{week:n,...t}=e;return R.createElement("th",{...t})}function Dl(e){return R.createElement("th",{...e})}function Cl(e){return R.createElement("tbody",{...e})}function Sl(e){const{components:n}=$t();return R.createElement(n.Dropdown,{...e})}const kl=Object.freeze(Object.defineProperty({__proto__:null,Button:tl,CaptionLabel:nl,Chevron:rl,Day:sl,DayButton:ol,Dropdown:al,DropdownNav:il,Footer:ll,Month:dl,MonthCaption:cl,MonthGrid:ul,Months:ml,MonthsDropdown:fl,Nav:gl,NextMonthButton:hl,Option:pl,PreviousMonthButton:xl,Root:vl,Select:bl,Week:wl,WeekNumber:Nl,WeekNumberHeader:Dl,Weekday:yl,Weekdays:jl,Weeks:Cl,YearsDropdown:Sl},Symbol.toStringTag,{value:"Module"}));function Ml(e){return{...kl,...e}}function Rl(e){const n={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([t,r])=>{t.startsWith("data-")&&(n[t]=r)}),n}function Xn(){const e={};for(const n in I)e[I[n]]=`rdp-${I[n]}`;for(const n in te)e[te[n]]=`rdp-${te[n]}`;for(const n in Ie)e[Ie[n]]=`rdp-${Ie[n]}`;for(const n in Ce)e[Ce[n]]=`rdp-${Ce[n]}`;return e}function Cs(e,n,t){return(t??new Ye(n)).format(e,"LLLL y")}const _l=Cs;function Pl(e,n,t){return(t??new Ye(n)).format(e,"d")}function Fl(e,n=ze){return n.format(e,"LLLL")}function El(e,n=ze){return e<10?n.formatNumber(`0${e.toLocaleString()}`):n.formatNumber(`${e.toLocaleString()}`)}function Il(){return""}function Ol(e,n,t){return(t??new Ye(n)).format(e,"cccccc")}function Ss(e,n=ze){return n.format(e,"yyyy")}const Tl=Ss,Al=Object.freeze(Object.defineProperty({__proto__:null,formatCaption:Cs,formatDay:Pl,formatMonthCaption:_l,formatMonthDropdown:Fl,formatWeekNumber:El,formatWeekNumberHeader:Il,formatWeekdayName:Ol,formatYearCaption:Tl,formatYearDropdown:Ss},Symbol.toStringTag,{value:"Module"}));function Wl(e){return e?.formatMonthCaption&&!e.formatCaption&&(e.formatCaption=e.formatMonthCaption),e?.formatYearCaption&&!e.formatYearDropdown&&(e.formatYearDropdown=e.formatYearCaption),{...Al,...e}}function $l(e,n,t,r,o){const{startOfMonth:a,startOfYear:i,endOfYear:l,eachMonthOfInterval:d,getMonth:c}=o;return d({start:i(e),end:l(e)}).map(g=>{const f=r.formatMonthDropdown(g,o),x=c(g),w=n&&g<a(n)||t&&g>a(t)||!1;return{value:x,label:f,disabled:w}})}function zl(e,n={},t={}){let r={...n?.[I.Day]};return Object.entries(e).filter(([,o])=>o===!0).forEach(([o])=>{r={...r,...t?.[o]}}),r}function Ll(e,n,t){const r=e.today(),o=n?e.startOfISOWeek(r):e.startOfWeek(r),a=[];for(let i=0;i<7;i++){const l=e.addDays(o,i);a.push(l)}return a}function Vl(e,n,t,r){if(!e||!n)return;const{startOfYear:o,endOfYear:a,addYears:i,getYear:l,isBefore:d,isSameYear:c}=r,u=o(e),h=a(n),g=[];let f=u;for(;d(f,h)||c(f,h);)g.push(f),f=i(f,1);return g.map(x=>{const w=t.formatYearDropdown(x,r);return{value:l(x),label:w,disabled:!1}})}function ks(e,n,t){return(t??new Ye(n)).format(e,"LLLL y")}const Bl=ks;function Hl(e,n,t,r){let o=(r??new Ye(t)).format(e,"PPPP");return n?.today&&(o=`Today, ${o}`),o}function Ms(e,n,t,r){let o=(r??new Ye(t)).format(e,"PPPP");return n.today&&(o=`Today, ${o}`),n.selected&&(o=`${o}, selected`),o}const Gl=Ms;function Yl(){return""}function ql(e){return"Choose the Month"}function Ul(e){return"Go to the Next Month"}function Zl(e){return"Go to the Previous Month"}function Xl(e,n,t){return(t??new Ye(n)).format(e,"cccc")}function Kl(e,n){return`Week ${e}`}function Ql(e){return"Week Number"}function Jl(e){return"Choose the Year"}const ed=Object.freeze(Object.defineProperty({__proto__:null,labelCaption:Bl,labelDay:Gl,labelDayButton:Ms,labelGrid:ks,labelGridcell:Hl,labelMonthDropdown:ql,labelNav:Yl,labelNext:Ul,labelPrevious:Zl,labelWeekNumber:Kl,labelWeekNumberHeader:Ql,labelWeekday:Xl,labelYearDropdown:Jl},Symbol.toStringTag,{value:"Module"})),zt=e=>e instanceof HTMLElement?e:null,xn=e=>[...e.querySelectorAll("[data-animated-month]")??[]],td=e=>zt(e.querySelector("[data-animated-month]")),vn=e=>zt(e.querySelector("[data-animated-caption]")),bn=e=>zt(e.querySelector("[data-animated-weeks]")),nd=e=>zt(e.querySelector("[data-animated-nav]")),rd=e=>zt(e.querySelector("[data-animated-weekdays]"));function sd(e,n,{classNames:t,months:r,focused:o,dateLib:a}){const i=p.useRef(null),l=p.useRef(r),d=p.useRef(!1);p.useLayoutEffect(()=>{const c=l.current;if(l.current=r,!n||!e.current||!(e.current instanceof HTMLElement)||r.length===0||c.length===0||r.length!==c.length)return;const u=a.isSameMonth(r[0].date,c[0].date),h=a.isAfter(r[0].date,c[0].date),g=h?t[Ce.caption_after_enter]:t[Ce.caption_before_enter],f=h?t[Ce.weeks_after_enter]:t[Ce.weeks_before_enter],x=i.current,w=e.current.cloneNode(!0);if(w instanceof HTMLElement?(xn(w).forEach(j=>{if(!(j instanceof HTMLElement))return;const k=td(j);k&&j.contains(k)&&j.removeChild(k);const S=vn(j);S&&S.classList.remove(g);const C=bn(j);C&&C.classList.remove(f)}),i.current=w):i.current=null,d.current||u||o)return;const b=x instanceof HTMLElement?xn(x):[],y=xn(e.current);if(y&&y.every(N=>N instanceof HTMLElement)&&b&&b.every(N=>N instanceof HTMLElement)){d.current=!0,e.current.style.isolation="isolate";const N=nd(e.current);N&&(N.style.zIndex="1"),y.forEach((j,k)=>{const S=b[k];if(!S)return;j.style.position="relative",j.style.overflow="hidden";const C=vn(j);C&&C.classList.add(g);const M=bn(j);M&&M.classList.add(f);const _=()=>{d.current=!1,e.current&&(e.current.style.isolation=""),N&&(N.style.zIndex=""),C&&C.classList.remove(g),M&&M.classList.remove(f),j.style.position="",j.style.overflow="",j.contains(S)&&j.removeChild(S)};S.style.pointerEvents="none",S.style.position="absolute",S.style.overflow="hidden",S.setAttribute("aria-hidden","true");const F=rd(S);F&&(F.style.opacity="0");const $=vn(S);$&&($.classList.add(h?t[Ce.caption_before_exit]:t[Ce.caption_after_exit]),$.addEventListener("animationend",_));const z=bn(S);z&&z.classList.add(h?t[Ce.weeks_before_exit]:t[Ce.weeks_after_exit]),j.insertBefore(S,j.firstChild)})}})}function od(e,n,t,r){const o=e[0],a=e[e.length-1],{ISOWeek:i,fixedWeeks:l,broadcastCalendar:d}=t??{},{addDays:c,differenceInCalendarDays:u,differenceInCalendarMonths:h,endOfBroadcastWeek:g,endOfISOWeek:f,endOfMonth:x,endOfWeek:w,isAfter:b,startOfBroadcastWeek:y,startOfISOWeek:N,startOfWeek:j}=r,k=d?y(o,r):i?N(o):j(o),S=d?g(a):i?f(x(a)):w(x(a)),C=u(S,k),M=h(a,o)+1,_=[];for(let z=0;z<=C;z++){const H=c(k,z);if(n&&b(H,n))break;_.push(H)}const $=(d?35:42)*M;if(l&&_.length<$){const z=$-_.length;for(let H=0;H<z;H++){const ae=c(_[_.length-1],1);_.push(ae)}}return _}function ad(e){const n=[];return e.reduce((t,r)=>{const o=r.weeks.reduce((a,i)=>[...a,...i.days],n);return[...t,...o]},n)}function id(e,n,t,r){const{numberOfMonths:o=1}=t,a=[];for(let i=0;i<o;i++){const l=r.addMonths(e,i);if(n&&l>n)break;a.push(l)}return a}function jr(e,n,t,r){const{month:o,defaultMonth:a,today:i=r.today(),numberOfMonths:l=1}=e;let d=o||a||i;const{differenceInCalendarMonths:c,addMonths:u,startOfMonth:h}=r;if(t&&c(t,d)<l-1){const g=-1*(l-1);d=u(t,g)}return n&&c(d,n)<0&&(d=n),h(d)}function ld(e,n,t,r){const{addDays:o,endOfBroadcastWeek:a,endOfISOWeek:i,endOfMonth:l,endOfWeek:d,getISOWeek:c,getWeek:u,startOfBroadcastWeek:h,startOfISOWeek:g,startOfWeek:f}=r,x=e.reduce((w,b)=>{const y=t.broadcastCalendar?h(b,r):t.ISOWeek?g(b):f(b),N=t.broadcastCalendar?a(b):t.ISOWeek?i(l(b)):d(l(b)),j=n.filter(M=>M>=y&&M<=N),k=t.broadcastCalendar?35:42;if(t.fixedWeeks&&j.length<k){const M=n.filter(_=>{const F=k-j.length;return _>N&&_<=o(N,F)});j.push(...M)}const S=j.reduce((M,_)=>{const F=t.ISOWeek?c(_):u(_),$=M.find(H=>H.weekNumber===F),z=new vs(_,b,r);return $?$.days.push(z):M.push(new Qi(F,[z])),M},[]),C=new Ki(b,S);return w.push(C),w},[]);return t.reverseMonths?x.reverse():x}function dd(e,n){let{startMonth:t,endMonth:r}=e;const{startOfYear:o,startOfDay:a,startOfMonth:i,endOfMonth:l,addYears:d,endOfYear:c,newDate:u,today:h}=n,{fromYear:g,toYear:f,fromMonth:x,toMonth:w}=e;!t&&x&&(t=x),!t&&g&&(t=n.newDate(g,0,1)),!r&&w&&(r=w),!r&&f&&(r=u(f,11,31));const b=e.captionLayout==="dropdown"||e.captionLayout==="dropdown-years";return t?t=i(t):g?t=u(g,0,1):!t&&b&&(t=o(d(e.today??h(),-100))),r?r=l(r):f?r=u(f,11,31):!r&&b&&(r=c(e.today??h())),[t&&a(t),r&&a(r)]}function cd(e,n,t,r){if(t.disableNavigation)return;const{pagedNavigation:o,numberOfMonths:a=1}=t,{startOfMonth:i,addMonths:l,differenceInCalendarMonths:d}=r,c=o?a:1,u=i(e);if(!n)return l(u,c);if(!(d(n,e)<a))return l(u,c)}function ud(e,n,t,r){if(t.disableNavigation)return;const{pagedNavigation:o,numberOfMonths:a}=t,{startOfMonth:i,addMonths:l,differenceInCalendarMonths:d}=r,c=o?a??1:1,u=i(e);if(!n)return l(u,-c);if(!(d(u,n)<=0))return l(u,-c)}function md(e){const n=[];return e.reduce((t,r)=>[...t,...r.weeks],n)}function Jt(e,n){const[t,r]=p.useState(e);return[n===void 0?t:n,r]}function fd(e,n){const[t,r]=dd(e,n),{startOfMonth:o,endOfMonth:a}=n,i=jr(e,t,r,n),[l,d]=Jt(i,e.month?i:void 0);p.useEffect(()=>{const C=jr(e,t,r,n);d(C)},[e.timeZone]);const c=id(l,r,e,n),u=od(c,e.endMonth?a(e.endMonth):void 0,e,n),h=ld(c,u,e,n),g=md(h),f=ad(h),x=ud(l,t,e,n),w=cd(l,r,e,n),{disableNavigation:b,onMonthChange:y}=e,N=C=>g.some(M=>M.days.some(_=>_.isEqualTo(C))),j=C=>{if(b)return;let M=o(C);t&&M<o(t)&&(M=o(t)),r&&M>o(r)&&(M=o(r)),d(M),y?.(M)};return{months:h,weeks:g,days:f,navStart:t,navEnd:r,previousMonth:x,nextMonth:w,goToMonth:j,goToDay:C=>{N(C)||j(C.date)}}}var Ae;(function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"})(Ae||(Ae={}));function Nr(e){return!e[te.disabled]&&!e[te.hidden]&&!e[te.outside]}function gd(e,n,t,r){let o,a=-1;for(const i of e){const l=n(i);Nr(l)&&(l[te.focused]&&a<Ae.FocusedModifier?(o=i,a=Ae.FocusedModifier):r?.isEqualTo(i)&&a<Ae.LastFocused?(o=i,a=Ae.LastFocused):t(i.date)&&a<Ae.Selected?(o=i,a=Ae.Selected):l[te.today]&&a<Ae.Today&&(o=i,a=Ae.Today))}return o||(o=e.find(i=>Nr(n(i)))),o}function hd(e,n,t,r,o,a,i){const{ISOWeek:l,broadcastCalendar:d}=a,{addDays:c,addMonths:u,addWeeks:h,addYears:g,endOfBroadcastWeek:f,endOfISOWeek:x,endOfWeek:w,max:b,min:y,startOfBroadcastWeek:N,startOfISOWeek:j,startOfWeek:k}=i;let C={day:c,week:h,month:u,year:g,startOfWeek:M=>d?N(M,i):l?j(M):k(M),endOfWeek:M=>d?f(M):l?x(M):w(M)}[e](t,n==="after"?1:-1);return n==="before"&&r?C=b([r,C]):n==="after"&&o&&(C=y([o,C])),C}function Rs(e,n,t,r,o,a,i,l=0){if(l>365)return;const d=hd(e,n,t.date,r,o,a,i),c=!!(a.disabled&&Ge(d,a.disabled,i)),u=!!(a.hidden&&Ge(d,a.hidden,i)),h=d,g=new vs(d,h,i);return!c&&!u?g:Rs(e,n,g,r,o,a,i,l+1)}function pd(e,n,t,r,o){const{autoFocus:a}=e,[i,l]=p.useState(),d=gd(n.days,t,r||(()=>!1),i),[c,u]=p.useState(a?d:void 0);return{isFocusTarget:w=>!!d?.isEqualTo(w),setFocused:u,focused:c,blur:()=>{l(c),u(void 0)},moveFocus:(w,b)=>{if(!c)return;const y=Rs(w,b,c,n.navStart,n.navEnd,e,o);y&&(n.goToDay(y),u(y))}}}function xd(e,n){const{selected:t,required:r,onSelect:o}=e,[a,i]=Jt(t,o?t:void 0),l=o?t:a,{isSameDay:d}=n,c=f=>l?.some(x=>d(x,f))??!1,{min:u,max:h}=e;return{selected:l,select:(f,x,w)=>{let b=[...l??[]];if(c(f)){if(l?.length===u||r&&l?.length===1)return;b=l?.filter(y=>!d(y,f))}else l?.length===h?b=[f]:b=[...b,f];return o||i(b),o?.(b,f,x,w),b},isSelected:c}}function vd(e,n,t=0,r=0,o=!1,a=ze){const{from:i,to:l}=n||{},{isSameDay:d,isAfter:c,isBefore:u}=a;let h;if(!i&&!l)h={from:e,to:t>0?void 0:e};else if(i&&!l)d(i,e)?o?h={from:i,to:void 0}:h=void 0:u(e,i)?h={from:e,to:i}:h={from:i,to:e};else if(i&&l)if(d(i,e)&&d(l,e))o?h={from:i,to:l}:h=void 0;else if(d(i,e))h={from:i,to:t>0?void 0:e};else if(d(l,e))h={from:e,to:t>0?void 0:e};else if(u(e,i))h={from:e,to:l};else if(c(e,i))h={from:i,to:e};else if(c(e,l))h={from:i,to:e};else throw new Error("Invalid range");if(h?.from&&h?.to){const g=a.differenceInCalendarDays(h.to,h.from);r>0&&g>r?h={from:e,to:void 0}:t>1&&g<t&&(h={from:e,to:void 0})}return h}function bd(e,n,t=ze){const r=Array.isArray(n)?n:[n];let o=e.from;const a=t.differenceInCalendarDays(e.to,e.from),i=Math.min(a,6);for(let l=0;l<=i;l++){if(r.includes(o.getDay()))return!0;o=t.addDays(o,1)}return!1}function Dr(e,n,t=ze){return He(e,n.from,!1,t)||He(e,n.to,!1,t)||He(n,e.from,!1,t)||He(n,e.to,!1,t)}function wd(e,n,t=ze){const r=Array.isArray(n)?n:[n];if(r.filter(l=>typeof l!="function").some(l=>typeof l=="boolean"?l:t.isDate(l)?He(e,l,!1,t):Ns(l,t)?l.some(d=>He(e,d,!1,t)):Zn(l)?l.from&&l.to?Dr(e,{from:l.from,to:l.to},t):!1:js(l)?bd(e,l.dayOfWeek,t):bs(l)?t.isAfter(l.before,l.after)?Dr(e,{from:t.addDays(l.after,1),to:t.addDays(l.before,-1)},t):Ge(e.from,l,t)||Ge(e.to,l,t):ws(l)||ys(l)?Ge(e.from,l,t)||Ge(e.to,l,t):!1))return!0;const i=r.filter(l=>typeof l=="function");if(i.length){let l=e.from;const d=t.differenceInCalendarDays(e.to,e.from);for(let c=0;c<=d;c++){if(i.some(u=>u(l)))return!0;l=t.addDays(l,1)}}return!1}function yd(e,n){const{disabled:t,excludeDisabled:r,selected:o,required:a,onSelect:i}=e,[l,d]=Jt(o,i?o:void 0),c=i?o:l;return{selected:c,select:(g,f,x)=>{const{min:w,max:b}=e,y=g?vd(g,c,w,b,a,n):void 0;return r&&t&&y?.from&&y.to&&wd({from:y.from,to:y.to},t,n)&&(y.from=g,y.to=void 0),i||d(y),i?.(y,g,f,x),y},isSelected:g=>c&&He(c,g,!1,n)}}function jd(e,n){const{selected:t,required:r,onSelect:o}=e,[a,i]=Jt(t,o?t:void 0),l=o?t:a,{isSameDay:d}=n;return{selected:l,select:(h,g,f)=>{let x=h;return!r&&l&&l&&d(h,l)&&(x=void 0),o||i(x),o?.(x,h,g,f),x},isSelected:h=>l?d(l,h):!1}}function Nd(e,n){const t=jd(e,n),r=xd(e,n),o=yd(e,n);switch(e.mode){case"single":return t;case"multiple":return r;case"range":return o;default:return}}function Dd(e){let n=e;n.timeZone&&(n={...e},n.today&&(n.today=new pe(n.today,n.timeZone)),n.month&&(n.month=new pe(n.month,n.timeZone)),n.defaultMonth&&(n.defaultMonth=new pe(n.defaultMonth,n.timeZone)),n.startMonth&&(n.startMonth=new pe(n.startMonth,n.timeZone)),n.endMonth&&(n.endMonth=new pe(n.endMonth,n.timeZone)),n.mode==="single"&&n.selected?n.selected=new pe(n.selected,n.timeZone):n.mode==="multiple"&&n.selected?n.selected=n.selected?.map(L=>new pe(L,n.timeZone)):n.mode==="range"&&n.selected&&(n.selected={from:n.selected.from?new pe(n.selected.from,n.timeZone):void 0,to:n.selected.to?new pe(n.selected.to,n.timeZone):void 0}));const{components:t,formatters:r,labels:o,dateLib:a,locale:i,classNames:l}=p.useMemo(()=>{const L={...qn,...n.locale};return{dateLib:new Ye({locale:L,weekStartsOn:n.broadcastCalendar?1:n.weekStartsOn,firstWeekContainsDate:n.firstWeekContainsDate,useAdditionalWeekYearTokens:n.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:n.useAdditionalDayOfYearTokens,timeZone:n.timeZone,numerals:n.numerals},n.dateLib),components:Ml(n.components),formatters:Wl(n.formatters),labels:{...ed,...n.labels},locale:L,classNames:{...Xn(),...n.classNames}}},[n.locale,n.broadcastCalendar,n.weekStartsOn,n.firstWeekContainsDate,n.useAdditionalWeekYearTokens,n.useAdditionalDayOfYearTokens,n.timeZone,n.numerals,n.dateLib,n.components,n.formatters,n.labels,n.classNames]),{captionLayout:d,mode:c,navLayout:u,numberOfMonths:h=1,onDayBlur:g,onDayClick:f,onDayFocus:x,onDayKeyDown:w,onDayMouseEnter:b,onDayMouseLeave:y,onNextClick:N,onPrevClick:j,showWeekNumber:k,styles:S}=n,{formatCaption:C,formatDay:M,formatMonthDropdown:_,formatWeekNumber:F,formatWeekNumberHeader:$,formatWeekdayName:z,formatYearDropdown:H}=r,ae=fd(n,a),{days:ie,months:le,navStart:ve,navEnd:Re,previousMonth:B,nextMonth:T,goToMonth:G}=ae,de=Ji(ie,n,ve,Re,a),{isSelected:be,select:re,selected:Y}=Nd(n,a)??{},{blur:we,focused:D,isFocusTarget:m,moveFocus:v,setFocused:P}=pd(n,ae,de,be??(()=>!1),a),{labelDayButton:V,labelGridcell:ne,labelGrid:ee,labelMonthDropdown:Q,labelNav:ye,labelPrevious:qe,labelNext:rn,labelWeekday:st,labelWeekNumber:to,labelWeekNumberHeader:no,labelYearDropdown:ro}=o,so=p.useMemo(()=>Ll(a,n.ISOWeek),[a,n.ISOWeek]),sr=c!==void 0||f!==void 0,sn=p.useCallback(()=>{B&&(G(B),j?.(B))},[B,G,j]),on=p.useCallback(()=>{T&&(G(T),N?.(T))},[G,T,N]),oo=p.useCallback((L,J)=>q=>{q.preventDefault(),q.stopPropagation(),P(L),re?.(L.date,J,q),f?.(L.date,J,q)},[re,f,P]),ao=p.useCallback((L,J)=>q=>{P(L),x?.(L.date,J,q)},[x,P]),io=p.useCallback((L,J)=>q=>{we(),g?.(L.date,J,q)},[we,g]),lo=p.useCallback((L,J)=>q=>{const Ue={ArrowLeft:[q.shiftKey?"month":"day",n.dir==="rtl"?"after":"before"],ArrowRight:[q.shiftKey?"month":"day",n.dir==="rtl"?"before":"after"],ArrowDown:[q.shiftKey?"year":"week","after"],ArrowUp:[q.shiftKey?"year":"week","before"],PageUp:[q.shiftKey?"year":"month","before"],PageDown:[q.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(Ue[q.key]){q.preventDefault(),q.stopPropagation();const[_e,Lt]=Ue[q.key];v(_e,Lt)}w?.(L.date,J,q)},[v,w,n.dir]),co=p.useCallback((L,J)=>q=>{b?.(L.date,J,q)},[b]),uo=p.useCallback((L,J)=>q=>{y?.(L.date,J,q)},[y]),mo=p.useCallback(L=>J=>{const q=Number(J.target.value),Ue=a.setMonth(a.startOfMonth(L),q);G(Ue)},[a,G]),fo=p.useCallback(L=>J=>{const q=Number(J.target.value),Ue=a.setYear(a.startOfMonth(L),q);G(Ue)},[a,G]),{className:go,style:ho}=p.useMemo(()=>({className:[l[I.Root],n.className].filter(Boolean).join(" "),style:{...S?.[I.Root],...n.style}}),[l,n.className,n.style,S]),po=Rl(n),or=p.useRef(null);sd(or,!!n.animate,{classNames:l,months:le,focused:D,dateLib:a});const xo={dayPickerProps:n,selected:Y,select:re,isSelected:be,months:le,nextMonth:T,previousMonth:B,goToMonth:G,getModifiers:de,components:t,classNames:l,styles:S,labels:o,formatters:r};return R.createElement(Ds.Provider,{value:xo},R.createElement(t.Root,{rootRef:n.animate?or:void 0,className:go,style:ho,dir:n.dir,id:n.id,lang:n.lang,nonce:n.nonce,title:n.title,role:n.role,"aria-label":n["aria-label"],...po},R.createElement(t.Months,{className:l[I.Months],style:S?.[I.Months]},!n.hideNavigation&&!u&&R.createElement(t.Nav,{"data-animated-nav":n.animate?"true":void 0,className:l[I.Nav],style:S?.[I.Nav],"aria-label":ye(),onPreviousClick:sn,onNextClick:on,previousMonth:B,nextMonth:T}),le.map((L,J)=>{const q=$l(L.date,ve,Re,r,a),Ue=Vl(ve,Re,r,a);return R.createElement(t.Month,{"data-animated-month":n.animate?"true":void 0,className:l[I.Month],style:S?.[I.Month],key:J,displayIndex:J,calendarMonth:L},u==="around"&&!n.hideNavigation&&J===0&&R.createElement(t.PreviousMonthButton,{type:"button",className:l[I.PreviousMonthButton],tabIndex:B?void 0:-1,"aria-disabled":B?void 0:!0,"aria-label":qe(B),onClick:sn,"data-animated-button":n.animate?"true":void 0},R.createElement(t.Chevron,{disabled:B?void 0:!0,className:l[I.Chevron],orientation:n.dir==="rtl"?"right":"left"})),R.createElement(t.MonthCaption,{"data-animated-caption":n.animate?"true":void 0,className:l[I.MonthCaption],style:S?.[I.MonthCaption],calendarMonth:L,displayIndex:J},d?.startsWith("dropdown")?R.createElement(t.DropdownNav,{className:l[I.Dropdowns],style:S?.[I.Dropdowns]},d==="dropdown"||d==="dropdown-months"?R.createElement(t.MonthsDropdown,{className:l[I.MonthsDropdown],"aria-label":Q(),classNames:l,components:t,disabled:!!n.disableNavigation,onChange:mo(L.date),options:q,style:S?.[I.Dropdown],value:a.getMonth(L.date)}):R.createElement("span",null,_(L.date,a)),d==="dropdown"||d==="dropdown-years"?R.createElement(t.YearsDropdown,{className:l[I.YearsDropdown],"aria-label":ro(a.options),classNames:l,components:t,disabled:!!n.disableNavigation,onChange:fo(L.date),options:Ue,style:S?.[I.Dropdown],value:a.getYear(L.date)}):R.createElement("span",null,H(L.date,a)),R.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},C(L.date,a.options,a))):R.createElement(t.CaptionLabel,{className:l[I.CaptionLabel],role:"status","aria-live":"polite"},C(L.date,a.options,a))),u==="around"&&!n.hideNavigation&&J===h-1&&R.createElement(t.NextMonthButton,{type:"button",className:l[I.NextMonthButton],tabIndex:T?void 0:-1,"aria-disabled":T?void 0:!0,"aria-label":rn(T),onClick:on,"data-animated-button":n.animate?"true":void 0},R.createElement(t.Chevron,{disabled:T?void 0:!0,className:l[I.Chevron],orientation:n.dir==="rtl"?"left":"right"})),J===h-1&&u==="after"&&!n.hideNavigation&&R.createElement(t.Nav,{"data-animated-nav":n.animate?"true":void 0,className:l[I.Nav],style:S?.[I.Nav],"aria-label":ye(),onPreviousClick:sn,onNextClick:on,previousMonth:B,nextMonth:T}),R.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":c==="multiple"||c==="range","aria-label":ee(L.date,a.options,a)||void 0,className:l[I.MonthGrid],style:S?.[I.MonthGrid]},!n.hideWeekdays&&R.createElement(t.Weekdays,{"data-animated-weekdays":n.animate?"true":void 0,className:l[I.Weekdays],style:S?.[I.Weekdays]},k&&R.createElement(t.WeekNumberHeader,{"aria-label":no(a.options),className:l[I.WeekNumberHeader],style:S?.[I.WeekNumberHeader],scope:"col"},$()),so.map((_e,Lt)=>R.createElement(t.Weekday,{"aria-label":st(_e,a.options,a),className:l[I.Weekday],key:Lt,style:S?.[I.Weekday],scope:"col"},z(_e,a.options,a)))),R.createElement(t.Weeks,{"data-animated-weeks":n.animate?"true":void 0,className:l[I.Weeks],style:S?.[I.Weeks]},L.weeks.map((_e,Lt)=>R.createElement(t.Week,{className:l[I.Week],key:_e.weekNumber,style:S?.[I.Week],week:_e},k&&R.createElement(t.WeekNumber,{week:_e,style:S?.[I.WeekNumber],"aria-label":to(_e.weekNumber,{locale:i}),className:l[I.WeekNumber],scope:"row",role:"rowheader"},F(_e.weekNumber,a)),_e.days.map(ce=>{const{date:Te}=ce,Z=de(ce);if(Z[te.focused]=!Z.hidden&&!!D?.isEqualTo(ce),Z[Ie.selected]=be?.(Te)||Z.selected,Zn(Y)){const{from:an,to:ln}=Y;Z[Ie.range_start]=!!(an&&ln&&a.isSameDay(Te,an)),Z[Ie.range_end]=!!(an&&ln&&a.isSameDay(Te,ln)),Z[Ie.range_middle]=He(Y,Te,!0,a)}const vo=zl(Z,S,n.modifiersStyles),bo=el(Z,l,n.modifiersClassNames),wo=!sr&&!Z.hidden?ne(Te,Z,a.options,a):void 0;return R.createElement(t.Day,{key:`${a.format(Te,"yyyy-MM-dd")}_${a.format(ce.displayMonth,"yyyy-MM")}`,day:ce,modifiers:Z,className:bo.join(" "),style:vo,role:"gridcell","aria-selected":Z.selected||void 0,"aria-label":wo,"data-day":a.format(Te,"yyyy-MM-dd"),"data-month":ce.outside?a.format(Te,"yyyy-MM"):void 0,"data-selected":Z.selected||void 0,"data-disabled":Z.disabled||void 0,"data-hidden":Z.hidden||void 0,"data-outside":ce.outside||void 0,"data-focused":Z.focused||void 0,"data-today":Z.today||void 0},!Z.hidden&&sr?R.createElement(t.DayButton,{className:l[I.DayButton],style:S?.[I.DayButton],type:"button",day:ce,modifiers:Z,disabled:Z.disabled||void 0,tabIndex:m(ce)?0:-1,"aria-label":V(Te,Z,a.options,a),onClick:oo(ce,Z),onBlur:io(ce,Z),onFocus:ao(ce,Z),onKeyDown:lo(ce,Z),onMouseEnter:co(ce,Z),onMouseLeave:uo(ce,Z)},M(Te,a.options,a)):!Z.hidden&&M(ce.date,a.options,a))}))))))})),n.footer&&R.createElement(t.Footer,{className:l[I.Footer],style:S?.[I.Footer],role:"status","aria-live":"polite"},n.footer)))}function _s({className:e,classNames:n,showOutsideDays:t=!0,captionLayout:r="label",buttonVariant:o="ghost",formatters:a,components:i,...l}){const d=Xn();return s.jsx(Dd,{showOutsideDays:t,className:E("bg-background group/calendar p-3 [--cell-size:2rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,e),captionLayout:r,formatters:{formatMonthDropdown:c=>c.toLocaleString("default",{month:"short"}),...a},classNames:{root:E("w-fit",d.root),months:E("relative flex flex-col gap-4 md:flex-row",d.months),month:E("flex w-full flex-col gap-4",d.month),nav:E("absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1",d.nav),button_previous:E(lr({variant:o}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",d.button_previous),button_next:E(lr({variant:o}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",d.button_next),month_caption:E("flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]",d.month_caption),dropdowns:E("flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-sm font-medium",d.dropdowns),dropdown_root:E("has-focus:border-ring border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border",d.dropdown_root),dropdown:E("bg-popover absolute inset-0 opacity-0",d.dropdown),caption_label:E("select-none font-medium",r==="label"?"text-sm":"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-sm [&>svg]:size-3.5",d.caption_label),table:"w-full border-collapse",weekdays:E("flex",d.weekdays),weekday:E("text-muted-foreground flex-1 select-none rounded-md text-[0.8rem] font-normal",d.weekday),week:E("mt-2 flex w-full",d.week),week_number_header:E("w-[--cell-size] select-none",d.week_number_header),week_number:E("text-muted-foreground select-none text-[0.8rem]",d.week_number),day:E("group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md",d.day),range_start:E("bg-accent rounded-l-md",d.range_start),range_middle:E("rounded-none",d.range_middle),range_end:E("bg-accent rounded-r-md",d.range_end),today:E("bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none",d.today),outside:E("text-muted-foreground aria-selected:text-muted-foreground",d.outside),disabled:E("text-muted-foreground opacity-50",d.disabled),hidden:E("invisible",d.hidden),...n},components:{Root:({className:c,rootRef:u,...h})=>s.jsx("div",{"data-slot":"calendar",ref:u,className:E(c),...h}),Chevron:({className:c,orientation:u,...h})=>u==="left"?s.jsx(Hn,{className:E("size-4",c),...h}):u==="right"?s.jsx(Gn,{className:E("size-4",c),...h}):s.jsx(yo,{className:E("size-4",c),...h}),DayButton:Cd,WeekNumber:({children:c,...u})=>s.jsx("td",{...u,children:s.jsx("div",{className:"flex size-[--cell-size] items-center justify-center text-center",children:c})}),...i},...l})}function Cd({className:e,day:n,modifiers:t,...r}){const o=Xn(),a=p.useRef(null);return p.useEffect(()=>{t.focused&&a.current?.focus()},[t.focused]),s.jsx(O,{ref:a,variant:"ghost",size:"icon","data-day":n.date.toLocaleDateString(),"data-selected-single":t.selected&&!t.range_start&&!t.range_end&&!t.range_middle,"data-range-start":t.range_start,"data-range-end":t.range_end,"data-range-middle":t.range_middle,className:E("data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-normal leading-none data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-xs [&>span]:opacity-70",o.day,e),...r})}const Ps=Qo,Fs=Jo,Kn=p.forwardRef(({className:e,align:n="center",sideOffset:t=4,...r},o)=>s.jsx(Ko,{children:s.jsx(Xr,{ref:o,align:n,sideOffset:t,className:E("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));Kn.displayName=Xr.displayName;const Es=(e=600)=>{const[n,t]=p.useState(!1),r=p.useCallback(()=>{if(n)return;t(!0);const a=setTimeout(()=>{requestAnimationFrame(()=>t(!1))},e);return()=>clearTimeout(a)},[n,e]);return{isRefreshing:n,startRefresh:r,refreshClasses:{container:`will-change-auto ${n?"animate-pulse":"transition-colors duration-200"}`,content:`will-change-auto ${n?"opacity-60":"opacity-100 transition-opacity duration-150"}`,icon:`will-change-transform ${n?"animate-spin":"transition-transform duration-100"}`}}};/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function nt(e,n){return typeof e=="function"?e(n):e}function ke(e,n){return t=>{n.setState(r=>({...r,[e]:nt(t,r[e])}))}}function en(e){return e instanceof Function}function Sd(e){return Array.isArray(e)&&e.every(n=>typeof n=="number")}function kd(e,n){const t=[],r=o=>{o.forEach(a=>{t.push(a);const i=n(a);i!=null&&i.length&&r(i)})};return r(e),t}function A(e,n,t){let r=[],o;return a=>{let i;t.key&&t.debug&&(i=Date.now());const l=e(a);if(!(l.length!==r.length||l.some((u,h)=>r[h]!==u)))return o;r=l;let c;if(t.key&&t.debug&&(c=Date.now()),o=n(...l),t==null||t.onChange==null||t.onChange(o),t.key&&t.debug&&t!=null&&t.debug()){const u=Math.round((Date.now()-i)*100)/100,h=Math.round((Date.now()-c)*100)/100,g=h/16,f=(x,w)=>{for(x=String(x);x.length<w;)x=" "+x;return x};console.info(`%c⏱ ${f(h,5)} /${f(u,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,t?.key)}return o}}function W(e,n,t,r){return{debug:()=>{var o;return(o=e?.debugAll)!=null?o:e[n]},key:!1,onChange:r}}function Md(e,n,t,r){const o=()=>{var i;return(i=a.getValue())!=null?i:e.options.renderFallbackValue},a={id:`${n.id}_${t.id}`,row:n,column:t,getValue:()=>n.getValue(r),renderValue:o,getContext:A(()=>[e,t,n,a],(i,l,d,c)=>({table:i,column:l,row:d,cell:c,getValue:c.getValue,renderValue:c.renderValue}),W(e.options,"debugCells"))};return e._features.forEach(i=>{i.createCell==null||i.createCell(a,t,n,e)},{}),a}function Rd(e,n,t,r){var o,a;const l={...e._getDefaultColumnDef(),...n},d=l.accessorKey;let c=(o=(a=l.id)!=null?a:d?typeof String.prototype.replaceAll=="function"?d.replaceAll(".","_"):d.replace(/\./g,"_"):void 0)!=null?o:typeof l.header=="string"?l.header:void 0,u;if(l.accessorFn?u=l.accessorFn:d&&(d.includes(".")?u=g=>{let f=g;for(const w of d.split(".")){var x;f=(x=f)==null?void 0:x[w]}return f}:u=g=>g[l.accessorKey]),!c)throw new Error;let h={id:`${String(c)}`,accessorFn:u,parent:r,depth:t,columnDef:l,columns:[],getFlatColumns:A(()=>[!0],()=>{var g;return[h,...(g=h.columns)==null?void 0:g.flatMap(f=>f.getFlatColumns())]},W(e.options,"debugColumns")),getLeafColumns:A(()=>[e._getOrderColumnsFn()],g=>{var f;if((f=h.columns)!=null&&f.length){let x=h.columns.flatMap(w=>w.getLeafColumns());return g(x)}return[h]},W(e.options,"debugColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(h,e);return h}const ue="debugHeaders";function Cr(e,n,t){var r;let a={id:(r=t.id)!=null?r:n.id,column:n,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const i=[],l=d=>{d.subHeaders&&d.subHeaders.length&&d.subHeaders.map(l),i.push(d)};return l(a),i},getContext:()=>({table:e,header:a,column:n})};return e._features.forEach(i=>{i.createHeader==null||i.createHeader(a,e)}),a}const _d={createTable:e=>{e.getHeaderGroups=A(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>{var a,i;const l=(a=r?.map(h=>t.find(g=>g.id===h)).filter(Boolean))!=null?a:[],d=(i=o?.map(h=>t.find(g=>g.id===h)).filter(Boolean))!=null?i:[],c=t.filter(h=>!(r!=null&&r.includes(h.id))&&!(o!=null&&o.includes(h.id)));return Ht(n,[...l,...c,...d],e)},W(e.options,ue)),e.getCenterHeaderGroups=A(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r,o)=>(t=t.filter(a=>!(r!=null&&r.includes(a.id))&&!(o!=null&&o.includes(a.id))),Ht(n,t,e,"center")),W(e.options,ue)),e.getLeftHeaderGroups=A(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(n,t,r)=>{var o;const a=(o=r?.map(i=>t.find(l=>l.id===i)).filter(Boolean))!=null?o:[];return Ht(n,a,e,"left")},W(e.options,ue)),e.getRightHeaderGroups=A(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(n,t,r)=>{var o;const a=(o=r?.map(i=>t.find(l=>l.id===i)).filter(Boolean))!=null?o:[];return Ht(n,a,e,"right")},W(e.options,ue)),e.getFooterGroups=A(()=>[e.getHeaderGroups()],n=>[...n].reverse(),W(e.options,ue)),e.getLeftFooterGroups=A(()=>[e.getLeftHeaderGroups()],n=>[...n].reverse(),W(e.options,ue)),e.getCenterFooterGroups=A(()=>[e.getCenterHeaderGroups()],n=>[...n].reverse(),W(e.options,ue)),e.getRightFooterGroups=A(()=>[e.getRightHeaderGroups()],n=>[...n].reverse(),W(e.options,ue)),e.getFlatHeaders=A(()=>[e.getHeaderGroups()],n=>n.map(t=>t.headers).flat(),W(e.options,ue)),e.getLeftFlatHeaders=A(()=>[e.getLeftHeaderGroups()],n=>n.map(t=>t.headers).flat(),W(e.options,ue)),e.getCenterFlatHeaders=A(()=>[e.getCenterHeaderGroups()],n=>n.map(t=>t.headers).flat(),W(e.options,ue)),e.getRightFlatHeaders=A(()=>[e.getRightHeaderGroups()],n=>n.map(t=>t.headers).flat(),W(e.options,ue)),e.getCenterLeafHeaders=A(()=>[e.getCenterFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),W(e.options,ue)),e.getLeftLeafHeaders=A(()=>[e.getLeftFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),W(e.options,ue)),e.getRightLeafHeaders=A(()=>[e.getRightFlatHeaders()],n=>n.filter(t=>{var r;return!((r=t.subHeaders)!=null&&r.length)}),W(e.options,ue)),e.getLeafHeaders=A(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(n,t,r)=>{var o,a,i,l,d,c;return[...(o=(a=n[0])==null?void 0:a.headers)!=null?o:[],...(i=(l=t[0])==null?void 0:l.headers)!=null?i:[],...(d=(c=r[0])==null?void 0:c.headers)!=null?d:[]].map(u=>u.getLeafHeaders()).flat()},W(e.options,ue))}};function Ht(e,n,t,r){var o,a;let i=0;const l=function(g,f){f===void 0&&(f=1),i=Math.max(i,f),g.filter(x=>x.getIsVisible()).forEach(x=>{var w;(w=x.columns)!=null&&w.length&&l(x.columns,f+1)},0)};l(e);let d=[];const c=(g,f)=>{const x={depth:f,id:[r,`${f}`].filter(Boolean).join("_"),headers:[]},w=[];g.forEach(b=>{const y=[...w].reverse()[0],N=b.column.depth===x.depth;let j,k=!1;if(N&&b.column.parent?j=b.column.parent:(j=b.column,k=!0),y&&y?.column===j)y.subHeaders.push(b);else{const S=Cr(t,j,{id:[r,f,j.id,b?.id].filter(Boolean).join("_"),isPlaceholder:k,placeholderId:k?`${w.filter(C=>C.column===j).length}`:void 0,depth:f,index:w.length});S.subHeaders.push(b),w.push(S)}x.headers.push(b),b.headerGroup=x}),d.push(x),f>0&&c(w,f-1)},u=n.map((g,f)=>Cr(t,g,{depth:i,index:f}));c(u,i-1),d.reverse();const h=g=>g.filter(x=>x.column.getIsVisible()).map(x=>{let w=0,b=0,y=[0];x.subHeaders&&x.subHeaders.length?(y=[],h(x.subHeaders).forEach(j=>{let{colSpan:k,rowSpan:S}=j;w+=k,y.push(S)})):w=1;const N=Math.min(...y);return b=b+N,x.colSpan=w,x.rowSpan=b,{colSpan:w,rowSpan:b}});return h((o=(a=d[0])==null?void 0:a.headers)!=null?o:[]),d}const Qn=(e,n,t,r,o,a,i)=>{let l={id:n,index:r,original:t,depth:o,parentId:i,_valuesCache:{},_uniqueValuesCache:{},getValue:d=>{if(l._valuesCache.hasOwnProperty(d))return l._valuesCache[d];const c=e.getColumn(d);if(c!=null&&c.accessorFn)return l._valuesCache[d]=c.accessorFn(l.original,r),l._valuesCache[d]},getUniqueValues:d=>{if(l._uniqueValuesCache.hasOwnProperty(d))return l._uniqueValuesCache[d];const c=e.getColumn(d);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(l._uniqueValuesCache[d]=c.columnDef.getUniqueValues(l.original,r),l._uniqueValuesCache[d]):(l._uniqueValuesCache[d]=[l.getValue(d)],l._uniqueValuesCache[d])},renderValue:d=>{var c;return(c=l.getValue(d))!=null?c:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>kd(l.subRows,d=>d.subRows),getParentRow:()=>l.parentId?e.getRow(l.parentId,!0):void 0,getParentRows:()=>{let d=[],c=l;for(;;){const u=c.getParentRow();if(!u)break;d.push(u),c=u}return d.reverse()},getAllCells:A(()=>[e.getAllLeafColumns()],d=>d.map(c=>Md(e,l,c,c.id)),W(e.options,"debugRows")),_getAllCellsByColumnId:A(()=>[l.getAllCells()],d=>d.reduce((c,u)=>(c[u.column.id]=u,c),{}),W(e.options,"debugRows"))};for(let d=0;d<e._features.length;d++){const c=e._features[d];c==null||c.createRow==null||c.createRow(l,e)}return l},Pd={createColumn:(e,n)=>{e._getFacetedRowModel=n.options.getFacetedRowModel&&n.options.getFacetedRowModel(n,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():n.getPreFilteredRowModel(),e._getFacetedUniqueValues=n.options.getFacetedUniqueValues&&n.options.getFacetedUniqueValues(n,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=n.options.getFacetedMinMaxValues&&n.options.getFacetedMinMaxValues(n,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Is=(e,n,t)=>{var r,o;const a=t==null||(r=t.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(n))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(a))};Is.autoRemove=e=>Oe(e);const Os=(e,n,t)=>{var r;return!!(!((r=e.getValue(n))==null||(r=r.toString())==null)&&r.includes(t))};Os.autoRemove=e=>Oe(e);const Ts=(e,n,t)=>{var r;return((r=e.getValue(n))==null||(r=r.toString())==null?void 0:r.toLowerCase())===t?.toLowerCase()};Ts.autoRemove=e=>Oe(e);const As=(e,n,t)=>{var r;return(r=e.getValue(n))==null?void 0:r.includes(t)};As.autoRemove=e=>Oe(e);const Ws=(e,n,t)=>!t.some(r=>{var o;return!((o=e.getValue(n))!=null&&o.includes(r))});Ws.autoRemove=e=>Oe(e)||!(e!=null&&e.length);const $s=(e,n,t)=>t.some(r=>{var o;return(o=e.getValue(n))==null?void 0:o.includes(r)});$s.autoRemove=e=>Oe(e)||!(e!=null&&e.length);const zs=(e,n,t)=>e.getValue(n)===t;zs.autoRemove=e=>Oe(e);const Ls=(e,n,t)=>e.getValue(n)==t;Ls.autoRemove=e=>Oe(e);const Jn=(e,n,t)=>{let[r,o]=t;const a=e.getValue(n);return a>=r&&a<=o};Jn.resolveFilterValue=e=>{let[n,t]=e,r=typeof n!="number"?parseFloat(n):n,o=typeof t!="number"?parseFloat(t):t,a=n===null||Number.isNaN(r)?-1/0:r,i=t===null||Number.isNaN(o)?1/0:o;if(a>i){const l=a;a=i,i=l}return[a,i]};Jn.autoRemove=e=>Oe(e)||Oe(e[0])&&Oe(e[1]);const Ve={includesString:Is,includesStringSensitive:Os,equalsString:Ts,arrIncludes:As,arrIncludesAll:Ws,arrIncludesSome:$s,equals:zs,weakEquals:Ls,inNumberRange:Jn};function Oe(e){return e==null||e===""}const Fd={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:ke("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,n)=>{e.getAutoFilterFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t?.getValue(e.id);return typeof r=="string"?Ve.includesString:typeof r=="number"?Ve.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?Ve.equals:Array.isArray(r)?Ve.arrIncludes:Ve.weakEquals},e.getFilterFn=()=>{var t,r;return en(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(r=n.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?t:Ve[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,r,o;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((r=n.options.enableColumnFilters)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=n.getState().columnFilters)==null||(t=t.find(r=>r.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,r;return(t=(r=n.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.setFilterValue=t=>{n.setColumnFilters(r=>{const o=e.getFilterFn(),a=r?.find(u=>u.id===e.id),i=nt(t,a?a.value:void 0);if(Sr(o,i,e)){var l;return(l=r?.filter(u=>u.id!==e.id))!=null?l:[]}const d={id:e.id,value:i};if(a){var c;return(c=r?.map(u=>u.id===e.id?d:u))!=null?c:[]}return r!=null&&r.length?[...r,d]:[d]})}},createRow:(e,n)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=n=>{const t=e.getAllLeafColumns(),r=o=>{var a;return(a=nt(n,o))==null?void 0:a.filter(i=>{const l=t.find(d=>d.id===i.id);if(l){const d=l.getFilterFn();if(Sr(d,i.value,l))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=n=>{var t,r;e.setColumnFilters(n?[]:(t=(r=e.initialState)==null?void 0:r.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function Sr(e,n,t){return(e&&e.autoRemove?e.autoRemove(n,t):!1)||typeof n>"u"||typeof n=="string"&&!n}const Ed=(e,n,t)=>t.reduce((r,o)=>{const a=o.getValue(e);return r+(typeof a=="number"?a:0)},0),Id=(e,n,t)=>{let r;return t.forEach(o=>{const a=o.getValue(e);a!=null&&(r>a||r===void 0&&a>=a)&&(r=a)}),r},Od=(e,n,t)=>{let r;return t.forEach(o=>{const a=o.getValue(e);a!=null&&(r<a||r===void 0&&a>=a)&&(r=a)}),r},Td=(e,n,t)=>{let r,o;return t.forEach(a=>{const i=a.getValue(e);i!=null&&(r===void 0?i>=i&&(r=o=i):(r>i&&(r=i),o<i&&(o=i)))}),[r,o]},Ad=(e,n)=>{let t=0,r=0;if(n.forEach(o=>{let a=o.getValue(e);a!=null&&(a=+a)>=a&&(++t,r+=a)}),t)return r/t},Wd=(e,n)=>{if(!n.length)return;const t=n.map(a=>a.getValue(e));if(!Sd(t))return;if(t.length===1)return t[0];const r=Math.floor(t.length/2),o=t.sort((a,i)=>a-i);return t.length%2!==0?o[r]:(o[r-1]+o[r])/2},$d=(e,n)=>Array.from(new Set(n.map(t=>t.getValue(e))).values()),zd=(e,n)=>new Set(n.map(t=>t.getValue(e))).size,Ld=(e,n)=>n.length,wn={sum:Ed,min:Id,max:Od,extent:Td,mean:Ad,median:Wd,unique:$d,uniqueCount:zd,count:Ld},Vd={getDefaultColumnDef:()=>({aggregatedCell:e=>{var n,t;return(n=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?n:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:ke("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,n)=>{e.toggleGrouping=()=>{n.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(r=>r!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,r;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((r=n.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=n.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=n.getCoreRowModel().flatRows[0],r=t?.getValue(e.id);if(typeof r=="number")return wn.sum;if(Object.prototype.toString.call(r)==="[object Date]")return wn.extent},e.getAggregationFn=()=>{var t,r;if(!e)throw new Error;return en(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(r=n.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?t:wn[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=n=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(n),e.resetGrouping=n=>{var t,r;e.setGrouping(n?[]:(t=(r=e.initialState)==null?void 0:r.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,n)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const r=n.getColumn(t);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[t]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,n,t,r)=>{e.getIsGrouped=()=>n.getIsGrouped()&&n.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&n.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=t.subRows)!=null&&o.length)}}};function Bd(e,n,t){if(!(n!=null&&n.length)||!t)return e;const r=e.filter(a=>!n.includes(a.id));return t==="remove"?r:[...n.map(a=>e.find(i=>i.id===a)).filter(Boolean),...r]}const Hd={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:ke("columnOrder",e)}),createColumn:(e,n)=>{e.getIndex=A(t=>[Ft(n,t)],t=>t.findIndex(r=>r.id===e.id),W(n.options,"debugColumns")),e.getIsFirstColumn=t=>{var r;return((r=Ft(n,t)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=t=>{var r;const o=Ft(n,t);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=n=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(n),e.resetColumnOrder=n=>{var t;e.setColumnOrder(n?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=A(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(n,t,r)=>o=>{let a=[];if(!(n!=null&&n.length))a=o;else{const i=[...n],l=[...o];for(;l.length&&i.length;){const d=i.shift(),c=l.findIndex(u=>u.id===d);c>-1&&a.push(l.splice(c,1)[0])}a=[...a,...l]}return Bd(a,t,r)},W(e.options,"debugTable"))}},yn=()=>({left:[],right:[]}),Gd={getInitialState:e=>({columnPinning:yn(),...e}),getDefaultOptions:e=>({onColumnPinningChange:ke("columnPinning",e)}),createColumn:(e,n)=>{e.pin=t=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);n.setColumnPinning(o=>{var a,i;if(t==="right"){var l,d;return{left:((l=o?.left)!=null?l:[]).filter(h=>!(r!=null&&r.includes(h))),right:[...((d=o?.right)!=null?d:[]).filter(h=>!(r!=null&&r.includes(h))),...r]}}if(t==="left"){var c,u;return{left:[...((c=o?.left)!=null?c:[]).filter(h=>!(r!=null&&r.includes(h))),...r],right:((u=o?.right)!=null?u:[]).filter(h=>!(r!=null&&r.includes(h)))}}return{left:((a=o?.left)!=null?a:[]).filter(h=>!(r!=null&&r.includes(h))),right:((i=o?.right)!=null?i:[]).filter(h=>!(r!=null&&r.includes(h)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,a,i;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((a=(i=n.options.enableColumnPinning)!=null?i:n.options.enablePinning)!=null?a:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(l=>l.id),{left:r,right:o}=n.getState().columnPinning,a=t.some(l=>r?.includes(l)),i=t.some(l=>o?.includes(l));return a?"left":i?"right":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();return o?(t=(r=n.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?t:-1:0}},createRow:(e,n)=>{e.getCenterVisibleCells=A(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left,n.getState().columnPinning.right],(t,r,o)=>{const a=[...r??[],...o??[]];return t.filter(i=>!a.includes(i.column.id))},W(n.options,"debugRows")),e.getLeftVisibleCells=A(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left],(t,r)=>(r??[]).map(a=>t.find(i=>i.column.id===a)).filter(Boolean).map(a=>({...a,position:"left"})),W(n.options,"debugRows")),e.getRightVisibleCells=A(()=>[e._getAllVisibleCells(),n.getState().columnPinning.right],(t,r)=>(r??[]).map(a=>t.find(i=>i.column.id===a)).filter(Boolean).map(a=>({...a,position:"right"})),W(n.options,"debugRows"))},createTable:e=>{e.setColumnPinning=n=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(n),e.resetColumnPinning=n=>{var t,r;return e.setColumnPinning(n?yn():(t=(r=e.initialState)==null?void 0:r.columnPinning)!=null?t:yn())},e.getIsSomeColumnsPinned=n=>{var t;const r=e.getState().columnPinning;if(!n){var o,a;return!!((o=r.left)!=null&&o.length||(a=r.right)!=null&&a.length)}return!!((t=r[n])!=null&&t.length)},e.getLeftLeafColumns=A(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),W(e.options,"debugColumns")),e.getRightLeafColumns=A(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(n,t)=>(t??[]).map(r=>n.find(o=>o.id===r)).filter(Boolean),W(e.options,"debugColumns")),e.getCenterLeafColumns=A(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,t,r)=>{const o=[...t??[],...r??[]];return n.filter(a=>!o.includes(a.id))},W(e.options,"debugColumns"))}};function Yd(e){return e||(typeof document<"u"?document:null)}const Gt={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},jn=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),qd={getDefaultColumnDef:()=>Gt,getInitialState:e=>({columnSizing:{},columnSizingInfo:jn(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:ke("columnSizing",e),onColumnSizingInfoChange:ke("columnSizingInfo",e)}),createColumn:(e,n)=>{e.getSize=()=>{var t,r,o;const a=n.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:Gt.minSize,(r=a??e.columnDef.size)!=null?r:Gt.size),(o=e.columnDef.maxSize)!=null?o:Gt.maxSize)},e.getStart=A(t=>[t,Ft(n,t),n.getState().columnSizing],(t,r)=>r.slice(0,e.getIndex(t)).reduce((o,a)=>o+a.getSize(),0),W(n.options,"debugColumns")),e.getAfter=A(t=>[t,Ft(n,t),n.getState().columnSizing],(t,r)=>r.slice(e.getIndex(t)+1).reduce((o,a)=>o+a.getSize(),0),W(n.options,"debugColumns")),e.resetSize=()=>{n.setColumnSizing(t=>{let{[e.id]:r,...o}=t;return o})},e.getCanResize=()=>{var t,r;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((r=n.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>n.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,n)=>{e.getSize=()=>{let t=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var a;t+=(a=o.column.getSize())!=null?a:0}};return r(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const r=n.getColumn(e.column.id),o=r?.getCanResize();return a=>{if(!r||!o||(a.persist==null||a.persist(),Nn(a)&&a.touches&&a.touches.length>1))return;const i=e.getSize(),l=e?e.getLeafHeaders().map(y=>[y.column.id,y.column.getSize()]):[[r.id,r.getSize()]],d=Nn(a)?Math.round(a.touches[0].clientX):a.clientX,c={},u=(y,N)=>{typeof N=="number"&&(n.setColumnSizingInfo(j=>{var k,S;const C=n.options.columnResizeDirection==="rtl"?-1:1,M=(N-((k=j?.startOffset)!=null?k:0))*C,_=Math.max(M/((S=j?.startSize)!=null?S:0),-.999999);return j.columnSizingStart.forEach(F=>{let[$,z]=F;c[$]=Math.round(Math.max(z+z*_,0)*100)/100}),{...j,deltaOffset:M,deltaPercentage:_}}),(n.options.columnResizeMode==="onChange"||y==="end")&&n.setColumnSizing(j=>({...j,...c})))},h=y=>u("move",y),g=y=>{u("end",y),n.setColumnSizingInfo(N=>({...N,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f=Yd(t),x={moveHandler:y=>h(y.clientX),upHandler:y=>{f?.removeEventListener("mousemove",x.moveHandler),f?.removeEventListener("mouseup",x.upHandler),g(y.clientX)}},w={moveHandler:y=>(y.cancelable&&(y.preventDefault(),y.stopPropagation()),h(y.touches[0].clientX),!1),upHandler:y=>{var N;f?.removeEventListener("touchmove",w.moveHandler),f?.removeEventListener("touchend",w.upHandler),y.cancelable&&(y.preventDefault(),y.stopPropagation()),g((N=y.touches[0])==null?void 0:N.clientX)}},b=Ud()?{passive:!1}:!1;Nn(a)?(f?.addEventListener("touchmove",w.moveHandler,b),f?.addEventListener("touchend",w.upHandler,b)):(f?.addEventListener("mousemove",x.moveHandler,b),f?.addEventListener("mouseup",x.upHandler,b)),n.setColumnSizingInfo(y=>({...y,startOffset:d,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:l,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=n=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(n),e.setColumnSizingInfo=n=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(n),e.resetColumnSizing=n=>{var t;e.setColumnSizing(n?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=n=>{var t;e.setColumnSizingInfo(n?jn():(t=e.initialState.columnSizingInfo)!=null?t:jn())},e.getTotalSize=()=>{var n,t;return(n=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getLeftTotalSize=()=>{var n,t;return(n=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getCenterTotalSize=()=>{var n,t;return(n=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0},e.getRightTotalSize=()=>{var n,t;return(n=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((r,o)=>r+o.getSize(),0))!=null?n:0}}};let Yt=null;function Ud(){if(typeof Yt=="boolean")return Yt;let e=!1;try{const n={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,n),window.removeEventListener("test",t)}catch{e=!1}return Yt=e,Yt}function Nn(e){return e.type==="touchstart"}const Zd={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:ke("columnVisibility",e)}),createColumn:(e,n)=>{e.toggleVisibility=t=>{e.getCanHide()&&n.setColumnVisibility(r=>({...r,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,r;const o=e.columns;return(t=o.length?o.some(a=>a.getIsVisible()):(r=n.getState().columnVisibility)==null?void 0:r[e.id])!=null?t:!0},e.getCanHide=()=>{var t,r;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((r=n.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,n)=>{e._getAllVisibleCells=A(()=>[e.getAllCells(),n.getState().columnVisibility],t=>t.filter(r=>r.column.getIsVisible()),W(n.options,"debugRows")),e.getVisibleCells=A(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,r,o)=>[...t,...r,...o],W(n.options,"debugRows"))},createTable:e=>{const n=(t,r)=>A(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(a=>a.getIsVisible==null?void 0:a.getIsVisible()),W(e.options,"debugColumns"));e.getVisibleFlatColumns=n("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=n("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=n("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=n("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=n("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var r;e.setColumnVisibility(t?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=t=>{var r;t=(r=t)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,a)=>({...o,[a.id]:t||!(a.getCanHide!=null&&a.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var r;e.toggleAllColumnsVisible((r=t.target)==null?void 0:r.checked)}}};function Ft(e,n){return n?n==="center"?e.getCenterVisibleLeafColumns():n==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Xd={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Kd={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:ke("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:n=>{var t;const r=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[n.id])==null?void 0:t.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,n)=>{e.getCanGlobalFilter=()=>{var t,r,o,a;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((r=n.options.enableGlobalFilter)!=null?r:!0)&&((o=n.options.enableFilters)!=null?o:!0)&&((a=n.options.getColumnCanGlobalFilter==null?void 0:n.options.getColumnCanGlobalFilter(e))!=null?a:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>Ve.includesString,e.getGlobalFilterFn=()=>{var n,t;const{globalFilterFn:r}=e.options;return en(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(n=(t=e.options.filterFns)==null?void 0:t[r])!=null?n:Ve[r]},e.setGlobalFilter=n=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(n)},e.resetGlobalFilter=n=>{e.setGlobalFilter(n?void 0:e.initialState.globalFilter)}}},Qd={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:ke("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let n=!1,t=!1;e._autoResetExpanded=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,a;e.setExpanded(r?{}:(o=(a=e.initialState)==null?void 0:a.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(a=>{const i=a.split(".");r=Math.max(r,i.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,n)=>{e.toggleExpanded=t=>{n.setExpanded(r=>{var o;const a=r===!0?!0:!!(r!=null&&r[e.id]);let i={};if(r===!0?Object.keys(n.getRowModel().rowsById).forEach(l=>{i[l]=!0}):i=r,t=(o=t)!=null?o:!a,!a&&t)return{...i,[e.id]:!0};if(a&&!t){const{[e.id]:l,...d}=i;return d}return r})},e.getIsExpanded=()=>{var t;const r=n.getState().expanded;return!!((t=n.options.getIsRowExpanded==null?void 0:n.options.getIsRowExpanded(e))!=null?t:r===!0||r?.[e.id])},e.getCanExpand=()=>{var t,r,o;return(t=n.options.getRowCanExpand==null?void 0:n.options.getRowCanExpand(e))!=null?t:((r=n.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let t=!0,r=e;for(;t&&r.parentId;)r=n.getRow(r.parentId,!0),t=r.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},An=0,Wn=10,Dn=()=>({pageIndex:An,pageSize:Wn}),Jd={getInitialState:e=>({...e,pagination:{...Dn(),...e?.pagination}}),getDefaultOptions:e=>({onPaginationChange:ke("pagination",e)}),createTable:e=>{let n=!1,t=!1;e._autoResetPageIndex=()=>{var r,o;if(!n){e._queue(()=>{n=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=r=>{const o=a=>nt(r,a);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?Dn():(o=e.initialState.pagination)!=null?o:Dn())},e.setPageIndex=r=>{e.setPagination(o=>{let a=nt(r,o.pageIndex);const i=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return a=Math.max(0,Math.min(a,i)),{...o,pageIndex:a}})},e.resetPageIndex=r=>{var o,a;e.setPageIndex(r?An:(o=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageIndex)!=null?o:An)},e.resetPageSize=r=>{var o,a;e.setPageSize(r?Wn:(o=(a=e.initialState)==null||(a=a.pagination)==null?void 0:a.pageSize)!=null?o:Wn)},e.setPageSize=r=>{e.setPagination(o=>{const a=Math.max(1,nt(r,o.pageSize)),i=o.pageSize*o.pageIndex,l=Math.floor(i/a);return{...o,pageIndex:l,pageSize:a}})},e.setPageCount=r=>e.setPagination(o=>{var a;let i=nt(r,(a=e.options.pageCount)!=null?a:-1);return typeof i=="number"&&(i=Math.max(-1,i)),{...o,pageCount:i}}),e.getPageOptions=A(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((a,i)=>i)),o},W(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},Cn=()=>({top:[],bottom:[]}),ec={getInitialState:e=>({rowPinning:Cn(),...e}),getDefaultOptions:e=>({onRowPinningChange:ke("rowPinning",e)}),createRow:(e,n)=>{e.pin=(t,r,o)=>{const a=r?e.getLeafRows().map(d=>{let{id:c}=d;return c}):[],i=o?e.getParentRows().map(d=>{let{id:c}=d;return c}):[],l=new Set([...i,e.id,...a]);n.setRowPinning(d=>{var c,u;if(t==="bottom"){var h,g;return{top:((h=d?.top)!=null?h:[]).filter(w=>!(l!=null&&l.has(w))),bottom:[...((g=d?.bottom)!=null?g:[]).filter(w=>!(l!=null&&l.has(w))),...Array.from(l)]}}if(t==="top"){var f,x;return{top:[...((f=d?.top)!=null?f:[]).filter(w=>!(l!=null&&l.has(w))),...Array.from(l)],bottom:((x=d?.bottom)!=null?x:[]).filter(w=>!(l!=null&&l.has(w)))}}return{top:((c=d?.top)!=null?c:[]).filter(w=>!(l!=null&&l.has(w))),bottom:((u=d?.bottom)!=null?u:[]).filter(w=>!(l!=null&&l.has(w)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:r,enablePinning:o}=n.options;return typeof r=="function"?r(e):(t=r??o)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:r,bottom:o}=n.getState().rowPinning,a=t.some(l=>r?.includes(l)),i=t.some(l=>o?.includes(l));return a?"top":i?"bottom":!1},e.getPinnedIndex=()=>{var t,r;const o=e.getIsPinned();if(!o)return-1;const a=(t=o==="top"?n.getTopRows():n.getBottomRows())==null?void 0:t.map(i=>{let{id:l}=i;return l});return(r=a?.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=n=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(n),e.resetRowPinning=n=>{var t,r;return e.setRowPinning(n?Cn():(t=(r=e.initialState)==null?void 0:r.rowPinning)!=null?t:Cn())},e.getIsSomeRowsPinned=n=>{var t;const r=e.getState().rowPinning;if(!n){var o,a;return!!((o=r.top)!=null&&o.length||(a=r.bottom)!=null&&a.length)}return!!((t=r[n])!=null&&t.length)},e._getPinnedRows=(n,t,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(t??[]).map(i=>{const l=e.getRow(i,!0);return l.getIsAllParentsExpanded()?l:null}):(t??[]).map(i=>n.find(l=>l.id===i))).filter(Boolean).map(i=>({...i,position:r}))},e.getTopRows=A(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(n,t)=>e._getPinnedRows(n,t,"top"),W(e.options,"debugRows")),e.getBottomRows=A(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(n,t)=>e._getPinnedRows(n,t,"bottom"),W(e.options,"debugRows")),e.getCenterRows=A(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(n,t,r)=>{const o=new Set([...t??[],...r??[]]);return n.filter(a=>!o.has(a.id))},W(e.options,"debugRows"))}},tc={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:ke("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=n=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(n),e.resetRowSelection=n=>{var t;return e.setRowSelection(n?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=n=>{e.setRowSelection(t=>{n=typeof n<"u"?n:!e.getIsAllRowsSelected();const r={...t},o=e.getPreGroupedRowModel().flatRows;return n?o.forEach(a=>{a.getCanSelect()&&(r[a.id]=!0)}):o.forEach(a=>{delete r[a.id]}),r})},e.toggleAllPageRowsSelected=n=>e.setRowSelection(t=>{const r=typeof n<"u"?n:!e.getIsAllPageRowsSelected(),o={...t};return e.getRowModel().rows.forEach(a=>{$n(o,a.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=A(()=>[e.getState().rowSelection,e.getCoreRowModel()],(n,t)=>Object.keys(n).length?Sn(e,t):{rows:[],flatRows:[],rowsById:{}},W(e.options,"debugTable")),e.getFilteredSelectedRowModel=A(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(n,t)=>Object.keys(n).length?Sn(e,t):{rows:[],flatRows:[],rowsById:{}},W(e.options,"debugTable")),e.getGroupedSelectedRowModel=A(()=>[e.getState().rowSelection,e.getSortedRowModel()],(n,t)=>Object.keys(n).length?Sn(e,t):{rows:[],flatRows:[],rowsById:{}},W(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const n=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let r=!!(n.length&&Object.keys(t).length);return r&&n.some(o=>o.getCanSelect()&&!t[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:t}=e.getState();let r=!!n.length;return r&&n.some(o=>!t[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var n;const t=Object.keys((n=e.getState().rowSelection)!=null?n:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:n.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>n=>{e.toggleAllRowsSelected(n.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>n=>{e.toggleAllPageRowsSelected(n.target.checked)}},createRow:(e,n)=>{e.toggleSelected=(t,r)=>{const o=e.getIsSelected();n.setRowSelection(a=>{var i;if(t=typeof t<"u"?t:!o,e.getCanSelect()&&o===t)return a;const l={...a};return $n(l,e.id,t,(i=r?.selectChildren)!=null?i:!0,n),l})},e.getIsSelected=()=>{const{rowSelection:t}=n.getState();return er(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=n.getState();return zn(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=n.getState();return zn(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof n.options.enableRowSelection=="function"?n.options.enableRowSelection(e):(t=n.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof n.options.enableSubRowSelection=="function"?n.options.enableSubRowSelection(e):(t=n.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof n.options.enableMultiRowSelection=="function"?n.options.enableMultiRowSelection(e):(t=n.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return r=>{var o;t&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},$n=(e,n,t,r,o)=>{var a;const i=o.getRow(n,!0);t?(i.getCanMultiSelect()||Object.keys(e).forEach(l=>delete e[l]),i.getCanSelect()&&(e[n]=!0)):delete e[n],r&&(a=i.subRows)!=null&&a.length&&i.getCanSelectSubRows()&&i.subRows.forEach(l=>$n(e,l.id,t,r,o))};function Sn(e,n){const t=e.getState().rowSelection,r=[],o={},a=function(i,l){return i.map(d=>{var c;const u=er(d,t);if(u&&(r.push(d),o[d.id]=d),(c=d.subRows)!=null&&c.length&&(d={...d,subRows:a(d.subRows)}),u)return d}).filter(Boolean)};return{rows:a(n.rows),flatRows:r,rowsById:o}}function er(e,n){var t;return(t=n[e.id])!=null?t:!1}function zn(e,n,t){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,a=!1;return e.subRows.forEach(i=>{if(!(a&&!o)&&(i.getCanSelect()&&(er(i,n)?a=!0:o=!1),i.subRows&&i.subRows.length)){const l=zn(i,n);l==="all"?a=!0:(l==="some"&&(a=!0),o=!1)}}),o?"all":a?"some":!1}const Ln=/([0-9]+)/gm,nc=(e,n,t)=>Vs(rt(e.getValue(t)).toLowerCase(),rt(n.getValue(t)).toLowerCase()),rc=(e,n,t)=>Vs(rt(e.getValue(t)),rt(n.getValue(t))),sc=(e,n,t)=>tr(rt(e.getValue(t)).toLowerCase(),rt(n.getValue(t)).toLowerCase()),oc=(e,n,t)=>tr(rt(e.getValue(t)),rt(n.getValue(t))),ac=(e,n,t)=>{const r=e.getValue(t),o=n.getValue(t);return r>o?1:r<o?-1:0},ic=(e,n,t)=>tr(e.getValue(t),n.getValue(t));function tr(e,n){return e===n?0:e>n?1:-1}function rt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Vs(e,n){const t=e.split(Ln).filter(Boolean),r=n.split(Ln).filter(Boolean);for(;t.length&&r.length;){const o=t.shift(),a=r.shift(),i=parseInt(o,10),l=parseInt(a,10),d=[i,l].sort();if(isNaN(d[0])){if(o>a)return 1;if(a>o)return-1;continue}if(isNaN(d[1]))return isNaN(i)?-1:1;if(i>l)return 1;if(l>i)return-1}return t.length-r.length}const Ct={alphanumeric:nc,alphanumericCaseSensitive:rc,text:sc,textCaseSensitive:oc,datetime:ac,basic:ic},lc={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:ke("sorting",e),isMultiSortEvent:n=>n.shiftKey}),createColumn:(e,n)=>{e.getAutoSortingFn=()=>{const t=n.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of t){const a=o?.getValue(e.id);if(Object.prototype.toString.call(a)==="[object Date]")return Ct.datetime;if(typeof a=="string"&&(r=!0,a.split(Ln).length>1))return Ct.alphanumeric}return r?Ct.text:Ct.basic},e.getAutoSortDir=()=>{const t=n.getFilteredRowModel().flatRows[0];return typeof t?.getValue(e.id)=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,r;if(!e)throw new Error;return en(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(r=n.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?t:Ct[e.columnDef.sortingFn]},e.toggleSorting=(t,r)=>{const o=e.getNextSortingOrder(),a=typeof t<"u"&&t!==null;n.setSorting(i=>{const l=i?.find(f=>f.id===e.id),d=i?.findIndex(f=>f.id===e.id);let c=[],u,h=a?t:o==="desc";if(i!=null&&i.length&&e.getCanMultiSort()&&r?l?u="toggle":u="add":i!=null&&i.length&&d!==i.length-1?u="replace":l?u="toggle":u="replace",u==="toggle"&&(a||o||(u="remove")),u==="add"){var g;c=[...i,{id:e.id,desc:h}],c.splice(0,c.length-((g=n.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else u==="toggle"?c=i.map(f=>f.id===e.id?{...f,desc:h}:f):u==="remove"?c=i.filter(f=>f.id!==e.id):c=[{id:e.id,desc:h}];return c})},e.getFirstSortDir=()=>{var t,r;return((t=(r=e.columnDef.sortDescFirst)!=null?r:n.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var r,o;const a=e.getFirstSortDir(),i=e.getIsSorted();return i?i!==a&&((r=n.options.enableSortingRemoval)==null||r)&&(!(t&&(o=n.options.enableMultiRemove)!=null)||o)?!1:i==="desc"?"asc":"desc":a},e.getCanSort=()=>{var t,r;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((r=n.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,r;return(t=(r=e.columnDef.enableMultiSort)!=null?r:n.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const r=(t=n.getState().sorting)==null?void 0:t.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,r;return(t=(r=n.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?t:-1},e.clearSorting=()=>{n.setSorting(t=>t!=null&&t.length?t.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return r=>{t&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?n.options.isMultiSortEvent==null?void 0:n.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=n=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(n),e.resetSorting=n=>{var t,r;e.setSorting(n?[]:(t=(r=e.initialState)==null?void 0:r.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},dc=[_d,Zd,Hd,Gd,Pd,Fd,Xd,Kd,lc,Vd,Qd,Jd,ec,tc,qd];function cc(e){var n,t;const r=[...dc,...(n=e._features)!=null?n:[]];let o={_features:r};const a=o._features.reduce((g,f)=>Object.assign(g,f.getDefaultOptions==null?void 0:f.getDefaultOptions(o)),{}),i=g=>o.options.mergeOptions?o.options.mergeOptions(a,g):{...a,...g};let d={...{},...(t=e.initialState)!=null?t:{}};o._features.forEach(g=>{var f;d=(f=g.getInitialState==null?void 0:g.getInitialState(d))!=null?f:d});const c=[];let u=!1;const h={_features:r,options:{...a,...e},initialState:d,_queue:g=>{c.push(g),u||(u=!0,Promise.resolve().then(()=>{for(;c.length;)c.shift()();u=!1}).catch(f=>setTimeout(()=>{throw f})))},reset:()=>{o.setState(o.initialState)},setOptions:g=>{const f=nt(g,o.options);o.options=i(f)},getState:()=>o.options.state,setState:g=>{o.options.onStateChange==null||o.options.onStateChange(g)},_getRowId:(g,f,x)=>{var w;return(w=o.options.getRowId==null?void 0:o.options.getRowId(g,f,x))!=null?w:`${x?[x.id,f].join("."):f}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(g,f)=>{let x=(f?o.getPrePaginationRowModel():o.getRowModel()).rowsById[g];if(!x&&(x=o.getCoreRowModel().rowsById[g],!x))throw new Error;return x},_getDefaultColumnDef:A(()=>[o.options.defaultColumn],g=>{var f;return g=(f=g)!=null?f:{},{header:x=>{const w=x.header.column.columnDef;return w.accessorKey?w.accessorKey:w.accessorFn?w.id:null},cell:x=>{var w,b;return(w=(b=x.renderValue())==null||b.toString==null?void 0:b.toString())!=null?w:null},...o._features.reduce((x,w)=>Object.assign(x,w.getDefaultColumnDef==null?void 0:w.getDefaultColumnDef()),{}),...g}},W(e,"debugColumns")),_getColumnDefs:()=>o.options.columns,getAllColumns:A(()=>[o._getColumnDefs()],g=>{const f=function(x,w,b){return b===void 0&&(b=0),x.map(y=>{const N=Rd(o,y,b,w),j=y;return N.columns=j.columns?f(j.columns,N,b+1):[],N})};return f(g)},W(e,"debugColumns")),getAllFlatColumns:A(()=>[o.getAllColumns()],g=>g.flatMap(f=>f.getFlatColumns()),W(e,"debugColumns")),_getAllFlatColumnsById:A(()=>[o.getAllFlatColumns()],g=>g.reduce((f,x)=>(f[x.id]=x,f),{}),W(e,"debugColumns")),getAllLeafColumns:A(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(g,f)=>{let x=g.flatMap(w=>w.getLeafColumns());return f(x)},W(e,"debugColumns")),getColumn:g=>o._getAllFlatColumnsById()[g]};Object.assign(o,h);for(let g=0;g<o._features.length;g++){const f=o._features[g];f==null||f.createTable==null||f.createTable(o)}return o}function uc(){return e=>A(()=>[e.options.data],n=>{const t={rows:[],flatRows:[],rowsById:{}},r=function(o,a,i){a===void 0&&(a=0);const l=[];for(let c=0;c<o.length;c++){const u=Qn(e,e._getRowId(o[c],c,i),o[c],c,a,void 0,i?.id);if(t.flatRows.push(u),t.rowsById[u.id]=u,l.push(u),e.options.getSubRows){var d;u.originalSubRows=e.options.getSubRows(o[c],c),(d=u.originalSubRows)!=null&&d.length&&(u.subRows=r(u.originalSubRows,a+1,u))}}return l};return t.rows=r(n),t},W(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function mc(e){const n=[],t=r=>{var o;n.push(r),(o=r.subRows)!=null&&o.length&&r.getIsExpanded()&&r.subRows.forEach(t)};return e.rows.forEach(t),{rows:n,flatRows:e.flatRows,rowsById:e.rowsById}}function fc(e,n,t){return t.options.filterFromLeafRows?gc(e,n,t):hc(e,n,t)}function gc(e,n,t){var r;const o=[],a={},i=(r=t.options.maxLeafRowFilterDepth)!=null?r:100,l=function(d,c){c===void 0&&(c=0);const u=[];for(let g=0;g<d.length;g++){var h;let f=d[g];const x=Qn(t,f.id,f.original,f.index,f.depth,void 0,f.parentId);if(x.columnFilters=f.columnFilters,(h=f.subRows)!=null&&h.length&&c<i){if(x.subRows=l(f.subRows,c+1),f=x,n(f)&&!x.subRows.length){u.push(f),a[f.id]=f,o.push(f);continue}if(n(f)||x.subRows.length){u.push(f),a[f.id]=f,o.push(f);continue}}else f=x,n(f)&&(u.push(f),a[f.id]=f,o.push(f))}return u};return{rows:l(e),flatRows:o,rowsById:a}}function hc(e,n,t){var r;const o=[],a={},i=(r=t.options.maxLeafRowFilterDepth)!=null?r:100,l=function(d,c){c===void 0&&(c=0);const u=[];for(let g=0;g<d.length;g++){let f=d[g];if(n(f)){var h;if((h=f.subRows)!=null&&h.length&&c<i){const w=Qn(t,f.id,f.original,f.index,f.depth,void 0,f.parentId);w.subRows=l(f.subRows,c+1),f=w}u.push(f),o.push(f),a[f.id]=f}}return u};return{rows:l(e),flatRows:o,rowsById:a}}function pc(){return e=>A(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(n,t,r)=>{if(!n.rows.length||!(t!=null&&t.length)&&!r){for(let g=0;g<n.flatRows.length;g++)n.flatRows[g].columnFilters={},n.flatRows[g].columnFiltersMeta={};return n}const o=[],a=[];(t??[]).forEach(g=>{var f;const x=e.getColumn(g.id);if(!x)return;const w=x.getFilterFn();w&&o.push({id:g.id,filterFn:w,resolvedValue:(f=w.resolveFilterValue==null?void 0:w.resolveFilterValue(g.value))!=null?f:g.value})});const i=(t??[]).map(g=>g.id),l=e.getGlobalFilterFn(),d=e.getAllLeafColumns().filter(g=>g.getCanGlobalFilter());r&&l&&d.length&&(i.push("__global__"),d.forEach(g=>{var f;a.push({id:g.id,filterFn:l,resolvedValue:(f=l.resolveFilterValue==null?void 0:l.resolveFilterValue(r))!=null?f:r})}));let c,u;for(let g=0;g<n.flatRows.length;g++){const f=n.flatRows[g];if(f.columnFilters={},o.length)for(let x=0;x<o.length;x++){c=o[x];const w=c.id;f.columnFilters[w]=c.filterFn(f,w,c.resolvedValue,b=>{f.columnFiltersMeta[w]=b})}if(a.length){for(let x=0;x<a.length;x++){u=a[x];const w=u.id;if(u.filterFn(f,w,u.resolvedValue,b=>{f.columnFiltersMeta[w]=b})){f.columnFilters.__global__=!0;break}}f.columnFilters.__global__!==!0&&(f.columnFilters.__global__=!1)}}const h=g=>{for(let f=0;f<i.length;f++)if(g.columnFilters[i[f]]===!1)return!1;return!0};return fc(n.rows,h,e)},W(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function xc(e){return n=>A(()=>[n.getState().pagination,n.getPrePaginationRowModel(),n.options.paginateExpandedRows?void 0:n.getState().expanded],(t,r)=>{if(!r.rows.length)return r;const{pageSize:o,pageIndex:a}=t;let{rows:i,flatRows:l,rowsById:d}=r;const c=o*a,u=c+o;i=i.slice(c,u);let h;n.options.paginateExpandedRows?h={rows:i,flatRows:l,rowsById:d}:h=mc({rows:i,flatRows:l,rowsById:d}),h.flatRows=[];const g=f=>{h.flatRows.push(f),f.subRows.length&&f.subRows.forEach(g)};return h.rows.forEach(g),h},W(n.options,"debugTable"))}function vc(){return e=>A(()=>[e.getState().sorting,e.getPreSortedRowModel()],(n,t)=>{if(!t.rows.length||!(n!=null&&n.length))return t;const r=e.getState().sorting,o=[],a=r.filter(d=>{var c;return(c=e.getColumn(d.id))==null?void 0:c.getCanSort()}),i={};a.forEach(d=>{const c=e.getColumn(d.id);c&&(i[d.id]={sortUndefined:c.columnDef.sortUndefined,invertSorting:c.columnDef.invertSorting,sortingFn:c.getSortingFn()})});const l=d=>{const c=d.map(u=>({...u}));return c.sort((u,h)=>{for(let f=0;f<a.length;f+=1){var g;const x=a[f],w=i[x.id],b=w.sortUndefined,y=(g=x?.desc)!=null?g:!1;let N=0;if(b){const j=u.getValue(x.id),k=h.getValue(x.id),S=j===void 0,C=k===void 0;if(S||C){if(b==="first")return S?-1:1;if(b==="last")return S?1:-1;N=S&&C?0:S?b:-b}}if(N===0&&(N=w.sortingFn(u,h,x.id)),N!==0)return y&&(N*=-1),w.invertSorting&&(N*=-1),N}return u.index-h.index}),c.forEach(u=>{var h;o.push(u),(h=u.subRows)!=null&&h.length&&(u.subRows=l(u.subRows))}),c};return{rows:l(t.rows),flatRows:o,rowsById:t.rowsById}},W(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function kr(e,n){return e?bc(e)?p.createElement(e,n):e:null}function bc(e){return wc(e)||typeof e=="function"||yc(e)}function wc(e){return typeof e=="function"&&(()=>{const n=Object.getPrototypeOf(e);return n.prototype&&n.prototype.isReactComponent})()}function yc(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function jc(e){const n={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=p.useState(()=>({current:cc(n)})),[r,o]=p.useState(()=>t.current.initialState);return t.current.setOptions(a=>({...a,...e,state:{...r,...e.state},onStateChange:i=>{o(i),e.onStateChange==null||e.onStateChange(i)}})),t.current}const Bs=p.forwardRef(({className:e,...n},t)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:t,className:E("w-full caption-bottom text-sm",e),...n})}));Bs.displayName="Table";const Hs=p.forwardRef(({className:e,...n},t)=>s.jsx("thead",{ref:t,className:E("[&_tr]:border-b",e),...n}));Hs.displayName="TableHeader";const Gs=p.forwardRef(({className:e,...n},t)=>s.jsx("tbody",{ref:t,className:E("[&_tr:last-child]:border-0",e),...n}));Gs.displayName="TableBody";const Nc=p.forwardRef(({className:e,...n},t)=>s.jsx("tfoot",{ref:t,className:E("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...n}));Nc.displayName="TableFooter";const Xt=p.forwardRef(({className:e,...n},t)=>s.jsx("tr",{ref:t,className:E("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...n}));Xt.displayName="TableRow";const Ys=p.forwardRef(({className:e,...n},t)=>s.jsx("th",{ref:t,className:E("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...n}));Ys.displayName="TableHead";const Vn=p.forwardRef(({className:e,...n},t)=>s.jsx("td",{ref:t,className:E("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...n}));Vn.displayName="TableCell";const Dc=p.forwardRef(({className:e,...n},t)=>s.jsx("caption",{ref:t,className:E("mt-4 text-sm text-muted-foreground",e),...n}));Dc.displayName="TableCaption";const Mr=[{accessorKey:"referenceId",header:"Reference ID",cell:({row:e})=>s.jsx("div",{className:"font-medium",children:e.getValue("referenceId")})},{accessorKey:"title",header:({column:e})=>s.jsxs(O,{variant:"ghost",onClick:()=>e.toggleSorting(e.getIsSorted()==="asc"),children:["Title",s.jsx(Do,{className:"ml-2 h-4 w-4"})]}),cell:({row:e})=>s.jsx("div",{children:e.getValue("title")})},{accessorKey:"category",header:"Category",cell:({row:e})=>s.jsx("div",{children:e.getValue("category")})},{accessorKey:"status",header:"Status",cell:({row:e})=>{const n=e.getValue("status");return s.jsx(ft,{className:n==="submitted"?"bg-blue-600 text-white":n==="in-review"?"bg-yellow-600 text-white":n==="resolved"?"bg-green-600 text-white":"bg-red-600 text-white",children:n.charAt(0).toUpperCase()+n.slice(1).replace("-"," ")})}},{accessorKey:"priority",header:"Priority",cell:({row:e})=>{const n=e.getValue("priority");return s.jsx(ft,{className:n==="urgent"?"bg-red-600 text-white":n==="high"?"bg-orange-600 text-white":n==="medium"?"bg-yellow-600 text-white":"bg-gray-600 text-white",children:n.charAt(0).toUpperCase()+n.slice(1)})}},{accessorKey:"filesCount",header:"Files",cell:({row:e})=>{const n=e.getValue("filesCount");return s.jsxs("div",{className:"flex items-center",children:[s.jsx(Qt,{className:"mr-1 h-4 w-4"}),n]})}},{accessorKey:"submittedAt",header:"Submitted",cell:({row:e})=>{const n=new Date(e.getValue("submittedAt"));return s.jsx("div",{children:n.toLocaleDateString()})}},{id:"actions",header:"Actions",enableHiding:!1,cell:({row:e})=>s.jsx(ea,{grievanceId:e.original._id})}];function Cc(){const[e,n]=p.useState([]),[t,r]=p.useState([]),[o,a]=p.useState({}),[i,l]=p.useState({}),[d,c]=p.useState(void 0),[u,h]=p.useState(!1),[g,f]=p.useState("all"),[x,w]=p.useState("all"),[b,y]=p.useState("all"),[N,j]=p.useState(""),{isRefreshing:k,startRefresh:S,refreshClasses:C}=Es(),{user:M}=$e(),{grievances:_,myGrievances:F,getGrievances:$,isLoading:z}=Yn(),ae=M?.role==="admin"||M?.role==="officer"?_:F;p.useEffect(()=>{console.log("🔄 GrievancesTable mounted, fetching grievances..."),$()},[$]);const ie=p.useMemo(()=>ae.map(T=>({_id:T._id,id:T._id,referenceId:T.referenceId,title:T.title,category:T.category,status:T.status==="pending"?"submitted":T.status==="in_progress"?"in-review":T.status,priority:T.priority,filesCount:T.attachments?.length||0,submittedAt:T.submittedAt})),[ae]),le=p.useMemo(()=>ie.filter(T=>{const G=N===""||T.title.toLowerCase().includes(N.toLowerCase())||T.referenceId.toLowerCase().includes(N.toLowerCase()),de=g==="all"||T.status===g,be=x==="all"||T.category.toLowerCase()===x.toLowerCase(),re=b==="all"||T.priority===b,Y=!d||new Date(T.submittedAt).toDateString()===d.toDateString();return G&&de&&be&&re&&Y}),[N,g,x,b,d]),ve=()=>{l({}),S(),$()},Re=()=>{j(""),f("all"),w("all"),y("all"),c(void 0),B.getColumn("title")?.setFilterValue("")},B=jc({data:le,columns:Mr,onSortingChange:n,onColumnFiltersChange:r,getCoreRowModel:uc(),getPaginationRowModel:xc(),getSortedRowModel:vc(),getFilteredRowModel:pc(),onColumnVisibilityChange:a,onRowSelectionChange:l,initialState:{pagination:{pageSize:50}},state:{sorting:e,columnFilters:t,columnVisibility:o,rowSelection:i}});return s.jsxs(Ee,{className:`bg-card dark:bg-[#171717] ${C.container} relative`,children:[z&&s.jsx("div",{className:"absolute inset-0 bg-background/50 dark:bg-background/50 flex items-center justify-center z-10 rounded-lg",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Loading grievances..."})]})}),s.jsx(at,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs(Rt,{className:"text-foreground",children:["My Grievances (",le.length,")"]}),s.jsx(O,{variant:"ghost",size:"sm",onClick:ve,disabled:k,children:s.jsx(jo,{className:`h-4 w-4 ${C.icon}`})})]})}),s.jsxs(Le,{children:[s.jsxs("div",{className:"flex flex-wrap items-center gap-4 py-4",children:[s.jsxs("div",{className:"relative w-[250px]",children:[s.jsx(Gr,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),s.jsx(se,{placeholder:"Search by title or reference...",value:N,onChange:T=>j(T.target.value),className:"pl-8 pr-8 bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800 text-foreground dark:text-white placeholder:text-muted-foreground focus:bg-card dark:focus:bg-[#0D0D0D] [&:-webkit-autofill]:bg-card [&:-webkit-autofill]:dark:bg-[#0D0D0D] [&:-webkit-autofill]:text-foreground [&:-webkit-autofill]:dark:text-white"}),N&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>j(""),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(Qe,{value:g,onValueChange:f,children:[s.jsx(Je,{className:"w-[140px] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx(et,{placeholder:"All statuses"})}),s.jsxs(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(U,{value:"all",children:"All statuses"}),s.jsx(U,{value:"submitted",children:"Submitted"}),s.jsx(U,{value:"in-review",children:"In Review"}),s.jsx(U,{value:"resolved",children:"Resolved"}),s.jsx(U,{value:"rejected",children:"Rejected"})]})]}),g!=="all"&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>f("all"),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(Qe,{value:x,onValueChange:w,children:[s.jsx(Je,{className:"w-[140px] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx(et,{placeholder:"All categories"})}),s.jsxs(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(U,{value:"all",children:"All categories"}),s.jsx(U,{value:"infrastructure",children:"Infrastructure"}),s.jsx(U,{value:"sanitation",children:"Sanitation"}),s.jsx(U,{value:"utilities",children:"Utilities"}),s.jsx(U,{value:"recreation",children:"Recreation"}),s.jsx(U,{value:"public safety",children:"Public Safety"}),s.jsx(U,{value:"administration",children:"Administration"}),s.jsx(U,{value:"transportation",children:"Transportation"}),s.jsx(U,{value:"public services",children:"Public Services"})]})]}),x!=="all"&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>w("all"),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(Qe,{value:b,onValueChange:y,children:[s.jsx(Je,{className:"w-[140px] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx(et,{placeholder:"All priorities"})}),s.jsxs(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(U,{value:"all",children:"All priorities"}),s.jsx(U,{value:"urgent",children:"Urgent"}),s.jsx(U,{value:"high",children:"High"}),s.jsx(U,{value:"medium",children:"Medium"}),s.jsx(U,{value:"low",children:"Low"})]})]}),b!=="all"&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>y("all"),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(Ps,{open:u,onOpenChange:h,children:[s.jsx(Fs,{asChild:!0,children:s.jsxs(O,{variant:"outline","data-empty":!d,className:"data-[empty=true]:text-muted-foreground w-[180px] justify-start text-left font-normal bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(Yr,{className:"mr-2 h-4 w-4"}),d?Un(d,"MMM dd, yyyy"):s.jsx("span",{children:"Filter by date"})]})}),s.jsx(Kn,{className:"w-auto p-0 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",align:"start",children:s.jsx(_s,{mode:"single",selected:d,onSelect:T=>{c(T),h(!1)},className:"bg-card dark:bg-[#0D0D0D] text-foreground dark:text-white [&_button]:bg-card [&_button]:dark:bg-[#0D0D0D] [&_button]:text-foreground [&_button]:dark:text-white [&_button:hover]:bg-muted [&_button:hover]:dark:bg-zinc-800"})})]}),d&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>c(void 0),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs(O,{variant:"outline",size:"sm",onClick:Re,className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(No,{className:"mr-2 h-4 w-4"}),"Clear"]})]}),s.jsx("div",{className:`rounded-md border ${C.content}`,children:s.jsxs(Bs,{children:[s.jsx(Hs,{children:B.getHeaderGroups().map(T=>s.jsx(Xt,{children:T.headers.map(G=>s.jsx(Ys,{children:G.isPlaceholder?null:kr(G.column.columnDef.header,G.getContext())},G.id))},T.id))}),s.jsx(Gs,{children:B.getRowModel().rows?.length?B.getRowModel().rows.map(T=>s.jsx(Xt,{"data-state":T.getIsSelected()&&"selected",children:T.getVisibleCells().map(G=>s.jsx(Vn,{children:kr(G.column.columnDef.cell,G.getContext())},G.id))},T.id)):s.jsx(Xt,{children:s.jsx(Vn,{colSpan:Mr.length,className:"h-24 text-center",children:"No results."})})})]})}),s.jsxs("div",{className:"flex items-center justify-end space-x-2 py-4",children:[s.jsxs("div",{className:"text-muted-foreground flex-1 text-sm",children:["Showing"," ",B.getState().pagination.pageIndex*B.getState().pagination.pageSize+1," ","to"," ",Math.min((B.getState().pagination.pageIndex+1)*B.getState().pagination.pageSize,B.getFilteredRowModel().rows.length)," ","of ",B.getFilteredRowModel().rows.length," grievances"]}),s.jsxs("div",{className:"space-x-2",children:[s.jsx(O,{variant:"outline",size:"sm",onClick:()=>B.previousPage(),disabled:!B.getCanPreviousPage(),className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Previous"}),s.jsx(O,{variant:"outline",size:"sm",onClick:()=>B.nextPage(),disabled:!B.getCanNextPage(),className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Next"})]})]})]})]})}function Sc(){const{refreshClasses:e}=Es(),{getGrievances:n,getStats:t,grievances:r,isLoading:o,error:a,stats:i}=Yn();p.useEffect(()=>{(async()=>{try{await Promise.all([n(),t()])}catch(c){console.error("Error loading grievances page data:",c)}})()},[n,t]);const l=[{title:"Total Grievances",value:i?.total?.toString()||"0",change:"+12.5%",trend:"up",icon:Co,color:"text-blue-500"},{title:"Resolved",value:i?.resolved?.toString()||"0",change:"+8.2%",trend:"up",icon:Kt,color:"text-green-500"},{title:"In Progress",value:i?.inProgress?.toString()||"0",change:"+18.8%",trend:"up",icon:In,color:"text-yellow-500"},{title:"Pending",value:i?.pending?.toString()||"0",change:"-15%",trend:"down",icon:So,color:"text-purple-500"}];return o&&r.length===0?s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),s.jsx("p",{className:"text-muted-foreground",children:"Loading grievances..."})]})}):a?s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsxs("p",{className:"text-red-500 mb-4",children:["Error loading grievances: ",a]}),s.jsx("button",{onClick:()=>n(),className:"px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90",children:"Retry"})]})}):s.jsxs(Se.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:l.map((d,c)=>s.jsx(Se.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:c*.1},children:s.jsx(Ee,{className:`bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-lg hover:shadow-xl ${e.container}`,children:s.jsxs(Le,{className:"p-6",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("div",{className:`p-2 rounded-lg bg-opacity-10 ${d.color.replace("text-","bg-")}/10`,children:s.jsx(d.icon,{className:`h-5 w-5 ${d.color}`})}),s.jsxs(ft,{variant:"outline",className:`text-xs ${d.trend==="up"?"bg-green-500/20 text-green-400 border-green-500/30":d.trend==="down"?"bg-red-500/20 text-red-400 border-red-500/30":"bg-yellow-500/20 text-yellow-400 border-yellow-500/30"}`,children:[d.change," ",d.trend==="up"?"↗":d.trend==="down"?"↘":""]})]}),s.jsxs("div",{className:"space-y-1",children:[s.jsx("p",{className:"text-2xl font-bold text-foreground",children:d.value}),s.jsx("p",{className:"text-sm text-muted-foreground",children:d.title})]})]})})},d.title))}),s.jsx(Se.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:s.jsx(Cc,{})})]})}const zu=Object.freeze(Object.defineProperty({__proto__:null,GrievancesPage:Sc},Symbol.toStringTag,{value:"Module"}));function kc(e){const[n,t]=p.useState(e.initialValue||""),r=p.useCallback(o=>{const a=o.target.value;a.length<=e.maxLength&&t(a)},[e.maxLength]);return{value:n,characterCount:n.length,handleChange:r,maxLength:e.maxLength}}function nr(e={}){const{accept:n="*/*",maxSize:t=5*1024*1024,multiple:r=!0,maxFiles:o=5,initialFiles:a=[]}=e,[i,l]=p.useState(a),[d,c]=p.useState(!1),[u,h]=p.useState([]),g=p.useCallback(C=>{const M=Kr(C);return M.isValid?C.size>t?`File ${C.name} is too large. Maximum size is ${ta(t)}`:null:M.error||"File validation failed"},[t]),f=p.useCallback(C=>{const M=C.type.startsWith("image/")?URL.createObjectURL(C):"";return{id:`${C.name}-${Date.now()}`,file:C,preview:M,name:C.name,size:C.size,type:C.type}},[]),x=p.useCallback(C=>{const M=[],_=[];for(const F of C){if(i.length+M.length>=o){_.push(`Maximum ${o} files allowed`);break}const $=g(F);$?_.push($):M.push(f(F))}l(F=>[...F,...M]),h(_)},[i.length,o,g,f]),w=p.useCallback(C=>{l(M=>{const _=M.filter($=>$.id!==C),F=M.find($=>$.id===C);return F?.preview&&URL.revokeObjectURL(F.preview),_})},[]),b=p.useCallback(C=>{C.preventDefault(),c(!0)},[]),y=p.useCallback(C=>{C.preventDefault(),c(!1)},[]),N=p.useCallback(C=>{C.preventDefault()},[]),j=p.useCallback(C=>{C.preventDefault(),c(!1);const M=Array.from(C.dataTransfer.files);x(M)},[x]),k=p.useCallback(()=>{const C=document.createElement("input");C.type="file",C.accept=n,C.multiple=r,C.onchange=M=>{const _=M.target;_.files&&x(Array.from(_.files))},C.click()},[n,r,x]),S=p.useCallback(()=>({type:"file",accept:n,multiple:r,onChange:C=>{C.target.files&&x(Array.from(C.target.files))}}),[n,r,x]);return[{files:i,isDragging:d,errors:u},{handleDragEnter:b,handleDragLeave:y,handleDragOver:N,handleDrop:j,openFileDialog:k,removeFile:w,getInputProps:S,addFiles:x}]}const Ne=p.forwardRef(({className:e,...n},t)=>s.jsx(Qr,{className:E("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...n,ref:t,children:s.jsx(na,{className:E("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));Ne.displayName=Qr.displayName;var kn,Rr;function Mc(){if(Rr)return kn;Rr=1;var e=!1,n,t,r,o,a,i,l,d,c,u,h,g,f,x,w;function b(){if(!e){e=!0;var N=navigator.userAgent,j=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(N),k=/(Mac OS X)|(Windows)|(Linux)/.exec(N);if(g=/\b(iPhone|iP[ao]d)/.exec(N),f=/\b(iP[ao]d)/.exec(N),u=/Android/i.exec(N),x=/FBAN\/\w+;/i.exec(N),w=/Mobile/i.exec(N),h=!!/Win64/.exec(N),j){n=j[1]?parseFloat(j[1]):j[5]?parseFloat(j[5]):NaN,n&&document&&document.documentMode&&(n=document.documentMode);var S=/(?:Trident\/(\d+.\d+))/.exec(N);i=S?parseFloat(S[1])+4:n,t=j[2]?parseFloat(j[2]):NaN,r=j[3]?parseFloat(j[3]):NaN,o=j[4]?parseFloat(j[4]):NaN,o?(j=/(?:Chrome\/(\d+\.\d+))/.exec(N),a=j&&j[1]?parseFloat(j[1]):NaN):a=NaN}else n=t=r=a=o=NaN;if(k){if(k[1]){var C=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(N);l=C?parseFloat(C[1].replace("_",".")):!0}else l=!1;d=!!k[2],c=!!k[3]}else l=d=c=!1}}var y={ie:function(){return b()||n},ieCompatibilityMode:function(){return b()||i>n},ie64:function(){return y.ie()&&h},firefox:function(){return b()||t},opera:function(){return b()||r},webkit:function(){return b()||o},safari:function(){return y.webkit()},chrome:function(){return b()||a},windows:function(){return b()||d},osx:function(){return b()||l},linux:function(){return b()||c},iphone:function(){return b()||g},mobile:function(){return b()||g||f||u||w},nativeApp:function(){return b()||x},android:function(){return b()||u},ipad:function(){return b()||f}};return kn=y,kn}var Mn,_r;function Rc(){if(_r)return Mn;_r=1;var e=!!(typeof window<"u"&&window.document&&window.document.createElement),n={canUseDOM:e,canUseWorkers:typeof Worker<"u",canUseEventListeners:e&&!!(window.addEventListener||window.attachEvent),canUseViewport:e&&!!window.screen,isInWorker:!e};return Mn=n,Mn}var Rn,Pr;function _c(){if(Pr)return Rn;Pr=1;var e=Rc(),n;e.canUseDOM&&(n=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!==!0);/**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */function t(r,o){if(!e.canUseDOM||o&&!("addEventListener"in document))return!1;var a="on"+r,i=a in document;if(!i){var l=document.createElement("div");l.setAttribute(a,"return;"),i=typeof l[a]=="function"}return!i&&n&&r==="wheel"&&(i=document.implementation.hasFeature("Events.wheel","3.0")),i}return Rn=t,Rn}var _n,Fr;function Pc(){if(Fr)return _n;Fr=1;var e=Mc(),n=_c(),t=10,r=40,o=800;function a(i){var l=0,d=0,c=0,u=0;return"detail"in i&&(d=i.detail),"wheelDelta"in i&&(d=-i.wheelDelta/120),"wheelDeltaY"in i&&(d=-i.wheelDeltaY/120),"wheelDeltaX"in i&&(l=-i.wheelDeltaX/120),"axis"in i&&i.axis===i.HORIZONTAL_AXIS&&(l=d,d=0),c=l*t,u=d*t,"deltaY"in i&&(u=i.deltaY),"deltaX"in i&&(c=i.deltaX),(c||u)&&i.deltaMode&&(i.deltaMode==1?(c*=r,u*=r):(c*=o,u*=o)),c&&!l&&(l=c<1?-1:1),u&&!d&&(d=u<1?-1:1),{spinX:l,spinY:d,pixelX:c,pixelY:u}}return a.getEventType=function(){return e.firefox()?"DOMMouseScroll":n("wheel")?"wheel":"mousewheel"},_n=a,_n}var Pn,Er;function Fc(){return Er||(Er=1,Pn=Pc()),Pn}var Ec=Fc();const Ic=ua(Ec);function Oc(e,n,t,r,o,a){a===void 0&&(a=0);var i=xt(e,n,a),l=i.width,d=i.height,c=Math.min(l,t),u=Math.min(d,r);return c>u*o?{width:u*o,height:u}:{width:c,height:c/o}}function Tc(e){return e.width>e.height?e.width/e.naturalWidth:e.height/e.naturalHeight}function St(e,n,t,r,o){o===void 0&&(o=0);var a=xt(n.width,n.height,o),i=a.width,l=a.height;return{x:Ir(e.x,i,t.width,r),y:Ir(e.y,l,t.height,r)}}function Ir(e,n,t,r){var o=n*r/2-t/2;return tn(e,-o,o)}function Or(e,n){return Math.sqrt(Math.pow(e.y-n.y,2)+Math.pow(e.x-n.x,2))}function Tr(e,n){return Math.atan2(n.y-e.y,n.x-e.x)*180/Math.PI}function Ac(e,n,t,r,o,a,i){a===void 0&&(a=0),i===void 0&&(i=!0);var l=i?Wc:$c,d=xt(n.width,n.height,a),c=xt(n.naturalWidth,n.naturalHeight,a),u={x:l(100,((d.width-t.width/o)/2-e.x/o)/d.width*100),y:l(100,((d.height-t.height/o)/2-e.y/o)/d.height*100),width:l(100,t.width/d.width*100/o),height:l(100,t.height/d.height*100/o)},h=Math.round(l(c.width,u.width*c.width/100)),g=Math.round(l(c.height,u.height*c.height/100)),f=c.width>=c.height*r,x=f?{width:Math.round(g*r),height:g}:{width:h,height:Math.round(h/r)},w=he(he({},x),{x:Math.round(l(c.width-x.width,u.x*c.width/100)),y:Math.round(l(c.height-x.height,u.y*c.height/100))});return{croppedAreaPercentages:u,croppedAreaPixels:w}}function Wc(e,n){return Math.min(e,Math.max(0,n))}function $c(e,n){return n}function zc(e,n,t,r,o,a){var i=xt(n.width,n.height,t),l=tn(r.width/i.width*(100/e.width),o,a),d={x:l*i.width/2-r.width/2-i.width*l*(e.x/100),y:l*i.height/2-r.height/2-i.height*l*(e.y/100)};return{crop:d,zoom:l}}function Lc(e,n,t){var r=Tc(n);return t.height>t.width?t.height/(e.height*r):t.width/(e.width*r)}function Vc(e,n,t,r,o,a){t===void 0&&(t=0);var i=xt(n.naturalWidth,n.naturalHeight,t),l=tn(Lc(e,n,r),o,a),d=r.height>r.width?r.height/e.height:r.width/e.width,c={x:((i.width-e.width)/2-e.x)*d,y:((i.height-e.height)/2-e.y)*d};return{crop:c,zoom:l}}function Ar(e,n){return{x:(n.x+e.x)/2,y:(n.y+e.y)/2}}function Bc(e){return e*Math.PI/180}function xt(e,n,t){var r=Bc(t);return{width:Math.abs(Math.cos(r)*e)+Math.abs(Math.sin(r)*n),height:Math.abs(Math.sin(r)*e)+Math.abs(Math.cos(r)*n)}}function tn(e,n,t){return Math.min(Math.max(e,n),t)}function qt(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return e.filter(function(t){return typeof t=="string"&&t.length>0}).join(" ").trim()}var Hc=`.reactEasyCrop_Container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  user-select: none;
  touch-action: none;
  cursor: move;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reactEasyCrop_Image,
.reactEasyCrop_Video {
  will-change: transform; /* this improves performances and prevent painting issues on iOS Chrome */
}

.reactEasyCrop_Contain {
  max-width: 100%;
  max-height: 100%;
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.reactEasyCrop_Cover_Horizontal {
  width: 100%;
  height: auto;
}
.reactEasyCrop_Cover_Vertical {
  width: auto;
  height: 100%;
}

.reactEasyCrop_CropArea {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-sizing: border-box;
  box-shadow: 0 0 0 9999em;
  color: rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.reactEasyCrop_CropAreaRound {
  border-radius: 50%;
}

.reactEasyCrop_CropAreaGrid::before {
  content: ' ';
  box-sizing: border-box;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.5);
  top: 0;
  bottom: 0;
  left: 33.33%;
  right: 33.33%;
  border-top: 0;
  border-bottom: 0;
}

.reactEasyCrop_CropAreaGrid::after {
  content: ' ';
  box-sizing: border-box;
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.5);
  top: 33.33%;
  bottom: 33.33%;
  left: 0;
  right: 0;
  border-left: 0;
  border-right: 0;
}
`,Gc=1,Yc=3,qc=1,Uc=function(e){ra(n,e);function n(){var t=e!==null&&e.apply(this,arguments)||this;return t.cropperRef=p.createRef(),t.imageRef=p.createRef(),t.videoRef=p.createRef(),t.containerPosition={x:0,y:0},t.containerRef=null,t.styleRef=null,t.containerRect=null,t.mediaSize={width:0,height:0,naturalWidth:0,naturalHeight:0},t.dragStartPosition={x:0,y:0},t.dragStartCrop={x:0,y:0},t.gestureZoomStart=0,t.gestureRotationStart=0,t.isTouching=!1,t.lastPinchDistance=0,t.lastPinchRotation=0,t.rafDragTimeout=null,t.rafPinchTimeout=null,t.wheelTimer=null,t.currentDoc=typeof document<"u"?document:null,t.currentWindow=typeof window<"u"?window:null,t.resizeObserver=null,t.state={cropSize:null,hasWheelJustStarted:!1,mediaObjectFit:void 0},t.initResizeObserver=function(){if(!(typeof window.ResizeObserver>"u"||!t.containerRef)){var r=!0;t.resizeObserver=new window.ResizeObserver(function(o){if(r){r=!1;return}t.computeSizes()}),t.resizeObserver.observe(t.containerRef)}},t.preventZoomSafari=function(r){return r.preventDefault()},t.cleanEvents=function(){t.currentDoc&&(t.currentDoc.removeEventListener("mousemove",t.onMouseMove),t.currentDoc.removeEventListener("mouseup",t.onDragStopped),t.currentDoc.removeEventListener("touchmove",t.onTouchMove),t.currentDoc.removeEventListener("touchend",t.onDragStopped),t.currentDoc.removeEventListener("gesturechange",t.onGestureChange),t.currentDoc.removeEventListener("gestureend",t.onGestureEnd),t.currentDoc.removeEventListener("scroll",t.onScroll))},t.clearScrollEvent=function(){t.containerRef&&t.containerRef.removeEventListener("wheel",t.onWheel),t.wheelTimer&&clearTimeout(t.wheelTimer)},t.onMediaLoad=function(){var r=t.computeSizes();r&&(t.emitCropData(),t.setInitialCrop(r)),t.props.onMediaLoaded&&t.props.onMediaLoaded(t.mediaSize)},t.setInitialCrop=function(r){if(t.props.initialCroppedAreaPercentages){var o=zc(t.props.initialCroppedAreaPercentages,t.mediaSize,t.props.rotation,r,t.props.minZoom,t.props.maxZoom),a=o.crop,i=o.zoom;t.props.onCropChange(a),t.props.onZoomChange&&t.props.onZoomChange(i)}else if(t.props.initialCroppedAreaPixels){var l=Vc(t.props.initialCroppedAreaPixels,t.mediaSize,t.props.rotation,r,t.props.minZoom,t.props.maxZoom),a=l.crop,i=l.zoom;t.props.onCropChange(a),t.props.onZoomChange&&t.props.onZoomChange(i)}},t.computeSizes=function(){var r,o,a,i,l,d,c=t.imageRef.current||t.videoRef.current;if(c&&t.containerRef){t.containerRect=t.containerRef.getBoundingClientRect(),t.saveContainerPosition();var u=t.containerRect.width/t.containerRect.height,h=((r=t.imageRef.current)===null||r===void 0?void 0:r.naturalWidth)||((o=t.videoRef.current)===null||o===void 0?void 0:o.videoWidth)||0,g=((a=t.imageRef.current)===null||a===void 0?void 0:a.naturalHeight)||((i=t.videoRef.current)===null||i===void 0?void 0:i.videoHeight)||0,f=c.offsetWidth<h||c.offsetHeight<g,x=h/g,w=void 0;if(f)switch(t.state.mediaObjectFit){default:case"contain":w=u>x?{width:t.containerRect.height*x,height:t.containerRect.height}:{width:t.containerRect.width,height:t.containerRect.width/x};break;case"horizontal-cover":w={width:t.containerRect.width,height:t.containerRect.width/x};break;case"vertical-cover":w={width:t.containerRect.height*x,height:t.containerRect.height};break}else w={width:c.offsetWidth,height:c.offsetHeight};t.mediaSize=he(he({},w),{naturalWidth:h,naturalHeight:g}),t.props.setMediaSize&&t.props.setMediaSize(t.mediaSize);var b=t.props.cropSize?t.props.cropSize:Oc(t.mediaSize.width,t.mediaSize.height,t.containerRect.width,t.containerRect.height,t.props.aspect,t.props.rotation);return(((l=t.state.cropSize)===null||l===void 0?void 0:l.height)!==b.height||((d=t.state.cropSize)===null||d===void 0?void 0:d.width)!==b.width)&&t.props.onCropSizeChange&&t.props.onCropSizeChange(b),t.setState({cropSize:b},t.recomputeCropPosition),t.props.setCropSize&&t.props.setCropSize(b),b}},t.saveContainerPosition=function(){if(t.containerRef){var r=t.containerRef.getBoundingClientRect();t.containerPosition={x:r.left,y:r.top}}},t.onMouseDown=function(r){t.currentDoc&&(r.preventDefault(),t.currentDoc.addEventListener("mousemove",t.onMouseMove),t.currentDoc.addEventListener("mouseup",t.onDragStopped),t.saveContainerPosition(),t.onDragStart(n.getMousePoint(r)))},t.onMouseMove=function(r){return t.onDrag(n.getMousePoint(r))},t.onScroll=function(r){t.currentDoc&&(r.preventDefault(),t.saveContainerPosition())},t.onTouchStart=function(r){t.currentDoc&&(t.isTouching=!0,!(t.props.onTouchRequest&&!t.props.onTouchRequest(r))&&(t.currentDoc.addEventListener("touchmove",t.onTouchMove,{passive:!1}),t.currentDoc.addEventListener("touchend",t.onDragStopped),t.saveContainerPosition(),r.touches.length===2?t.onPinchStart(r):r.touches.length===1&&t.onDragStart(n.getTouchPoint(r.touches[0]))))},t.onTouchMove=function(r){r.preventDefault(),r.touches.length===2?t.onPinchMove(r):r.touches.length===1&&t.onDrag(n.getTouchPoint(r.touches[0]))},t.onGestureStart=function(r){t.currentDoc&&(r.preventDefault(),t.currentDoc.addEventListener("gesturechange",t.onGestureChange),t.currentDoc.addEventListener("gestureend",t.onGestureEnd),t.gestureZoomStart=t.props.zoom,t.gestureRotationStart=t.props.rotation)},t.onGestureChange=function(r){if(r.preventDefault(),!t.isTouching){var o=n.getMousePoint(r),a=t.gestureZoomStart-1+r.scale;if(t.setNewZoom(a,o,{shouldUpdatePosition:!0}),t.props.onRotationChange){var i=t.gestureRotationStart+r.rotation;t.props.onRotationChange(i)}}},t.onGestureEnd=function(r){t.cleanEvents()},t.onDragStart=function(r){var o,a,i=r.x,l=r.y;t.dragStartPosition={x:i,y:l},t.dragStartCrop=he({},t.props.crop),(a=(o=t.props).onInteractionStart)===null||a===void 0||a.call(o)},t.onDrag=function(r){var o=r.x,a=r.y;t.currentWindow&&(t.rafDragTimeout&&t.currentWindow.cancelAnimationFrame(t.rafDragTimeout),t.rafDragTimeout=t.currentWindow.requestAnimationFrame(function(){if(t.state.cropSize&&!(o===void 0||a===void 0)){var i=o-t.dragStartPosition.x,l=a-t.dragStartPosition.y,d={x:t.dragStartCrop.x+i,y:t.dragStartCrop.y+l},c=t.props.restrictPosition?St(d,t.mediaSize,t.state.cropSize,t.props.zoom,t.props.rotation):d;t.props.onCropChange(c)}}))},t.onDragStopped=function(){var r,o;t.isTouching=!1,t.cleanEvents(),t.emitCropData(),(o=(r=t.props).onInteractionEnd)===null||o===void 0||o.call(r)},t.onWheel=function(r){if(t.currentWindow&&!(t.props.onWheelRequest&&!t.props.onWheelRequest(r))){r.preventDefault();var o=n.getMousePoint(r),a=Ic(r).pixelY,i=t.props.zoom-a*t.props.zoomSpeed/200;t.setNewZoom(i,o,{shouldUpdatePosition:!0}),t.state.hasWheelJustStarted||t.setState({hasWheelJustStarted:!0},function(){var l,d;return(d=(l=t.props).onInteractionStart)===null||d===void 0?void 0:d.call(l)}),t.wheelTimer&&clearTimeout(t.wheelTimer),t.wheelTimer=t.currentWindow.setTimeout(function(){return t.setState({hasWheelJustStarted:!1},function(){var l,d;return(d=(l=t.props).onInteractionEnd)===null||d===void 0?void 0:d.call(l)})},250)}},t.getPointOnContainer=function(r,o){var a=r.x,i=r.y;if(!t.containerRect)throw new Error("The Cropper is not mounted");return{x:t.containerRect.width/2-(a-o.x),y:t.containerRect.height/2-(i-o.y)}},t.getPointOnMedia=function(r){var o=r.x,a=r.y,i=t.props,l=i.crop,d=i.zoom;return{x:(o+l.x)/d,y:(a+l.y)/d}},t.setNewZoom=function(r,o,a){var i=a===void 0?{}:a,l=i.shouldUpdatePosition,d=l===void 0?!0:l;if(!(!t.state.cropSize||!t.props.onZoomChange)){var c=tn(r,t.props.minZoom,t.props.maxZoom);if(d){var u=t.getPointOnContainer(o,t.containerPosition),h=t.getPointOnMedia(u),g={x:h.x*c-u.x,y:h.y*c-u.y},f=t.props.restrictPosition?St(g,t.mediaSize,t.state.cropSize,c,t.props.rotation):g;t.props.onCropChange(f)}t.props.onZoomChange(c)}},t.getCropData=function(){if(!t.state.cropSize)return null;var r=t.props.restrictPosition?St(t.props.crop,t.mediaSize,t.state.cropSize,t.props.zoom,t.props.rotation):t.props.crop;return Ac(r,t.mediaSize,t.state.cropSize,t.getAspect(),t.props.zoom,t.props.rotation,t.props.restrictPosition)},t.emitCropData=function(){var r=t.getCropData();if(r){var o=r.croppedAreaPercentages,a=r.croppedAreaPixels;t.props.onCropComplete&&t.props.onCropComplete(o,a),t.props.onCropAreaChange&&t.props.onCropAreaChange(o,a)}},t.emitCropAreaChange=function(){var r=t.getCropData();if(r){var o=r.croppedAreaPercentages,a=r.croppedAreaPixels;t.props.onCropAreaChange&&t.props.onCropAreaChange(o,a)}},t.recomputeCropPosition=function(){if(t.state.cropSize){var r=t.props.restrictPosition?St(t.props.crop,t.mediaSize,t.state.cropSize,t.props.zoom,t.props.rotation):t.props.crop;t.props.onCropChange(r),t.emitCropData()}},t.onKeyDown=function(r){var o,a,i=t.props,l=i.crop,d=i.onCropChange,c=i.keyboardStep,u=i.zoom,h=i.rotation,g=c;if(t.state.cropSize){r.shiftKey&&(g*=.2);var f=he({},l);switch(r.key){case"ArrowUp":f.y-=g,r.preventDefault();break;case"ArrowDown":f.y+=g,r.preventDefault();break;case"ArrowLeft":f.x-=g,r.preventDefault();break;case"ArrowRight":f.x+=g,r.preventDefault();break;default:return}t.props.restrictPosition&&(f=St(f,t.mediaSize,t.state.cropSize,u,h)),r.repeat||(a=(o=t.props).onInteractionStart)===null||a===void 0||a.call(o),d(f)}},t.onKeyUp=function(r){var o,a;switch(r.key){case"ArrowUp":case"ArrowDown":case"ArrowLeft":case"ArrowRight":r.preventDefault();break;default:return}t.emitCropData(),(a=(o=t.props).onInteractionEnd)===null||a===void 0||a.call(o)},t}return n.prototype.componentDidMount=function(){!this.currentDoc||!this.currentWindow||(this.containerRef&&(this.containerRef.ownerDocument&&(this.currentDoc=this.containerRef.ownerDocument),this.currentDoc.defaultView&&(this.currentWindow=this.currentDoc.defaultView),this.initResizeObserver(),typeof window.ResizeObserver>"u"&&this.currentWindow.addEventListener("resize",this.computeSizes),this.props.zoomWithScroll&&this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}),this.containerRef.addEventListener("gesturestart",this.onGestureStart)),this.currentDoc.addEventListener("scroll",this.onScroll),this.props.disableAutomaticStylesInjection||(this.styleRef=this.currentDoc.createElement("style"),this.styleRef.setAttribute("type","text/css"),this.props.nonce&&this.styleRef.setAttribute("nonce",this.props.nonce),this.styleRef.innerHTML=Hc,this.currentDoc.head.appendChild(this.styleRef)),this.imageRef.current&&this.imageRef.current.complete&&this.onMediaLoad(),this.props.setImageRef&&this.props.setImageRef(this.imageRef),this.props.setVideoRef&&this.props.setVideoRef(this.videoRef),this.props.setCropperRef&&this.props.setCropperRef(this.cropperRef))},n.prototype.componentWillUnmount=function(){var t,r;!this.currentDoc||!this.currentWindow||(typeof window.ResizeObserver>"u"&&this.currentWindow.removeEventListener("resize",this.computeSizes),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),this.containerRef&&this.containerRef.removeEventListener("gesturestart",this.preventZoomSafari),this.styleRef&&((r=this.styleRef.parentNode)===null||r===void 0||r.removeChild(this.styleRef)),this.cleanEvents(),this.props.zoomWithScroll&&this.clearScrollEvent())},n.prototype.componentDidUpdate=function(t){var r,o,a,i,l,d,c,u,h;t.rotation!==this.props.rotation?(this.computeSizes(),this.recomputeCropPosition()):t.aspect!==this.props.aspect?this.computeSizes():t.objectFit!==this.props.objectFit?this.computeSizes():t.zoom!==this.props.zoom?this.recomputeCropPosition():((r=t.cropSize)===null||r===void 0?void 0:r.height)!==((o=this.props.cropSize)===null||o===void 0?void 0:o.height)||((a=t.cropSize)===null||a===void 0?void 0:a.width)!==((i=this.props.cropSize)===null||i===void 0?void 0:i.width)?this.computeSizes():(((l=t.crop)===null||l===void 0?void 0:l.x)!==((d=this.props.crop)===null||d===void 0?void 0:d.x)||((c=t.crop)===null||c===void 0?void 0:c.y)!==((u=this.props.crop)===null||u===void 0?void 0:u.y))&&this.emitCropAreaChange(),t.zoomWithScroll!==this.props.zoomWithScroll&&this.containerRef&&(this.props.zoomWithScroll?this.containerRef.addEventListener("wheel",this.onWheel,{passive:!1}):this.clearScrollEvent()),t.video!==this.props.video&&((h=this.videoRef.current)===null||h===void 0||h.load());var g=this.getObjectFit();g!==this.state.mediaObjectFit&&this.setState({mediaObjectFit:g},this.computeSizes)},n.prototype.getAspect=function(){var t=this.props,r=t.cropSize,o=t.aspect;return r?r.width/r.height:o},n.prototype.getObjectFit=function(){var t,r,o,a;if(this.props.objectFit==="cover"){var i=this.imageRef.current||this.videoRef.current;if(i&&this.containerRef){this.containerRect=this.containerRef.getBoundingClientRect();var l=this.containerRect.width/this.containerRect.height,d=((t=this.imageRef.current)===null||t===void 0?void 0:t.naturalWidth)||((r=this.videoRef.current)===null||r===void 0?void 0:r.videoWidth)||0,c=((o=this.imageRef.current)===null||o===void 0?void 0:o.naturalHeight)||((a=this.videoRef.current)===null||a===void 0?void 0:a.videoHeight)||0,u=d/c;return u<l?"horizontal-cover":"vertical-cover"}return"horizontal-cover"}return this.props.objectFit},n.prototype.onPinchStart=function(t){var r=n.getTouchPoint(t.touches[0]),o=n.getTouchPoint(t.touches[1]);this.lastPinchDistance=Or(r,o),this.lastPinchRotation=Tr(r,o),this.onDragStart(Ar(r,o))},n.prototype.onPinchMove=function(t){var r=this;if(!(!this.currentDoc||!this.currentWindow)){var o=n.getTouchPoint(t.touches[0]),a=n.getTouchPoint(t.touches[1]),i=Ar(o,a);this.onDrag(i),this.rafPinchTimeout&&this.currentWindow.cancelAnimationFrame(this.rafPinchTimeout),this.rafPinchTimeout=this.currentWindow.requestAnimationFrame(function(){var l=Or(o,a),d=r.props.zoom*(l/r.lastPinchDistance);r.setNewZoom(d,i,{shouldUpdatePosition:!1}),r.lastPinchDistance=l;var c=Tr(o,a),u=r.props.rotation+(c-r.lastPinchRotation);r.props.onRotationChange&&r.props.onRotationChange(u),r.lastPinchRotation=c})}},n.prototype.render=function(){var t=this,r,o=this.props,a=o.image,i=o.video,l=o.mediaProps,d=o.cropperProps,c=o.transform,u=o.crop,h=u.x,g=u.y,f=o.rotation,x=o.zoom,w=o.cropShape,b=o.showGrid,y=o.roundCropAreaPixels,N=o.style,j=N.containerStyle,k=N.cropAreaStyle,S=N.mediaStyle,C=o.classes,M=C.containerClassName,_=C.cropAreaClassName,F=C.mediaClassName,$=(r=this.state.mediaObjectFit)!==null&&r!==void 0?r:this.getObjectFit();return p.createElement("div",{onMouseDown:this.onMouseDown,onTouchStart:this.onTouchStart,ref:function(H){return t.containerRef=H},"data-testid":"container",style:j,className:qt("reactEasyCrop_Container",M)},a?p.createElement("img",he({alt:"",className:qt("reactEasyCrop_Image",$==="contain"&&"reactEasyCrop_Contain",$==="horizontal-cover"&&"reactEasyCrop_Cover_Horizontal",$==="vertical-cover"&&"reactEasyCrop_Cover_Vertical",F)},l,{src:a,ref:this.imageRef,style:he(he({},S),{transform:c||"translate(".concat(h,"px, ").concat(g,"px) rotate(").concat(f,"deg) scale(").concat(x,")")}),onLoad:this.onMediaLoad})):i&&p.createElement("video",he({autoPlay:!0,playsInline:!0,loop:!0,muted:!0,className:qt("reactEasyCrop_Video",$==="contain"&&"reactEasyCrop_Contain",$==="horizontal-cover"&&"reactEasyCrop_Cover_Horizontal",$==="vertical-cover"&&"reactEasyCrop_Cover_Vertical",F)},l,{ref:this.videoRef,onLoadedMetadata:this.onMediaLoad,style:he(he({},S),{transform:c||"translate(".concat(h,"px, ").concat(g,"px) rotate(").concat(f,"deg) scale(").concat(x,")")}),controls:!1}),(Array.isArray(i)?i:[{src:i}]).map(function(z){return p.createElement("source",he({key:z.src},z))})),this.state.cropSize&&p.createElement("div",he({ref:this.cropperRef,style:he(he({},k),{width:y?Math.round(this.state.cropSize.width):this.state.cropSize.width,height:y?Math.round(this.state.cropSize.height):this.state.cropSize.height}),tabIndex:0,onKeyDown:this.onKeyDown,onKeyUp:this.onKeyUp,"data-testid":"cropper",className:qt("reactEasyCrop_CropArea",w==="round"&&"reactEasyCrop_CropAreaRound",b&&"reactEasyCrop_CropAreaGrid",_)},d)))},n.defaultProps={zoom:1,rotation:0,aspect:4/3,maxZoom:Yc,minZoom:Gc,cropShape:"rect",objectFit:"contain",showGrid:!0,style:{},classes:{},mediaProps:{},cropperProps:{},zoomSpeed:1,restrictPosition:!0,zoomWithScroll:!0,keyboardStep:qc},n.getMousePoint=function(t){return{x:Number(t.clientX),y:Number(t.clientY)}},n.getTouchPoint=function(t){return{x:Number(t.clientX),y:Number(t.clientY)}},n}(p.Component);const qs=({image:e,aspectRatio:n=1,className:t,onCropComplete:r})=>{const[o,a]=p.useState({x:0,y:0}),[i,l]=p.useState(1),d=p.useCallback((c,u)=>{r?.(c,u)},[r]);return s.jsx("div",{className:E("relative w-full h-full",t),children:s.jsx(Uc,{image:e,crop:o,zoom:i,aspect:n,onCropChange:a,onZoomChange:l,onCropComplete:d,cropShape:n===1?"round":"rect"})})},Us=({children:e})=>s.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:e||"Drag to reposition and resize the crop area"}),Zs=async(e,n,t=0)=>{const r=await Zc(e),o=document.createElement("canvas"),a=o.getContext("2d");return o.width=n.width,o.height=n.height,a.drawImage(r,n.x,n.y,n.width,n.height,0,0,n.width,n.height),o.toDataURL("image/jpeg")};function Zc(e){return new Promise((n,t)=>{const r=new Image;r.addEventListener("load",()=>n(r)),r.addEventListener("error",o=>t(o)),r.setAttribute("crossOrigin","anonymous"),r.src=e})}const Xc=[],Kc=[];function Qc({isEditMode:e}){const{user:n}=$e(),[{files:t},{removeFile:r,openFileDialog:o,getInputProps:a}]=nr({accept:"image/*",initialFiles:Xc}),[i,l]=p.useState(!1),[d,c]=p.useState(null),[u,h]=p.useState(null),[g,f]=p.useState(null),x=t[0]?.preview||null,w=n?.coverPhotoUrl||null,b=u||x||w;R.useEffect(()=>{t.length>0&&t[0]?.preview&&(c(t[0].preview),l(!0))},[t]);const y=(k,S)=>{f(S)},N=async()=>{if(!(!d||!g))try{const k=await Zs(d,g);h(k),l(!1),await j(k)}catch(k){console.error("Error cropping image:",k)}},j=async k=>{try{const C=await(await fetch(k)).blob(),M=new FormData;M.append("coverPhoto",C,"cover-photo.jpg");const _=await ht.post("/uploads/cover-photo",M,{headers:{"Content-Type":"multipart/form-data"}});_.data.success&&($e.getState().setUser(_.data.data.user),console.log("Cover photo uploaded successfully:",_.data.data.coverPhotoUrl))}catch(S){console.error("Error uploading cover photo:",S)}};return s.jsxs("div",{className:"h-32 relative",children:[s.jsx("div",{className:"bg-muted dark:bg-[#0D0D0D] relative flex size-full items-center justify-center overflow-hidden",children:b?s.jsx("img",{className:"size-full object-cover",src:b,alt:"Profile background",width:512,height:96}):s.jsxs("button",{type:"button",className:"flex flex-col items-center justify-center text-muted-foreground hover:text-foreground transition-colors",onClick:e?o:void 0,disabled:!e,children:[s.jsx(Ur,{size:24,className:"mb-2"}),s.jsx("span",{className:"text-sm",children:e?"Add Cover Photo":"Cover Photo"})]})}),b&&e&&s.jsxs("div",{className:"absolute top-2 right-2 flex gap-2",children:[s.jsx("button",{type:"button",className:"h-8 px-3 rounded-md bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border transition-colors flex items-center justify-center text-xs",onClick:()=>l(!0),"aria-label":"Re-crop background image",children:"Re-crop"}),s.jsx("button",{type:"button",className:"h-8 w-8 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border transition-colors flex items-center justify-center",onClick:async()=>{if(r(t[0]?.id),h(null),n?.coverPhoto)try{const k=await ht.delete("/uploads/cover-photo");k.data.success&&($e.getState().setUser(k.data.data.user),console.log("Cover photo deleted successfully"))}catch(k){console.error("Error deleting cover photo:",k)}},"aria-label":"Remove background image",children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsx("input",{...a(),className:"sr-only","aria-label":"Upload background image"}),s.jsx(lt,{open:i,onOpenChange:l,children:s.jsxs(dt,{className:"max-w-2xl bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsxs(ct,{children:[s.jsx(ut,{children:"Crop Cover Photo"}),s.jsx(Us,{})]}),s.jsxs("div",{className:"flex flex-col items-center gap-4 py-4",children:[d&&s.jsx("div",{className:"h-80 w-full relative",children:s.jsx(qs,{image:d,aspectRatio:16/9,className:"h-full w-full",onCropComplete:y})}),s.jsxs("div",{className:"flex gap-3",children:[s.jsx(O,{variant:"outline",onClick:()=>l(!1),className:"bg-muted dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Cancel"}),s.jsx(O,{onClick:N,children:"Apply Crop"})]})]})]})})]})}function Jc({isEditMode:e}){const{user:n}=$e(),[{files:t},{removeFile:r,openFileDialog:o,getInputProps:a}]=nr({accept:"image/*",initialFiles:Kc}),[i,l]=p.useState(!1),[d,c]=p.useState(null),[u,h]=p.useState(null),[g,f]=p.useState(null),x=t[0]?.preview||null,w=n?.profilePhotoUrl||null,b=u||x||w;R.useEffect(()=>{t.length>0&&t[0]?.preview&&(c(t[0].preview),l(!0))},[t]);const y=(k,S)=>{f(S)},N=async()=>{if(!(!d||!g))try{const k=await Zs(d,g);h(k),l(!1),await j(k)}catch(k){console.error("Error cropping image:",k)}},j=async k=>{try{const C=await(await fetch(k)).blob(),M=new FormData;M.append("profilePhoto",C,"profile-photo.jpg");const _=await ht.post("/uploads/profile-photo",M,{headers:{"Content-Type":"multipart/form-data"}});_.data.success&&($e.getState().setUser(_.data.data.user),console.log("Profile photo uploaded successfully:",_.data.data.profilePhotoUrl))}catch(S){console.error("Error uploading profile photo:",S)}};return s.jsxs("div",{className:"-mt-10 px-6 relative",children:[s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsxs("div",{className:"border-background dark:border-[#0D0D0D] bg-muted dark:bg-[#0D0D0D] relative flex size-20 items-center justify-center overflow-hidden rounded-full border-4 shadow-xs shadow-black/10",children:[b?s.jsx("img",{src:b,className:"size-full object-cover",width:80,height:80,alt:"Profile image"}):s.jsxs("button",{type:"button",className:"flex flex-col items-center justify-center text-muted-foreground hover:text-foreground transition-colors size-full",onClick:e?o:void 0,disabled:!e,children:[s.jsx(Ur,{size:20,className:"mb-1"}),s.jsx("span",{className:"text-xs",children:e?"Add Photo":"Photo"})]}),s.jsx("input",{...a(),className:"sr-only","aria-label":"Upload profile picture"})]}),b&&e&&s.jsxs("div",{className:"flex gap-2 mt-2",children:[s.jsx(O,{variant:"outline",size:"sm",className:"text-xs bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border",onClick:()=>l(!0),children:"Re-crop"}),s.jsx("button",{type:"button",className:"px-3 py-1 text-xs bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border rounded-md transition-colors",onClick:async()=>{if(r(t[0]?.id),h(null),n?.profilePhoto)try{const k=await ht.delete("/uploads/profile-photo");k.data.success&&($e.getState().setUser(k.data.data.user),console.log("Profile photo deleted successfully"))}catch(k){console.error("Error deleting profile photo:",k)}},"aria-label":"Remove profile picture",children:"Remove Photo"})]})]}),s.jsx(lt,{open:i,onOpenChange:l,children:s.jsxs(dt,{className:"max-w-md bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsxs(ct,{children:[s.jsx(ut,{children:"Crop Profile Picture"}),s.jsx(Us,{})]}),s.jsxs("div",{className:"flex flex-col items-center gap-4 py-4",children:[d&&s.jsx("div",{className:"h-80 w-full relative",children:s.jsx(qs,{image:d,aspectRatio:1,className:"h-full w-full",onCropComplete:y})}),s.jsxs("div",{className:"flex gap-3",children:[s.jsx(O,{variant:"outline",onClick:()=>l(!1),className:"bg-muted dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Cancel"}),s.jsx(O,{onClick:N,children:"Apply Crop"})]})]})]})})]})}function eu(){const e=p.useId(),{user:n}=$e(),[t,r]=p.useState(!1),[o,a]=p.useState(!1),[i,l]=p.useState(""),[d,c]=p.useState(""),[u,h]=p.useState(""),[g,f]=p.useState(""),[x,w]=p.useState(""),[b,y]=p.useState(!1),[N,j]=p.useState(""),[k,S]=p.useState(!1),[C,M]=p.useState(""),[_,F]=p.useState(""),[$,z]=p.useState(""),[H,ae]=p.useState(!1),[ie,le]=p.useState(!1),[ve,Re]=p.useState(!1),[B,T]=p.useState(!1),[G,de]=p.useState(""),be=()=>{a(!0),j(""),y(!1)},re=()=>{if(a(!1),j(""),y(!1),n){const m=n.fullName?.split(" ")||["",""];l(m[0]||""),c(m.slice(1).join(" ")||""),h(n.username||""),f(n.mobile||""),w(n.address||"")}};kc({maxLength:180,initialValue:"Hey, I am John, a civic engagement enthusiast who loves helping communities solve problems!"}),p.useEffect(()=>{if(n){const m=n.fullName?.split(" ")||["",""];l(m[0]||""),c(m.slice(1).join(" ")||""),h(n.username||""),f(n.mobile||""),w(n.address||"")}},[n]);const we=async()=>{r(!0),j(""),y(!1);try{if(g&&!/^[0-9]{10}$/.test(g))throw new Error("Mobile number must be exactly 10 digits");const m={fullName:`${i.trim()} ${d.trim()}`.trim(),username:u.trim(),mobile:g.trim(),address:x.trim()},v=await ht.patch("/auth/update-me",m);v.data.success&&(y(!0),$e.getState().setUser(v.data.data),a(!1),setTimeout(()=>y(!1),3e3))}catch(m){j(m.response?.data?.message||m.message||"Failed to update profile")}finally{r(!1)}},D=async()=>{S(!0),de(""),T(!1);try{if(!C)throw new Error("Current password is required");if(!_||_.length<6)throw new Error("New password must be at least 6 characters long");if(_!==$)throw new Error("New passwords do not match");(await ht.patch("/auth/update-password",{currentPassword:C,newPassword:_})).data.success&&(T(!0),M(""),F(""),z(""),setTimeout(()=>T(!1),3e3))}catch(m){de(m.response?.data?.message||m.message||"Failed to change password")}finally{S(!1)}};return s.jsxs(sa,{defaultValue:"account",className:"w-full max-w-4xl mx-auto bg-background dark:bg-[#0D0D0D] p-6",children:[s.jsxs(oa,{className:"grid w-full grid-cols-5 rounded-none border-b bg-transparent p-0 mb-8",children:[s.jsxs(yt,{value:"account",className:"flex items-center gap-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent",children:[s.jsx(ko,{className:"h-4 w-4"}),"Account"]}),s.jsxs(yt,{value:"mydata",className:"flex items-center gap-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent",children:[s.jsx(Mt,{className:"h-4 w-4"}),"My Data"]}),s.jsxs(yt,{value:"security",className:"flex items-center gap-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent",children:[s.jsx(Ze,{className:"h-4 w-4"}),"Security"]}),s.jsxs(yt,{value:"privacy",className:"flex items-center gap-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent",children:[s.jsx(bt,{className:"h-4 w-4"}),"Privacy"]}),s.jsxs(yt,{value:"preferences",className:"flex items-center gap-2 rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent",children:[s.jsx(qr,{className:"h-4 w-4"}),"Preferences"]})]}),s.jsx(jt,{value:"account",className:"mt-0",children:s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg overflow-hidden",children:[s.jsx(Qc,{isEditMode:o}),s.jsx(Jc,{isEditMode:o}),s.jsxs("div",{className:"px-6 pt-4 pb-6",children:[b&&s.jsx(Bt,{type:"success",message:"Profile updated successfully!",className:"mb-4"}),N&&s.jsx(Bt,{type:"error",message:N,className:"mb-4"}),s.jsxs("form",{className:"space-y-4",onSubmit:m=>{m.preventDefault(),we()},children:[s.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[s.jsxs("div",{className:"flex-1 space-y-2",children:[s.jsx(Me,{htmlFor:`${e}-first-name`,children:"First name"}),s.jsx(se,{id:`${e}-first-name`,placeholder:"Enter first name",value:i,onChange:m=>l(m.target.value),type:"text",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",disabled:!o,required:!0})]}),s.jsxs("div",{className:"flex-1 space-y-2",children:[s.jsx(Me,{htmlFor:`${e}-last-name`,children:"Last name"}),s.jsx(se,{id:`${e}-last-name`,placeholder:"Enter last name",value:d,onChange:m=>c(m.target.value),type:"text",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",disabled:!o,required:!0})]})]}),s.jsxs("div",{className:"*:not-first:mt-2",children:[s.jsx(Me,{htmlFor:`${e}-username`,children:"Username"}),s.jsxs("div",{className:"relative",children:[s.jsx(se,{id:`${e}-username`,className:"peer pe-9 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",placeholder:"Enter username",value:u,onChange:m=>h(m.target.value),type:"text",disabled:!o,required:!0}),s.jsx("div",{className:"text-muted-foreground/80 pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-3 peer-disabled:opacity-50",children:s.jsx(Mo,{size:16,className:"text-emerald-500","aria-hidden":"true"})})]})]}),s.jsxs("div",{className:"*:not-first:mt-2",children:[s.jsx(Me,{htmlFor:`${e}-email`,children:"Email Address"}),s.jsx(se,{id:`${e}-email`,value:n?.gmail||"",type:"email",readOnly:!0,className:"bg-muted dark:bg-[#0D0D0D] cursor-not-allowed border-border dark:border-gray-800"}),s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"To change your email, please contact support."})]}),s.jsxs("div",{className:"*:not-first:mt-2",children:[s.jsx(Me,{htmlFor:`${e}-phone`,children:"Mobile Number"}),s.jsxs("div",{className:"flex rounded-md",children:[s.jsx("span",{className:"border-input bg-muted dark:bg-[#0D0D0D] text-muted-foreground inline-flex items-center rounded-l-md border border-r-0 px-3 text-sm",children:"+91"}),s.jsx(se,{id:`${e}-phone`,className:"rounded-l-none border-l-0 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",placeholder:"Enter 10-digit mobile number",value:g,onChange:m=>{const v=m.target.value.replace(/\D/g,"").slice(0,10);f(v)},type:"tel",maxLength:10,disabled:!o})]}),s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Enter exactly 10 digits (without country code)"})]}),s.jsxs("div",{className:"*:not-first:mt-2",children:[s.jsx(Me,{htmlFor:`${e}-address`,children:"Address"}),s.jsx(se,{id:`${e}-address`,placeholder:"Enter your address",value:x,onChange:m=>w(m.target.value),type:"text",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",disabled:!o})]})]})]}),s.jsxs("div",{className:"border-t px-6 py-4 flex justify-between items-center",children:[s.jsxs(lt,{children:[s.jsx(Ut,{asChild:!0,children:s.jsx(O,{variant:"outline",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Change to Admin"})}),s.jsxs(dt,{className:"sm:max-w-md bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(ct,{children:s.jsx(ut,{children:"Admin Access"})}),s.jsxs("div",{className:"space-y-4 py-4",children:[s.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter your password to gain admin permissions."}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(Me,{htmlFor:"admin-password",children:"Password"}),s.jsx(se,{id:"admin-password",type:"password",placeholder:"Enter your password",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800"})]}),s.jsxs("div",{className:"flex justify-end gap-3",children:[s.jsx(Ut,{asChild:!0,children:s.jsx(O,{variant:"outline",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Cancel"})}),s.jsx(O,{children:"Grant Admin Access"})]})]})]})]}),s.jsx("div",{className:"flex gap-3",children:o?s.jsxs(s.Fragment,{children:[s.jsx(O,{type:"button",variant:"outline",onClick:re,className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Cancel"}),s.jsxs(O,{type:"button",onClick:we,disabled:t,children:[t&&s.jsx(Et,{className:"mr-2 h-4 w-4 animate-spin"}),"Save changes"]})]}):s.jsxs(O,{type:"button",onClick:be,className:"bg-primary hover:bg-primary/90",children:[s.jsx(Ro,{className:"mr-2 h-4 w-4"}),"Edit Profile"]})})]})]})}),s.jsx(jt,{value:"preferences",children:s.jsx("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(_o,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Language"})]}),s.jsxs(Qe,{defaultValue:"en-US",children:[s.jsx(Je,{className:"w-full bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx(et,{placeholder:"Select language"})}),s.jsxs(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(U,{value:"en-US",children:"English (US)"}),s.jsx(U,{value:"en-GB",children:"English (UK)"}),s.jsx(U,{value:"hi-IN",children:"Hindi"}),s.jsx(U,{value:"es-ES",children:"Spanish"}),s.jsx(U,{value:"fr-FR",children:"French"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Notification Preferences"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"font-medium text-foreground",children:"Grievance Updates"}),s.jsxs("div",{className:"space-y-3 pl-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Status Change - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(wt,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Status Change - Email"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"New Remark - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(wt,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"New Remark - Email"})]}),s.jsx(Ne,{})]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"font-medium text-foreground",children:"AI Chat"}),s.jsxs("div",{className:"space-y-3 pl-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Report Ready - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"New Grievance - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(wt,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Response Ready - Email"})]}),s.jsx(Ne,{})]})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"font-medium text-foreground",children:"System Alerts"}),s.jsxs("div",{className:"space-y-3 pl-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Announcements - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(wt,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Announcements - Email"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Ze,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Security Alerts - In-App"})]}),s.jsx(Ne,{defaultChecked:!0})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(wt,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"text-sm",children:"Security Alerts - Email"})]}),s.jsx(Ne,{defaultChecked:!0})]})]})]})]}),s.jsx("div",{className:"flex justify-end pt-4 border-t",children:s.jsx(O,{children:"Save Preferences"})})]})})}),s.jsxs(jt,{value:"security",className:"space-y-6",children:[s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(bt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Change Password"})]}),B&&s.jsx(Bt,{type:"success",message:"Password changed successfully!",className:"mb-4 max-w-md"}),G&&s.jsx(Bt,{type:"error",message:G,className:"mb-4 max-w-md"}),s.jsxs("form",{className:"space-y-4 max-w-md",onSubmit:m=>{m.preventDefault(),D()},children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx(Me,{htmlFor:"current-password",children:"Current Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(se,{id:"current-password",type:H?"text":"password",value:C,onChange:m=>M(m.target.value),placeholder:"Enter current password",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 pr-10",required:!0}),s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>ae(!H),children:H?s.jsx(dn,{className:"h-4 w-4 text-muted-foreground"}):s.jsx(Be,{className:"h-4 w-4 text-muted-foreground"})})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(Me,{htmlFor:"new-password",children:"New Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(se,{id:"new-password",type:ie?"text":"password",value:_,onChange:m=>F(m.target.value),placeholder:"Enter new password (min 6 characters)",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 pr-10",required:!0,minLength:6}),s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>le(!ie),children:ie?s.jsx(dn,{className:"h-4 w-4 text-muted-foreground"}):s.jsx(Be,{className:"h-4 w-4 text-muted-foreground"})})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(Me,{htmlFor:"confirm-password",children:"Confirm New Password"}),s.jsxs("div",{className:"relative",children:[s.jsx(se,{id:"confirm-password",type:ve?"text":"password",value:$,onChange:m=>z(m.target.value),placeholder:"Confirm new password",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 pr-10",required:!0}),s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>Re(!ve),children:ve?s.jsx(dn,{className:"h-4 w-4 text-muted-foreground"}):s.jsx(Be,{className:"h-4 w-4 text-muted-foreground"})})]})]}),s.jsxs(O,{type:"submit",className:"w-full",disabled:k,children:[k&&s.jsx(Et,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Password"]})]}),s.jsx("div",{className:"mt-4 p-3 bg-muted/50 dark:bg-[#1A1A1A] rounded-lg max-w-md",children:s.jsxs("p",{className:"text-xs text-muted-foreground",children:[s.jsx("strong",{children:"Note:"})," This uses direct password change without requiring current password verification. Your password will be updated immediately."]})})]}),s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(ar,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Active Sessions"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(ar,{className:"h-4 w-4 text-muted-foreground"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Chrome on Windows"}),s.jsxs("p",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[s.jsx(It,{className:"h-3 w-3"})," Mumbai, India •"," ",s.jsx(In,{className:"h-3 w-3"})," Active now"]})]})]}),s.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Current"})]}),s.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(Po,{className:"h-4 w-4 text-muted-foreground"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"Mobile App"}),s.jsxs("p",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[s.jsx(It,{className:"h-3 w-3"})," Delhi, India •"," ",s.jsx(In,{className:"h-3 w-3"})," 2 hours ago"]})]})]}),s.jsx(O,{variant:"outline",size:"sm",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:"Log out"})]}),s.jsx(O,{variant:"destructive",className:"w-full",children:"Log out of all other devices"})]})]}),s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(Mt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Login History"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center gap-3 p-4 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium",children:"Successful Login"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"192.168.1.1 • Mumbai, India • 2 hours ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-4 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium",children:"Successful Login"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"192.168.1.2 • Delhi, India • 1 day ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-4 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full flex-shrink-0"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"text-sm font-medium",children:"Failed Login"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"203.0.113.1 • Unknown • 2 days ago"})]})]})]})]}),s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(bt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Two-Factor Authentication"})]}),s.jsxs("div",{className:"p-4 border rounded-lg bg-muted/50 dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Add an extra layer of security to your account"}),s.jsx(O,{disabled:!0,children:"Setup 2FA (Coming Soon)"})]})]})]}),s.jsxs(jt,{value:"privacy",className:"space-y-6",children:[s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(Mt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"My Activity Feed"})]}),s.jsxs(lt,{children:[s.jsx(Ut,{asChild:!0,children:s.jsxs(O,{variant:"outline",className:"w-full bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(Mt,{className:"mr-2 h-4 w-4"}),"View Recent Activity"]})}),s.jsxs(dt,{className:"max-w-4xl max-h-[80vh] overflow-y-auto bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(ct,{children:s.jsx(ut,{children:"Recent Activity"})}),s.jsxs("div",{className:"space-y-3 mt-4",children:[s.jsxs("div",{className:"flex items-center gap-3 p-3 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"You created grievance #GRV-2025-0009"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"2 hours ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-3 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"You updated your profile picture"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"1 day ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-3 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"You started a chat with the AI assistant"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"2 days ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-3 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"You submitted a remark on grievance #GRV-2025-0008"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"3 days ago"})]})]}),s.jsxs("div",{className:"flex items-center gap-3 p-3 border rounded-lg",children:[s.jsx("div",{className:"w-2 h-2 bg-teal-500 rounded-full"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium",children:"You logged in from a new device"}),s.jsx("p",{className:"text-xs text-muted-foreground",children:"1 week ago"})]})]})]})]})]})]}),s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(gt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Export My Data"})]}),s.jsxs("div",{className:"p-4 border rounded-lg",children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Download a copy of all your personal data including profile information, grievances, and chat logs."}),s.jsxs(O,{className:"w-full",children:[s.jsx(gt,{className:"mr-2 h-4 w-4"}),"Request Data Export"]})]})]}),s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(bt,{className:"h-5 w-5 text-destructive"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Delete My Data"})]}),s.jsxs("div",{className:"p-4 border border-destructive/20 rounded-lg bg-destructive/5",children:[s.jsx("p",{className:"text-sm text-muted-foreground mb-3",children:"Permanently delete all your personal data. This action cannot be undone."}),s.jsxs(O,{variant:"destructive",className:"w-full",children:[s.jsx(bt,{className:"mr-2 h-4 w-4"}),"Delete All My Data"]})]})]})]}),s.jsx(jt,{value:"mydata",className:"space-y-6",children:s.jsx(tu,{})})]})}const Wr=[{id:"f1",name:"streetlight_before.jpg",size:2.4*1024*1024,type:"image/jpeg",uploadDate:"2025-01-04",grievanceId:"GRV-2025-0009",grievanceTitle:"Street Light Not Working",url:"#"},{id:"f2",name:"repair_report.pdf",size:1.2*1024*1024,type:"application/pdf",uploadDate:"2025-01-04",grievanceId:"GRV-2025-0009",grievanceTitle:"Street Light Not Working",url:"#"},{id:"f3",name:"garbage_issue.png",size:1.8*1024*1024,type:"image/png",uploadDate:"2025-01-03",grievanceId:"GRV-2025-0008",grievanceTitle:"Garbage Collection Issue",url:"#"},{id:"f4",name:"complaint_audio.mp3",size:3.2*1024*1024,type:"audio/mpeg",uploadDate:"2025-01-03",grievanceId:"GRV-2025-0008",grievanceTitle:"Garbage Collection Issue",url:"#"},{id:"f5",name:"pothole_video.mp4",size:15.6*1024*1024,type:"video/mp4",uploadDate:"2025-01-02",grievanceId:"GRV-2025-0007",grievanceTitle:"Road Pothole Repair",url:"#"},{id:"f6",name:"water_supply_doc.docx",size:.8*1024*1024,type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",uploadDate:"2025-01-01",grievanceId:"GRV-2025-0006",grievanceTitle:"Water Supply Issue",url:"#"},{id:"f7",name:"noise_complaint.wav",size:4.1*1024*1024,type:"audio/wav",uploadDate:"2025-01-01",grievanceId:"GRV-2025-0005",grievanceTitle:"Noise Complaint",url:"#"},{id:"f8",name:"park_maintenance.gif",size:2.8*1024*1024,type:"image/gif",uploadDate:"2024-12-30",grievanceId:"GRV-2024-0050",grievanceTitle:"Park Maintenance",url:"#"}],tu=()=>{const[e,n]=p.useState("all"),[t,r]=p.useState("all"),[o,a]=p.useState(void 0),[i,l]=p.useState(null),d=[{id:"all",label:"All Files",icon:"📁"},{id:"images",label:"Images",icon:"🖼️"},{id:"documents",label:"Documents",icon:"📄"},{id:"audio",label:"Audio",icon:"🎵"},{id:"video",label:"Video",icon:"🎬"}],c=b=>b.startsWith("image/")?"images":b.startsWith("audio/")?"audio":b.startsWith("video/")?"video":"documents",u=b=>b.split(".").pop()?.toUpperCase()||"",h=Wr.filter(b=>{const y=e==="all"||c(b.type)===e,N=t==="all"||b.grievanceTitle.toLowerCase().includes(t.toLowerCase())||b.grievanceId.toLowerCase().includes(t.toLowerCase()),j=!o||new Date(b.uploadDate).toDateString()===o.toDateString();return y&&N&&j}),g=h.reduce((b,y)=>{const N=y.uploadDate;return b[N]||(b[N]=[]),b[N].push(y),b},{}),f=b=>b.reduce((y,N)=>{const j=u(N.name);return y[j]||(y[j]=[]),y[j].push(N),y},{}),x=b=>b.startsWith("image/")?"🖼️":b.startsWith("video/")?"🎬":b.startsWith("audio/")?"🎵":b.includes("pdf")?"📄":b.includes("word")||b.includes("docx")?"📝":"📄",w=b=>{if(b===0)return"0 Bytes";const y=1024,N=["Bytes","KB","MB","GB"],j=Math.floor(Math.log(b)/Math.log(y));return parseFloat((b/Math.pow(y,j)).toFixed(2))+" "+N[j]};return s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-xl shadow-lg p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx(Mt,{className:"h-5 w-5 text-primary"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"My Data"}),s.jsxs("span",{className:"text-sm text-muted-foreground",children:["(",Wr.length," files)"]})]}),s.jsxs("div",{className:"flex flex-wrap gap-4 mb-6 p-4 bg-muted/50 dark:bg-[#1A1A1A] rounded-lg",children:[s.jsx("div",{className:"flex gap-2",children:d.map(b=>s.jsxs(O,{variant:e===b.id?"secondary":"outline",size:"sm",onClick:()=>n(b.id),className:`text-xs ${e===b.id?"bg-muted text-foreground":"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800"}`,children:[b.icon," ",b.label]},b.id))}),s.jsxs("div",{className:"relative w-[250px]",children:[s.jsx(Gr,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),s.jsx(se,{placeholder:"Search by title or reference...",value:t==="all"?"":t,onChange:b=>r(b.target.value||"all"),className:"pl-8 pr-8 bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800 text-foreground dark:text-white placeholder:text-muted-foreground focus:bg-card dark:focus:bg-[#0D0D0D]"}),t!=="all"&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>r("all"),children:s.jsx(De,{className:"h-3 w-3"})})]}),s.jsxs("div",{className:"relative",children:[s.jsxs(Ps,{children:[s.jsx(Fs,{asChild:!0,children:s.jsxs(O,{variant:"outline",className:"w-[180px] justify-start text-left font-normal bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(Yr,{className:"mr-2 h-4 w-4"}),o?Un(o,"MMM dd, yyyy"):s.jsx("span",{children:"Filter by date"})]})}),s.jsx(Kn,{className:"w-auto p-0 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",align:"start",children:s.jsx(_s,{mode:"single",selected:o,onSelect:a,className:"bg-card dark:bg-[#0D0D0D] text-foreground dark:text-white [&_button]:bg-card [&_button]:dark:bg-[#0D0D0D] [&_button]:text-foreground [&_button]:dark:text-white [&_button:hover]:bg-muted [&_button:hover]:dark:bg-zinc-800"})})]}),o&&s.jsx(O,{variant:"ghost",size:"sm",className:"absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 bg-background dark:bg-[#0D0D0D] hover:bg-muted border border-border dark:border-gray-800",onClick:()=>a(void 0),children:s.jsx(De,{className:"h-3 w-3"})})]})]}),s.jsx("div",{className:"max-h-[60vh] overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-muted [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-muted-foreground/50",children:e==="all"?Object.entries(g).map(([b,y])=>s.jsxs("div",{className:"mb-6",children:[s.jsx("h4",{className:"text-md font-semibold text-foreground mb-3",children:new Date(b).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:y.map(N=>s.jsx(Ee,{className:"p-4 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 hover:bg-muted/50 transition-colors cursor-pointer",onClick:()=>l(N),children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-2xl",children:x(N.type)}),s.jsxs("div",{className:"min-w-0 flex-1",children:[s.jsx("p",{className:"text-sm font-medium text-foreground truncate",children:N.name}),s.jsx("p",{className:"text-xs text-muted-foreground",children:w(N.size)}),s.jsx("p",{className:"text-xs text-primary",children:N.grievanceId}),s.jsx("p",{className:"text-xs text-muted-foreground truncate",children:N.grievanceTitle})]})]})},N.id))})]},b)):(()=>{const b=h.filter(N=>c(N.type)===e),y=f(b);return Object.entries(y).map(([N,j])=>s.jsxs("div",{className:"mb-6",children:[s.jsxs("h4",{className:"text-md font-semibold text-foreground mb-3",children:[N," Files (",j.length,")"]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:j.map(k=>s.jsx(Ee,{className:"p-4 bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 hover:bg-muted/50 transition-colors cursor-pointer",onClick:()=>l(k),children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-2xl",children:x(k.type)}),s.jsxs("div",{className:"min-w-0 flex-1",children:[s.jsx("p",{className:"text-sm font-medium text-foreground truncate",children:k.name}),s.jsx("p",{className:"text-xs text-muted-foreground",children:w(k.size)}),s.jsx("p",{className:"text-xs text-primary",children:k.grievanceId}),s.jsx("p",{className:"text-xs text-muted-foreground truncate",children:k.grievanceTitle})]})]})},k.id))})]},N))})()}),i&&s.jsx(lt,{open:!!i,onOpenChange:()=>l(null),children:s.jsxs(dt,{className:"max-w-4xl max-h-[90vh] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(ct,{children:s.jsx(ut,{className:"text-foreground",children:i.name})}),s.jsxs("div",{className:"flex items-center justify-center p-4 min-h-[400px]",children:[i.type.startsWith("image/")&&s.jsx("img",{src:`https://picsum.photos/800/600?random=${i.id}`,alt:i.name,className:"max-w-full max-h-[60vh] object-contain rounded-lg"}),i.type.startsWith("video/")&&s.jsx("video",{src:"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",controls:!0,className:"max-w-full max-h-[60vh] rounded-lg"}),i.type.startsWith("audio/")&&s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"🎵"}),s.jsx("p",{className:"text-foreground mb-2",children:i.name}),s.jsx("audio",{src:"https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",controls:!0,className:"w-full max-w-md"})]}),i.type.includes("pdf")&&s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"📄"}),s.jsx("p",{className:"text-foreground mb-2",children:i.name}),s.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:w(i.size)}),s.jsxs(O,{className:"bg-primary hover:bg-primary/90",children:[s.jsx(gt,{className:"mr-2 h-4 w-4"}),"Download PDF"]})]}),(i.type.includes("word")||i.type.includes("docx"))&&s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"📝"}),s.jsx("p",{className:"text-foreground mb-2",children:i.name}),s.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:w(i.size)}),s.jsxs(O,{className:"bg-primary hover:bg-primary/90",children:[s.jsx(gt,{className:"mr-2 h-4 w-4"}),"Download Document"]})]})]})]})})]})},Lu=Object.freeze(Object.defineProperty({__proto__:null,ProfileSettings:eu},Symbol.toStringTag,{value:"Module"})),nu=({className:e,...n})=>s.jsx(Fo,{className:E("animate-spin",e),...n}),ru=({className:e,...n})=>s.jsx(Et,{className:E("animate-spin",e),...n}),su=({className:e,...n})=>s.jsx(Eo,{className:E("animate-spin",e),...n}),ou=({className:e,size:n=24,...t})=>s.jsxs("div",{className:"relative",style:{width:n,height:n},children:[s.jsx("div",{className:"absolute inset-0 rotate-180",children:s.jsx(Et,{className:E("animate-spin",e,"text-foreground opacity-20"),size:n,...t})}),s.jsx(Et,{className:E("relative animate-spin",e),size:n,...t})]}),au=({size:e=24,...n})=>s.jsxs("svg",{height:e,viewBox:"0 0 32 24",width:e*1.3,xmlns:"http://www.w3.org/2000/svg",...n,children:[s.jsx("title",{children:"Loading..."}),s.jsx("circle",{cx:"6",cy:"12",fill:"currentColor",r:"4",children:s.jsx("animate",{attributeName:"cy",begin:"0;ellipsis3.end+0.25s",calcMode:"spline",dur:"0.6s",id:"ellipsis1",keySplines:".33,.66,.66,1;.33,0,.66,.33",values:"12;6;12"})}),s.jsx("circle",{cx:"16",cy:"12",fill:"currentColor",r:"4",children:s.jsx("animate",{attributeName:"cy",begin:"ellipsis1.begin+0.1s",calcMode:"spline",dur:"0.6s",keySplines:".33,.66,.66,1;.33,0,.66,.33",values:"12;6;12"})}),s.jsx("circle",{cx:"26",cy:"12",fill:"currentColor",r:"4",children:s.jsx("animate",{attributeName:"cy",begin:"ellipsis1.begin+0.2s",calcMode:"spline",dur:"0.6s",id:"ellipsis3",keySplines:".33,.66,.66,1;.33,0,.66,.33",values:"12;6;12"})})]}),iu=({size:e=24,...n})=>s.jsxs("svg",{height:e,stroke:"currentColor",viewBox:"0 0 44 44",width:e,xmlns:"http://www.w3.org/2000/svg",...n,children:[s.jsx("title",{children:"Loading..."}),s.jsxs("g",{fill:"none",fillRule:"evenodd",strokeWidth:"2",children:[s.jsxs("circle",{cx:"22",cy:"22",r:"1",children:[s.jsx("animate",{attributeName:"r",begin:"0s",calcMode:"spline",dur:"1.8s",keySplines:"0.165, 0.84, 0.44, 1",keyTimes:"0; 1",repeatCount:"indefinite",values:"1; 20"}),s.jsx("animate",{attributeName:"stroke-opacity",begin:"0s",calcMode:"spline",dur:"1.8s",keySplines:"0.3, 0.61, 0.355, 1",keyTimes:"0; 1",repeatCount:"indefinite",values:"1; 0"})]}),s.jsxs("circle",{cx:"22",cy:"22",r:"1",children:[s.jsx("animate",{attributeName:"r",begin:"-0.9s",calcMode:"spline",dur:"1.8s",keySplines:"0.165, 0.84, 0.44, 1",keyTimes:"0; 1",repeatCount:"indefinite",values:"1; 20"}),s.jsx("animate",{attributeName:"stroke-opacity",begin:"-0.9s",calcMode:"spline",dur:"1.8s",keySplines:"0.3, 0.61, 0.355, 1",keyTimes:"0; 1",repeatCount:"indefinite",values:"1; 0"})]})]})]}),lu=({size:e=24,...n})=>s.jsxs("svg",{height:e,viewBox:"0 0 24 24",width:e,xmlns:"http://www.w3.org/2000/svg",...n,children:[s.jsx("title",{children:"Loading..."}),s.jsx("style",{children:`
      .spinner-bar {
        animation: spinner-bars-animation .8s linear infinite;
        animation-delay: -.8s;
      }
      .spinner-bars-2 {
        animation-delay: -.65s;
      }
      .spinner-bars-3 {
        animation-delay: -0.5s;
      }
      @keyframes spinner-bars-animation {
        0% {
          y: 1px;
          height: 22px;
        }
        93.75% {
          y: 5px;
          height: 14px;
          opacity: 0.2;
        }
      }
    `}),s.jsx("rect",{className:"spinner-bar",fill:"currentColor",height:"22",width:"6",x:"1",y:"1"}),s.jsx("rect",{className:"spinner-bar spinner-bars-2",fill:"currentColor",height:"22",width:"6",x:"9",y:"1"}),s.jsx("rect",{className:"spinner-bar spinner-bars-3",fill:"currentColor",height:"22",width:"6",x:"17",y:"1"})]}),du=({size:e=24,...n})=>s.jsxs("svg",{height:e,preserveAspectRatio:"xMidYMid",viewBox:"0 0 100 100",width:e,xmlns:"http://www.w3.org/2000/svg",...n,children:[s.jsx("title",{children:"Loading..."}),s.jsx("path",{d:"M24.3 30C11.4 30 5 43.3 5 50s6.4 20 19.3 20c19.3 0 32.1-40 51.4-40 C88.6 30 95 43.3 95 50s-6.4 20-19.3 20C56.4 70 43.6 30 24.3 30z",fill:"none",stroke:"currentColor",strokeDasharray:"205.271142578125 51.317785644531256",strokeLinecap:"round",strokeWidth:"10",style:{transform:"scale(0.8)",transformOrigin:"50px 50px"},children:s.jsx("animate",{attributeName:"stroke-dashoffset",dur:"2s",keyTimes:"0;1",repeatCount:"indefinite",values:"0;256.58892822265625"})})]}),$r=({variant:e,...n})=>{switch(e){case"circle":return s.jsx(ru,{...n});case"pinwheel":return s.jsx(su,{...n});case"circle-filled":return s.jsx(ou,{...n});case"ellipsis":return s.jsx(au,{...n});case"ring":return s.jsx(iu,{...n});case"bars":return s.jsx(lu,{...n});case"infinite":return s.jsx(du,{...n});default:return s.jsx(nu,{...n})}},cu=(e,n)=>{const[t,r]=p.useState(!1),[o,a]=p.useState(null);return p.useEffect(()=>{if(console.log(`[Script Loader] Attempting to load: ${e} with id: ${n}`),console.log("[Script Loader] Current window.Quill status:",typeof window.Quill),console.log("[Script Loader] Existing scripts:",Array.from(document.scripts).map(u=>u.src).filter(u=>u.includes("quill"))),typeof window.Quill=="function"){console.log("[Script Loader] Quill already available globally"),r(!0);return}const i=document.getElementById(n);i&&(console.log(`[Script Loader] Script with id ${n} already exists`),typeof window.Quill=="function"?r(!0):(console.log("[Script Loader] Removing existing script and retrying"),i.remove()));const l=document.createElement("script");l.id=n,l.src=e,l.async=!0;const d=()=>{console.log(`[Script Loader] Script loaded: ${e}`);{let u=0;const h=100,g=setInterval(()=>{u++,typeof window.Quill=="function"?(console.log(`[Script Loader] Quill available after ${u} attempts`),clearInterval(g),r(!0)):u>=h&&(console.error(`[Script Loader] Quill not available after ${h} attempts`),clearInterval(g),a(`Quill not available after loading script from ${e}`))},50)}},c=u=>{console.error(`[Script Loader] Failed to load script: ${e}`,u);{const h=["https://unpkg.com/quill@2.0.2/dist/quill.js","https://cdnjs.cloudflare.com/ajax/libs/quill/2.0.2/quill.min.js","https://cdn.quilljs.com/1.3.7/quill.min.js"];let g=0;const f=()=>{if(g<h.length){const x=h[g];console.log(`[Script Loader] Trying fallback ${g+1}: ${x}`);const w=document.createElement("script");w.src=x,w.onload=d,w.onerror=()=>{console.error(`[Script Loader] Fallback ${g+1} failed: ${x}`),g++,f()},document.body.appendChild(w)}else console.error("[Script Loader] All fallback CDNs failed"),a("Failed to load Quill from all CDN sources. Check network connection.")};f()}};return l.addEventListener("load",d),l.addEventListener("error",c),document.body.appendChild(l),()=>{l.removeEventListener("load",d),l.removeEventListener("error",c)}},[e,n]),{loaded:t,error:o||!1}};let Fe=null,Fn=null;const uu=({initialHtml:e,onHtmlChange:n})=>{const t=p.useRef(null);return p.useEffect(()=>{if(t.current&&typeof window.Quill=="function"){if(Fe&&Fn)try{Fe.off("text-change"),Fn.textContent=""}catch(a){console.warn("Error cleaning up previous Quill instance:",a)}Fn=t.current;const r=window.Quill.import("formats/font");r.whitelist=["arial","verdana","helvetica","tahoma","trebuchet-ms","georgia","times-new-roman","palatino","courier-new","lucida-console","monaco"],window.Quill.register(r,!0);const o=window.Quill.import("attributors/style/size");o.whitelist=["10px","12px","14px","16px","18px","20px","24px","28px","32px","36px"],window.Quill.register(o,!0),Fe=new window.Quill(t.current,{theme:"snow",placeholder:"Start drafting your grievance here...",modules:{toolbar:[[{font:["arial","verdana","helvetica","tahoma","trebuchet-ms","georgia","times-new-roman","palatino","courier-new","lucida-console","monaco"]}],[{size:["10px","12px","14px","16px","18px","20px","24px","28px","32px","36px"]}],[{header:[1,2,3,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{align:[]}],["blockquote"],["link","image"],["clean"]]}}),e&&Fe.clipboard.dangerouslyPasteHTML(e),Fe.on("text-change",()=>{n(Fe.root.innerHTML)})}return()=>{}},[]),p.useEffect(()=>{if(Fe&&e!==Fe.root.innerHTML){const r=Fe.root.innerHTML;e&&e!==r?Fe.clipboard.dangerouslyPasteHTML(e):!e&&r!=="<p><br></p>"&&Fe.setText("")}},[e]),s.jsx("div",{className:"flex-grow flex flex-col overflow-hidden bg-[#0D0D0D]",children:s.jsx("div",{className:"flex-grow overflow-y-auto custom-scrollbar p-6",children:s.jsx("div",{ref:t,className:"h-full min-h-[500px] bg-[#0D0D0D] text-white rounded-md"})})})},mu=({progress:e})=>s.jsx("div",{className:"w-full bg-zinc-800 rounded-full h-1 mb-4",children:s.jsx("div",{className:"bg-blue-500 h-1 rounded-full transition-all duration-300 ease-out",style:{width:`${e}%`}})}),fu=({progress:e})=>s.jsxs("div",{className:"space-y-4",children:[s.jsx(mu,{progress:e}),s.jsxs("div",{className:"animate-pulse space-y-4",children:[s.jsx("div",{className:"h-8 bg-zinc-700 rounded w-3/4 mx-auto"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-full"}),s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-5/6"}),s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-4/5"})]}),s.jsx("div",{className:"h-6 bg-zinc-700 rounded w-1/2"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-full"}),s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-3/4"}),s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-5/6"}),s.jsx("div",{className:"h-4 bg-zinc-700 rounded w-2/3"})]})]}),s.jsxs("div",{className:"text-center text-sm text-zinc-400 mt-4",children:["Rendering preview... ",Math.round(e),"%"]})]}),gu=({htmlContent:e})=>{const[n,t]=p.useState(!0),[r,o]=p.useState(0),a=p.useRef(null),i=e&&e.trim()!=="<p><br></p>"&&e.trim()!=="";return p.useEffect(()=>{t(!0),o(0);const l=setInterval(()=>{o(u=>Math.min(u+15,90))},100),d=()=>{if(a.current&&i&&window.getComputedStyle(a.current).lineHeight!=="normal"){o(100),setTimeout(()=>t(!1),200);return}setTimeout(d,100)};setTimeout(()=>{clearInterval(l),d()},600);const c=setTimeout(()=>{clearInterval(l),o(100),t(!1)},1500);return()=>{clearInterval(l),clearTimeout(c)}},[e,i]),s.jsx("div",{className:"flex-grow flex flex-col overflow-hidden bg-[#0D0D0D]",children:s.jsx("div",{className:"flex-grow overflow-y-auto custom-scrollbar p-6",children:n?s.jsx(fu,{progress:r}):s.jsx("div",{ref:a,className:"prose dark:prose-invert max-w-none text-white transition-opacity duration-300",dangerouslySetInnerHTML:{__html:i?e:'<p class="text-zinc-400 text-center py-8">Start typing in the Edit tab to see your preview here.</p>'}})})})},rr=p.createContext(null),hu=({defaultValue:e,className:n="",children:t})=>{const[r,o]=p.useState(e);return s.jsx("div",{className:`flex flex-col ${n}`,children:s.jsx(rr.Provider,{value:{activeTab:r,setActiveTab:o},children:t})})},pu=({children:e,className:n=""})=>s.jsx("div",{className:`inline-flex h-9 items-center justify-center rounded-md bg-[#1A1A1A] p-1 text-zinc-500 overflow-x-auto whitespace-nowrap flex-shrink-0 ${n}`,children:e}),zr=({value:e,children:n,className:t=""})=>{const r=p.useContext(rr);if(!r)return null;const{activeTab:o,setActiveTab:a}=r;return s.jsx("button",{className:`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ring-offset-background
        ${o===e?"bg-[#0D0D0D] text-white shadow":"bg-transparent text-zinc-400 hover:bg-zinc-700 hover:text-white"} ${t}`,onClick:()=>a(e),children:n})},Lr=({value:e,children:n,className:t=""})=>{const r=p.useContext(rr);if(!r)return null;const{activeTab:o}=r;return o===e?s.jsx("div",{className:`mt-2 flex-grow flex flex-col ${t}`,children:n}):null},xu=()=>{const{content:e,closeCanvas:n}=At(),[t,r]=p.useState(e),[o,a]=p.useState(!1),i=d=>{r(d)},l=()=>{const d=t&&t.trim()!=="<p><br></p>"&&t.trim()!=="";if(!(o||!d)){a(!0);try{const c=`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Grievance Report - CivicAssist</title>
          <style>
            body {
              font-family: 'Times New Roman', serif;
              line-height: 1.6;
              margin: 40px;
              color: #000;
              background: #fff;
              position: relative;
            }
            h1 { font-size: 28px; text-align: center; margin-bottom: 24px; }
            h2 { font-size: 22px; margin-top: 32px; }
            h3 { font-size: 18px; }
            p, li { font-size: 16px; margin-bottom: 16px; }
            ul, ol { padding-left: 30px; }
            .info-group {
              margin-bottom: 20px;
              padding: 16px;
              border: 1px solid #ccc;
              border-radius: 4px;
              background-color: #f9f9f9;
            }
            .watermark {
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(-45deg);
              font-size: 72px;
              color: rgba(0, 0, 0, 0.1);
              font-weight: bold;
              z-index: -1;
              pointer-events: none;
              font-family: Arial, sans-serif;
            }
            /* Image size constraints for downloaded document */
            img {
              max-width: 400px !important;
              max-height: 300px !important;
              width: auto !important;
              height: auto !important;
              object-fit: contain !important;
              display: block;
              margin: 10px 0;
            }
          </style>
        </head>
        <body>
          <div class="watermark">CivicAssist</div>
          ${t}
          <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
            Generated by CivicAssist - ${new Date().toLocaleDateString()}
          </div>
        </body>
        </html>
      `,u=new Blob([c],{type:"text/html;charset=utf-8"}),h=URL.createObjectURL(u),g=document.createElement("a");g.href=h,g.download=`grievance-report-${new Date().toISOString().split("T")[0]}.html`,document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(h)}catch(c){console.error("Failed to generate document:",c)}finally{a(!1)}}};return s.jsx("div",{className:"h-full w-full flex flex-col bg-[#0D0D0D] overflow-hidden",children:s.jsxs(hu,{defaultValue:"edit",className:"flex-grow flex flex-col overflow-hidden",children:[s.jsxs("div",{className:"p-4 border-b border-zinc-800 bg-[#0D0D0D] flex items-center justify-between flex-shrink-0",children:[s.jsxs(pu,{className:"space-x-2",children:[s.jsx(zr,{value:"edit",children:"Edit"}),s.jsx(zr,{value:"preview",children:"Preview"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(O,{variant:"outline",size:"sm",onClick:l,disabled:o,className:"text-xs bg-[#0D0D0D] border-zinc-700 text-white hover:bg-zinc-800",children:o?"Downloading...":"Download Document"}),s.jsx(O,{variant:"ghost",size:"sm",onClick:n,className:"h-8 w-8 p-0",children:s.jsx(Io,{className:"h-4 w-4"})})]})]}),s.jsx(Lr,{value:"edit",className:"flex-grow flex flex-col overflow-hidden",children:s.jsx(uu,{initialHtml:t,onHtmlChange:i})}),s.jsx(Lr,{value:"preview",className:"flex-grow flex flex-col overflow-hidden",children:s.jsx(gu,{htmlContent:t})})]})})},vu=()=>{const{isOpen:e,width:n,setWidth:t}=At(),{loaded:r,error:o}=cu("https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js","quill-script"),[a,i]=p.useState(!1),l=r,d=o,c=u=>{i(!0),u.preventDefault()};return p.useEffect(()=>{const u=g=>{a&&requestAnimationFrame(()=>{const f=Math.max(30,Math.min(70,(window.innerWidth-g.clientX)/window.innerWidth*100));t(f)})},h=()=>{i(!1)};return a&&(document.addEventListener("mousemove",u),document.addEventListener("mouseup",h),document.body.style.cursor="ew-resize",document.body.style.userSelect="none"),()=>{document.removeEventListener("mousemove",u),document.removeEventListener("mouseup",h),document.body.style.cursor="",document.body.style.userSelect=""}},[a,t]),s.jsxs(s.Fragment,{children:[s.jsx("link",{href:"https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css",rel:"stylesheet"}),s.jsx(On,{children:e&&s.jsxs(Se.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",damping:30,stiffness:400,mass:.8},className:"fixed inset-y-0 right-0 bg-[#0D0D0D] border-l border-zinc-800 shadow-2xl z-40 flex flex-col smooth-transform gpu-accelerated",style:{width:`${n}vw`,minWidth:"320px",maxWidth:"70vw"},children:[s.jsx("div",{onMouseDown:c,className:"absolute left-0 top-0 w-1 bg-zinc-700 cursor-ew-resize select-none z-10",style:{height:"100%"}}),d?s.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-destructive p-6 text-center",children:[s.jsx("div",{className:"text-red-400 mb-4 text-lg font-semibold",children:"⚠️ Editor Loading Failed"}),s.jsx("div",{className:"text-sm text-red-300 mb-4 max-w-md",children:typeof o=="string"?o:"Failed to load Quill editor scripts"}),s.jsx("div",{className:"text-xs text-zinc-400 mb-4",children:"Check browser console for detailed error information"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors",children:"Reload Page"})]}):l?s.jsx(xu,{}):s.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-muted-foreground",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mb-4"}),s.jsx("div",{className:"text-sm",children:"Loading editor..."}),s.jsx("div",{className:"text-xs text-zinc-500 mt-2",children:"Initializing Quill editor"})]})]})})]})},bu=e=>{const{openCanvas:n}=At.getState();n(e)},Vr=e=>{const n=e.toLowerCase();return n.includes("open canvas")||n.includes("canvas")||n.includes("draft")||n.includes("grievance report")};function wu(){const{isOpen:e,width:n}=At(),t=typeof window<"u"&&window.innerWidth<768;return{style:e&&!t?{marginRight:`${n}vw`}:void 0,className:e?`max-w-none transition-all duration-150 ease-out ${t?"sm:mr-0":""}`:"max-w-4xl mx-auto transition-all duration-150 ease-out",isCanvasOpen:e,canvasWidth:n,isMobile:t}}const yu=`
.smooth-transform { transform: translateZ(0); backface-visibility: hidden; perspective: 1000px; }
.gpu-accelerated { will-change: transform, opacity; transform: translate3d(0, 0, 0); }
.optimize-scroll { -webkit-overflow-scrolling: touch; scroll-behavior: smooth; }
.contain-layout { contain: layout style paint; }
`,En=[{id:"gpt-4",name:"GPT-4"},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo"},{id:"claude-2",name:"Claude 2"},{id:"claude-instant",name:"Claude Instant"},{id:"palm-2",name:"PaLM 2"},{id:"llama-2-70b",name:"Llama 2 70B"},{id:"llama-2-13b",name:"Llama 2 13B"},{id:"cohere-command",name:"Command"},{id:"mistral-7b",name:"Mistral 7B"}];function ju(){At();const{style:e,className:n}=wu(),[t,r]=p.useState(""),[o]=p.useState(En[0].id),[a]=p.useState("ready"),[i,l]=p.useState([]),[d,c]=p.useState(0),[u,h]=p.useState(null),[g,f]=p.useState(""),[x,w]=p.useState({}),[b,y]=p.useState(new Set),N=p.useRef(null),[{files:j,errors:k},{openFileDialog:S,removeFile:C}]=nr({accept:"image/*,application/pdf,.doc,.docx,.txt,audio/*,video/*",maxSize:5*1024*1024,multiple:!0,maxFiles:5}),[M,_]=p.useState(!1),[F,$]=p.useState(0),z=p.useRef(null),H=p.useRef(null),ae=D=>{if(D.preventDefault(),!t.trim())return;const m=d+1,v={id:Date.now(),type:"user",content:t,sequenceNumber:m},P={id:Date.now()+1,type:"ai",content:"",isThinking:!0,sequenceNumber:m};l(V=>[...V,v,P]),c(m),r(""),Vr(t)&&bu(),setTimeout(()=>{let V=`I understand you're asking about: "${v.content}". This is a simulated response from ${En.find(ne=>ne.id===o)?.name}. How can I help you further with your grievance or civic concern?`;Vr(v.content)&&(V=`I've opened the canvas editor for you! You can now draft your grievance report with a professional template. The canvas includes:

• Rich text editor with formatting options
• Professional grievance template
• Preview mode with PDF export
• Edit and preview tabs

You can customize the content, add your specific details, and export it as a PDF when ready. How else can I assist you with your grievance?`),l(ne=>ne.map(ee=>ee.id===P.id?{...ee,content:V,isThinking:!1}:ee))},2e3)},ie=()=>{if(!("webkitSpeechRecognition"in window)&&!("SpeechRecognition"in window)){alert("Speech recognition not supported in this browser");return}const D=window.SpeechRecognition||window.webkitSpeechRecognition,m=new D;H.current=m,m.continuous=!0,m.interimResults=!0,m.lang="en-US",m.onstart=()=>{_(!0),$(0),z.current=setInterval(()=>{$(v=>{const P=v+1;return P>=90&&setTimeout(()=>{H.current&&(H.current.stop(),H.current=null),z.current&&(clearInterval(z.current),z.current=null),_(!1),$(0)},100),P})},1e3)},m.onresult=v=>{let P="";for(let V=v.resultIndex;V<v.results.length;V++)v.results[V].isFinal&&(P+=v.results[V][0].transcript);P&&r(V=>V+P+" ")},m.onerror=v=>{console.log("Speech recognition error:",v.error),z.current&&(clearInterval(z.current),z.current=null),_(!1),$(0)},m.onend=()=>{setTimeout(()=>{if(M&&F<89)try{const v=new D;H.current=v,v.continuous=!0,v.interimResults=!0,v.lang="en-US",v.onresult=m.onresult,v.onerror=m.onerror,v.onend=m.onend,v.start()}catch(v){console.log("Error restarting recognition:",v),setTimeout(()=>{M&&F<88&&ie()},500)}else z.current&&(clearInterval(z.current),z.current=null),_(!1),$(0)},200)},m.start()},le=()=>{_(!1),z.current&&(clearInterval(z.current),z.current=null),H.current&&(H.current.stop(),H.current=null),$(0)},ve=D=>{const m=Math.floor(D/60),v=D%60;return`${m}:${v.toString().padStart(2,"0")}`},Re=(D,m)=>{const v=m?.split(".").pop()?.toLowerCase();return D.startsWith("image/")?s.jsx(Ao,{className:"h-4 w-4 text-white"}):D.includes("pdf")||v==="pdf"?s.jsx(Ke,{className:"h-4 w-4 text-white"}):v==="doc"||v==="docx"||D.includes("document")?s.jsx(Ke,{className:"h-4 w-4 text-white"}):v==="xls"||v==="xlsx"||D.includes("spreadsheet")?s.jsx(Wo,{className:"h-4 w-4 text-white"}):v==="ppt"||v==="pptx"||D.includes("presentation")?s.jsx($o,{className:"h-4 w-4 text-white"}):v==="txt"||D.includes("text")?s.jsx(Ke,{className:"h-4 w-4 text-white"}):D.startsWith("audio/")||["mp3","wav","m4a","aac","flac","ogg"].includes(v||"")?s.jsx(zo,{className:"h-4 w-4 text-white"}):D.startsWith("video/")||["mp4","mov","avi","mkv","webm","flv"].includes(v||"")?s.jsx(Lo,{className:"h-4 w-4 text-white"}):s.jsx(Vo,{className:"h-4 w-4 text-white"})},B=(D,m)=>{const v=m?.split(".").pop()?.toLowerCase();return D.includes("pdf")||v==="pdf"?"PDF":v==="doc"||v==="docx"?"DOC":v==="xls"||v==="xlsx"?"XLS":v==="ppt"||v==="pptx"?"PPT":v==="txt"?"TXT":D.startsWith("audio/")?"AUDIO":D.startsWith("video/")?"VIDEO":D.startsWith("image/")?"IMAGE":v?.toUpperCase()||"FILE"},T=(D,m)=>{h(D),f(m)},G=D=>{const m=i.findIndex(P=>P.id===D),v=i[m];if(v&&v.type==="user"){const P=d+1;c(P);const V={...v,content:g,sequenceNumber:P,userBranches:v.userBranches?[...v.userBranches.filter(ne=>ne!==g),g]:[v.content,g]};w(ne=>({...ne,[v.id]:V.userBranches.length-1})),l(ne=>ne.map((ee,Q)=>Q===m?V:Q===m+1&&ee.type==="ai"?{...ee,content:"",isThinking:!0,sequenceNumber:P,branches:ee.branches?[...ee.branches,""]:[ee.content,""]}:ee)),setTimeout(()=>{const ne=`I understand you're asking about: "${g}". This is a response from ${En.find(ee=>ee.id===o)?.name}. How can I help you further?`;l(ee=>ee.map((Q,ye)=>{if(ye===m+1&&Q.type==="ai"){const qe=Q.branches?[...Q.branches]:[Q.content,""];return qe[qe.length-1]=ne,console.assert(V.userBranches.length===qe.length,"Branch count mismatch after AI response"),{...Q,content:ne,isThinking:!1,sequenceNumber:P,branches:qe}}return Q}))},2e3)}h(null),f("")},de=()=>{h(null),f("")},be=(D,m)=>{const v=i.find(P=>P.id===D);v?.userBranches&&m>=0&&m<v.userBranches.length&&(w(P=>({...P,[D]:m})),console.log(`Showing branch ${m+1} of ${v.userBranches.length}`))},re=D=>{const m=i.indexOf(D),v=i[m-1];if(v?.userBranches){const P=x[v.id]??0;return console.assert(v.userBranches.length===(D.branches?.length||1),"Branch count mismatch"),D.branches?.[P]??D.content}return D.content},Y=D=>{y(m=>new Set([...m,D]))},we=k.filter(D=>!b.has(D));return p.useEffect(()=>{N.current?.scrollIntoView({behavior:"smooth"})},[i]),p.useEffect(()=>{if(we.length>0){const D=setTimeout(()=>{Y(we[0])},5e3);return()=>clearTimeout(D)}},[we]),s.jsxs(s.Fragment,{children:[s.jsx("style",{dangerouslySetInnerHTML:{__html:yu}}),s.jsx(vu,{}),we.length>0&&s.jsx("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-right-2 duration-300",children:s.jsx("div",{className:"bg-destructive text-destructive-foreground rounded-xl px-4 py-3 shadow-lg border border-destructive/20 backdrop-blur-sm",children:s.jsxs("div",{className:"flex items-center justify-between gap-3 min-w-[300px]",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(Hr,{className:"h-4 w-4"})}),s.jsx("span",{className:"text-sm font-medium",children:we[0]})]}),s.jsx("button",{onClick:()=>Y(we[0]),className:"flex-shrink-0 h-6 w-6 rounded-full bg-destructive-foreground/10 hover:bg-destructive-foreground/20 transition-colors flex items-center justify-center",children:s.jsx(De,{className:"h-3 w-3"})})]})})}),s.jsxs("div",{className:`flex flex-col h-[calc(100vh-8rem)] bg-background dark:bg-[#0D0D0D] gpu-accelerated contain-layout ${n}`,style:e,children:[s.jsxs("div",{className:"flex-1 overflow-y-auto overflow-x-hidden px-4 py-6 space-y-6 optimize-scroll [&::-webkit-scrollbar]:hidden",children:[i.length===0?s.jsx("div",{className:"flex items-center justify-center h-full",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-6xl mb-4",children:"💬"}),s.jsx("h2",{className:"text-2xl font-semibold text-foreground mb-2",children:"Chat"})]})}):i.map(D=>s.jsx(Se.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`flex flex-col gap-2 ${D.type==="user"?"items-end":"items-start"}`,children:s.jsx("div",{className:"w-full",children:D.type==="user"&&D.userBranches?s.jsx("div",{className:"flex justify-end",children:s.jsx("div",{className:"relative group max-w-[80%] min-w-0",children:u===D.id?s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{value:g,onChange:m=>f(m.target.value),className:"w-full min-h-[60px] p-4 pr-20 text-sm bg-background dark:bg-[#0D0D0D] text-foreground dark:text-white rounded-xl border dark:border-gray-800 resize-none focus:outline-none focus:ring-1 focus:ring-primary [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-border [&::-webkit-scrollbar-thumb]:rounded-full",onKeyDown:m=>{m.key==="Enter"&&!m.shiftKey&&(m.preventDefault(),G(D.id)),m.key==="Escape"&&de()},autoFocus:!0}),s.jsxs("div",{className:"absolute bottom-2 right-2 flex items-center gap-1",children:[s.jsx("button",{onClick:de,className:"w-6 h-6 rounded-full bg-muted text-muted-foreground hover:text-destructive transition-colors flex items-center justify-center",children:s.jsx(De,{size:12})}),s.jsx("button",{onClick:()=>G(D.id),className:"w-6 h-6 rounded-full bg-primary text-primary-foreground dark:bg-[#1a1a1a] dark:text-white hover:bg-primary/90 dark:hover:bg-[#2a2a2a] transition-colors flex items-center justify-center",children:s.jsx(cn,{size:12})})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-primary text-primary-foreground dark:bg-[#1a1a1a] dark:text-white rounded-xl px-4 py-3 shadow-md border dark:border-gray-800",children:s.jsx("p",{className:"text-sm break-words",children:D.userBranches?.[x[D.id]??0]??""})}),s.jsxs("div",{className:"flex justify-end items-center gap-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[s.jsx("button",{onClick:()=>be(D.id,(x[D.id]??0)-1),className:"p-1 text-muted-foreground hover:text-foreground transition-colors",disabled:(x[D.id]??0)===0,children:s.jsx(Hn,{size:12})}),s.jsxs("span",{className:"text-xs text-muted-foreground",children:[(x[D.id]??0)+1," of ",D.userBranches?.length??0]}),s.jsx("button",{onClick:()=>be(D.id,(x[D.id]??0)+1),className:"p-1 text-muted-foreground hover:text-foreground transition-colors",disabled:(x[D.id]??0)===(D.userBranches?.length??1)-1,children:s.jsx(Gn,{size:12})}),s.jsx("button",{onClick:()=>T(D.id,D.userBranches?.[x[D.id]??0]??""),className:"p-1 text-muted-foreground hover:text-foreground transition-colors",children:s.jsx(ir,{size:12})})]})]})})}):D.type==="ai"&&!D.isThinking?s.jsx("div",{className:"flex justify-start",children:s.jsx("p",{className:"text-sm break-words text-foreground max-w-[80%]",children:re(D)})}):s.jsx("div",{className:`flex flex-col ${D.type==="user"?"items-end":"items-start"}`,children:s.jsxs("div",{className:"relative group max-w-[80%] min-w-0",children:[D.type==="user"?u===D.id?s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{value:g,onChange:m=>f(m.target.value),className:"w-full min-h-[60px] p-4 pr-20 text-sm bg-background dark:bg-[#0D0D0D] text-foreground dark:text-white rounded-xl border dark:border-gray-800 resize-none focus:outline-none focus:ring-1 focus:ring-primary [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-border [&::-webkit-scrollbar-thumb]:rounded-full",onKeyDown:m=>{m.key==="Enter"&&!m.shiftKey&&(m.preventDefault(),G(D.id)),m.key==="Escape"&&de()},autoFocus:!0}),s.jsxs("div",{className:"absolute bottom-2 right-2 flex items-center gap-1",children:[s.jsx("button",{onClick:de,className:"w-6 h-6 rounded-full bg-muted text-muted-foreground hover:text-destructive transition-colors flex items-center justify-center",children:s.jsx(De,{size:12})}),s.jsx("button",{onClick:()=>G(D.id),className:"w-6 h-6 rounded-full bg-primary text-primary-foreground dark:bg-[#1a1a1a] dark:text-white hover:bg-primary/90 dark:hover:bg-[#2a2a2a] transition-colors flex items-center justify-center",children:s.jsx(cn,{size:12})})]})]}):s.jsx("div",{className:"bg-primary text-primary-foreground dark:bg-[#1a1a1a] dark:text-white rounded-xl px-4 py-3 shadow-md border dark:border-gray-800",children:s.jsx("p",{className:"text-sm break-words",children:D.content})}):D.isThinking?s.jsx("p",{className:"text-sm text-foreground",children:s.jsxs("span",{className:"animate-[pulse_0.5s_ease-in-out_infinite] font-medium",children:["Thinking",s.jsx("span",{className:"animate-[bounce_0.6s_ease-in-out_infinite] inline-block ml-1",children:"."}),s.jsx("span",{className:"animate-[bounce_0.6s_ease-in-out_0.2s_infinite] inline-block",children:"."}),s.jsx("span",{className:"animate-[bounce_0.6s_ease-in-out_0.4s_infinite] inline-block",children:"."})]})}):s.jsx("p",{className:"text-sm break-words text-foreground max-w-[80%]",children:D.content}),D.type==="user"&&u!==D.id&&s.jsx("div",{className:"flex justify-end items-center gap-3 mt-1",children:s.jsx("button",{onClick:()=>T(D.id,D.content),className:"p-1 text-muted-foreground hover:text-foreground transition-colors opacity-0 group-hover:opacity-100",children:s.jsx(ir,{size:12})})})]})})})},D.id)),s.jsx("div",{ref:N})]}),s.jsx("div",{className:"p-4",children:s.jsxs("div",{className:"bg-card dark:bg-[#0D0D0D] rounded-[2rem] border shadow-lg p-6 backdrop-blur-sm",children:[j.length>0&&s.jsx("div",{className:"mb-4",children:s.jsx("div",{className:"flex flex-wrap gap-2",children:j.map(D=>s.jsxs("div",{className:"relative group",children:[D.type.startsWith("image/")?s.jsxs(lt,{children:[s.jsx(Ut,{asChild:!0,children:s.jsx("div",{className:"w-16 h-16 rounded-lg overflow-hidden bg-muted dark:bg-[#1a1a1a] border dark:border-gray-800 cursor-pointer hover:opacity-80 transition-opacity p-1",children:s.jsx("img",{src:D.preview,alt:D.name,className:"w-full h-full object-cover rounded"})})}),s.jsxs(dt,{className:"max-w-3xl bg-card dark:bg-[#1a1a1a] dark:border-gray-800",children:[s.jsx(ct,{children:s.jsx(ut,{className:"text-foreground dark:text-white",children:D.name})}),s.jsx("div",{className:"flex justify-center bg-background dark:bg-[#0D0D0D] rounded-lg p-4",children:s.jsx("img",{src:D.preview,alt:D.name,className:"max-w-full max-h-[70vh] object-contain rounded"})})]})]}):s.jsx("div",{className:"relative flex items-center bg-muted dark:bg-neutral-800 rounded-xl px-3 py-2 w-32 h-16 shadow-md",children:s.jsxs("div",{className:"flex items-center gap-2 w-full",children:[s.jsx("div",{className:"flex-shrink-0",children:Re(D.type,D.name)}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-xs font-medium text-foreground dark:text-white truncate",children:D.name}),s.jsx("p",{className:"text-xs text-muted-foreground",children:B(D.type,D.name)})]})]})}),s.jsx("button",{onClick:()=>C(D.id),className:"absolute -top-1 -right-1 h-5 w-5 rounded-full bg-background text-foreground hover:bg-muted border border-border transition-colors flex items-center justify-center",children:s.jsx(De,{className:"h-3 w-3"})})]},D.id))})}),s.jsxs("form",{onSubmit:ae,className:"flex flex-col gap-3",children:[s.jsx("textarea",{value:t,onChange:D=>r(D.target.value),placeholder:"Ask me about your grievances, civic issues, or any concerns...",className:"min-h-[80px] w-full resize-none border-0 bg-transparent p-0 text-base text-foreground placeholder:text-muted-foreground focus-visible:outline-none leading-relaxed [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-border [&::-webkit-scrollbar-thumb]:rounded-full",onKeyDown:D=>{D.key==="Enter"&&!D.shiftKey&&(D.preventDefault(),ae(D))}}),s.jsxs("div",{className:"flex items-center justify-between pt-2",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(fn,{children:s.jsxs(gn,{children:[s.jsx(hn,{asChild:!0,children:s.jsx("button",{type:"button",onClick:S,className:"inline-flex items-center justify-center w-8 h-8 rounded-full text-foreground transition-colors hover:bg-muted",disabled:j.length>=5,children:s.jsx(Oo,{size:16})})}),s.jsx(Zt,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx("p",{children:"Add Files"})})]})}),M?s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("div",{className:"relative",children:[s.jsx($r,{variant:"ring",size:24,className:"text-red-500"}),s.jsx("button",{type:"button",onClick:le,className:"absolute inset-0 flex items-center justify-center w-6 h-6 bg-red-500 rounded-full text-white hover:bg-red-600 transition-colors",children:s.jsx("div",{className:"w-2 h-2 bg-white rounded-sm"})})]}),s.jsx("span",{className:"text-sm font-mono text-red-500 min-w-[3rem] font-medium",children:ve(F)})]}):s.jsx(fn,{children:s.jsxs(gn,{children:[s.jsx(hn,{asChild:!0,children:s.jsx("button",{type:"button",onClick:ie,className:"inline-flex items-center justify-center w-8 h-8 rounded-full text-foreground transition-colors hover:bg-muted",children:s.jsx(To,{size:16})})}),s.jsx(Zt,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx("p",{children:"Voice Input"})})]})})]}),s.jsx(fn,{children:s.jsxs(gn,{children:[s.jsx(hn,{asChild:!0,children:s.jsx("button",{type:"submit",disabled:!t.trim()||a!=="ready",className:`inline-flex items-center justify-center transition-all duration-300 disabled:opacity-50 disabled:pointer-events-none ${a==="submitted"||a==="streaming"?"w-auto h-auto bg-transparent p-0":"w-8 h-8 rounded-full bg-primary text-primary-foreground hover:bg-primary/90"}`,children:s.jsx("div",{className:"transition-all duration-300",children:a==="submitted"||a==="streaming"?s.jsx($r,{variant:"ellipsis",size:16}):s.jsx(cn,{size:16})})})}),s.jsx(Zt,{className:"bg-card dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:s.jsx("p",{children:"Send Message"})})]})})]})]})]})})]})]})}const Vu=Object.freeze(Object.defineProperty({__proto__:null,ChatPage:ju},Symbol.toStringTag,{value:"Module"}));function Br(e,n){try{var t=e()}catch(r){return n(r)}return t&&t.then?t.then(void 0,n):t}function Nu(e,n){for(var t={};e.length;){var r=e[0],o=r.code,a=r.message,i=r.path.join(".");if(!t[i])if("unionErrors"in r){var l=r.unionErrors[0].errors[0];t[i]={message:l.message,type:l.code}}else t[i]={message:a,type:o};if("unionErrors"in r&&r.unionErrors.forEach(function(u){return u.errors.forEach(function(h){return e.push(h)})}),n){var d=t[i].types,c=d&&d[r.code];t[i]=ns(i,n,t,o,c?[].concat(c,r.message):r.message)}e.shift()}return t}function Du(e,n){for(var t={};e.length;){var r=e[0],o=r.code,a=r.message,i=r.path.join(".");if(!t[i])if(r.code==="invalid_union"){var l=r.errors[0][0];t[i]={message:l.message,type:l.code}}else t[i]={message:a,type:o};if(r.code==="invalid_union"&&r.errors.forEach(function(u){return u.forEach(function(h){return e.push(h)})}),n){var d=t[i].types,c=d&&d[r.code];t[i]=ns(i,n,t,o,c?[].concat(c,r.message):r.message)}e.shift()}return t}function Cu(e,n,t){if(t===void 0&&(t={}),function(r){return"_def"in r&&typeof r._def=="object"&&"typeName"in r._def}(e))return function(r,o,a){try{return Promise.resolve(Br(function(){return Promise.resolve(e[t.mode==="sync"?"parse":"parseAsync"](r,n)).then(function(i){return a.shouldUseNativeValidation&&dr({},a),{errors:{},values:t.raw?Object.assign({},r):i}})},function(i){if(function(l){return Array.isArray(l?.issues)}(i))return{values:{},errors:cr(Nu(i.errors,!a.shouldUseNativeValidation&&a.criteriaMode==="all"),a)};throw i}))}catch(i){return Promise.reject(i)}};if(function(r){return"_zod"in r&&typeof r._zod=="object"}(e))return function(r,o,a){try{return Promise.resolve(Br(function(){return Promise.resolve((t.mode==="sync"?fa:ga)(e,r,n)).then(function(i){return a.shouldUseNativeValidation&&dr({},a),{errors:{},values:t.raw?Object.assign({},r):i}})},function(i){if(function(l){return l instanceof ha}(i))return{values:{},errors:cr(Du(i.issues,!a.shouldUseNativeValidation&&a.criteriaMode==="all"),a)};throw i}))}catch(i){return Promise.reject(i)}};throw new Error("Invalid input: not a Zod schema")}const Su=pa,Xs=p.createContext({}),xe=({...e})=>s.jsx(Xs.Provider,{value:{name:e.name},children:s.jsx(xa,{...e})}),nn=()=>{const e=p.useContext(Xs),n=p.useContext(Ks),{getFieldState:t,formState:r}=va(),o=t(e.name,r);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:a}=n;return{id:a,name:e.name,formItemId:`${a}-form-item`,formDescriptionId:`${a}-form-item-description`,formMessageId:`${a}-form-item-message`,...o}},Ks=p.createContext({}),me=p.forwardRef(({className:e,...n},t)=>{const r=p.useId();return s.jsx(Ks.Provider,{value:{id:r},children:s.jsx("div",{ref:t,className:E("space-y-2",e),...n})})});me.displayName="FormItem";const fe=p.forwardRef(({className:e,...n},t)=>{const{error:r,formItemId:o}=nn();return s.jsx(Me,{ref:t,className:E(r&&"text-destructive",e),htmlFor:o,...n})});fe.displayName="FormLabel";const ge=p.forwardRef(({...e},n)=>{const{error:t,formItemId:r,formDescriptionId:o,formMessageId:a}=nn();return s.jsx(aa,{ref:n,id:r,"aria-describedby":t?`${o} ${a}`:`${o}`,"aria-invalid":!!t,...e})});ge.displayName="FormControl";const Bn=p.forwardRef(({className:e,...n},t)=>{const{formDescriptionId:r}=nn();return s.jsx("p",{ref:t,id:r,className:E("text-sm text-muted-foreground",e),...n})});Bn.displayName="FormDescription";const je=p.forwardRef(({className:e,children:n,...t},r)=>{const{error:o,formMessageId:a}=nn(),i=o?String(o?.message):n;return i?s.jsx("p",{ref:r,id:a,className:E("text-sm font-medium text-destructive",e),...t,children:i}):null});je.displayName="FormMessage";const Qs=p.forwardRef(({className:e,...n},t)=>s.jsx(Jr,{className:E("grid gap-2",e),...n,ref:t}));Qs.displayName=Jr.displayName;const Js=p.forwardRef(({className:e,...n},t)=>s.jsx(es,{ref:t,className:E("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),...n,children:s.jsx(ia,{className:"flex items-center justify-center",children:s.jsx(Bo,{className:"h-2.5 w-2.5 fill-current text-current"})})}));Js.displayName=es.displayName;const eo=p.forwardRef(({className:e,value:n,...t},r)=>s.jsx(ts,{ref:r,className:E("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...t,children:s.jsx(la,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(n||0)}%)`}})}));eo.displayName=ts.displayName;const ku=Vt({title:Pe().min(10,"Title must be at least 10 characters").max(100,"Title must not exceed 100 characters").regex(/^[a-zA-Z0-9\s\-\.\,\!\?]+$/,"Title contains invalid characters"),description:Pe().min(20,"Description must be at least 20 characters").max(2e3,"Description must not exceed 2000 characters"),category:mn(["infrastructure","utilities","transportation","healthcare","education","environment","safety","corruption","other"]),priority:mn(["low","medium","high","urgent"]),location:Vt({address:Pe().max(500).optional(),city:Pe().max(100).optional(),state:Pe().max(100).optional(),district:Pe().max(50).optional(),pincode:Pe().regex(/^[0-9]{6}$/,"Please enter a valid 6-digit pincode").optional(),coordinates:Vt({latitude:mr().default(0),longitude:mr().default(0)}).optional()}).optional(),department:Pe().max(100).optional(),tags:wa(Pe()).optional(),isPublic:ur().default(!0),isAnonymous:ur().default(!1),contactInfo:Vt({phone:Pe().regex(/^[0-9]{10}$/,"Please enter a valid 10-digit phone number").optional(),email:Pe().email("Please enter a valid email address").optional(),preferredContact:mn(["phone","email","none"]).default("none")}).optional()}),kt=[{id:1,title:"Details",icon:Ke},{id:2,title:"Location",icon:It},{id:3,title:"Attachments",icon:Qt},{id:4,title:"Review",icon:Be}],Mu=[{value:"infrastructure",label:"Infrastructure"},{value:"utilities",label:"Utilities"},{value:"transportation",label:"Transportation"},{value:"healthcare",label:"Healthcare"},{value:"education",label:"Education"},{value:"environment",label:"Environment"},{value:"safety",label:"Public Safety"},{value:"corruption",label:"Corruption"},{value:"other",label:"Other"}],Ru=[{value:"low",label:"Low",color:"bg-gray-500"},{value:"medium",label:"Medium",color:"bg-yellow-500"},{value:"high",label:"High",color:"bg-orange-500"},{value:"urgent",label:"Urgent",color:"bg-red-500"}],_u=[{value:"municipal",label:"Municipal Corporation"},{value:"water",label:"Water Department"},{value:"electricity",label:"Electricity Board"},{value:"transport",label:"Transport Department"},{value:"health",label:"Health Department"},{value:"education",label:"Education Department"},{value:"police",label:"Police Department"},{value:"fire",label:"Fire Department"},{value:"environment",label:"Environment Department"},{value:"other",label:"Other"}],Pu=[{value:"andhra-pradesh",label:"Andhra Pradesh"},{value:"arunachal-pradesh",label:"Arunachal Pradesh"},{value:"assam",label:"Assam"},{value:"bihar",label:"Bihar"},{value:"chhattisgarh",label:"Chhattisgarh"},{value:"goa",label:"Goa"},{value:"gujarat",label:"Gujarat"},{value:"haryana",label:"Haryana"},{value:"himachal-pradesh",label:"Himachal Pradesh"},{value:"jharkhand",label:"Jharkhand"},{value:"karnataka",label:"Karnataka"},{value:"kerala",label:"Kerala"},{value:"madhya-pradesh",label:"Madhya Pradesh"},{value:"maharashtra",label:"Maharashtra"},{value:"manipur",label:"Manipur"},{value:"meghalaya",label:"Meghalaya"},{value:"mizoram",label:"Mizoram"},{value:"nagaland",label:"Nagaland"},{value:"odisha",label:"Odisha"},{value:"punjab",label:"Punjab"},{value:"rajasthan",label:"Rajasthan"},{value:"sikkim",label:"Sikkim"},{value:"tamil-nadu",label:"Tamil Nadu"},{value:"telangana",label:"Telangana"},{value:"tripura",label:"Tripura"},{value:"uttar-pradesh",label:"Uttar Pradesh"},{value:"uttarakhand",label:"Uttarakhand"},{value:"west-bengal",label:"West Bengal"},{value:"delhi",label:"Delhi"},{value:"chandigarh",label:"Chandigarh"},{value:"puducherry",label:"Puducherry"}],Fu=({warning:e,onDismiss:n})=>s.jsx(Se.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"rounded-md border border-amber-500/50 px-4 py-3 text-amber-600 dark:text-amber-400 bg-amber-50 dark:bg-amber-900/20 mb-4",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("p",{className:"text-sm flex items-center",children:[s.jsx(qo,{className:"me-3 -mt-0.5 inline-flex opacity-60",size:16,"aria-hidden":"true"}),e.message]}),s.jsx("button",{onClick:()=>n(e.id),className:"ml-4 text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-200",children:s.jsx(De,{size:16})})]})});function Eu(){const{user:e}=$e(),[n,t]=p.useState(1),[r,o]=p.useState([]),[a,i]=p.useState(!1),[l,d]=p.useState(!1),[c,u]=p.useState(null),[h,g]=p.useState(""),[f,x]=p.useState(!1),[w,b]=p.useState(!1),[y,N]=p.useState([]),[j,k]=p.useState(new Set),[S,C]=p.useState(!1),M=m=>Array.prototype.map.call(new Uint8Array(m),v=>("00"+v.toString(16)).slice(-2)).join(""),_=async m=>new Promise((v,P)=>{const V=new FileReader;V.onload=async ne=>{try{const ee=ne.target?.result,Q=await crypto.subtle.digest("SHA-256",ee),ye=M(Q);v(ye)}catch(ee){P(ee)}},V.onerror=()=>P(new Error("Failed to read file")),V.readAsArrayBuffer(m)});p.useEffect(()=>{(async()=>{if(!window.mammoth){const v=document.createElement("script");v.src="https://cdn.jsdelivr.net/npm/mammoth@1.4.15/mammoth.browser.min.js",document.head.appendChild(v)}x(!0)})()},[]);const F=ba({resolver:Cu(ku),defaultValues:{title:"",description:"",category:"other",priority:"medium",location:{address:"",city:"",state:"",district:"",pincode:"",coordinates:{latitude:0,longitude:0}},department:"",tags:[],isPublic:!0,isAnonymous:!1,contactInfo:{phone:e?.mobile||"",email:e?.gmail||"",preferredContact:"none"}}}),$=(m,v)=>{const P=[];switch(m){case"title":v&&v.trim().length>0&&v.trim().length<10&&P.push({id:`title-${Date.now()}`,field:"title",message:"Title must be at least 10 characters long",type:"warning"}),v&&v.trim().length===0&&P.push({id:`title-empty-${Date.now()}`,field:"title",message:"Title is required",type:"error"});break;case"description":v&&v.trim().length>0&&v.trim().length<20&&P.push({id:`description-${Date.now()}`,field:"description",message:"Description must be at least 20 characters long",type:"warning"}),v&&v.trim().length===0&&P.push({id:`description-empty-${Date.now()}`,field:"description",message:"Description is required",type:"error"});break;case"category":v||P.push({id:`category-${Date.now()}`,field:"category",message:"Please select a category",type:"error"});break;case"priority":v||P.push({id:`priority-${Date.now()}`,field:"priority",message:"Please select a priority level (Low, Medium, High, or Urgent)",type:"error"});break}return P},z=(m,v)=>{j.has(m)||k(V=>new Set([...V,m]));const P=$(m,v);N(V=>[...V.filter(ne=>ne.field!==m),...P])},H=m=>{N(v=>v.filter(P=>P.id!==m))};p.useEffect(()=>{y.forEach(m=>{const v=setTimeout(()=>{H(m.id)},5e3);return()=>clearTimeout(v)})},[y]);const ae=(m,v)=>{if(m.key==="Enter"){m.preventDefault();const P=F.getValues(v);z(v,P)}},ie=n/kt.length*100,le=()=>{const m=F.getValues();switch(n){case 1:return m.title&&m.title.length>=10&&m.description&&m.description.length>=20&&m.category&&m.priority;case 2:return!0;case 3:return!0;case 4:return!0;default:return!1}},ve=()=>{i(!0),setTimeout(()=>{i(!1),d(!0)},1e3)},Re=()=>{n<kt.length&&le()&&t(n+1)},B=()=>{n>1&&t(n-1)},T=async m=>{const v=Array.from(m.target.files||[]);if(v.length!==0){b(!0);try{const P=[],V=[];for(const Q of v){const ye=Kr(Q);ye.isValid?P.push(Q):V.push(`${Q.name}: ${ye.error}`)}if(V.length>0&&(alert(`File validation errors:
${V.join(`
`)}`),P.length===0)){b(!1);return}const ne=[],ee=new Set;for(const Q of P){const ye=await _(Q),qe=r.some(st=>st.hash===ye),rn=ee.has(ye);if(!qe&&!rn){const st=Q;st.hash=ye,(Q.type.startsWith("image/")||Q.type.startsWith("video/"))&&(st.preview=URL.createObjectURL(Q)),ne.push(st),ee.add(ye)}}if(r.length+ne.length>10){alert("Maximum 10 files allowed");return}o(Q=>[...Q,...ne])}catch(P){console.error("Error processing files:",P)}finally{b(!1),m.target.value=""}}},G=m=>{o(v=>{const P=[...v];return P[m].preview&&URL.revokeObjectURL(P[m].preview),P.splice(m,1),P})},{createGrievance:de}=Yn(),{showToast:be}=Na(),re=F.watch(),Y=p.useMemo(()=>({title:re.title||"Not specified",category:re.category||"Not selected",priority:re.priority,description:re.description||"No description provided",location:re.location||{}}),[re.title,re.category,re.priority,re.description,re.location]);p.useEffect(()=>{e&&(F.setValue("contactInfo.phone",e.mobile||""),F.setValue("contactInfo.email",e.gmail||"")),C(!0)},[e,F]);const we=p.useCallback(async m=>{i(!0),F.clearErrors();try{const v=new FormData;v.append("title",m.title),v.append("description",m.description),v.append("category",m.category),v.append("priority",m.priority),m.location&&(v.append("location[address]",m.location.address||""),v.append("location[city]",m.location.city||""),v.append("location[state]",m.location.state||""),v.append("location[district]",m.location.district||""),v.append("location[pincode]",m.location.pincode||""),m.location.coordinates?.latitude&&v.append("location[coordinates][latitude]",m.location.coordinates.latitude.toString()),m.location.coordinates?.longitude&&v.append("location[coordinates][longitude]",m.location.coordinates.longitude.toString())),m.department&&v.append("department",m.department),m.tags&&m.tags.length>0&&m.tags.forEach((P,V)=>{v.append(`tags[${V}]`,P)}),v.append("isPublic",m.isPublic.toString()),v.append("isAnonymous",m.isAnonymous.toString()),m.contactInfo&&(m.contactInfo.phone&&v.append("contactInfo[phone]",m.contactInfo.phone),m.contactInfo.email&&v.append("contactInfo[email]",m.contactInfo.email),v.append("contactInfo[preferredContact]",m.contactInfo.preferredContact)),r.forEach(P=>{v.append("attachments",P)}),await de(v),be("Grievance submitted successfully!","success"),d(!0)}catch(v){console.error("Failed to submit grievance:",v),be(v instanceof Error?v.message:"Failed to submit grievance. Please try again.","error")}finally{i(!1)}},[de,be,r,F]),D={hidden:{opacity:0,x:20},visible:{opacity:1,x:0},exit:{opacity:0,x:-20}};return S?l?s.jsx(Se.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"max-w-2xl mx-auto p-6",children:s.jsx(Ee,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-xl",children:s.jsxs(Le,{className:"p-8 text-center",children:[s.jsx(Se.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring"},children:s.jsx(Kt,{className:"h-16 w-16 text-green-500 mx-auto mb-4"})}),s.jsx("h2",{className:"text-2xl font-bold text-foreground mb-2",children:"Grievance Submitted Successfully!"}),s.jsxs("p",{className:"text-muted-foreground mb-6",children:["Your grievance has been submitted and assigned reference ID:"," ",s.jsxs("strong",{children:["GRV-2025-",Math.floor(Math.random()*1e3).toString().padStart(4,"0")]})]}),s.jsx(O,{onClick:()=>window.location.reload(),className:"bg-primary hover:bg-primary/90 rounded-lg",children:"Submit Another Grievance"})]})})}):s.jsx("div",{className:"h-screen overflow-y-auto p-4",children:s.jsxs(Se.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-4xl mx-auto",children:[s.jsxs(Ee,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-xl shadow-lg",children:[s.jsxs(at,{className:"pb-6",children:[s.jsx("div",{className:"flex items-center justify-between mb-4",children:s.jsxs(ft,{variant:"outline",className:"text-sm",children:["Step ",n," of ",kt.length]})}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(eo,{value:ie,className:"h-2"}),s.jsx("div",{className:"flex justify-between",children:kt.map(m=>s.jsxs("div",{className:`flex items-center gap-1 sm:gap-2 text-xs sm:text-sm ${m.id<=n?"text-primary":"text-muted-foreground"}`,children:[s.jsx(m.icon,{className:"h-3 w-3 sm:h-4 sm:w-4"}),s.jsx("span",{className:"hidden sm:inline",children:m.title})]},m.id))})]})]}),s.jsx(Su,{...F,children:s.jsxs("form",{onSubmit:F.handleSubmit(we),noValidate:!0,role:"form","aria-label":"Grievance submission form",className:"[&_input:invalid]:shadow-none [&_input]:shadow-none [&_textarea:invalid]:shadow-none [&_textarea]:shadow-none [&_select:invalid]:shadow-none [&_select]:shadow-none",children:[s.jsxs(Le,{className:"max-h-[60vh] overflow-y-auto",children:[s.jsx(On,{children:y.map(m=>s.jsx(Fu,{warning:m,onDismiss:H},m.id))}),s.jsxs(On,{mode:"wait",children:[n===1&&s.jsx(Se.div,{variants:D,initial:"hidden",animate:"visible",exit:"exit",className:"space-y-6",children:s.jsxs(Ee,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-lg",children:[s.jsx(at,{className:"pb-4",children:s.jsxs(Rt,{className:"flex items-center gap-3",children:[s.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:s.jsx(Ke,{className:"h-5 w-5 text-primary"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Grievance Details"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Provide basic information about your grievance"})]})]})}),s.jsxs(Le,{className:"space-y-4 sm:space-y-6 p-4 sm:p-6",children:[s.jsx(xe,{control:F.control,name:"title",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Grievance Title *"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:"Brief title describing your grievance",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",style:{WebkitBoxShadow:"0 0 0 1000px #0D0D0D inset",WebkitTextFillColor:"inherit",transition:"background-color 5000s ease-in-out 0s"},onKeyDown:v=>ae(v,"title"),onBlur:v=>z("title",v.target.value)})}),s.jsx("div",{className:"hidden",children:s.jsx(je,{})})]})}),s.jsx(xe,{control:F.control,name:"description",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Description *"}),s.jsx(ge,{children:s.jsx(da,{...m,placeholder:"Provide detailed description of your grievance...",className:"min-h-[120px] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",onKeyDown:v=>ae(v,"description"),onBlur:v=>z("description",v.target.value)})}),s.jsx("div",{className:"hidden",children:s.jsx(je,{})})]})}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(xe,{control:F.control,name:"category",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Category *"}),s.jsxs(Qe,{onValueChange:v=>{m.onChange(v),z("category",v)},defaultValue:m.value,children:[s.jsx(ge,{children:s.jsx(Je,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md",children:s.jsx(et,{placeholder:"Select category"})})}),s.jsx(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:Mu.map(v=>s.jsx(U,{value:v.value,children:v.label},v.value))})]}),s.jsx("div",{className:"hidden",children:s.jsx(je,{})})]})}),s.jsx(xe,{control:F.control,name:"priority",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Priority *"}),s.jsx(ge,{children:s.jsx(Qs,{onValueChange:v=>{m.onChange(v),z("priority",v)},value:m.value,className:"grid grid-cols-2 gap-3",children:Ru.map(v=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Js,{value:v.value,id:v.value}),s.jsxs(Me,{htmlFor:v.value,className:"flex items-center gap-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${v.color}`}),v.label]})]},v.value))})}),s.jsx("div",{className:"hidden",children:s.jsx(je,{})})]})})]})]})]})},"step1"),n===2&&s.jsxs(Se.div,{variants:D,initial:"hidden",animate:"visible",exit:"exit",className:"space-y-6",children:[s.jsxs(Ee,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-lg",children:[s.jsx(at,{className:"pb-4",children:s.jsxs(Rt,{className:"flex items-center gap-3",children:[s.jsx("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:s.jsx(It,{className:"h-5 w-5 text-blue-500"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Location Information"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Help us locate your grievance (Optional)"})]})]})}),s.jsxs(Le,{children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(xe,{control:F.control,name:"location.address",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Address"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:"Street address",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",style:{WebkitBoxShadow:"0 0 0 1000px #0D0D0D inset",WebkitTextFillColor:"#ffffff",color:"#ffffff",transition:"background-color 5000s ease-in-out 0s"}})}),s.jsx(je,{})]})}),s.jsx(xe,{control:F.control,name:"location.city",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"City"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:"City name",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",style:{WebkitBoxShadow:"0 0 0 1000px #0D0D0D inset",WebkitTextFillColor:"#ffffff",color:"#ffffff",transition:"background-color 5000s ease-in-out 0s"}})}),s.jsx(je,{})]})}),s.jsx(xe,{control:F.control,name:"location.state",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"State"}),s.jsxs(Qe,{onValueChange:m.onChange,defaultValue:m.value,children:[s.jsx(ge,{children:s.jsx(Je,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md",children:s.jsx(et,{placeholder:"Select state"})})}),s.jsx(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:Pu.map(v=>s.jsx(U,{value:v.label,children:v.label},v.value))})]}),s.jsx(je,{})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx(xe,{control:F.control,name:"location.district",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"District"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:"District name",className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",style:{WebkitBoxShadow:"0 0 0 1000px #0D0D0D inset",WebkitTextFillColor:"#ffffff",color:"#ffffff",transition:"background-color 5000s ease-in-out 0s"}})}),s.jsx(je,{})]})}),s.jsx(xe,{control:F.control,name:"location.pincode",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Pincode"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:"6-digit pincode",maxLength:6,className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md focus:bg-background dark:focus:bg-[#0D0D0D] focus-visible:bg-background dark:focus-visible:bg-[#0D0D0D] active:bg-background dark:active:bg-[#0D0D0D]",onChange:v=>{const P=v.target.value.replace(/\D/g,"");m.onChange(P)},style:{WebkitBoxShadow:"0 0 0 1000px #0D0D0D inset",WebkitTextFillColor:"#ffffff",color:"#ffffff",transition:"background-color 5000s ease-in-out 0s"}})}),s.jsx(je,{})]})})]})]})]}),s.jsxs(Ee,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-lg",children:[s.jsx(at,{className:"pb-4",children:s.jsxs(Rt,{className:"flex items-center gap-3",children:[s.jsx("div",{className:"p-2 bg-purple-500/10 rounded-lg",children:s.jsx(qr,{className:"h-5 w-5 text-purple-500"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Additional Information"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Department, contact preferences, and other details"})]})]})}),s.jsxs(Le,{className:"space-y-6",children:[s.jsx(xe,{control:F.control,name:"department",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Relevant Department (Optional)"}),s.jsxs(Qe,{onValueChange:m.onChange,defaultValue:m.value,children:[s.jsx(ge,{children:s.jsx(Je,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md",children:s.jsx(et,{placeholder:"Select department"})})}),s.jsx(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:_u.map(v=>s.jsx(U,{value:v.value,children:v.label},v.value))})]}),s.jsx(je,{})]})}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("h4",{className:"text-sm font-medium text-foreground",children:"Contact Information"}),s.jsx(ft,{variant:"secondary",className:"text-xs",children:"Auto-populated from profile"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(xe,{control:F.control,name:"contactInfo.phone",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Phone Number"}),s.jsx(ge,{children:s.jsx(se,{...m,placeholder:e?.mobile||"10-digit phone number",maxLength:10,className:"bg-muted/50 border-border dark:border-gray-800 rounded-md",onChange:v=>{const P=v.target.value.replace(/\D/g,"");m.onChange(P)}})}),s.jsx(je,{})]})}),s.jsx(xe,{control:F.control,name:"contactInfo.email",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Email Address"}),s.jsx(ge,{children:s.jsx(se,{...m,type:"email",placeholder:e?.gmail||"<EMAIL>",className:"bg-muted/50 border-border dark:border-gray-800 rounded-md"})}),s.jsx(je,{})]})})]}),s.jsx(xe,{control:F.control,name:"contactInfo.preferredContact",render:({field:m})=>s.jsxs(me,{children:[s.jsx(fe,{className:"text-foreground",children:"Preferred Contact Method"}),s.jsxs(Qe,{onValueChange:m.onChange,defaultValue:m.value,children:[s.jsx(ge,{children:s.jsx(Je,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800 rounded-md",children:s.jsx(et,{placeholder:"Select contact preference"})})}),s.jsxs(tt,{className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(U,{value:"none",children:"No follow-up needed"}),s.jsx(U,{value:"email",children:"Email"}),s.jsx(U,{value:"phone",children:"Phone"})]})]}),s.jsx(je,{})]})})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"text-sm font-medium text-foreground",children:"Privacy Settings"}),s.jsxs("div",{className:"space-y-3",children:[s.jsx(xe,{control:F.control,name:"isPublic",render:({field:m})=>s.jsxs(me,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(fe,{className:"text-base",children:"Public Grievance"}),s.jsx(Bn,{children:"Allow others to view this grievance publicly"})]}),s.jsx(ge,{children:s.jsx(Ne,{checked:m.value,onCheckedChange:m.onChange})})]})}),s.jsx(xe,{control:F.control,name:"isAnonymous",render:({field:m})=>s.jsxs(me,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[s.jsxs("div",{className:"space-y-0.5",children:[s.jsx(fe,{className:"text-base",children:"Anonymous Submission"}),s.jsx(Bn,{children:"Hide your identity from public view"})]}),s.jsx(ge,{children:s.jsx(Ne,{checked:m.value,onCheckedChange:m.onChange})})]})})]})]})]})]})]},"step2"),n===3&&s.jsx(Se.div,{variants:D,initial:"hidden",animate:"visible",exit:"exit",className:"space-y-6",children:s.jsxs(Ee,{className:"bg-background/50 dark:bg-[#0D0D0D]/50 border-border dark:border-gray-800 shadow-lg",children:[s.jsx(at,{className:"pb-4",children:s.jsxs(Rt,{className:"flex items-center gap-3",children:[s.jsx("div",{className:"p-2 bg-green-500/10 rounded-lg",children:s.jsx(Qt,{className:"h-5 w-5 text-green-500"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Document Attachments"}),s.jsx("p",{className:"text-sm text-muted-foreground",children:"Upload supporting documents (Optional - Max 10 files)"})]})]})}),s.jsxs(Le,{children:[s.jsxs("div",{className:"border-2 border-dashed border-border dark:border-gray-800 rounded-lg p-8 text-center bg-background/50 dark:bg-[#0D0D0D]/50",children:[s.jsx(Ho,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),s.jsx("p",{className:"text-foreground mb-2",children:"Drag and drop files here, or click to browse"}),s.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Supports: Images, Videos, Audio, PDFs, Documents (Max 10MB each, 10 files total)"}),s.jsx("input",{type:"file",multiple:!0,accept:"image/*,video/*,audio/*,.pdf,.docx,.txt,.ppt,.pptx,.xls,.xlsx",onChange:T,className:"hidden",id:"file-upload"}),s.jsx(O,{type:"button",variant:"outline",onClick:()=>document.getElementById("file-upload")?.click(),disabled:w,className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:w?"Processing...":"Choose Files"})]}),r.length>0&&s.jsxs("div",{className:"space-y-3",children:[s.jsxs("h4",{className:"font-medium text-foreground",children:["Uploaded Files (",r.length,")"]}),s.jsx(un,{className:"max-h-[40vh]",children:s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 pr-4",children:r.map((m,v)=>s.jsxs("div",{className:"flex items-center gap-3 p-3 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg border border-border dark:border-gray-800",children:[m.preview?m.type.startsWith("video/")?s.jsx("video",{src:m.preview,className:"w-10 h-10 object-cover rounded",muted:!0}):s.jsx("img",{src:m.preview,alt:m.name,className:"w-10 h-10 object-cover rounded"}):m.type.startsWith("audio/")||m.type.includes("mp3")||m.type.includes("wav")||m.type.includes("ogg")||m.type.includes("m4a")||m.type.includes("flac")||m.type.includes("aac")?s.jsx(Go,{className:"w-10 h-10 text-purple-500"}):s.jsx(Ke,{className:"w-10 h-10 text-muted-foreground"}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-foreground truncate",children:m.name}),s.jsxs("p",{className:"text-xs text-muted-foreground",children:[(m.size/1024/1024).toFixed(2)," ","MB"]})]}),s.jsxs("div",{className:"flex gap-1",children:[(m.type.startsWith("image/")||m.type.startsWith("video/"))&&s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(m),className:"text-primary hover:text-primary",children:s.jsx(Be,{className:"h-4 w-4"})}),(m.type==="application/pdf"||m.name.toLowerCase().endsWith(".pdf"))&&s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(m),className:"text-white hover:text-gray-200",children:s.jsx(Be,{className:"h-4 w-4"})}),(m.type.startsWith("audio/")||m.type.includes("mp3")||m.type.includes("wav")||m.type.includes("ogg")||m.type.includes("m4a")||m.type.includes("flac")||m.type.includes("aac"))&&s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(m),className:"text-white hover:text-gray-200",children:s.jsx(Be,{className:"h-4 w-4"})}),(m.type.includes("text")||m.type.includes("txt")||m.name.toLowerCase().endsWith(".txt"))&&s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(m),className:"text-white hover:text-gray-200",children:s.jsx(Be,{className:"h-4 w-4"})}),(m.type.includes("docx")||m.name.toLowerCase().endsWith(".docx"))&&s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(m),className:"text-white hover:text-gray-200",children:s.jsx(Be,{className:"h-4 w-4"})}),s.jsx(O,{type:"button",variant:"ghost",size:"sm",onClick:()=>G(v),className:"text-destructive hover:text-destructive",children:s.jsx(De,{className:"h-4 w-4"})})]})]},v))})})]})]})]})},"step3"),n===4&&s.jsxs(Se.div,{variants:D,initial:"hidden",animate:"visible",exit:"exit",className:"space-y-6",children:[s.jsxs("div",{className:"text-center mb-6",children:[s.jsx(Yo,{className:"h-12 w-12 text-primary mx-auto mb-2"}),s.jsx("h3",{className:"text-lg font-semibold text-foreground",children:"Review Your Grievance"}),s.jsx("p",{className:"text-muted-foreground",children:"Please review all details before submitting"})]}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 dark:from-primary/20 dark:to-primary/10 rounded-xl p-6 border border-primary/20",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"p-2 bg-primary/20 rounded-lg",children:s.jsx(Ke,{className:"h-5 w-5 text-primary"})}),s.jsx("h4",{className:"text-xl font-bold text-foreground",children:"Grievance Summary"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"p-3 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Title"}),s.jsx("p",{className:"text-sm font-semibold text-foreground mt-1",children:Y.title})]}),s.jsxs("div",{className:"p-3 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Category"}),s.jsx("p",{className:"text-sm font-semibold text-foreground mt-1",children:Y.category})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"p-3 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Priority"}),s.jsx("div",{className:"mt-1",children:Y.priority?s.jsx(ft,{className:`${Y.priority==="high"?"bg-red-500 hover:bg-red-600":Y.priority==="medium"?"bg-yellow-500 hover:bg-yellow-600":"bg-gray-500 hover:bg-gray-600"} text-white`,children:Y.priority?.toUpperCase()}):s.jsx("span",{className:"text-sm text-muted-foreground",children:"Not selected"})})]}),s.jsxs("div",{className:"p-3 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Reference ID"}),s.jsx("p",{className:"text-sm font-semibold text-foreground mt-1",children:"Will be generated after submission"})]})]})]})]}),s.jsxs("div",{className:"p-6 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-xl border border-border dark:border-gray-800",children:[s.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-2 h-2 bg-primary rounded-full"}),"Description"]}),s.jsx("div",{className:"p-4 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg border-l-4 border-primary",children:s.jsx("p",{className:"text-sm text-foreground leading-relaxed",children:Y.description})})]}),(Y.location.address||Y.location.city||Y.location.district||Y.location.pincode)&&s.jsxs("div",{className:"p-6 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-xl border border-border dark:border-gray-800",children:[s.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[s.jsx(It,{className:"h-4 w-4 text-primary"}),"Location Details"]}),s.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[Y.location.address&&s.jsxs("div",{className:"p-3 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground",children:"Address"}),s.jsx("p",{className:"text-sm text-foreground",children:Y.location.address})]}),Y.location.city&&s.jsxs("div",{className:"p-3 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground",children:"City"}),s.jsx("p",{className:"text-sm text-foreground",children:Y.location.city})]}),Y.location.district&&s.jsxs("div",{className:"p-3 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground",children:"District"}),s.jsx("p",{className:"text-sm text-foreground",children:Y.location.district})]}),Y.location.pincode&&s.jsxs("div",{className:"p-3 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg",children:[s.jsx("p",{className:"text-xs font-medium text-muted-foreground",children:"Postal Code"}),s.jsx("p",{className:"text-sm text-foreground",children:Y.location.pincode})]})]})]}),r.length>0&&s.jsxs("div",{className:"p-6 bg-background/50 dark:bg-[#0D0D0D]/50 rounded-xl border border-border dark:border-gray-800",children:[s.jsxs("h4",{className:"font-semibold text-foreground mb-3 flex items-center gap-2",children:[s.jsx(Qt,{className:"h-4 w-4 text-primary"}),"Attached Documents (",r.length,"/10)"]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3",children:r.map((m,v)=>s.jsxs("div",{className:"p-3 bg-muted/30 dark:bg-[#0D0D0D]/30 rounded-lg border border-border/50",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[m.type.startsWith("image/")&&s.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),m.type.startsWith("video/")&&s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),m.type.startsWith("audio/")&&s.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),m.type.includes("pdf")&&s.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),!m.type.startsWith("image/")&&!m.type.startsWith("video/")&&!m.type.startsWith("audio/")&&!m.type.includes("pdf")&&s.jsx("div",{className:"w-2 h-2 bg-gray-500 rounded-full"}),s.jsx("p",{className:"text-xs font-medium text-foreground truncate",children:m.name})]}),s.jsxs("p",{className:"text-xs text-muted-foreground",children:[(m.size/1024/1024).toFixed(2)," MB"]})]},v))})]}),s.jsx("div",{className:"p-4 bg-primary/5 dark:bg-primary/10 rounded-lg border border-primary/20",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx(Kt,{className:"h-5 w-5 text-primary mt-0.5"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-foreground",children:"Ready for Submission"}),s.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Your grievance will be reviewed by the appropriate department and you'll receive updates via email and notifications."})]})]})})]})]},"step4")]})]}),s.jsxs(ca,{className:"flex justify-between pt-6",children:[s.jsxs(O,{type:"button",variant:"outline",onClick:B,disabled:n===1,className:"bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(Hn,{className:"h-4 w-4 mr-2"}),"Previous"]}),n<kt.length?s.jsxs(O,{type:"button",onClick:Re,disabled:!le(),className:`rounded-lg ${le()?"bg-primary hover:bg-primary/90":"bg-gray-400 cursor-not-allowed"}`,children:["Next",s.jsx(Gn,{className:"h-4 w-4 ml-2"})]}):s.jsx(O,{type:"button",onClick:ve,disabled:a,className:"bg-primary hover:bg-primary/90 rounded-lg",children:a?"Submitting...":"Submit Grievance"})]})]})})]}),c&&s.jsx(lt,{open:!!c,onOpenChange:()=>u(null),children:s.jsxs(dt,{className:"max-w-4xl max-h-[90vh] bg-background dark:bg-[#0D0D0D] border-border dark:border-gray-800",children:[s.jsx(ct,{children:s.jsx(ut,{className:"text-foreground",children:c.name})}),s.jsxs("div",{className:"flex items-center justify-center p-4 min-h-[400px]",children:[c.type.startsWith("image/")&&s.jsx("img",{src:c.preview,alt:c.name,className:"max-w-full max-h-[60vh] object-contain rounded-lg"}),c.type.startsWith("video/")&&s.jsx("video",{src:c.preview,controls:!0,className:"max-w-full max-h-[60vh] rounded-lg"}),c.type.includes("pdf")&&s.jsx("object",{data:c.preview||URL.createObjectURL(c),type:"application/pdf",className:"w-full h-[70vh]",children:s.jsx("embed",{src:c.preview||URL.createObjectURL(c),type:"application/pdf",className:"w-full h-[70vh]"})}),!c.type.startsWith("image/")&&!c.type.startsWith("video/")&&!c.type.startsWith("audio/")&&!c.type.includes("pdf")&&!c.type.includes("document")&&!c.type.includes("word")&&!c.type.includes("text")&&!c.type.includes("doc")&&!c.type.includes("docx")&&s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"mb-4",children:s.jsx("div",{className:"w-16 h-16 mx-auto bg-gray-500/20 rounded-full flex items-center justify-center",children:s.jsx(Ke,{className:"h-8 w-8 text-gray-500"})})}),s.jsx("p",{className:"text-foreground mb-2 font-medium",children:c.name}),s.jsxs("p",{className:"text-sm text-muted-foreground mb-4",children:["Size: ",(c.size/1024/1024).toFixed(2)," MB"]}),s.jsxs(O,{onClick:()=>{const m=document.createElement("a");m.href=c.preview,m.download=c.name,m.click()},className:"bg-primary hover:bg-primary/90",children:[s.jsx(gt,{className:"h-4 w-4 mr-2"}),"Download File"]})]}),(c.type.startsWith("audio/")||c.type.includes("mp3")||c.type.includes("wav")||c.type.includes("ogg")||c.type.includes("m4a")||c.type.includes("flac")||c.type.includes("aac"))&&s.jsx("audio",{src:c.preview||URL.createObjectURL(c),controls:!0,className:"w-full h-12"}),(c.type.includes("txt")||c.type.includes("text"))&&s.jsx(un,{className:"w-full h-[70vh]",children:s.jsx("pre",{className:"text-foreground text-sm font-mono whitespace-pre-wrap p-4",children:(()=>{const m=new FileReader;return m.onload=v=>g(v.target?.result),m.readAsText(c),h})()})}),(c.type.includes("docx")||c.name.toLowerCase().endsWith(".docx"))&&s.jsx(un,{className:"w-full h-[70vh]",children:s.jsx("div",{className:"p-4 text-foreground text-sm",children:(()=>{if(!f)return s.jsx("div",{className:"text-muted-foreground",children:"Loading DOCX viewer..."});const m=new FileReader;return m.onload=async v=>{const P=v.target?.result;if(window.mammoth)try{const V=await window.mammoth.convertToHtml({arrayBuffer:P});g(V.value)}catch{g("Error reading DOCX file")}},m.readAsArrayBuffer(c),s.jsx("div",{dangerouslySetInnerHTML:{__html:h},className:"text-foreground"})})()})})]}),s.jsx("div",{className:"flex justify-center mt-4",children:s.jsxs(O,{onClick:()=>{const m=document.createElement("a");m.href=c.preview||URL.createObjectURL(c),m.download=c.name,m.click()},className:"bg-primary hover:bg-primary/90",children:[s.jsx(gt,{className:"h-4 w-4 mr-2"}),"Download File"]})})]})})]})}):s.jsxs(Ee,{className:"w-full max-w-4xl mx-auto",children:[s.jsxs(at,{children:[s.jsx(ot,{className:"h-8 w-64"}),s.jsx(ot,{className:"h-4 w-96"})]}),s.jsxs(Le,{className:"space-y-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsx(ot,{className:"h-10 w-full"}),s.jsx(ot,{className:"h-10 w-full"})]}),s.jsx(ot,{className:"h-32 w-full"}),s.jsxs("div",{className:"flex gap-4",children:[s.jsx(ot,{className:"h-10 w-24"}),s.jsx(ot,{className:"h-10 w-24"})]})]})]})}const Bu=Object.freeze(Object.defineProperty({__proto__:null,GrievanceForm:Eu},Symbol.toStringTag,{value:"Module"}));export{Bt as A,_s as C,se as I,Ps as P,ot as S,fn as T,gn as a,hn as b,Zt as c,Fs as d,Kn as e,Un as f,$u as g,zu as h,Vu as i,Bu as j,Lu as p,At as u};
