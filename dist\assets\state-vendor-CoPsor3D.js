import{R as a}from"./ui-vendor-CJ9vBRYM.js";const S=t=>{let e;const n=new Set,c=(s,u)=>{const o=typeof s=="function"?s(e):s;if(!Object.is(o,e)){const f=e;e=u??(typeof o!="object"||o===null)?o:Object.assign({},e,o),n.forEach(g=>g(e,f))}},i=()=>e,r={setState:c,getState:i,getInitialState:()=>b,subscribe:s=>(n.add(s),()=>n.delete(s))},b=e=t(c,i,r);return r},d=t=>t?S(t):S,I=t=>t;function j(t,e=I){const n=a.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return a.useDebugValue(n),n}const l=t=>{const e=d(t),n=c=>j(e,c);return Object.assign(n,e),n},x=t=>t?l(t):l;export{x as c};
