import{r as js,g as go}from"./react-vendor-DavUf6mE.js";function vo(t,e){for(var n=0;n<e.length;n++){const s=e[n];if(typeof s!="string"&&!Array.isArray(s)){for(const i in s)if(i!=="default"&&!(i in t)){const r=Object.getOwnPropertyDescriptor(s,i);r&&Object.defineProperty(t,i,r.get?r:{enumerable:!0,get:()=>s[i]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}var ne={exports:{}},vt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Tn;function xo(){if(Tn)return vt;Tn=1;var t=js(),e=Symbol.for("react.element"),n=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,r={key:!0,ref:!0,__self:!0,__source:!0};function o(a,c,u){var l,h={},d=null,f=null;u!==void 0&&(d=""+u),c.key!==void 0&&(d=""+c.key),c.ref!==void 0&&(f=c.ref);for(l in c)s.call(c,l)&&!r.hasOwnProperty(l)&&(h[l]=c[l]);if(a&&a.defaultProps)for(l in c=a.defaultProps,c)h[l]===void 0&&(h[l]=c[l]);return{$$typeof:e,type:a,key:d,ref:f,props:h,_owner:i.current}}return vt.Fragment=n,vt.jsx=o,vt.jsxs=o,vt}var kn;function To(){return kn||(kn=1,ne.exports=xo()),ne.exports}var G=To(),y=js();const ko=go(y),$h=vo({__proto__:null,default:ko},[y]);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mo=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),wo=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,n,s)=>s?s.toUpperCase():n.toLowerCase()),Mn=t=>{const e=wo(t);return e.charAt(0).toUpperCase()+e.slice(1)},Fs=(...t)=>t.filter((e,n,s)=>!!e&&e.trim()!==""&&s.indexOf(e)===n).join(" ").trim(),Po=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var bo={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=y.forwardRef(({color:t="currentColor",size:e=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:i="",children:r,iconNode:o,...a},c)=>y.createElement("svg",{ref:c,...bo,width:e,height:e,stroke:t,strokeWidth:s?Number(n)*24/Number(e):n,className:Fs("lucide",i),...!r&&!Po(a)&&{"aria-hidden":"true"},...a},[...o.map(([u,l])=>y.createElement(u,l)),...Array.isArray(r)?r:[r]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=(t,e)=>{const n=y.forwardRef(({className:s,...i},r)=>y.createElement(Ao,{ref:r,iconNode:e,className:Fs(`lucide-${Mo(Mn(t))}`,`lucide-${t}`,s),...i}));return n.displayName=Mn(t),n};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const So=[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]],Uh=p("activity",So);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vo=[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]],zh=p("archive",Vo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Co=[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]],qh=p("arrow-left",Co);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Do=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]],Hh=p("arrow-right",Do);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ro=[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]],Wh=p("arrow-up-down",Ro);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eo=[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]],Kh=p("arrow-up",Eo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],Gh=p("bell",Lo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _o=[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]],Xh=p("bookmark",_o);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const No=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],Yh=p("building",No);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jo=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Zh=p("calendar",jo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fo=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],Jh=p("chart-column",Fo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Qh=p("check",Bo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Io=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],td=p("chevron-down",Io);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oo=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],ed=p("chevron-left",Oo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],nd=p("chevron-right",$o);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uo=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],sd=p("chevron-up",Uo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zo=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],id=p("chevrons-up-down",zo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qo=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],od=p("circle-alert",qo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],rd=p("circle-check-big",Ho);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]],ad=p("circle-x",Wo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ko=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],cd=p("circle",Ko);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Go=[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]],ld=p("clipboard-check",Go);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xo=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],ud=p("clock",Xo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yo=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],hd=p("copy",Yo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zo=[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]],dd=p("crown",Zo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jo=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],fd=p("download",Jo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qo=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],pd=p("ellipsis",Qo);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],md=p("external-link",tr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const er=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],yd=p("eye-off",er);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nr=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],gd=p("eye",nr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sr=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]],vd=p("file-spreadsheet",sr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ir=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],xd=p("file-text",ir);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]],Td=p("file",or);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rr=[["path",{d:"M4 22V4a1 1 0 0 1 .4-.8A6 6 0 0 1 8 2c3 0 5 2 7.333 2q2 0 3.067-.8A1 1 0 0 1 20 4v10a1 1 0 0 1-.4.8A6 6 0 0 1 16 16c-3 0-5-2-8-2a6 6 0 0 0-4 1.528",key:"1jaruq"}]],kd=p("flag",rr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Md=p("funnel",ar);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],wd=p("globe",cr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]],Pd=p("history",lr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ur=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],bd=p("house",ur);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=[["path",{d:"M16 5h6",key:"1vod17"}],["path",{d:"M19 2v6",key:"4bpg5p"}],["path",{d:"M21 11.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7.5",key:"1ue2ih"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}]],Ad=p("image-plus",hr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dr=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],Sd=p("image",dr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],Vd=p("loader-circle",fr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=[["path",{d:"M22 12a1 1 0 0 1-10 0 1 1 0 0 0-10 0",key:"1lzz15"}],["path",{d:"M7 20.7a1 1 0 1 1 5-8.7 1 1 0 1 0 5-8.6",key:"1gnrpi"}],["path",{d:"M7 3.3a1 1 0 1 1 5 8.6 1 1 0 1 0 5 8.6",key:"u9yy5q"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Cd=p("loader-pinwheel",pr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]],Dd=p("loader",mr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yr=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],Rd=p("log-out",yr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gr=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],Ed=p("mail",gr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vr=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],Ld=p("map-pin",vr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xr=[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]],_d=p("megaphone",xr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],Nd=p("message-circle",Tr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],jd=p("message-square",kr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mr=[["path",{d:"M12 19v3",key:"npa21l"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["rect",{x:"9",y:"2",width:"6",height:"13",rx:"3",key:"s6n7sd"}]],Fd=p("mic",Mr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Bd=p("monitor",wr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pr=[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]],Id=p("moon",Pr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const br=[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]],Od=p("music",br);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ar=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],$d=p("panel-left-close",Ar);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sr=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Ud=p("panel-left",Sr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m10 15-3-3 3-3",key:"1pgupc"}]],zd=p("panel-right-open",Vr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cr=[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]],qd=p("paperclip",Cr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dr=[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]],Hd=p("pencil",Dr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rr=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],Wd=p("phone",Rr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],Kd=p("play",Er);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Gd=p("plus",Lr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]],Xd=p("presentation",_r);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nr=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Yd=p("refresh-cw",Nr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jr=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],Zd=p("rotate-ccw",jr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fr=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Jd=p("search",Fr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Br=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Qd=p("send",Br);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ir=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],tf=p("settings",Ir);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Or=[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]],ef=p("share-2",Or);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $r=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],nf=p("shield",$r);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]],sf=p("smartphone",Ur);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zr=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],of=p("square-pen",zr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qr=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],rf=p("star",qr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hr=[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]],af=p("sun",Hr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wr=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],cf=p("tag",Wr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kr=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],lf=p("trash-2",Kr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gr=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],uf=p("trending-up",Gr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],hf=p("triangle-alert",Xr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]],df=p("upload",Yr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zr=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],ff=p("user-check",Zr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jr=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],pf=p("user-plus",Jr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],mf=p("user",Qr);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ta=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],yf=p("users",ta);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ea=[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]],gf=p("video",ea);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const na=[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]],vf=p("volume-2",na);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sa=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],xf=p("x",sa);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ia=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],Tf=p("zap",ia),Fe=y.createContext({});function Be(t){const e=y.useRef(null);return e.current===null&&(e.current=t()),e.current}const Ie=typeof window<"u",Bs=Ie?y.useLayoutEffect:y.useEffect,Yt=y.createContext(null);function Oe(t,e){t.indexOf(e)===-1&&t.push(e)}function $e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const X=(t,e,n)=>n>e?e:n<t?t:n;let Ue=()=>{};const Y={},Is=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function Os(t){return typeof t=="object"&&t!==null}const $s=t=>/^0[^.\s]+$/u.test(t);function ze(t){let e;return()=>(e===void 0&&(e=t()),e)}const U=t=>t,oa=(t,e)=>n=>e(t(n)),Lt=(...t)=>t.reduce(oa),At=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class qe{constructor(){this.subscriptions=[]}add(e){return Oe(this.subscriptions,e),()=>$e(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let r=0;r<i;r++){const o=this.subscriptions[r];o&&o(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const q=t=>t*1e3,H=t=>t/1e3;function Us(t,e){return e?t*(1e3/e):0}const zs=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,ra=1e-7,aa=12;function ca(t,e,n,s,i){let r,o,a=0;do o=e+(n-e)/2,r=zs(o,s,i)-t,r>0?n=o:e=o;while(Math.abs(r)>ra&&++a<aa);return o}function _t(t,e,n,s){if(t===e&&n===s)return U;const i=r=>ca(r,0,1,t,n);return r=>r===0||r===1?r:zs(i(r),e,s)}const qs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Hs=t=>e=>1-t(1-e),Ws=_t(.33,1.53,.69,.99),He=Hs(Ws),Ks=qs(He),Gs=t=>(t*=2)<1?.5*He(t):.5*(2-Math.pow(2,-10*(t-1))),We=t=>1-Math.sin(Math.acos(t)),Xs=Hs(We),Ys=qs(We),la=_t(.42,0,1,1),ua=_t(0,0,.58,1),Zs=_t(.42,0,.58,1),ha=t=>Array.isArray(t)&&typeof t[0]!="number",Js=t=>Array.isArray(t)&&typeof t[0]=="number",da={linear:U,easeIn:la,easeInOut:Zs,easeOut:ua,circIn:We,circInOut:Ys,circOut:Xs,backIn:He,backInOut:Ks,backOut:Ws,anticipate:Gs},fa=t=>typeof t=="string",wn=t=>{if(Js(t)){Ue(t.length===4);const[e,n,s,i]=t;return _t(e,n,s,i)}else if(fa(t))return da[t];return t},Ft=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function pa(t,e){let n=new Set,s=new Set,i=!1,r=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function c(l){o.has(l)&&(u.schedule(l),t()),l(a)}const u={schedule:(l,h=!1,d=!1)=>{const m=d&&i?n:s;return h&&o.add(l),m.has(l)||m.add(l),l},cancel:l=>{s.delete(l),o.delete(l)},process:l=>{if(a=l,i){r=!0;return}i=!0,[n,s]=[s,n],n.forEach(c),n.clear(),i=!1,r&&(r=!1,u.process(l))}};return u}const ma=40;function Qs(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},r=()=>n=!0,o=Ft.reduce((x,b)=>(x[b]=pa(r),x),{}),{setup:a,read:c,resolveKeyframes:u,preUpdate:l,update:h,preRender:d,render:f,postRender:m}=o,v=()=>{const x=Y.useManualTiming?i.timestamp:performance.now();n=!1,Y.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(x-i.timestamp,ma),1)),i.timestamp=x,i.isProcessing=!0,a.process(i),c.process(i),u.process(i),l.process(i),h.process(i),d.process(i),f.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(v))},k=()=>{n=!0,s=!0,i.isProcessing||t(v)};return{schedule:Ft.reduce((x,b)=>{const M=o[b];return x[b]=(A,S=!1,P=!1)=>(n||k(),M.schedule(A,S,P)),x},{}),cancel:x=>{for(let b=0;b<Ft.length;b++)o[Ft[b]].cancel(x)},state:i,steps:o}}const{schedule:V,cancel:Q,state:_,steps:se}=Qs(typeof requestAnimationFrame<"u"?requestAnimationFrame:U,!0);let $t;function ya(){$t=void 0}const I={now:()=>($t===void 0&&I.set(_.isProcessing||Y.useManualTiming?_.timestamp:performance.now()),$t),set:t=>{$t=t,queueMicrotask(ya)}},ti=t=>e=>typeof e=="string"&&e.startsWith(t),Ke=ti("--"),ga=ti("var(--"),Ge=t=>ga(t)?va.test(t.split("/*")[0].trim()):!1,va=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,mt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},St={...mt,transform:t=>X(0,1,t)},Bt={...mt,default:1},kt=t=>Math.round(t*1e5)/1e5,Xe=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function xa(t){return t==null}const Ta=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ye=(t,e)=>n=>!!(typeof n=="string"&&Ta.test(n)&&n.startsWith(t)||e&&!xa(n)&&Object.prototype.hasOwnProperty.call(n,e)),ei=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,r,o,a]=s.match(Xe);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},ka=t=>X(0,255,t),ie={...mt,transform:t=>Math.round(ka(t))},it={test:Ye("rgb","red"),parse:ei("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ie.transform(t)+", "+ie.transform(e)+", "+ie.transform(n)+", "+kt(St.transform(s))+")"};function Ma(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const ge={test:Ye("#"),parse:Ma,transform:it.transform},Nt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),J=Nt("deg"),W=Nt("%"),w=Nt("px"),wa=Nt("vh"),Pa=Nt("vw"),Pn={...W,parse:t=>W.parse(t)/100,transform:t=>W.transform(t*100)},ct={test:Ye("hsl","hue"),parse:ei("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+W.transform(kt(e))+", "+W.transform(kt(n))+", "+kt(St.transform(s))+")"},E={test:t=>it.test(t)||ge.test(t)||ct.test(t),parse:t=>it.test(t)?it.parse(t):ct.test(t)?ct.parse(t):ge.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?it.transform(t):ct.transform(t),getAnimatableNone:t=>{const e=E.parse(t);return e.alpha=0,E.transform(e)}},ba=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Aa(t){return isNaN(t)&&typeof t=="string"&&(t.match(Xe)?.length||0)+(t.match(ba)?.length||0)>0}const ni="number",si="color",Sa="var",Va="var(",bn="${}",Ca=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Vt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(Ca,c=>(E.test(c)?(s.color.push(r),i.push(si),n.push(E.parse(c))):c.startsWith(Va)?(s.var.push(r),i.push(Sa),n.push(c)):(s.number.push(r),i.push(ni),n.push(parseFloat(c))),++r,bn)).split(bn);return{values:n,split:a,indexes:s,types:i}}function ii(t){return Vt(t).values}function oi(t){const{split:e,types:n}=Vt(t),s=e.length;return i=>{let r="";for(let o=0;o<s;o++)if(r+=e[o],i[o]!==void 0){const a=n[o];a===ni?r+=kt(i[o]):a===si?r+=E.transform(i[o]):r+=i[o]}return r}}const Da=t=>typeof t=="number"?0:E.test(t)?E.getAnimatableNone(t):t;function Ra(t){const e=ii(t);return oi(t)(e.map(Da))}const tt={test:Aa,parse:ii,createTransformer:oi,getAnimatableNone:Ra};function oe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ea({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,r=0,o=0;if(!e)i=r=o=n;else{const a=n<.5?n*(1+e):n+e-n*e,c=2*n-a;i=oe(c,a,t+1/3),r=oe(c,a,t),o=oe(c,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(r*255),blue:Math.round(o*255),alpha:s}}function Ht(t,e){return n=>n>0?e:t}const C=(t,e,n)=>t+(e-t)*n,re=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},La=[ge,it,ct],_a=t=>La.find(e=>e.test(t));function An(t){const e=_a(t);if(!e)return!1;let n=e.parse(t);return e===ct&&(n=Ea(n)),n}const Sn=(t,e)=>{const n=An(t),s=An(e);if(!n||!s)return Ht(t,e);const i={...n};return r=>(i.red=re(n.red,s.red,r),i.green=re(n.green,s.green,r),i.blue=re(n.blue,s.blue,r),i.alpha=C(n.alpha,s.alpha,r),it.transform(i))},ve=new Set(["none","hidden"]);function Na(t,e){return ve.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function ja(t,e){return n=>C(t,e,n)}function Ze(t){return typeof t=="number"?ja:typeof t=="string"?Ge(t)?Ht:E.test(t)?Sn:Ia:Array.isArray(t)?ri:typeof t=="object"?E.test(t)?Sn:Fa:Ht}function ri(t,e){const n=[...t],s=n.length,i=t.map((r,o)=>Ze(r)(r,e[o]));return r=>{for(let o=0;o<s;o++)n[o]=i[o](r);return n}}function Fa(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Ze(t[i])(t[i],e[i]));return i=>{for(const r in s)n[r]=s[r](i);return n}}function Ba(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],o=t.indexes[r][s[r]],a=t.values[o]??0;n[i]=a,s[r]++}return n}const Ia=(t,e)=>{const n=tt.createTransformer(e),s=Vt(t),i=Vt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?ve.has(t)&&!i.values.length||ve.has(e)&&!s.values.length?Na(t,e):Lt(ri(Ba(s,i),i.values),n):Ht(t,e)};function ai(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?C(t,e,n):Ze(t)(t,e)}const Oa=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>V.update(e,n),stop:()=>Q(e),now:()=>_.isProcessing?_.timestamp:I.now()}},ci=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let r=0;r<i;r++)s+=Math.round(t(r/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Wt=2e4;function Je(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Wt;)e+=n,s=t.next(e);return e>=Wt?1/0:e}function $a(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Je(s),Wt);return{type:"keyframes",ease:r=>s.next(i*r).value/e,duration:H(i)}}const Ua=5;function li(t,e,n){const s=Math.max(e-Ua,0);return Us(n-t(s),e-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ae=.001;function za({duration:t=D.duration,bounce:e=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let i,r,o=1-e;o=X(D.minDamping,D.maxDamping,o),t=X(D.minDuration,D.maxDuration,H(t)),o<1?(i=u=>{const l=u*o,h=l*t,d=l-n,f=xe(u,o),m=Math.exp(-h);return ae-d/f*m},r=u=>{const h=u*o*t,d=h*n+n,f=Math.pow(o,2)*Math.pow(u,2)*t,m=Math.exp(-h),v=xe(Math.pow(u,2),o);return(-i(u)+ae>0?-1:1)*((d-f)*m)/v}):(i=u=>{const l=Math.exp(-u*t),h=(u-n)*t+1;return-ae+l*h},r=u=>{const l=Math.exp(-u*t),h=(n-u)*(t*t);return l*h});const a=5/t,c=Ha(i,r,a);if(t=q(t),isNaN(c))return{stiffness:D.stiffness,damping:D.damping,duration:t};{const u=Math.pow(c,2)*s;return{stiffness:u,damping:o*2*Math.sqrt(s*u),duration:t}}}const qa=12;function Ha(t,e,n){let s=n;for(let i=1;i<qa;i++)s=s-t(s)/e(s);return s}function xe(t,e){return t*Math.sqrt(1-e*e)}const Wa=["duration","bounce"],Ka=["stiffness","damping","mass"];function Vn(t,e){return e.some(n=>t[n]!==void 0)}function Ga(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!Vn(t,Ka)&&Vn(t,Wa))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,r=2*X(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:D.mass,stiffness:i,damping:r}}else{const n=za(t);e={...e,...n,mass:D.mass},e.isResolvedFromDuration=!0}return e}function Kt(t=D.visualDuration,e=D.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const r=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:r},{stiffness:c,damping:u,mass:l,duration:h,velocity:d,isResolvedFromDuration:f}=Ga({...n,velocity:-H(n.velocity||0)}),m=d||0,v=u/(2*Math.sqrt(c*l)),k=o-r,g=H(Math.sqrt(c/l)),T=Math.abs(k)<5;s||(s=T?D.restSpeed.granular:D.restSpeed.default),i||(i=T?D.restDelta.granular:D.restDelta.default);let x;if(v<1){const M=xe(g,v);x=A=>{const S=Math.exp(-v*g*A);return o-S*((m+v*g*k)/M*Math.sin(M*A)+k*Math.cos(M*A))}}else if(v===1)x=M=>o-Math.exp(-g*M)*(k+(m+g*k)*M);else{const M=g*Math.sqrt(v*v-1);x=A=>{const S=Math.exp(-v*g*A),P=Math.min(M*A,300);return o-S*((m+v*g*k)*Math.sinh(P)+M*k*Math.cosh(P))/M}}const b={calculatedDuration:f&&h||null,next:M=>{const A=x(M);if(f)a.done=M>=h;else{let S=M===0?m:0;v<1&&(S=M===0?q(m):li(x,M,A));const P=Math.abs(S)<=s,L=Math.abs(o-A)<=i;a.done=P&&L}return a.value=a.done?o:A,a},toString:()=>{const M=Math.min(Je(b),Wt),A=ci(S=>b.next(M*S).value,M,30);return M+"ms "+A},toTransition:()=>{}};return b}Kt.applyToOptions=t=>{const e=$a(t,100,Kt);return t.ease=e.ease,t.duration=q(e.duration),t.type="keyframes",t};function Te({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:c,restDelta:u=.5,restSpeed:l}){const h=t[0],d={done:!1,value:h},f=P=>a!==void 0&&P<a||c!==void 0&&P>c,m=P=>a===void 0?c:c===void 0||Math.abs(a-P)<Math.abs(c-P)?a:c;let v=n*e;const k=h+v,g=o===void 0?k:o(k);g!==k&&(v=g-h);const T=P=>-v*Math.exp(-P/s),x=P=>g+T(P),b=P=>{const L=T(P),j=x(P);d.done=Math.abs(L)<=u,d.value=d.done?g:j};let M,A;const S=P=>{f(d.value)&&(M=P,A=Kt({keyframes:[d.value,m(d.value)],velocity:li(x,P,d.value),damping:i,stiffness:r,restDelta:u,restSpeed:l}))};return S(0),{calculatedDuration:null,next:P=>{let L=!1;return!A&&M===void 0&&(L=!0,b(P),S(P)),M!==void 0&&P>=M?A.next(P-M):(!L&&b(P),d)}}}function Xa(t,e,n){const s=[],i=n||Y.mix||ai,r=t.length-1;for(let o=0;o<r;o++){let a=i(t[o],t[o+1]);if(e){const c=Array.isArray(e)?e[o]||U:e;a=Lt(c,a)}s.push(a)}return s}function Ya(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const r=t.length;if(Ue(r===e.length),r===1)return()=>e[0];if(r===2&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Xa(e,s,i),c=a.length,u=l=>{if(o&&l<t[0])return e[0];let h=0;if(c>1)for(;h<t.length-2&&!(l<t[h+1]);h++);const d=At(t[h],t[h+1],l);return a[h](d)};return n?l=>u(X(t[0],t[r-1],l)):u}function Za(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=At(0,e,s);t.push(C(n,1,i))}}function Ja(t){const e=[0];return Za(e,t.length-1),e}function Qa(t,e){return t.map(n=>n*e)}function tc(t,e){return t.map(()=>e||Zs).splice(0,t.length-1)}function Mt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=ha(s)?s.map(wn):wn(s),r={done:!1,value:e[0]},o=Qa(n&&n.length===e.length?n:Ja(e),t),a=Ya(o,e,{ease:Array.isArray(i)?i:tc(e,i)});return{calculatedDuration:t,next:c=>(r.value=a(c),r.done=c>=t,r)}}const ec=t=>t!==null;function Qe(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(ec),a=i<0||e&&n!=="loop"&&e%2===1?0:r.length-1;return!a||s===void 0?r[a]:s}const nc={decay:Te,inertia:Te,tween:Mt,keyframes:Mt,spring:Kt};function ui(t){typeof t.type=="string"&&(t.type=nc[t.type])}class tn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const sc=t=>t/100;class en extends tn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==I.now()&&this.tick(I.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;ui(e);const{type:n=Mt,repeat:s=0,repeatDelay:i=0,repeatType:r,velocity:o=0}=e;let{keyframes:a}=e;const c=n||Mt;c!==Mt&&typeof a[0]!="number"&&(this.mixKeyframes=Lt(sc,ai(a[0],a[1])),a=[0,100]);const u=c({...e,keyframes:a});r==="mirror"&&(this.mirroredGenerator=c({...e,keyframes:[...a].reverse(),velocity:-o})),u.calculatedDuration===null&&(u.calculatedDuration=Je(u));const{calculatedDuration:l}=u;this.calculatedDuration=l,this.resolvedDuration=l+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=u}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:c}=this;if(this.startTime===null)return s.next(0);const{delay:u=0,keyframes:l,repeat:h,repeatType:d,repeatDelay:f,type:m,onUpdate:v,finalKeyframe:k}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),T=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let x=this.currentTime,b=s;if(h){const P=Math.min(this.currentTime,i)/a;let L=Math.floor(P),j=P%1;!j&&P>=1&&(j=1),j===1&&L--,L=Math.min(L,h+1),!!(L%2)&&(d==="reverse"?(j=1-j,f&&(j-=f/a)):d==="mirror"&&(b=o)),x=X(0,1,j)*a}const M=T?{done:!1,value:l[0]}:b.next(x);r&&(M.value=r(M.value));let{done:A}=M;!T&&c!==null&&(A=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return S&&m!==Te&&(M.value=Qe(l,this.options,k,this.speed)),v&&v(M.value),S&&this.finish(),M}then(e,n){return this.finished.then(e,n)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(e){e=q(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(I.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;const{driver:e=Oa,startTime:n}=this.options;this.driver||(this.driver=e(i=>this.tick(i))),this.options.onPlay?.();const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(I.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}function ic(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const ot=t=>t*180/Math.PI,ke=t=>{const e=ot(Math.atan2(t[1],t[0]));return Me(e)},oc={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ke,rotateZ:ke,skewX:t=>ot(Math.atan(t[1])),skewY:t=>ot(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Me=t=>(t=t%360,t<0&&(t+=360),t),Cn=ke,Dn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Rn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),rc={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Dn,scaleY:Rn,scale:t=>(Dn(t)+Rn(t))/2,rotateX:t=>Me(ot(Math.atan2(t[6],t[5]))),rotateY:t=>Me(ot(Math.atan2(-t[2],t[0]))),rotateZ:Cn,rotate:Cn,skewX:t=>ot(Math.atan(t[4])),skewY:t=>ot(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function we(t){return t.includes("scale")?1:0}function Pe(t,e){if(!t||t==="none")return we(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=rc,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=oc,i=a}if(!i)return we(e);const r=s[e],o=i[1].split(",").map(cc);return typeof r=="function"?r(o):o[r]}const ac=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Pe(n,e)};function cc(t){return parseFloat(t.trim())}const yt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gt=new Set(yt),En=t=>t===mt||t===w,lc=new Set(["x","y","z"]),uc=yt.filter(t=>!lc.has(t));function hc(t){const e=[];return uc.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const rt={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Pe(e,"x"),y:(t,{transform:e})=>Pe(e,"y")};rt.translateX=rt.x;rt.translateY=rt.y;const at=new Set;let be=!1,Ae=!1,Se=!1;function hi(){if(Ae){const t=Array.from(at).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=hc(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([r,o])=>{s.getValue(r)?.set(o)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}Ae=!1,be=!1,at.forEach(t=>t.complete(Se)),at.clear()}function di(){at.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ae=!0)})}function dc(){Se=!0,di(),hi(),Se=!1}class nn{constructor(e,n,s,i,r,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=r,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(at.add(this),be||(be=!0,V.read(di),V.resolveKeyframes(hi))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const r=i?.get(),o=e[e.length-1];if(r!==void 0)e[0]=r;else if(s&&n){const a=s.readValue(n,o);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=o),i&&r===void 0&&i.set(e[0])}ic(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),at.delete(this)}cancel(){this.state==="scheduled"&&(at.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const fc=t=>t.startsWith("--");function pc(t,e,n){fc(e)?t.style.setProperty(e,n):t.style[e]=n}const mc=ze(()=>window.ScrollTimeline!==void 0),yc={};function gc(t,e){const n=ze(t);return()=>yc[e]??n()}const fi=gc(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Tt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ln={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Tt([0,.65,.55,1]),circOut:Tt([.55,0,1,.45]),backIn:Tt([.31,.01,.66,-.59]),backOut:Tt([.33,1.53,.69,.99])};function pi(t,e){if(t)return typeof t=="function"?fi()?ci(t,e):"ease-out":Js(t)?Tt(t):Array.isArray(t)?t.map(n=>pi(n,e)||Ln.easeOut):Ln[t]}function vc(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:c}={},u=void 0){const l={[e]:n};c&&(l.offset=c);const h=pi(a,i);Array.isArray(h)&&(l.easing=h);const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:o==="reverse"?"alternate":"normal"};return u&&(d.pseudoElement=u),t.animate(l,d)}function mi(t){return typeof t=="function"&&"applyToOptions"in t}function xc({type:t,...e}){return mi(t)&&fi()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class Tc extends tn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:a,onComplete:c}=e;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=e,Ue(typeof e.type!="string");const u=xc(e);this.animation=vc(n,s,i,u,r),u.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const l=Qe(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(l):pc(n,s,l),this.animation.cancel()}c?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const e=this.animation.effect?.getComputedTiming?.().duration||0;return H(Number(e))}get time(){return H(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=q(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&mc()?(this.animation.timeline=e,U):n(this)}}const yi={anticipate:Gs,backInOut:Ks,circInOut:Ys};function kc(t){return t in yi}function Mc(t){typeof t.ease=="string"&&kc(t.ease)&&(t.ease=yi[t.ease])}const _n=10;class wc extends Tc{constructor(e){Mc(e),ui(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:r,...o}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new en({...o,autoplay:!1}),c=q(this.finishedTime??this.time);n.setWithVelocity(a.sample(c-_n).value,a.sample(c).value,_n),a.stop()}}const Nn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(tt.test(t)||t==="0")&&!t.startsWith("url("));function Pc(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function bc(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const r=t[t.length-1],o=Nn(i,e),a=Nn(r,e);return!o||!a?!1:Pc(t)||(n==="spring"||mi(n))&&s}const Ac=new Set(["opacity","clipPath","filter","transform"]),Sc=ze(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Vc(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:r,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:c,transformTemplate:u}=e.owner.getProps();return Sc()&&n&&Ac.has(n)&&(n!=="transform"||!u)&&!c&&!s&&i!=="mirror"&&r!==0&&o!=="inertia"}const Cc=40;class Dc extends tn{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o="loop",keyframes:a,name:c,motionValue:u,element:l,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=I.now();const d={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:r,repeatType:o,name:c,motionValue:u,element:l,...h},f=l?.KeyframeResolver||nn;this.keyframeResolver=new f(a,(m,v,k)=>this.onKeyframesResolved(m,v,d,!k),c,u,l),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:c,isHandoff:u,onUpdate:l}=s;this.resolvedAt=I.now(),bc(e,r,o,a)||((Y.instantAnimations||!c)&&l?.(Qe(e,s,n)),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const d={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Cc?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},f=!u&&Vc(d)?new wc({...d,element:d.motionValue.owner.current}):new en(d);f.finished.then(()=>this.notifyFinished()).catch(U),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),dc()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Rc=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Ec(t){const e=Rc.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function gi(t,e,n=1){const[s,i]=Ec(t);if(!s)return;const r=window.getComputedStyle(e).getPropertyValue(s);if(r){const o=r.trim();return Is(o)?parseFloat(o):o}return Ge(i)?gi(i,e,n+1):i}function sn(t,e){return t?.[e]??t?.default??t}const vi=new Set(["width","height","top","left","right","bottom",...yt]),Lc={test:t=>t==="auto",parse:t=>t},xi=t=>e=>e.test(t),Ti=[mt,w,W,J,Pa,wa,Lc],jn=t=>Ti.find(xi(t));function _c(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||$s(t):!0}const Nc=new Set(["brightness","contrast","saturate","opacity"]);function jc(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Xe)||[];if(!s)return t;const i=n.replace(s,"");let r=Nc.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Fc=/\b([a-z-]*)\(.*?\)/gu,Ve={...tt,getAnimatableNone:t=>{const e=t.match(Fc);return e?e.map(jc).join(" "):t}},Fn={...mt,transform:Math.round},Bc={rotate:J,rotateX:J,rotateY:J,rotateZ:J,scale:Bt,scaleX:Bt,scaleY:Bt,scaleZ:Bt,skew:J,skewX:J,skewY:J,distance:w,translateX:w,translateY:w,translateZ:w,x:w,y:w,z:w,perspective:w,transformPerspective:w,opacity:St,originX:Pn,originY:Pn,originZ:w},on={borderWidth:w,borderTopWidth:w,borderRightWidth:w,borderBottomWidth:w,borderLeftWidth:w,borderRadius:w,radius:w,borderTopLeftRadius:w,borderTopRightRadius:w,borderBottomRightRadius:w,borderBottomLeftRadius:w,width:w,maxWidth:w,height:w,maxHeight:w,top:w,right:w,bottom:w,left:w,padding:w,paddingTop:w,paddingRight:w,paddingBottom:w,paddingLeft:w,margin:w,marginTop:w,marginRight:w,marginBottom:w,marginLeft:w,backgroundPositionX:w,backgroundPositionY:w,...Bc,zIndex:Fn,fillOpacity:St,strokeOpacity:St,numOctaves:Fn},Ic={...on,color:E,backgroundColor:E,outlineColor:E,fill:E,stroke:E,borderColor:E,borderTopColor:E,borderRightColor:E,borderBottomColor:E,borderLeftColor:E,filter:Ve,WebkitFilter:Ve},ki=t=>Ic[t];function Mi(t,e){let n=ki(t);return n!==Ve&&(n=tt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Oc=new Set(["auto","none","0"]);function $c(t,e,n){let s=0,i;for(;s<t.length&&!i;){const r=t[s];typeof r=="string"&&!Oc.has(r)&&Vt(r).values.length&&(i=t[s]),s++}if(i&&n)for(const r of e)t[r]=Mi(n,i)}class Uc extends nn{constructor(e,n,s,i,r){super(e,n,s,i,r,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let c=0;c<e.length;c++){let u=e[c];if(typeof u=="string"&&(u=u.trim(),Ge(u))){const l=gi(u,n.current);l!==void 0&&(e[c]=l),c===e.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!vi.has(s)||e.length!==2)return;const[i,r]=e,o=jn(i),a=jn(r);if(o!==a)if(En(o)&&En(a))for(let c=0;c<e.length;c++){const u=e[c];typeof u=="string"&&(e[c]=parseFloat(u))}else rt[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||_c(e[i]))&&s.push(i);s.length&&$c(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=rt[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const r=s.length-1,o=s[r];s[r]=rt[n](e.measureViewportBox(),window.getComputedStyle(e.current)),o!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,c])=>{e.getValue(a).set(c)}),this.resolveNoneKeyframes()}}function zc(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const wi=(t,e)=>e&&typeof t=="number"?e.transform(t):t;function Pi(t){return Os(t)&&"offsetHeight"in t}const Bn=30,qc=t=>!isNaN(parseFloat(t));class Hc{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{const r=I.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty();i&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=I.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=qc(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new qe);const s=this.events[e].add(n);return e==="change"?()=>{s(),V.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=I.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Bn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Bn);return Us(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ft(t,e){return new Hc(t,e)}const{schedule:rn}=Qs(queueMicrotask,!1),z={x:!1,y:!1};function bi(){return z.x||z.y}function Wc(t){return t==="x"||t==="y"?z[t]?null:(z[t]=!0,()=>{z[t]=!1}):z.x||z.y?null:(z.x=z.y=!0,()=>{z.x=z.y=!1})}function Ai(t,e){const n=zc(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function In(t){return!(t.pointerType==="touch"||bi())}function Kc(t,e,n={}){const[s,i,r]=Ai(t,n),o=a=>{if(!In(a))return;const{target:c}=a,u=e(c,a);if(typeof u!="function"||!c)return;const l=h=>{In(h)&&(u(h),c.removeEventListener("pointerleave",l))};c.addEventListener("pointerleave",l,i)};return s.forEach(a=>{a.addEventListener("pointerenter",o,i)}),r}const Si=(t,e)=>e?t===e?!0:Si(t,e.parentElement):!1,an=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,Gc=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Xc(t){return Gc.has(t.tagName)||t.tabIndex!==-1}const Ut=new WeakSet;function On(t){return e=>{e.key==="Enter"&&t(e)}}function ce(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const Yc=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=On(()=>{if(Ut.has(n))return;ce(n,"down");const i=On(()=>{ce(n,"up")}),r=()=>ce(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",r,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function $n(t){return an(t)&&!bi()}function Zc(t,e,n={}){const[s,i,r]=Ai(t,n),o=a=>{const c=a.currentTarget;if(!$n(a))return;Ut.add(c);const u=e(c,a),l=(f,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",d),Ut.has(c)&&Ut.delete(c),$n(f)&&typeof u=="function"&&u(f,{success:m})},h=f=>{l(f,c===window||c===document||n.useGlobalTarget||Si(c,f.target))},d=f=>{l(f,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",d,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,i),Pi(a)&&(a.addEventListener("focus",u=>Yc(u,i)),!Xc(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),r}function Vi(t){return Os(t)&&"ownerSVGElement"in t}function Jc(t){return Vi(t)&&t.tagName==="svg"}const N=t=>!!(t&&t.getVelocity),Qc=[...Ti,E,tt],tl=t=>Qc.find(xi(t)),cn=y.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class el extends y.Component{getSnapshotBeforeUpdate(e){const n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){const s=n.offsetParent,i=Pi(s)&&s.offsetWidth||0,r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function nl({children:t,isPresent:e,anchorX:n,root:s}){const i=y.useId(),r=y.useRef(null),o=y.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=y.useContext(cn);return y.useInsertionEffect(()=>{const{width:c,height:u,top:l,left:h,right:d}=o.current;if(e||!r.current||!c||!u)return;const f=n==="left"?`left: ${h}`:`right: ${d}`;r.current.dataset.motionPopId=i;const m=document.createElement("style");a&&(m.nonce=a);const v=s??document.head;return v.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${c}px !important;
            height: ${u}px !important;
            ${f}px !important;
            top: ${l}px !important;
          }
        `),()=>{v.removeChild(m),v.contains(m)&&v.removeChild(m)}},[e]),G.jsx(el,{isPresent:e,childRef:r,sizeRef:o,children:y.cloneElement(t,{ref:r})})}const sl=({children:t,initial:e,isPresent:n,onExitComplete:s,custom:i,presenceAffectsLayout:r,mode:o,anchorX:a,root:c})=>{const u=Be(il),l=y.useId();let h=!0,d=y.useMemo(()=>(h=!1,{id:l,initial:e,isPresent:n,custom:i,onExitComplete:f=>{u.set(f,!0);for(const m of u.values())if(!m)return;s&&s()},register:f=>(u.set(f,!1),()=>u.delete(f))}),[n,u,s]);return r&&h&&(d={...d}),y.useMemo(()=>{u.forEach((f,m)=>u.set(m,!1))},[n]),y.useEffect(()=>{!n&&!u.size&&s&&s()},[n]),o==="popLayout"&&(t=G.jsx(nl,{isPresent:n,anchorX:a,root:c,children:t})),G.jsx(Yt.Provider,{value:d,children:t})};function il(){return new Map}function Ci(t=!0){const e=y.useContext(Yt);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,r=y.useId();y.useEffect(()=>{if(t)return i(r)},[t]);const o=y.useCallback(()=>t&&s&&s(r),[r,s,t]);return!n&&s?[!1,o]:[!0]}const It=t=>t.key||"";function Un(t){const e=[];return y.Children.forEach(t,n=>{y.isValidElement(n)&&e.push(n)}),e}const kf=({children:t,custom:e,initial:n=!0,onExitComplete:s,presenceAffectsLayout:i=!0,mode:r="sync",propagate:o=!1,anchorX:a="left",root:c})=>{const[u,l]=Ci(o),h=y.useMemo(()=>Un(t),[t]),d=o&&!u?[]:h.map(It),f=y.useRef(!0),m=y.useRef(h),v=Be(()=>new Map),[k,g]=y.useState(h),[T,x]=y.useState(h);Bs(()=>{f.current=!1,m.current=h;for(let A=0;A<T.length;A++){const S=It(T[A]);d.includes(S)?v.delete(S):v.get(S)!==!0&&v.set(S,!1)}},[T,d.length,d.join("-")]);const b=[];if(h!==k){let A=[...h];for(let S=0;S<T.length;S++){const P=T[S],L=It(P);d.includes(L)||(A.splice(S,0,P),b.push(P))}return r==="wait"&&b.length&&(A=b),x(Un(A)),g(h),null}const{forceRender:M}=y.useContext(Fe);return G.jsx(G.Fragment,{children:T.map(A=>{const S=It(A),P=o&&!u?!1:h===T||d.includes(S),L=()=>{if(v.has(S))v.set(S,!0);else return;let j=!0;v.forEach(Z=>{Z||(j=!1)}),j&&(M?.(),x(m.current),o&&l?.(),s&&s())};return G.jsx(sl,{isPresent:P,initial:!f.current||n?void 0:!1,custom:e,presenceAffectsLayout:i,mode:r,root:c,onExitComplete:P?void 0:L,anchorX:a,children:A},S)})})},Di=y.createContext({strict:!1}),zn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},pt={};for(const t in zn)pt[t]={isEnabled:e=>zn[t].some(n=>!!e[n])};function ol(t){for(const e in t)pt[e]={...pt[e],...t[e]}}const rl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Gt(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rl.has(t)}let Ri=t=>!Gt(t);function al(t){typeof t=="function"&&(Ri=e=>e.startsWith("on")?!Gt(e):t(e))}try{al(require("@emotion/is-prop-valid").default)}catch{}function cl(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Ri(i)||n===!0&&Gt(i)||!e&&!Gt(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}const Zt=y.createContext({});function Jt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Ct(t){return typeof t=="string"||Array.isArray(t)}const ln=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],un=["initial",...ln];function Qt(t){return Jt(t.animate)||un.some(e=>Ct(t[e]))}function Ei(t){return!!(Qt(t)||t.variants)}function ll(t,e){if(Qt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Ct(n)?n:void 0,animate:Ct(s)?s:void 0}}return t.inherit!==!1?e:{}}function ul(t){const{initial:e,animate:n}=ll(t,y.useContext(Zt));return y.useMemo(()=>({initial:e,animate:n}),[qn(e),qn(n)])}function qn(t){return Array.isArray(t)?t.join(" "):t}const Dt={};function hl(t){for(const e in t)Dt[e]=t[e],Ke(e)&&(Dt[e].isCSSVariable=!0)}function Li(t,{layout:e,layoutId:n}){return gt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Dt[t]||t==="opacity")}const dl={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},fl=yt.length;function pl(t,e,n){let s="",i=!0;for(let r=0;r<fl;r++){const o=yt[r],a=t[o];if(a===void 0)continue;let c=!0;if(typeof a=="number"?c=a===(o.startsWith("scale")?1:0):c=parseFloat(a)===0,!c||n){const u=wi(a,on[o]);if(!c){i=!1;const l=dl[o]||o;s+=`${l}(${u}) `}n&&(e[o]=u)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function hn(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let o=!1,a=!1;for(const c in e){const u=e[c];if(gt.has(c)){o=!0;continue}else if(Ke(c)){i[c]=u;continue}else{const l=wi(u,on[c]);c.startsWith("origin")?(a=!0,r[c]=l):s[c]=l}}if(e.transform||(o||n?s.transform=pl(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:c="50%",originY:u="50%",originZ:l=0}=r;s.transformOrigin=`${c} ${u} ${l}`}}const dn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function _i(t,e,n){for(const s in e)!N(e[s])&&!Li(s,n)&&(t[s]=e[s])}function ml({transformTemplate:t},e){return y.useMemo(()=>{const n=dn();return hn(n,e,t),Object.assign({},n.vars,n.style)},[e])}function yl(t,e){const n=t.style||{},s={};return _i(s,n,t),Object.assign(s,ml(t,e)),s}function gl(t,e){const n={},s=yl(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const vl={offset:"stroke-dashoffset",array:"stroke-dasharray"},xl={offset:"strokeDashoffset",array:"strokeDasharray"};function Tl(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?vl:xl;t[r.offset]=w.transform(-s);const o=w.transform(e),a=w.transform(n);t[r.array]=`${o} ${a}`}function Ni(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:o=0,...a},c,u,l){if(hn(t,a,u),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=l?.transformBox??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&Tl(h,i,r,o,!1)}const ji=()=>({...dn(),attrs:{}}),Fi=t=>typeof t=="string"&&t.toLowerCase()==="svg";function kl(t,e,n,s){const i=y.useMemo(()=>{const r=ji();return Ni(r,e,Fi(s),t.transformTemplate,t.style),{...r.attrs,style:{...r.style}}},[e]);if(t.style){const r={};_i(r,t.style,t),i.style={...r,...i.style}}return i}const Ml=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function fn(t){return typeof t!="string"||t.includes("-")?!1:!!(Ml.indexOf(t)>-1||/[A-Z]/u.test(t))}function wl(t,e,n,{latestValues:s},i,r=!1){const a=(fn(t)?kl:gl)(e,s,i,t),c=cl(e,typeof t=="string",r),u=t!==y.Fragment?{...c,...a,ref:n}:{},{children:l}=e,h=y.useMemo(()=>N(l)?l.get():l,[l]);return y.createElement(t,{...u,children:h})}function Hn(t){const e=[{},{}];return t?.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function pn(t,e,n,s){if(typeof e=="function"){const[i,r]=Hn(s);e=e(n!==void 0?n:t.custom,i,r)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,r]=Hn(s);e=e(n!==void 0?n:t.custom,i,r)}return e}function zt(t){return N(t)?t.get():t}function Pl({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:bl(n,s,i,t),renderState:e()}}function bl(t,e,n,s){const i={},r=s(t,{});for(const d in r)i[d]=zt(r[d]);let{initial:o,animate:a}=t;const c=Qt(t),u=Ei(t);e&&u&&!c&&t.inherit!==!1&&(o===void 0&&(o=e.initial),a===void 0&&(a=e.animate));let l=n?n.initial===!1:!1;l=l||o===!1;const h=l?a:o;if(h&&typeof h!="boolean"&&!Jt(h)){const d=Array.isArray(h)?h:[h];for(let f=0;f<d.length;f++){const m=pn(t,d[f]);if(m){const{transitionEnd:v,transition:k,...g}=m;for(const T in g){let x=g[T];if(Array.isArray(x)){const b=l?x.length-1:0;x=x[b]}x!==null&&(i[T]=x)}for(const T in v)i[T]=v[T]}}}return i}const Bi=t=>(e,n)=>{const s=y.useContext(Zt),i=y.useContext(Yt),r=()=>Pl(t,e,s,i);return n?r():Be(r)};function mn(t,e,n){const{style:s}=t,i={};for(const r in s)(N(s[r])||e.style&&N(e.style[r])||Li(r,t)||n?.getValue(r)?.liveStyle!==void 0)&&(i[r]=s[r]);return i}const Al=Bi({scrapeMotionValuesFromProps:mn,createRenderState:dn});function Ii(t,e,n){const s=mn(t,e,n);for(const i in t)if(N(t[i])||N(e[i])){const r=yt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[r]=t[i]}return s}const Sl=Bi({scrapeMotionValuesFromProps:Ii,createRenderState:ji}),Vl=Symbol.for("motionComponentSymbol");function lt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Cl(t,e,n){return y.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):lt(n)&&(n.current=s))},[e])}const yn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Dl="framerAppearId",Oi="data-"+yn(Dl),$i=y.createContext({});function Rl(t,e,n,s,i){const{visualElement:r}=y.useContext(Zt),o=y.useContext(Di),a=y.useContext(Yt),c=y.useContext(cn).reducedMotion,u=y.useRef(null);s=s||o.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:r,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:c}));const l=u.current,h=y.useContext($i);l&&!l.projection&&i&&(l.type==="html"||l.type==="svg")&&El(u.current,n,i,h);const d=y.useRef(!1);y.useInsertionEffect(()=>{l&&d.current&&l.update(n,a)});const f=n[Oi],m=y.useRef(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return Bs(()=>{l&&(d.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),m.current&&l.animationState&&l.animationState.animateChanges())}),y.useEffect(()=>{l&&(!m.current&&l.animationState&&l.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1))}),l}function El(t,e,n,s){const{layoutId:i,layout:r,drag:o,dragConstraints:a,layoutScroll:c,layoutRoot:u,layoutCrossfade:l}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ui(t.parent)),t.projection.setOptions({layoutId:i,layout:r,alwaysMeasureLayout:!!o||a&&lt(a),visualElement:t,animationType:typeof r=="string"?r:"both",initialPromotionConfig:s,crossfade:l,layoutScroll:c,layoutRoot:u})}function Ui(t){if(t)return t.options.allowProjection!==!1?t.projection:Ui(t.parent)}function le(t,{forwardMotionProps:e=!1}={},n,s){n&&ol(n);const i=fn(t)?Sl:Al;function r(a,c){let u;const l={...y.useContext(cn),...a,layoutId:Ll(a)},{isStatic:h}=l,d=ul(a),f=i(a,h);if(!h&&Ie){_l();const m=Nl(l);u=m.MeasureLayout,d.visualElement=Rl(t,f,l,s,m.ProjectionNode)}return G.jsxs(Zt.Provider,{value:d,children:[u&&d.visualElement?G.jsx(u,{visualElement:d.visualElement,...l}):null,wl(t,a,Cl(f,d.visualElement,c),f,h,e)]})}r.displayName=`motion.${typeof t=="string"?t:`create(${t.displayName??t.name??""})`}`;const o=y.forwardRef(r);return o[Vl]=t,o}function Ll({layoutId:t}){const e=y.useContext(Fe).id;return e&&t!==void 0?e+"-"+t:t}function _l(t,e){y.useContext(Di).strict}function Nl(t){const{drag:e,layout:n}=pt;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}function jl(t,e){if(typeof Proxy>"u")return le;const n=new Map,s=(r,o)=>le(r,o,t,e),i=(r,o)=>s(r,o);return new Proxy(i,{get:(r,o)=>o==="create"?s:(n.has(o)||n.set(o,le(o,void 0,t,e)),n.get(o))})}function zi({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function Fl({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Bl(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function ue(t){return t===void 0||t===1}function Ce({scale:t,scaleX:e,scaleY:n}){return!ue(t)||!ue(e)||!ue(n)}function st(t){return Ce(t)||qi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function qi(t){return Wn(t.x)||Wn(t.y)}function Wn(t){return t&&t!=="0%"}function Xt(t,e,n){const s=t-n,i=e*s;return n+i}function Kn(t,e,n,s,i){return i!==void 0&&(t=Xt(t,i,s)),Xt(t,n,s)+e}function De(t,e=0,n=1,s,i){t.min=Kn(t.min,e,n,s,i),t.max=Kn(t.max,e,n,s,i)}function Hi(t,{x:e,y:n}){De(t.x,e.translate,e.scale,e.originPoint),De(t.y,n.translate,n.scale,n.originPoint)}const Gn=.999999999999,Xn=1.0000000000001;function Il(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let r,o;for(let a=0;a<i;a++){r=n[a],o=r.projectionDelta;const{visualElement:c}=r.options;c&&c.props.style&&c.props.style.display==="contents"||(s&&r.options.layoutScroll&&r.scroll&&r!==r.root&&ht(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,Hi(t,o)),s&&st(r.latestValues)&&ht(t,r.latestValues))}e.x<Xn&&e.x>Gn&&(e.x=1),e.y<Xn&&e.y>Gn&&(e.y=1)}function ut(t,e){t.min=t.min+e,t.max=t.max+e}function Yn(t,e,n,s,i=.5){const r=C(t.min,t.max,i);De(t,e,n,r,s)}function ht(t,e){Yn(t.x,e.x,e.scaleX,e.scale,e.originX),Yn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Wi(t,e){return zi(Bl(t.getBoundingClientRect(),e))}function Ol(t,e,n){const s=Wi(t,n),{scroll:i}=e;return i&&(ut(s.x,i.offset.x),ut(s.y,i.offset.y)),s}const Zn=()=>({translate:0,scale:1,origin:0,originPoint:0}),dt=()=>({x:Zn(),y:Zn()}),Jn=()=>({min:0,max:0}),R=()=>({x:Jn(),y:Jn()}),Re={current:null},Ki={current:!1};function $l(){if(Ki.current=!0,!!Ie)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Re.current=t.matches;t.addEventListener("change",e),e()}else Re.current=!1}const Ul=new WeakMap;function zl(t,e,n){for(const s in e){const i=e[s],r=n[s];if(N(i))t.addValue(s,i);else if(N(r))t.addValue(s,ft(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const o=t.getValue(s);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=t.getStaticValue(s);t.addValue(s,ft(o!==void 0?o:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Qn=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ql{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:r,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=nn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=I.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,V.render(this.render,!1,!0))};const{latestValues:c,renderState:u}=o;this.latestValues=c,this.baseTarget={...c},this.initialValues=n.initial?{...c}:{},this.renderState=u,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!r,this.isControllingVariants=Qt(n),this.isVariantNode=Ei(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:l,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const f=h[d];c[d]!==void 0&&N(f)&&f.set(c[d],!1)}}mount(e){this.current=e,Ul.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),Ki.current||$l(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Re.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Q(this.notifyUpdate),Q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=gt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&V.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),r(),o&&o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in pt){const n=pt[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const r=this.features[e];r.isMounted?r.update():(r.mount(),r.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):R()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Qn.length;s++){const i=Qn[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const r="on"+i,o=e[r];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=zl(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ft(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Is(s)||$s(s))?s=parseFloat(s):!tl(s)&&tt.test(n)&&(s=Mi(e,n)),this.setBaseTarget(e,N(s)?s.get():s)),N(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=pn(this.props,n,this.presenceContext?.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!N(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new qe),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}scheduleRenderMicrotask(){rn.render(this.render)}}class Gi extends ql{constructor(){super(...arguments),this.KeyframeResolver=Uc}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;N(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Xi(t,{style:e,vars:n},s,i){const r=t.style;let o;for(o in e)r[o]=e[o];i?.applyProjectionStyles(r,s);for(o in n)r.setProperty(o,n[o])}function Hl(t){return window.getComputedStyle(t)}class Wl extends Gi{constructor(){super(...arguments),this.type="html",this.renderInstance=Xi}readValueFromInstance(e,n){if(gt.has(n))return this.projection?.isProjecting?we(n):ac(e,n);{const s=Hl(e),i=(Ke(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Wi(e,n)}build(e,n,s){hn(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return mn(e,n,s)}}const Yi=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Kl(t,e,n,s){Xi(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Yi.has(i)?i:yn(i),e.attrs[i])}class Gl extends Gi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=R}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(gt.has(n)){const s=ki(n);return s&&s.default||0}return n=Yi.has(n)?n:yn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Ii(e,n,s)}build(e,n,s){Ni(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Kl(e,n,s,i)}mount(e){this.isSVGTag=Fi(e.tagName),super.mount(e)}}const Xl=(t,e)=>fn(t)?new Gl(e):new Wl(e,{allowProjection:t!==y.Fragment});function Rt(t,e,n){const s=t.getProps();return pn(s,e,n!==void 0?n:s.custom,t)}const Ee=t=>Array.isArray(t);function Yl(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ft(n))}function Zl(t){return Ee(t)?t[t.length-1]||0:t}function Jl(t,e){const n=Rt(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const o in r){const a=Zl(r[o]);Yl(t,o,a)}}function Ql(t){return!!(N(t)&&t.add)}function Le(t,e){const n=t.getValue("willChange");if(Ql(n))return n.add(e);if(!n&&Y.WillChange){const s=new Y.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Zi(t){return t.props[Oi]}const tu=t=>t!==null;function eu(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(tu),r=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[r]}const nu={type:"spring",stiffness:500,damping:25,restSpeed:10},su=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ou={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ru=(t,{keyframes:e})=>e.length>2?iu:gt.has(t)?t.startsWith("scale")?su(e[1]):nu:ou;function au({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:o,repeatDelay:a,from:c,elapsed:u,...l}){return!!Object.keys(l).length}const gn=(t,e,n,s={},i,r)=>o=>{const a=sn(s,t)||{},c=a.delay||s.delay||0;let{elapsed:u=0}=s;u=u-q(c);const l={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:i};au(a)||Object.assign(l,ru(t,l)),l.duration&&(l.duration=q(l.duration)),l.repeatDelay&&(l.repeatDelay=q(l.repeatDelay)),l.from!==void 0&&(l.keyframes[0]=l.from);let h=!1;if((l.type===!1||l.duration===0&&!l.repeatDelay)&&(l.duration=0,l.delay===0&&(h=!0)),(Y.instantAnimations||Y.skipAnimations)&&(h=!0,l.duration=0,l.delay=0),l.allowFlatten=!a.type&&!a.ease,h&&!r&&e.get()!==void 0){const d=eu(l.keyframes,a);if(d!==void 0){V.update(()=>{l.onUpdate(d),l.onComplete()});return}}return a.isSync?new en(l):new Dc(l)};function cu({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Ji(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(r=s);const c=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const l in a){const h=t.getValue(l,t.latestValues[l]??null),d=a[l];if(d===void 0||u&&cu(u,l))continue;const f={delay:n,...sn(r||{},l)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(d)&&d===m&&!f.velocity)continue;let v=!1;if(window.MotionHandoffAnimation){const g=Zi(t);if(g){const T=window.MotionHandoffAnimation(g,l,V);T!==null&&(f.startTime=T,v=!0)}}Le(t,l),h.start(gn(l,h,d,t.shouldReduceMotion&&vi.has(l)?{type:!1}:f,t,v));const k=h.animation;k&&c.push(k)}return o&&Promise.all(c).then(()=>{V.update(()=>{o&&Jl(t,o)})}),c}function _e(t,e,n={}){const s=Rt(t,e,n.type==="exit"?t.presenceContext?.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const r=s?()=>Promise.all(Ji(t,s,n)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:l,staggerDirection:h}=i;return lu(t,e,c,u,l,h,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,u]=a==="beforeChildren"?[r,o]:[o,r];return c().then(()=>u())}else return Promise.all([r(),o(n.delay)])}function lu(t,e,n=0,s=0,i=0,r=1,o){const a=[],c=t.variantChildren.size,u=(c-1)*i,l=typeof s=="function",h=l?d=>s(d,c):r===1?(d=0)=>d*i:(d=0)=>u-d*i;return Array.from(t.variantChildren).sort(uu).forEach((d,f)=>{d.notify("AnimationStart",e),a.push(_e(d,e,{...o,delay:n+(l?0:s)+h(f)}).then(()=>d.notify("AnimationComplete",e)))}),Promise.all(a)}function uu(t,e){return t.sortNodePosition(e)}function hu(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(r=>_e(t,r,n));s=Promise.all(i)}else if(typeof e=="string")s=_e(t,e,n);else{const i=typeof e=="function"?Rt(t,e,n.custom):e;s=Promise.all(Ji(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Qi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const du=un.length;function to(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?to(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<du;n++){const s=un[n],i=t.props[s];(Ct(i)||i===!1)&&(e[s]=i)}return e}const fu=[...ln].reverse(),pu=ln.length;function mu(t){return e=>Promise.all(e.map(({animation:n,options:s})=>hu(t,n,s)))}function yu(t){let e=mu(t),n=ts(),s=!0;const i=c=>(u,l)=>{const h=Rt(t,l,c==="exit"?t.presenceContext?.custom:void 0);if(h){const{transition:d,transitionEnd:f,...m}=h;u={...u,...m,...f}}return u};function r(c){e=c(t)}function o(c){const{props:u}=t,l=to(t.parent)||{},h=[],d=new Set;let f={},m=1/0;for(let k=0;k<pu;k++){const g=fu[k],T=n[g],x=u[g]!==void 0?u[g]:l[g],b=Ct(x),M=g===c?T.isActive:null;M===!1&&(m=k);let A=x===l[g]&&x!==u[g]&&b;if(A&&s&&t.manuallyAnimateOnMount&&(A=!1),T.protectedKeys={...f},!T.isActive&&M===null||!x&&!T.prevProp||Jt(x)||typeof x=="boolean")continue;const S=gu(T.prevProp,x);let P=S||g===c&&T.isActive&&!A&&b||k>m&&b,L=!1;const j=Array.isArray(x)?x:[x];let Z=j.reduce(i(g),{});M===!1&&(Z={});const{prevResolvedValues:vn={}}=T,yo={...vn,...Z},xn=F=>{P=!0,d.has(F)&&(L=!0,d.delete(F)),T.needsAnimating[F]=!0;const K=t.getValue(F);K&&(K.liveStyle=!1)};for(const F in yo){const K=Z[F],te=vn[F];if(f.hasOwnProperty(F))continue;let ee=!1;Ee(K)&&Ee(te)?ee=!Qi(K,te):ee=K!==te,ee?K!=null?xn(F):d.add(F):K!==void 0&&d.has(F)?xn(F):T.protectedKeys[F]=!0}T.prevProp=x,T.prevResolvedValues=Z,T.isActive&&(f={...f,...Z}),s&&t.blockInitialAnimation&&(P=!1),P&&(!(A&&S)||L)&&h.push(...j.map(F=>({animation:F,options:{type:g}})))}if(d.size){const k={};if(typeof u.initial!="boolean"){const g=Rt(t,Array.isArray(u.initial)?u.initial[0]:u.initial);g&&g.transition&&(k.transition=g.transition)}d.forEach(g=>{const T=t.getBaseTarget(g),x=t.getValue(g);x&&(x.liveStyle=!0),k[g]=T??null}),h.push({animation:k})}let v=!!h.length;return s&&(u.initial===!1||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(v=!1),s=!1,v?e(h):Promise.resolve()}function a(c,u){if(n[c].isActive===u)return Promise.resolve();t.variantChildren?.forEach(h=>h.animationState?.setActive(c,u)),n[c].isActive=u;const l=o(c);for(const h in n)n[h].protectedKeys={};return l}return{animateChanges:o,setActive:a,setAnimateFunction:r,getState:()=>n,reset:()=>{n=ts(),s=!0}}}function gu(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Qi(e,t):!1}function nt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ts(){return{animate:nt(!0),whileInView:nt(),whileHover:nt(),whileTap:nt(),whileDrag:nt(),whileFocus:nt(),exit:nt()}}class et{constructor(e){this.isMounted=!1,this.node=e}update(){}}class vu extends et{constructor(e){super(e),e.animationState||(e.animationState=yu(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Jt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let xu=0;class Tu extends et{constructor(){super(...arguments),this.id=xu++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const ku={animation:{Feature:vu},exit:{Feature:Tu}};function Et(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function jt(t){return{point:{x:t.pageX,y:t.pageY}}}const Mu=t=>e=>an(e)&&t(e,jt(e));function wt(t,e,n,s){return Et(t,e,Mu(n),s)}const eo=1e-4,wu=1-eo,Pu=1+eo,no=.01,bu=0-no,Au=0+no;function B(t){return t.max-t.min}function Su(t,e,n){return Math.abs(t-e)<=n}function es(t,e,n,s=.5){t.origin=s,t.originPoint=C(e.min,e.max,t.origin),t.scale=B(n)/B(e),t.translate=C(n.min,n.max,t.origin)-t.originPoint,(t.scale>=wu&&t.scale<=Pu||isNaN(t.scale))&&(t.scale=1),(t.translate>=bu&&t.translate<=Au||isNaN(t.translate))&&(t.translate=0)}function Pt(t,e,n,s){es(t.x,e.x,n.x,s?s.originX:void 0),es(t.y,e.y,n.y,s?s.originY:void 0)}function ns(t,e,n){t.min=n.min+e.min,t.max=t.min+B(e)}function Vu(t,e,n){ns(t.x,e.x,n.x),ns(t.y,e.y,n.y)}function ss(t,e,n){t.min=e.min-n.min,t.max=t.min+B(e)}function bt(t,e,n){ss(t.x,e.x,n.x),ss(t.y,e.y,n.y)}function $(t){return[t("x"),t("y")]}const so=({current:t})=>t?t.ownerDocument.defaultView:null,is=(t,e)=>Math.abs(t-e);function Cu(t,e){const n=is(t.x,e.x),s=is(t.y,e.y);return Math.sqrt(n**2+s**2)}class io{constructor(e,n,{transformPagePoint:s,contextWindow:i=window,dragSnapToOrigin:r=!1,distanceThreshold:o=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=de(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Cu(d.offset,{x:0,y:0})>=this.distanceThreshold;if(!f&&!m)return;const{point:v}=d,{timestamp:k}=_;this.history.push({...v,timestamp:k});const{onStart:g,onMove:T}=this.handlers;f||(g&&g(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),T&&T(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=he(f,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:k}=this.handlers;if(this.dragSnapToOrigin&&k&&k(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const g=de(d.type==="pointercancel"?this.lastMoveEventInfo:he(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,g),v&&v(d,g)},!an(e))return;this.dragSnapToOrigin=r,this.handlers=n,this.transformPagePoint=s,this.distanceThreshold=o,this.contextWindow=i||window;const a=jt(e),c=he(a,this.transformPagePoint),{point:u}=c,{timestamp:l}=_;this.history=[{...u,timestamp:l}];const{onSessionStart:h}=n;h&&h(e,de(c,this.history)),this.removeListeners=Lt(wt(this.contextWindow,"pointermove",this.handlePointerMove),wt(this.contextWindow,"pointerup",this.handlePointerUp),wt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Q(this.updatePoint)}}function he(t,e){return e?{point:e(t.point)}:t}function os(t,e){return{x:t.x-e.x,y:t.y-e.y}}function de({point:t},e){return{point:t,delta:os(t,oo(e)),offset:os(t,Du(e)),velocity:Ru(e,.1)}}function Du(t){return t[0]}function oo(t){return t[t.length-1]}function Ru(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=oo(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>q(e)));)n--;if(!s)return{x:0,y:0};const r=H(i.timestamp-s.timestamp);if(r===0)return{x:0,y:0};const o={x:(i.x-s.x)/r,y:(i.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Eu(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?C(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?C(n,t,s.max):Math.min(t,n)),t}function rs(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Lu(t,{top:e,left:n,bottom:s,right:i}){return{x:rs(t.x,n,i),y:rs(t.y,e,s)}}function as(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function _u(t,e){return{x:as(t.x,e.x),y:as(t.y,e.y)}}function Nu(t,e){let n=.5;const s=B(t),i=B(e);return i>s?n=At(e.min,e.max-s,t.min):s>i&&(n=At(t.min,t.max-i,e.min)),X(0,1,n)}function ju(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ne=.35;function Fu(t=Ne){return t===!1?t=0:t===!0&&(t=Ne),{x:cs(t,"left","right"),y:cs(t,"top","bottom")}}function cs(t,e,n){return{min:ls(t,e),max:ls(t,n)}}function ls(t,e){return typeof t=="number"?t:t[e]||0}const Bu=new WeakMap;class Iu{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=R(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:n=!1,distanceThreshold:s}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const r=h=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(jt(h).point)},o=(h,d)=>{const{drag:f,dragPropagation:m,onDragStart:v}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Wc(f),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=d,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$(g=>{let T=this.getAxisMotionValue(g).get()||0;if(W.test(T)){const{projection:x}=this.visualElement;if(x&&x.layout){const b=x.layout.layoutBox[g];b&&(T=B(b)*(parseFloat(T)/100))}}this.originPoint[g]=T}),v&&V.postRender(()=>v(h,d)),Le(this.visualElement,"transform");const{animationState:k}=this.visualElement;k&&k.setActive("whileDrag",!0)},a=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d;const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:v,onDrag:k}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:g}=d;if(m&&this.currentDirection===null){this.currentDirection=Ou(g),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,g),this.updateAxis("y",d.point,g),this.visualElement.render(),k&&k(h,d)},c=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d,this.stop(h,d),this.latestPointerEvent=null,this.latestPanInfo=null},u=()=>$(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:l}=this.getProps();this.panSession=new io(e,{onSessionStart:r,onStart:o,onMove:a,onSessionEnd:c,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,distanceThreshold:s,contextWindow:so(this.visualElement)})}stop(e,n){const s=e||this.latestPointerEvent,i=n||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!i||!s)return;const{velocity:o}=i;this.startAnimation(o);const{onDragEnd:a}=this.getProps();a&&V.postRender(()=>a(s,i))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Ot(e,i,this.currentDirection))return;const r=this.getAxisMotionValue(e);let o=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(o=Eu(o,this.constraints[e],this.elastic[e])),r.set(o)}resolveConstraints(){const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;e&&lt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Lu(s.layoutBox,e):this.constraints=!1,this.elastic=Fu(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&$(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=ju(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!lt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const r=Ol(s,i.root,this.visualElement.getTransformPagePoint());let o=_u(i.layout.layoutBox,r);if(n){const a=n(Fl(o));this.hasMutatedConstraints=!!a,a&&(o=zi(a))}return o}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:r,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},u=$(l=>{if(!Ot(l,n,this.currentDirection))return;let h=c&&c[l]||{};o&&(h={min:0,max:0});const d=i?200:1e6,f=i?40:1e7,m={type:"inertia",velocity:s?e[l]:0,bounceStiffness:d,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10,...r,...h};return this.startAxisValueAnimation(l,m)});return Promise.all(u).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Le(this.visualElement,e),s.start(gn(e,s,0,n,this.visualElement,!1))}stopAnimation(){$(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){$(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){$(n=>{const{drag:s}=this.getProps();if(!Ot(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,r=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];r.set(e[n]-C(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!lt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};$(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const c=a.get();i[o]=Nu({min:c,max:c},this.constraints[o])}});const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),$(o=>{if(!Ot(o,e,null))return;const a=this.getAxisMotionValue(o),{min:c,max:u}=this.constraints[o];a.set(C(c,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;Bu.set(this.visualElement,this);const e=this.visualElement.current,n=wt(e,"pointerdown",c=>{const{drag:u,dragListener:l=!0}=this.getProps();u&&l&&this.start(c)}),s=()=>{const{dragConstraints:c}=this.getProps();lt(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(s);const o=Et(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:c,hasLayoutChanged:u})=>{this.isDragging&&u&&($(l=>{const h=this.getAxisMotionValue(l);h&&(this.originPoint[l]+=c[l].translate,h.set(h.get()+c[l].translate))}),this.visualElement.render())});return()=>{o(),n(),r(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:r=!1,dragElastic:o=Ne,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:r,dragElastic:o,dragMomentum:a}}}function Ot(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function Ou(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class $u extends et{constructor(e){super(e),this.removeGroupControls=U,this.removeListeners=U,this.controls=new Iu(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U}unmount(){this.removeGroupControls(),this.removeListeners()}}const us=t=>(e,n)=>{t&&V.postRender(()=>t(e,n))};class Uu extends et{constructor(){super(...arguments),this.removePointerDownListener=U}onPointerDown(e){this.session=new io(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:so(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:us(e),onStart:us(n),onMove:s,onEnd:(r,o)=>{delete this.session,i&&V.postRender(()=>i(r,o))}}}mount(){this.removePointerDownListener=wt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function hs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const xt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(w.test(t))t=parseFloat(t);else return t;const n=hs(t,e.target.x),s=hs(t,e.target.y);return`${n}% ${s}%`}},zu={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=tt.parse(t);if(i.length>5)return s;const r=tt.createTransformer(t),o=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,c=n.y.scale*e.y;i[0+o]/=a,i[1+o]/=c;const u=C(a,c,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),r(i)}};let ds=!1;class qu extends y.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:r}=e;hl(Hu),r&&(n.group&&n.group.add(r),s&&s.register&&i&&s.register(r),ds&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),qt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:r}=this.props,{projection:o}=s;return o&&(o.isPresent=r,ds=!0,i||e.layoutDependency!==n||n===void 0||e.isPresent!==r?o.willUpdate():this.safeToRemove(),e.isPresent!==r&&(r?o.promote():o.relegate()||V.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ro(t){const[e,n]=Ci(),s=y.useContext(Fe);return G.jsx(qu,{...t,layoutGroup:s,switchLayoutGroup:y.useContext($i),isPresent:e,safeToRemove:n})}const Hu={borderRadius:{...xt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:xt,borderTopRightRadius:xt,borderBottomLeftRadius:xt,borderBottomRightRadius:xt,boxShadow:zu};function Wu(t,e,n){const s=N(t)?t:ft(t);return s.start(gn("",s,e,n)),s.animation}const Ku=(t,e)=>t.depth-e.depth;class Gu{constructor(){this.children=[],this.isDirty=!1}add(e){Oe(this.children,e),this.isDirty=!0}remove(e){$e(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Ku),this.isDirty=!1,this.children.forEach(e)}}function Xu(t,e){const n=I.now(),s=({timestamp:i})=>{const r=i-n;r>=e&&(Q(s),t(r-e))};return V.setup(s,!0),()=>Q(s)}const ao=["TopLeft","TopRight","BottomLeft","BottomRight"],Yu=ao.length,fs=t=>typeof t=="string"?parseFloat(t):t,ps=t=>typeof t=="number"||w.test(t);function Zu(t,e,n,s,i,r){i?(t.opacity=C(0,n.opacity??1,Ju(s)),t.opacityExit=C(e.opacity??1,0,Qu(s))):r&&(t.opacity=C(e.opacity??1,n.opacity??1,s));for(let o=0;o<Yu;o++){const a=`border${ao[o]}Radius`;let c=ms(e,a),u=ms(n,a);if(c===void 0&&u===void 0)continue;c||(c=0),u||(u=0),c===0||u===0||ps(c)===ps(u)?(t[a]=Math.max(C(fs(c),fs(u),s),0),(W.test(u)||W.test(c))&&(t[a]+="%")):t[a]=u}(e.rotate||n.rotate)&&(t.rotate=C(e.rotate||0,n.rotate||0,s))}function ms(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ju=co(0,.5,Xs),Qu=co(.5,.95,U);function co(t,e,n){return s=>s<t?0:s>e?1:n(At(t,e,s))}function ys(t,e){t.min=e.min,t.max=e.max}function O(t,e){ys(t.x,e.x),ys(t.y,e.y)}function gs(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function vs(t,e,n,s,i){return t-=e,t=Xt(t,1/n,s),i!==void 0&&(t=Xt(t,1/i,s)),t}function th(t,e=0,n=1,s=.5,i,r=t,o=t){if(W.test(e)&&(e=parseFloat(e),e=C(o.min,o.max,e/100)-o.min),typeof e!="number")return;let a=C(r.min,r.max,s);t===r&&(a-=e),t.min=vs(t.min,e,n,a,i),t.max=vs(t.max,e,n,a,i)}function xs(t,e,[n,s,i],r,o){th(t,e[n],e[s],e[i],e.scale,r,o)}const eh=["x","scaleX","originX"],nh=["y","scaleY","originY"];function Ts(t,e,n,s){xs(t.x,e,eh,n?n.x:void 0,s?s.x:void 0),xs(t.y,e,nh,n?n.y:void 0,s?s.y:void 0)}function ks(t){return t.translate===0&&t.scale===1}function lo(t){return ks(t.x)&&ks(t.y)}function Ms(t,e){return t.min===e.min&&t.max===e.max}function sh(t,e){return Ms(t.x,e.x)&&Ms(t.y,e.y)}function ws(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function uo(t,e){return ws(t.x,e.x)&&ws(t.y,e.y)}function Ps(t){return B(t.x)/B(t.y)}function bs(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ih{constructor(){this.members=[]}add(e){Oe(this.members,e),e.scheduleRender()}remove(e){if($e(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const r=this.members[i];if(r.isPresent!==!1){s=r;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function oh(t,e,n){let s="";const i=t.x.translate/e.x,r=t.y.translate/e.y,o=n?.z||0;if((i||r||o)&&(s=`translate3d(${i}px, ${r}px, ${o}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:u,rotate:l,rotateX:h,rotateY:d,skewX:f,skewY:m}=n;u&&(s=`perspective(${u}px) ${s}`),l&&(s+=`rotate(${l}deg) `),h&&(s+=`rotateX(${h}deg) `),d&&(s+=`rotateY(${d}deg) `),f&&(s+=`skewX(${f}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,c=t.y.scale*e.y;return(a!==1||c!==1)&&(s+=`scale(${a}, ${c})`),s||"none"}const fe=["","X","Y","Z"],rh=1e3;let ah=0;function pe(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function ho(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Zi(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(i||r))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&ho(s)}function fo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(o={},a=e?.()){this.id=ah++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(uh),this.nodes.forEach(ph),this.nodes.forEach(mh),this.nodes.forEach(hh)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new Gu)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new qe),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const c=this.eventHandlers.get(o);c&&c.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o){if(this.instance)return;this.isSVG=Vi(o)&&!Jc(o),this.instance=o;const{layoutId:a,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(c||a)&&(this.isLayoutDirty=!0),t){let l,h=0;const d=()=>this.root.updateBlockedByResize=!1;V.read(()=>{h=window.innerWidth}),t(o,()=>{const f=window.innerWidth;f!==h&&(h=f,this.root.updateBlockedByResize=!0,l&&l(),l=Xu(d,250),qt.hasAnimatedSinceResize&&(qt.hasAnimatedSinceResize=!1,this.nodes.forEach(Vs)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&u&&(a||c)&&this.addEventListener("didUpdate",({delta:l,hasLayoutChanged:h,hasRelativeLayoutChanged:d,layout:f})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||u.getDefaultTransition()||Th,{onLayoutAnimationStart:v,onLayoutAnimationComplete:k}=u.getProps(),g=!this.targetLayout||!uo(this.targetLayout,f),T=!h&&d;if(this.options.layoutRoot||this.resumeFrom||T||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const x={...sn(m,"layout"),onPlay:v,onComplete:k};(u.shouldReduceMotion||this.options.layoutRoot)&&(x.delay=0,x.type=!1),this.startAnimation(x),this.setAnimationOrigin(l,T)}else h||Vs(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=f})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(yh),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ho(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let l=0;l<this.path.length;l++){const h=this.path[l];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(As);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Ss);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(fh),this.nodes.forEach(ch),this.nodes.forEach(lh)):this.nodes.forEach(Ss),this.clearAllSnapshots();const a=I.now();_.delta=X(0,1e3/60,a-_.timestamp),_.timestamp=a,_.isProcessing=!0,se.update.process(_),se.preRender.process(_),se.render.process(_),_.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(dh),this.sharedNodes.forEach(gh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!B(this.snapshot.measuredBox.x)&&!B(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=R(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&this.instance){const c=s(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:c,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!lo(this.projectionDelta),c=this.getTransformTemplate(),u=c?c(this.latestValues,""):void 0,l=u!==this.prevTransformTemplateValue;o&&this.instance&&(a||st(this.latestValues)||l)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return o&&(c=this.removeTransform(c)),kh(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return R();const a=o.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Mh))){const{scroll:u}=this.root;u&&(ut(a.x,u.offset.x),ut(a.y,u.offset.y))}return a}removeElementScroll(o){const a=R();if(O(a,o),this.scroll?.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:l,options:h}=u;u!==this.root&&l&&h.layoutScroll&&(l.wasRoot&&O(a,o),ut(a.x,l.offset.x),ut(a.y,l.offset.y))}return a}applyTransform(o,a=!1){const c=R();O(c,o);for(let u=0;u<this.path.length;u++){const l=this.path[u];!a&&l.options.layoutScroll&&l.scroll&&l!==l.root&&ht(c,{x:-l.scroll.offset.x,y:-l.scroll.offset.y}),st(l.latestValues)&&ht(c,l.latestValues)}return st(this.latestValues)&&ht(c,this.latestValues),c}removeTransform(o){const a=R();O(a,o);for(let c=0;c<this.path.length;c++){const u=this.path[c];if(!u.instance||!st(u.latestValues))continue;Ce(u.latestValues)&&u.updateSnapshot();const l=R(),h=u.measurePageBox();O(l,h),Ts(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,l)}return st(this.latestValues)&&Ts(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==_.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(o||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:l,layoutId:h}=this.options;if(!(!this.layout||!(l||h))){if(this.resolvedRelativeTargetAt=_.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),bt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),O(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=R(),this.targetWithTransforms=R()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Vu(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):O(this.target,this.layout.layoutBox),Hi(this.target,this.targetDelta)):O(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=R(),this.relativeTargetOrigin=R(),bt(this.relativeTargetOrigin,this.target,d.target),O(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ce(this.parent.latestValues)||qi(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const o=this.getLead(),a=!!this.resumingFrom||this!==o;let c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===_.timestamp&&(c=!1),c)return;const{layout:u,layoutId:l}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||l))return;O(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,d=this.treeScale.y;Il(this.layoutCorrected,this.treeScale,this.path,a),o.layout&&!o.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(o.target=o.layout.layoutBox,o.targetWithTransforms=R());const{target:f}=o;if(!f){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(gs(this.prevProjectionDelta.x,this.projectionDelta.x),gs(this.prevProjectionDelta.y,this.projectionDelta.y)),Pt(this.projectionDelta,this.layoutCorrected,f,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==d||!bs(this.projectionDelta.x,this.prevProjectionDelta.x)||!bs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",f))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.visualElement?.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=dt(),this.projectionDelta=dt(),this.projectionDeltaWithTransform=dt()}setAnimationOrigin(o,a=!1){const c=this.snapshot,u=c?c.latestValues:{},l={...this.latestValues},h=dt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=R(),f=c?c.source:void 0,m=this.layout?this.layout.source:void 0,v=f!==m,k=this.getStack(),g=!k||k.members.length<=1,T=!!(v&&!g&&this.options.crossfade===!0&&!this.path.some(xh));this.animationProgress=0;let x;this.mixTargetDelta=b=>{const M=b/1e3;Cs(h.x,o.x,M),Cs(h.y,o.y,M),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(bt(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),vh(this.relativeTarget,this.relativeTargetOrigin,d,M),x&&sh(this.relativeTarget,x)&&(this.isProjectionDirty=!1),x||(x=R()),O(x,this.relativeTarget)),v&&(this.animationValues=l,Zu(l,u,this.latestValues,M,T,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=M},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{qt.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ft(0)),this.currentAnimation=Wu(this.motionValue,[0,1e3],{...o,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onStop:()=>{},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(rh),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:c,layout:u,latestValues:l}=o;if(!(!a||!c||!u)){if(this!==o&&this.layout&&u&&po(this.options.animationType,this.layout.layoutBox,u.layoutBox)){c=this.target||R();const h=B(this.layout.layoutBox.x);c.x.min=o.target.x.min,c.x.max=c.x.min+h;const d=B(this.layout.layoutBox.y);c.y.min=o.target.y.min,c.y.max=c.y.min+d}O(a,c),ht(a,l),Pt(this.projectionDeltaWithTransform,this.layoutCorrected,a,l)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new ih),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){const{layoutId:o}=this.options;return o?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:o}=this.options;return o?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:c}={}){const u=this.getStack();u&&u.promote(this,c),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:c}=o;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(a=!0),!a)return;const u={};c.z&&pe("z",o,u,this.animationValues);for(let l=0;l<fe.length;l++)pe(`rotate${fe[l]}`,o,u,this.animationValues),pe(`skew${fe[l]}`,o,u,this.animationValues);o.render();for(const l in u)o.setStaticValue(l,u[l]),this.animationValues&&(this.animationValues[l]=u[l]);o.scheduleRender()}applyProjectionStyles(o,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){o.visibility="hidden";return}const c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,o.visibility="",o.opacity="",o.pointerEvents=zt(a?.pointerEvents)||"",o.transform=c?c(this.latestValues,""):"none";return}const u=this.getLead();if(!this.projectionDelta||!this.layout||!u.target){this.options.layoutId&&(o.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,o.pointerEvents=zt(a?.pointerEvents)||""),this.hasProjected&&!st(this.latestValues)&&(o.transform=c?c({},""):"none",this.hasProjected=!1);return}o.visibility="";const l=u.animationValues||u.latestValues;this.applyTransformsToTarget();let h=oh(this.projectionDeltaWithTransform,this.treeScale,l);c&&(h=c(l,h)),o.transform=h;const{x:d,y:f}=this.projectionDelta;o.transformOrigin=`${d.origin*100}% ${f.origin*100}% 0`,u.animationValues?o.opacity=u===this?l.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:l.opacityExit:o.opacity=u===this?l.opacity!==void 0?l.opacity:"":l.opacityExit!==void 0?l.opacityExit:0;for(const m in Dt){if(l[m]===void 0)continue;const{correct:v,applyTo:k,isCSSVariable:g}=Dt[m],T=h==="none"?l[m]:v(l[m],u);if(k){const x=k.length;for(let b=0;b<x;b++)o[k[b]]=T}else g?this.options.visualElement.renderState.vars[m]=T:o[m]=T}this.options.layoutId&&(o.pointerEvents=u===this?zt(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>o.currentAnimation?.stop()),this.root.nodes.forEach(As),this.root.sharedNodes.clear()}}}function ch(t){t.updateLayout()}function lh(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:s}=t.layout,{animationType:i}=t.options,r=e.source!==t.layout.source;i==="size"?$(l=>{const h=r?e.measuredBox[l]:e.layoutBox[l],d=B(h);h.min=n[l].min,h.max=h.min+d}):po(i,e.layoutBox,n)&&$(l=>{const h=r?e.measuredBox[l]:e.layoutBox[l],d=B(n[l]);h.max=h.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[l].max=t.relativeTarget[l].min+d)});const o=dt();Pt(o,n,e.layoutBox);const a=dt();r?Pt(a,t.applyTransform(s,!0),e.measuredBox):Pt(a,n,e.layoutBox);const c=!lo(o);let u=!1;if(!t.resumeFrom){const l=t.getClosestProjectingParent();if(l&&!l.resumeFrom){const{snapshot:h,layout:d}=l;if(h&&d){const f=R();bt(f,e.layoutBox,h.layoutBox);const m=R();bt(m,n,d.layoutBox),uo(f,m)||(u=!0),l.options.layoutRoot&&(t.relativeTarget=m,t.relativeTargetOrigin=f,t.relativeParent=l)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:n}=t.options;n&&n()}t.options.transition=void 0}function uh(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function hh(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function dh(t){t.clearSnapshot()}function As(t){t.clearMeasurements()}function Ss(t){t.isLayoutDirty=!1}function fh(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Vs(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ph(t){t.resolveTargetDelta()}function mh(t){t.calcProjection()}function yh(t){t.resetSkewAndRotation()}function gh(t){t.removeLeadSnapshot()}function Cs(t,e,n){t.translate=C(e.translate,0,n),t.scale=C(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Ds(t,e,n,s){t.min=C(e.min,n.min,s),t.max=C(e.max,n.max,s)}function vh(t,e,n,s){Ds(t.x,e.x,n.x,s),Ds(t.y,e.y,n.y,s)}function xh(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Th={duration:.45,ease:[.4,0,.1,1]},Rs=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Es=Rs("applewebkit/")&&!Rs("chrome/")?Math.round:U;function Ls(t){t.min=Es(t.min),t.max=Es(t.max)}function kh(t){Ls(t.x),Ls(t.y)}function po(t,e,n){return t==="position"||t==="preserve-aspect"&&!Su(Ps(e),Ps(n),.2)}function Mh(t){return t!==t.root&&t.scroll?.wasRoot}const wh=fo({attachResizeListener:(t,e)=>Et(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),me={current:void 0},mo=fo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!me.current){const t=new wh({});t.mount(window),t.setOptions({layoutScroll:!0}),me.current=t}return me.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Ph={pan:{Feature:Uu},drag:{Feature:$u,ProjectionNode:mo,MeasureLayout:ro}};function _s(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,r=s[i];r&&V.postRender(()=>r(e,jt(e)))}class bh extends et{mount(){const{current:e}=this.node;e&&(this.unmount=Kc(e,(n,s)=>(_s(this.node,s,"Start"),i=>_s(this.node,i,"End"))))}unmount(){}}class Ah extends et{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Lt(Et(this.node.current,"focus",()=>this.onFocus()),Et(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ns(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),r=s[i];r&&V.postRender(()=>r(e,jt(e)))}class Sh extends et{mount(){const{current:e}=this.node;e&&(this.unmount=Zc(e,(n,s)=>(Ns(this.node,s,"Start"),(i,{success:r})=>Ns(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const je=new WeakMap,ye=new WeakMap,Vh=t=>{const e=je.get(t.target);e&&e(t)},Ch=t=>{t.forEach(Vh)};function Dh({root:t,...e}){const n=t||document;ye.has(n)||ye.set(n,{});const s=ye.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Ch,{root:t,...e})),s[i]}function Rh(t,e,n){const s=Dh(e);return je.set(t,n),s.observe(t),()=>{je.delete(t),s.unobserve(t)}}const Eh={some:0,all:1};class Lh extends et{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:r}=e,o={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Eh[i]},a=c=>{const{isIntersecting:u}=c;if(this.isInView===u||(this.isInView=u,r&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:l,onViewportLeave:h}=this.node.getProps(),d=u?l:h;d&&d(c)};return Rh(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(_h(e,n))&&this.startObserver()}unmount(){}}function _h({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Nh={inView:{Feature:Lh},tap:{Feature:Sh},focus:{Feature:Ah},hover:{Feature:bh}},jh={layout:{ProjectionNode:mo,MeasureLayout:ro}},Fh={...ku,...Nh,...Ph,...jh},wf=jl(Fh,Xl);export{Nd as $,zh as A,Xh as B,nd as C,fd as D,md as E,kd as F,wd as G,tf as H,ff as I,gd as J,Qd as K,Uh as L,Ld as M,Jh as N,Pd as O,Wd as P,df as Q,ko as R,ef as S,hf as T,mf as U,lf as V,Sd as W,xf as X,gf as Y,Od as Z,Td as _,$h as a,_d as a0,dd as a1,yf as a2,Hh as a3,pf as a4,Vd as a5,qd as a6,ed as a7,Jd as a8,Md as a9,id as aA,zd as aB,Wh as aa,uf as ab,Gh as ac,of as ad,yd as ae,Bd as af,sf as ag,Ad as ah,Dd as ai,Cd as aj,$d as ak,Kh as al,Hd as am,Gd as an,Fd as ao,vd as ap,Xd as aq,vf as ar,ld as as,Ud as at,bd as au,Rd as av,af as aw,Id as ax,qh as ay,Tf as az,Qh as b,cd as c,rd as d,cf as e,sd as f,td as g,Zh as h,ud as i,G as j,hd as k,Ed as l,pd as m,od as n,Yd as o,xd as p,nf as q,y as r,Yh as s,rf as t,kf as u,wf as v,ad as w,Zd as x,jd as y,Kd as z};
