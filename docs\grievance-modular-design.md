# Grievance View Modular Design Document

## 🎯 Project Overview

**Objective:** Extract the monolithic `grievance-view-enhanced.tsx` (3,796 lines) into modular, maintainable components while preserving all functionality.

**Current State:** Single massive component with embedded logic
**Target State:** Modular component architecture with clear separation of concerns

## 📊 Analysis Summary

### Current File Analysis
- **Total Lines:** 3,796
- **Main Component:** `EnhancedGrievanceView`
- **State Management:** Zustand store (`useGrievanceStore`)
- **UI Framework:** shadcn/ui components
- **Key Features:** Status management, file handling, comments, timeline, SLA tracking

### Component Extraction Status
| Component | Status | Lines | Completeness |
|-----------|--------|-------|--------------|
| DetailsTab | ✅ Created | 300 | 70% |
| AttachmentsTab | ✅ Created | 200 | 60% |
| CommentsTab | ✅ Created | 220 | 80% |
| ActionsTab | ✅ Created | 280 | 65% |
| TimelineTab | ✅ Existing | - | 90% |

## 🏗️ Architecture Design

### Component Hierarchy
```
GrievanceViewModular
├── GrievanceHeader
│   ├── Title & ID Display
│   ├── Status & Priority Badges
│   └── Action Buttons (Close, Share, etc.)
├── TabsContainer
│   ├── DetailsTabEnhanced
│   │   ├── BasicInformation
│   │   ├── SLATimingSection
│   │   ├── StatusPriorityDisplay
│   │   ├── LocationInformation
│   │   ├── AdditionalInformation
│   │   └── FeedbackSection
│   ├── TimelineTab (existing)
│   ├── AttachmentsTabEnhanced
│   │   ├── FileUploadZone
│   │   ├── FileGrid
│   │   └── FilePreviewModal
│   ├── CommentsTabEnhanced
│   │   ├── CategoryFilter
│   │   ├── CommentsList
│   │   ├── ReplySystem
│   │   └── AddCommentForm
│   └── ActionsTabEnhanced
│       ├── StatusOverview
│       ├── StatusTransitionForm
│       ├── PriorityManagement
│       └── AssignmentInfo
├── Dialogs
│   ├── FilePreviewDialog
│   ├── StatusChangeDialog
│   └── AttachmentRemovalDialog
└── ErrorBoundary
```

### State Management Strategy
- **Global State:** Continue using `useGrievanceStore()` for data operations
- **Local State:** Component-specific UI state (modals, forms, filters)
- **Prop Drilling:** Minimal - use context for deeply nested components

## 🔧 Implementation Plan

### Phase 1: Enhanced Components (Priority 1)
1. **Enhance DetailsTabComplete**
   - Add LocationInformation section
   - Add AdditionalInformation section  
   - Add FeedbackSection
   - Maintain exact styling from main file

2. **Enhance AttachmentsTabComplete**
   - Add file upload functionality
   - Add drag-drop support
   - Integrate removal confirmation dialog
   - Add file preview modal

3. **Create GrievanceHeader**
   - Extract header section from main file
   - Include title, ID, status badges
   - Add action buttons

### Phase 2: Dialog Components (Priority 2)
1. **FilePreviewDialog**
   - Full-screen file viewer
   - Support multiple file types
   - Download functionality

2. **StatusChangeDialog**
   - Status transition workflows
   - Predefined notes system
   - Validation logic

3. **AttachmentRemovalDialog**
   - Confirmation dialog
   - File details display
   - Loading states

### Phase 3: Integration (Priority 3)
1. **Create GrievanceViewModular**
   - Integrate all components
   - Maintain existing props interface
   - Preserve all functionality
   - Add error boundaries

2. **Testing & Validation**
   - TypeScript compilation
   - Functionality testing
   - Performance validation

## 📋 Missing Features Analysis

### Critical Missing Features
1. **File Upload System**
   - Drag-drop functionality
   - File validation
   - Progress indicators
   - Error handling

2. **Location Information**
   - GPS coordinates display
   - Google Maps integration
   - Address formatting

3. **Status Transition Workflows**
   - Complex validation rules
   - Predefined notes
   - Transition matrix

4. **Additional Information**
   - Tags management
   - Privacy settings
   - User feedback display

### Implementation Priority
- **P0 (Critical):** File upload, Status workflows
- **P1 (High):** Location info, Additional info
- **P2 (Medium):** Enhanced error handling, Performance optimizations

## 🎨 Design Principles

### Code Organization
- **Single Responsibility:** Each component has one clear purpose
- **Composition over Inheritance:** Build complex UI from simple components
- **Props Interface:** Clear, typed interfaces for all components
- **Error Boundaries:** Graceful error handling at component level

### Styling Consistency
- **Design System:** Continue using shadcn/ui components
- **Dark Mode:** Maintain existing dark mode support
- **Responsive:** Mobile-first responsive design
- **Accessibility:** WCAG 2.1 AA compliance

### Performance Considerations
- **Lazy Loading:** Load tabs only when accessed
- **Memoization:** React.memo for expensive components
- **Virtual Scrolling:** For large lists
- **Code Splitting:** Dynamic imports for heavy components

## 🔍 Quality Assurance

### Testing Strategy
- **Unit Tests:** Each component individually
- **Integration Tests:** Component interactions
- **E2E Tests:** Full user workflows
- **TypeScript:** Strict type checking

### Success Criteria
- ✅ TypeScript compilation without errors
- ✅ All existing functionality preserved
- ✅ Performance maintained or improved
- ✅ Code maintainability improved
- ✅ Component reusability achieved

## 📁 File Structure

```
src/components/grievance/
├── GrievanceViewModular.tsx          # Main integrated component
├── header/
│   └── GrievanceHeader.tsx           # Header component
├── tabs/
│   ├── DetailsTabEnhanced.tsx        # Enhanced details tab
│   ├── AttachmentsTabEnhanced.tsx    # Enhanced attachments tab
│   ├── CommentsTabEnhanced.tsx       # Enhanced comments tab
│   ├── ActionsTabEnhanced.tsx        # Enhanced actions tab
│   └── TimelineTab.tsx               # Existing timeline tab
├── dialogs/
│   ├── FilePreviewDialog.tsx         # File preview modal
│   ├── StatusChangeDialog.tsx        # Status change modal
│   └── AttachmentRemovalDialog.tsx   # Removal confirmation
├── sections/
│   ├── BasicInformation.tsx          # Details sub-components
│   ├── SLATimingSection.tsx
│   ├── LocationInformation.tsx
│   └── AdditionalInformation.tsx
└── utils/
    ├── fileHelpers.ts                # File utilities
    ├── grievanceHelpers.ts           # General utilities
    └── slaCalculations.ts            # SLA logic
```

## 🚀 Implementation Checklist

### Phase 1: Enhanced Components
- [ ] Enhance DetailsTabComplete with missing sections
- [ ] Add file upload to AttachmentsTabComplete  
- [ ] Create GrievanceHeader component
- [ ] Test TypeScript compilation

### Phase 2: Dialog Components
- [ ] Create FilePreviewDialog
- [ ] Create StatusChangeDialog
- [ ] Create AttachmentRemovalDialog
- [ ] Test dialog interactions

### Phase 3: Integration
- [ ] Create GrievanceViewModular
- [ ] Integrate all components
- [ ] Add error boundaries
- [ ] Final testing and validation

### Validation Gates
- [ ] `npm run build` passes without errors
- [ ] All TypeScript types resolve correctly
- [ ] All existing functionality works
- [ ] Performance benchmarks met
- [ ] Code review completed

---

**Next Steps:** Begin Phase 1 implementation with enhanced components, validating each step with TypeScript compilation.
