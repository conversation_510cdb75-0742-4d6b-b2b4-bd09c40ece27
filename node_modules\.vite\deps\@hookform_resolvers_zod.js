import {
  $ZodError,
  parse,
  parseAsync
} from "./chunk-QJ4GWDCZ.js";
import {
  appendErrors,
  get,
  set
} from "./chunk-DJLU2MHF.js";
import "./chunk-DRWLMN53.js";
import "./chunk-G3PMV62Z.js";

// node_modules/@hookform/resolvers/dist/resolvers.mjs
var r = (t2, r2, o2) => {
  if (t2 && "reportValidity" in t2) {
    const s3 = get(o2, r2);
    t2.setCustomValidity(s3 && s3.message || ""), t2.reportValidity();
  }
};
var o = (e, t2) => {
  for (const o2 in t2.fields) {
    const s3 = t2.fields[o2];
    s3 && s3.ref && "reportValidity" in s3.ref ? r(s3.ref, o2, e) : s3 && s3.refs && s3.refs.forEach((t3) => r(t3, o2, e));
  }
};
var s = (r2, s3) => {
  s3.shouldUseNativeValidation && o(r2, s3);
  const n2 = {};
  for (const o2 in r2) {
    const f = get(s3.fields, o2), c = Object.assign(r2[o2] || {}, { ref: f && f.ref });
    if (i(s3.names || Object.keys(r2), o2)) {
      const r3 = Object.assign({}, get(n2, o2));
      set(r3, "root", c), set(n2, o2, r3);
    } else set(n2, o2, c);
  }
  return n2;
};
var i = (e, t2) => {
  const r2 = n(t2);
  return e.some((e2) => n(e2).match(`^${r2}\\.\\d+`));
};
function n(e) {
  return e.replace(/\]|\[/g, "");
}

// node_modules/@hookform/resolvers/zod/dist/zod.mjs
function t(r2, e) {
  try {
    var o2 = r2();
  } catch (r3) {
    return e(r3);
  }
  return o2 && o2.then ? o2.then(void 0, e) : o2;
}
function s2(r2, e) {
  for (var n2 = {}; r2.length; ) {
    var t2 = r2[0], s3 = t2.code, i3 = t2.message, a2 = t2.path.join(".");
    if (!n2[a2]) if ("unionErrors" in t2) {
      var u = t2.unionErrors[0].errors[0];
      n2[a2] = { message: u.message, type: u.code };
    } else n2[a2] = { message: i3, type: s3 };
    if ("unionErrors" in t2 && t2.unionErrors.forEach(function(e2) {
      return e2.errors.forEach(function(e3) {
        return r2.push(e3);
      });
    }), e) {
      var c = n2[a2].types, f = c && c[t2.code];
      n2[a2] = appendErrors(a2, e, n2, s3, f ? [].concat(f, t2.message) : t2.message);
    }
    r2.shift();
  }
  return n2;
}
function i2(r2, e) {
  for (var n2 = {}; r2.length; ) {
    var t2 = r2[0], s3 = t2.code, i3 = t2.message, a2 = t2.path.join(".");
    if (!n2[a2]) if ("invalid_union" === t2.code) {
      var u = t2.errors[0][0];
      n2[a2] = { message: u.message, type: u.code };
    } else n2[a2] = { message: i3, type: s3 };
    if ("invalid_union" === t2.code && t2.errors.forEach(function(e2) {
      return e2.forEach(function(e3) {
        return r2.push(e3);
      });
    }), e) {
      var c = n2[a2].types, f = c && c[t2.code];
      n2[a2] = appendErrors(a2, e, n2, s3, f ? [].concat(f, t2.message) : t2.message);
    }
    r2.shift();
  }
  return n2;
}
function a(o2, a2, u) {
  if (void 0 === u && (u = {}), function(r2) {
    return "_def" in r2 && "object" == typeof r2._def && "typeName" in r2._def;
  }(o2)) return function(n2, i3, c) {
    try {
      return Promise.resolve(t(function() {
        return Promise.resolve(o2["sync" === u.mode ? "parse" : "parseAsync"](n2, a2)).then(function(e) {
          return c.shouldUseNativeValidation && o({}, c), { errors: {}, values: u.raw ? Object.assign({}, n2) : e };
        });
      }, function(r2) {
        if (function(r3) {
          return Array.isArray(null == r3 ? void 0 : r3.issues);
        }(r2)) return { values: {}, errors: s(s2(r2.errors, !c.shouldUseNativeValidation && "all" === c.criteriaMode), c) };
        throw r2;
      }));
    } catch (r2) {
      return Promise.reject(r2);
    }
  };
  if (function(r2) {
    return "_zod" in r2 && "object" == typeof r2._zod;
  }(o2)) return function(s3, c, f) {
    try {
      return Promise.resolve(t(function() {
        return Promise.resolve(("sync" === u.mode ? parse : parseAsync)(o2, s3, a2)).then(function(e) {
          return f.shouldUseNativeValidation && o({}, f), { errors: {}, values: u.raw ? Object.assign({}, s3) : e };
        });
      }, function(r2) {
        if (function(r3) {
          return r3 instanceof $ZodError;
        }(r2)) return { values: {}, errors: s(i2(r2.issues, !f.shouldUseNativeValidation && "all" === f.criteriaMode), f) };
        throw r2;
      }));
    } catch (r2) {
      return Promise.reject(r2);
    }
  };
  throw new Error("Invalid input: not a Zod schema");
}
export {
  a as zodResolver
};
//# sourceMappingURL=@hookform_resolvers_zod.js.map
