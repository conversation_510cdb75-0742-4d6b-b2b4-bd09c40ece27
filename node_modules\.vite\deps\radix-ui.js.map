{"version": 3, "sources": ["../../@radix-ui/react-accessible-icon/src/accessible-icon.tsx", "../../@radix-ui/react-accordion/src/accordion.tsx", "../../@radix-ui/react-collapsible/src/collapsible.tsx", "../../@radix-ui/react-alert-dialog/src/alert-dialog.tsx", "../../@radix-ui/react-aspect-ratio/src/aspect-ratio.tsx", "../../@radix-ui/react-checkbox/src/checkbox.tsx", "../../@radix-ui/react-context-menu/src/context-menu.tsx", "../../@radix-ui/react-form/src/form.tsx", "../../@radix-ui/react-hover-card/src/hover-card.tsx", "../../@radix-ui/react-menubar/src/menubar.tsx", "../../@radix-ui/react-navigation-menu/src/navigation-menu.tsx", "../../@radix-ui/react-one-time-password-field/src/one-time-password-field.tsx", "../../@radix-ui/react-password-toggle-field/src/password-toggle-field.tsx", "../../@radix-ui/react-slider/src/slider.tsx", "../../@radix-ui/react-toast/src/toast.tsx", "../../@radix-ui/react-toggle/src/toggle.tsx", "../../@radix-ui/react-toggle-group/src/toggle-group.tsx", "../../@radix-ui/react-toolbar/src/toolbar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nconst NAME = 'AccessibleIcon';\n\ninterface AccessibleIconProps {\n  children?: React.ReactNode;\n  /**\n   * The accessible label for the icon. This label will be visually hidden but announced to screen\n   * reader users, similar to `alt` text for `img` tags.\n   */\n  label: string;\n}\n\nconst AccessibleIcon: React.FC<AccessibleIconProps> = ({ children, label }) => {\n  const child = React.Children.only(children);\n  return (\n    <>\n      {React.cloneElement(child as React.ReactElement<React.SVGAttributes<SVGElement>>, {\n        // accessibility\n        'aria-hidden': 'true',\n        focusable: 'false', // See: https://allyjs.io/tutorials/focusing-in-svg.html#making-svg-elements-focusable\n      })}\n      <VisuallyHiddenPrimitive.Root>{label}</VisuallyHiddenPrimitive.Root>\n    </>\n  );\n};\n\nAccessibleIcon.displayName = NAME;\n\nconst Root = AccessibleIcon;\n\nexport {\n  AccessibleIcon,\n  //\n  Root,\n};\nexport type { AccessibleIconProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ComponentRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ComponentRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ComponentRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ComponentRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Collapsible\n * -----------------------------------------------------------------------------------------------*/\n\nconst COLLAPSIBLE_NAME = 'Collapsible';\n\ntype ScopedProps<P> = P & { __scopeCollapsible?: Scope };\nconst [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\n\ntype CollapsibleContextValue = {\n  contentId: string;\n  disabled?: boolean;\n  open: boolean;\n  onOpenToggle(): void;\n};\n\nconst [CollapsibleProvider, useCollapsibleContext] =\n  createCollapsibleContext<CollapsibleContextValue>(COLLAPSIBLE_NAME);\n\ntype CollapsibleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface CollapsibleProps extends PrimitiveDivProps {\n  defaultOpen?: boolean;\n  open?: boolean;\n  disabled?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst Collapsible = React.forwardRef<CollapsibleElement, CollapsibleProps>(\n  (props: ScopedProps<CollapsibleProps>, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME,\n    });\n\n    return (\n      <CollapsibleProvider\n        scope={__scopeCollapsible}\n        disabled={disabled}\n        contentId={useId()}\n        open={open}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      >\n        <Primitive.div\n          data-state={getState(open)}\n          data-disabled={disabled ? '' : undefined}\n          {...collapsibleProps}\n          ref={forwardedRef}\n        />\n      </CollapsibleProvider>\n    );\n  }\n);\n\nCollapsible.displayName = COLLAPSIBLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CollapsibleTrigger';\n\ntype CollapsibleTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CollapsibleTriggerProps extends PrimitiveButtonProps {}\n\nconst CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(\n  (props: ScopedProps<CollapsibleTriggerProps>, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-controls={context.contentId}\n        aria-expanded={context.open || false}\n        data-state={getState(context.open)}\n        data-disabled={context.disabled ? '' : undefined}\n        disabled={context.disabled}\n        {...triggerProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nCollapsibleTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'CollapsibleContent';\n\ntype CollapsibleContentElement = CollapsibleContentImplElement;\ninterface CollapsibleContentProps extends Omit<CollapsibleContentImplProps, 'present'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(\n  (props: ScopedProps<CollapsibleContentProps>, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return (\n      <Presence present={forceMount || context.open}>\n        {({ present }) => (\n          <CollapsibleContentImpl {...contentProps} ref={forwardedRef} present={present} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nCollapsibleContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype CollapsibleContentImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface CollapsibleContentImplProps extends PrimitiveDivProps {\n  present: boolean;\n}\n\nconst CollapsibleContentImpl = React.forwardRef<\n  CollapsibleContentImplElement,\n  CollapsibleContentImplProps\n>((props: ScopedProps<CollapsibleContentImplProps>, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef<CollapsibleContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef<number | undefined>(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef<number | undefined>(0);\n  const width = widthRef.current;\n  // when opening we want it to immediately open to retrieve dimensions\n  // when closing we delay `present` to retrieve dimensions before closing\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef<Record<string, string>>(undefined);\n\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName,\n      };\n      // block any animations/transitions so the element renders at its full dimensions\n      node.style.transitionDuration = '0s';\n      node.style.animationName = 'none';\n\n      // get width and height from full dimensions\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n\n      // kick off any animations/transitions that were originally set up if it isn't the initial mount\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration!;\n        node.style.animationName = originalStylesRef.current.animationName!;\n      }\n\n      setIsPresent(present);\n    }\n    /**\n     * depends on `context.open` because it will change to `false`\n     * when a close is triggered but `present` will be `false` on\n     * animation end (so when close finishes). This allows us to\n     * retrieve the dimensions *before* closing.\n     */\n  }, [context.open, present]);\n\n  return (\n    <Primitive.div\n      data-state={getState(context.open)}\n      data-disabled={context.disabled ? '' : undefined}\n      id={context.contentId}\n      hidden={!isOpen}\n      {...contentProps}\n      ref={composedRefs}\n      style={{\n        [`--radix-collapsible-content-height` as any]: height ? `${height}px` : undefined,\n        [`--radix-collapsible-content-width` as any]: width ? `${width}px` : undefined,\n        ...props.style,\n      }}\n    >\n      {isOpen && children}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Collapsible;\nconst Trigger = CollapsibleTrigger;\nconst Content = CollapsibleContent;\n\nexport {\n  createCollapsibleScope,\n  //\n  Collapsible,\n  CollapsibleTrigger,\n  CollapsibleContent,\n  //\n  Root,\n  Trigger,\n  Content,\n};\nexport type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps };\n", "import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { createDialogScope } from '@radix-ui/react-dialog';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_NAME = 'AlertDialog';\n\ntype ScopedProps<P> = P & { __scopeAlertDialog?: Scope };\nconst [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope,\n]);\nconst useDialogScope = createDialogScope();\n\ntype DialogProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>;\ninterface AlertDialogProps extends Omit<DialogProps, 'modal'> {}\n\nconst AlertDialog: React.FC<AlertDialogProps> = (props: ScopedProps<AlertDialogProps>) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Root {...dialogScope} {...alertDialogProps} modal={true} />;\n};\n\nAlertDialog.displayName = ROOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTrigger\n * -----------------------------------------------------------------------------------------------*/\nconst TRIGGER_NAME = 'AlertDialogTrigger';\n\ntype AlertDialogTriggerElement = React.ComponentRef<typeof DialogPrimitive.Trigger>;\ntype DialogTriggerProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>;\ninterface AlertDialogTriggerProps extends DialogTriggerProps {}\n\nconst AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(\n  (props: ScopedProps<AlertDialogTriggerProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Trigger {...dialogScope} {...triggerProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'AlertDialogPortal';\n\ntype DialogPortalProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>;\ninterface AlertDialogPortalProps extends DialogPortalProps {}\n\nconst AlertDialogPortal: React.FC<AlertDialogPortalProps> = (\n  props: ScopedProps<AlertDialogPortalProps>\n) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Portal {...dialogScope} {...portalProps} />;\n};\n\nAlertDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'AlertDialogOverlay';\n\ntype AlertDialogOverlayElement = React.ComponentRef<typeof DialogPrimitive.Overlay>;\ntype DialogOverlayProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>;\ninterface AlertDialogOverlayProps extends DialogOverlayProps {}\n\nconst AlertDialogOverlay = React.forwardRef<AlertDialogOverlayElement, AlertDialogOverlayProps>(\n  (props: ScopedProps<AlertDialogOverlayProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Overlay {...dialogScope} {...overlayProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogOverlay.displayName = OVERLAY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AlertDialogContent';\n\ntype AlertDialogContentContextValue = {\n  cancelRef: React.MutableRefObject<AlertDialogCancelElement | null>;\n};\n\nconst [AlertDialogContentProvider, useAlertDialogContentContext] =\n  createAlertDialogContext<AlertDialogContentContextValue>(CONTENT_NAME);\n\ntype AlertDialogContentElement = React.ComponentRef<typeof DialogPrimitive.Content>;\ntype DialogContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>;\ninterface AlertDialogContentProps\n  extends Omit<DialogContentProps, 'onPointerDownOutside' | 'onInteractOutside'> {}\n\nconst Slottable = createSlottable('AlertDialogContent');\n\nconst AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(\n  (props: ScopedProps<AlertDialogContentProps>, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef<AlertDialogContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef<AlertDialogCancelElement | null>(null);\n\n    return (\n      <DialogPrimitive.WarningProvider\n        contentName={CONTENT_NAME}\n        titleName={TITLE_NAME}\n        docsSlug=\"alert-dialog\"\n      >\n        <AlertDialogContentProvider scope={__scopeAlertDialog} cancelRef={cancelRef}>\n          <DialogPrimitive.Content\n            role=\"alertdialog\"\n            {...dialogScope}\n            {...contentProps}\n            ref={composedRefs}\n            onOpenAutoFocus={composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            })}\n            onPointerDownOutside={(event) => event.preventDefault()}\n            onInteractOutside={(event) => event.preventDefault()}\n          >\n            {/**\n             * We have to use `Slottable` here as we cannot wrap the `AlertDialogContentProvider`\n             * around everything, otherwise the `DescriptionWarning` would be rendered straight away.\n             * This is because we want the accessibility checks to run only once the content is actually\n             * open and that behaviour is already encapsulated in `DialogContent`.\n             */}\n            <Slottable>{children}</Slottable>\n            {process.env.NODE_ENV === 'development' && (\n              <DescriptionWarning contentRef={contentRef} />\n            )}\n          </DialogPrimitive.Content>\n        </AlertDialogContentProvider>\n      </DialogPrimitive.WarningProvider>\n    );\n  }\n);\n\nAlertDialogContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'AlertDialogTitle';\n\ntype AlertDialogTitleElement = React.ComponentRef<typeof DialogPrimitive.Title>;\ntype DialogTitleProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>;\ninterface AlertDialogTitleProps extends DialogTitleProps {}\n\nconst AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>(\n  (props: ScopedProps<AlertDialogTitleProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Title {...dialogScope} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'AlertDialogDescription';\n\ntype AlertDialogDescriptionElement = React.ComponentRef<typeof DialogPrimitive.Description>;\ntype DialogDescriptionProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>;\ninterface AlertDialogDescriptionProps extends DialogDescriptionProps {}\n\nconst AlertDialogDescription = React.forwardRef<\n  AlertDialogDescriptionElement,\n  AlertDialogDescriptionProps\n>((props: ScopedProps<AlertDialogDescriptionProps>, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Description {...dialogScope} {...descriptionProps} ref={forwardedRef} />;\n});\n\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'AlertDialogAction';\n\ntype AlertDialogActionElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ntype DialogCloseProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogActionProps extends DialogCloseProps {}\n\nconst AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(\n  (props: ScopedProps<AlertDialogActionProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Close {...dialogScope} {...actionProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogCancel\n * -----------------------------------------------------------------------------------------------*/\n\nconst CANCEL_NAME = 'AlertDialogCancel';\n\ntype AlertDialogCancelElement = React.ComponentRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogCancelProps extends DialogCloseProps {}\n\nconst AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(\n  (props: ScopedProps<AlertDialogCancelProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return <DialogPrimitive.Close {...dialogScope} {...cancelProps} ref={ref} />;\n  }\n);\n\nAlertDialogCancel.displayName = CANCEL_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<AlertDialogContentElement | null>;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute('aria-describedby')!\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n\n  return null;\n};\n\nconst Root = AlertDialog;\nconst Trigger = AlertDialogTrigger;\nconst Portal = AlertDialogPortal;\nconst Overlay = AlertDialogOverlay;\nconst Content = AlertDialogContent;\nconst Action = AlertDialogAction;\nconst Cancel = AlertDialogCancel;\nconst Title = AlertDialogTitle;\nconst Description = AlertDialogDescription;\n\nexport {\n  createAlertDialogScope,\n  //\n  AlertDialog,\n  AlertDialogTrigger,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogContent,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Action,\n  Cancel,\n  Title,\n  Description,\n};\nexport type {\n  AlertDialogProps,\n  AlertDialogTriggerProps,\n  AlertDialogPortalProps,\n  AlertDialogOverlayProps,\n  AlertDialogContentProps,\n  AlertDialogActionProps,\n  AlertDialogCancelProps,\n  AlertDialogTitleProps,\n  AlertDialogDescriptionProps,\n};\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * AspectRatio\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'AspectRatio';\n\ntype AspectRatioElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AspectRatioProps extends PrimitiveDivProps {\n  ratio?: number;\n}\n\nconst AspectRatio = React.forwardRef<AspectRatioElement, AspectRatioProps>(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return (\n      <div\n        style={{\n          // ensures inner element is contained\n          position: 'relative',\n          // ensures padding bottom trick maths works\n          width: '100%',\n          paddingBottom: `${100 / ratio}%`,\n        }}\n        data-radix-aspect-ratio-wrapper=\"\"\n      >\n        <Primitive.div\n          {...aspectRatioProps}\n          ref={forwardedRef}\n          style={{\n            ...style,\n            // ensures children expand in ratio\n            position: 'absolute',\n            top: 0,\n            right: 0,\n            bottom: 0,\n            left: 0,\n          }}\n        />\n      </div>\n    );\n  }\n);\n\nAspectRatio.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = AspectRatio;\n\nexport {\n  AspectRatio,\n  //\n  Root,\n};\nexport type { AspectRatioProps };\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue<State extends CheckedState | boolean = CheckedState> = {\n  checked: State | boolean;\n  setChecked: React.Dispatch<React.SetStateAction<State | boolean>>;\n  disabled: boolean | undefined;\n  control: HTMLButtonElement | null;\n  setControl: React.Dispatch<React.SetStateAction<HTMLButtonElement | null>>;\n  name: string | undefined;\n  form: string | undefined;\n  value: string | number | readonly string[];\n  hasConsumerStoppedPropagationRef: React.RefObject<boolean>;\n  required: boolean | undefined;\n  defaultChecked: boolean | undefined;\n  isFormControl: boolean;\n  bubbleInput: HTMLInputElement | null;\n  setBubbleInput: React.Dispatch<React.SetStateAction<HTMLInputElement | null>>;\n};\n\nconst [CheckboxProviderImpl, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CheckboxProviderProps<State extends CheckedState = CheckedState> {\n  checked?: State | boolean;\n  defaultChecked?: State | boolean;\n  required?: boolean;\n  onCheckedChange?(checked: State | boolean): void;\n  name?: string;\n  form?: string;\n  disabled?: boolean;\n  value?: string | number | readonly string[];\n  children?: React.ReactNode;\n}\n\nfunction CheckboxProvider<State extends CheckedState = CheckedState>(\n  props: ScopedProps<CheckboxProviderProps<State>>\n) {\n  const {\n    __scopeCheckbox,\n    checked: checkedProp,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = 'on',\n    // @ts-expect-error\n    internal_do_not_use_render,\n  } = props;\n\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME,\n  });\n  const [control, setControl] = React.useState<HTMLButtonElement | null>(null);\n  const [bubbleInput, setBubbleInput] = React.useState<HTMLInputElement | null>(null);\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = control\n    ? !!form || !!control.closest('form')\n    : // We set this to true by default so that events bubble to forms without JS (SSR)\n      true;\n\n  const context: CheckboxContextValue<State> = {\n    checked: checked,\n    disabled: disabled,\n    setChecked: setChecked,\n    control: control,\n    setControl: setControl,\n    name: name,\n    form: form,\n    value: value,\n    hasConsumerStoppedPropagationRef: hasConsumerStoppedPropagationRef,\n    required: required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl: isFormControl,\n    bubbleInput,\n    setBubbleInput,\n  };\n\n  return (\n    <CheckboxProviderImpl\n      scope={__scopeCheckbox}\n      {...(context as unknown as CheckboxContextValue<CheckedState>)}\n    >\n      {isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children}\n    </CheckboxProviderImpl>\n  );\n}\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CheckboxTrigger';\n\ninterface CheckboxTriggerProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof Primitive.button>,\n    keyof CheckboxProviderProps\n  > {\n  children?: React.ReactNode;\n}\n\nconst CheckboxTrigger = React.forwardRef<HTMLButtonElement, CheckboxTriggerProps>(\n  (\n    { __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }: ScopedProps<CheckboxTriggerProps>,\n    forwardedRef\n  ) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput,\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setControl);\n\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [control, setChecked]);\n\n    return (\n      <Primitive.button\n        type=\"button\"\n        role=\"checkbox\"\n        aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n        aria-required={required}\n        data-state={getState(checked)}\n        data-disabled={disabled ? '' : undefined}\n        disabled={disabled}\n        value={value}\n        {...checkboxProps}\n        ref={composedRefs}\n        onKeyDown={composeEventHandlers(onKeyDown, (event) => {\n          // According to WAI ARIA, Checkboxes don't activate on enter keypress\n          if (event.key === 'Enter') event.preventDefault();\n        })}\n        onClick={composeEventHandlers(onClick, (event) => {\n          setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            // if checkbox has a bubble input and is a form control, stop\n            // propagation from the button so that we only propagate one click\n            // event (from the input). We propagate changes from an input so\n            // that native form validation works and form events reflect\n            // checkbox updates.\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })}\n      />\n    );\n  }\n);\n\nCheckboxTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\ntype CheckboxElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n\n    return (\n      <CheckboxProvider\n        __scopeCheckbox={__scopeCheckbox}\n        checked={checked}\n        defaultChecked={defaultChecked}\n        disabled={disabled}\n        required={required}\n        onCheckedChange={onCheckedChange}\n        name={name}\n        form={form}\n        value={value}\n        // @ts-expect-error\n        internal_do_not_use_render={({ isFormControl }: CheckboxContextValue) => (\n          <>\n            <CheckboxTrigger\n              {...checkboxProps}\n              ref={forwardedRef}\n              // @ts-expect-error\n              __scopeCheckbox={__scopeCheckbox}\n            />\n            {isFormControl && (\n              <CheckboxBubbleInput\n                // @ts-expect-error\n                __scopeCheckbox={__scopeCheckbox}\n              />\n            )}\n          </>\n        )}\n      />\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence\n        present={forceMount || isIndeterminate(context.checked) || context.checked === true}\n      >\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'CheckboxBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface CheckboxBubbleInputProps extends Omit<InputProps, 'checked'> {}\n\nconst CheckboxBubbleInput = React.forwardRef<HTMLInputElement, CheckboxBubbleInputProps>(\n  ({ __scopeCheckbox, ...props }: ScopedProps<CheckboxBubbleInputProps>, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput,\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n\n    const composedRefs = useComposedRefs(forwardedRef, setBubbleInput);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return (\n      <Primitive.input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n        required={required}\n        disabled={disabled}\n        name={name}\n        value={value}\n        form={form}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: 'translateX(-100%)',\n        }}\n      />\n    );\n  }\n);\n\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxProvider,\n  CheckboxTrigger,\n  CheckboxIndicator,\n  CheckboxBubbleInput,\n  //\n  Checkbox as Root,\n  CheckboxProvider as Provider,\n  CheckboxTrigger as Trigger,\n  CheckboxIndicator as Indicator,\n  CheckboxBubbleInput as BubbleInput,\n};\nexport type {\n  CheckboxProps,\n  CheckboxProviderProps,\n  CheckboxTriggerProps,\n  CheckboxIndicatorProps,\n  CheckboxBubbleInputProps,\n  CheckedState,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\ntype Point = { x: number; y: number };\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTEXT_MENU_NAME = 'ContextMenu';\n\ntype ScopedProps<P> = P & { __scopeContextMenu?: Scope };\nconst [createContextMenuContext, createContextMenuScope] = createContextScope(CONTEXT_MENU_NAME, [\n  createMenuScope,\n]);\nconst useMenuScope = createMenuScope();\n\ntype ContextMenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  modal: boolean;\n};\n\nconst [ContextMenuProvider, useContextMenuContext] =\n  createContextMenuContext<ContextMenuContextValue>(CONTEXT_MENU_NAME);\n\ninterface ContextMenuProps {\n  children?: React.ReactNode;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst ContextMenu: React.FC<ContextMenuProps> = (props: ScopedProps<ContextMenuProps>) => {\n  const { __scopeContextMenu, children, onOpenChange, dir, modal = true } = props;\n  const [open, setOpen] = React.useState(false);\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const handleOpenChangeProp = useCallbackRef(onOpenChange);\n\n  const handleOpenChange = React.useCallback(\n    (open: boolean) => {\n      setOpen(open);\n      handleOpenChangeProp(open);\n    },\n    [handleOpenChangeProp]\n  );\n\n  return (\n    <ContextMenuProvider\n      scope={__scopeContextMenu}\n      open={open}\n      onOpenChange={handleOpenChange}\n      modal={modal}\n    >\n      <MenuPrimitive.Root\n        {...menuScope}\n        dir={dir}\n        open={open}\n        onOpenChange={handleOpenChange}\n        modal={modal}\n      >\n        {children}\n      </MenuPrimitive.Root>\n    </ContextMenuProvider>\n  );\n};\n\nContextMenu.displayName = CONTEXT_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'ContextMenuTrigger';\n\ntype ContextMenuTriggerElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface ContextMenuTriggerProps extends PrimitiveSpanProps {\n  disabled?: boolean;\n}\n\nconst ContextMenuTrigger = React.forwardRef<ContextMenuTriggerElement, ContextMenuTriggerProps>(\n  (props: ScopedProps<ContextMenuTriggerProps>, forwardedRef) => {\n    const { __scopeContextMenu, disabled = false, ...triggerProps } = props;\n    const context = useContextMenuContext(TRIGGER_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const pointRef = React.useRef<Point>({ x: 0, y: 0 });\n    const virtualRef = React.useRef({\n      getBoundingClientRect: () => DOMRect.fromRect({ width: 0, height: 0, ...pointRef.current }),\n    });\n    const longPressTimerRef = React.useRef(0);\n    const clearLongPress = React.useCallback(\n      () => window.clearTimeout(longPressTimerRef.current),\n      []\n    );\n    const handleOpen = (event: React.MouseEvent | React.PointerEvent) => {\n      pointRef.current = { x: event.clientX, y: event.clientY };\n      context.onOpenChange(true);\n    };\n\n    React.useEffect(() => clearLongPress, [clearLongPress]);\n    React.useEffect(() => void (disabled && clearLongPress()), [disabled, clearLongPress]);\n\n    return (\n      <>\n        <MenuPrimitive.Anchor {...menuScope} virtualRef={virtualRef} />\n        <Primitive.span\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          {...triggerProps}\n          ref={forwardedRef}\n          // prevent iOS context menu from appearing\n          style={{ WebkitTouchCallout: 'none', ...props.style }}\n          // if trigger is disabled, enable the native Context Menu\n          onContextMenu={\n            disabled\n              ? props.onContextMenu\n              : composeEventHandlers(props.onContextMenu, (event) => {\n                  // clearing the long press here because some platforms already support\n                  // long press to trigger a `contextmenu` event\n                  clearLongPress();\n                  handleOpen(event);\n                  event.preventDefault();\n                })\n          }\n          onPointerDown={\n            disabled\n              ? props.onPointerDown\n              : composeEventHandlers(\n                  props.onPointerDown,\n                  whenTouchOrPen((event) => {\n                    // clear the long press here in case there's multiple touch points\n                    clearLongPress();\n                    longPressTimerRef.current = window.setTimeout(() => handleOpen(event), 700);\n                  })\n                )\n          }\n          onPointerMove={\n            disabled\n              ? props.onPointerMove\n              : composeEventHandlers(props.onPointerMove, whenTouchOrPen(clearLongPress))\n          }\n          onPointerCancel={\n            disabled\n              ? props.onPointerCancel\n              : composeEventHandlers(props.onPointerCancel, whenTouchOrPen(clearLongPress))\n          }\n          onPointerUp={\n            disabled\n              ? props.onPointerUp\n              : composeEventHandlers(props.onPointerUp, whenTouchOrPen(clearLongPress))\n          }\n        />\n      </>\n    );\n  }\n);\n\nContextMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'ContextMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface ContextMenuPortalProps extends MenuPortalProps {}\n\nconst ContextMenuPortal: React.FC<ContextMenuPortalProps> = (\n  props: ScopedProps<ContextMenuPortalProps>\n) => {\n  const { __scopeContextMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nContextMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'ContextMenuContent';\n\ntype ContextMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface ContextMenuContentProps\n  extends Omit<MenuContentProps, 'onEntryFocus' | 'side' | 'sideOffset' | 'align'> {}\n\nconst ContextMenuContent = React.forwardRef<ContextMenuContentElement, ContextMenuContentProps>(\n  (props: ScopedProps<ContextMenuContentProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...contentProps } = props;\n    const context = useContextMenuContext(CONTENT_NAME, __scopeContextMenu);\n    const menuScope = useMenuScope(__scopeContextMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        side=\"right\"\n        sideOffset={2}\n        align=\"start\"\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented && hasInteractedOutsideRef.current) {\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented && !context.modal) hasInteractedOutsideRef.current = true;\n        }}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-context-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-context-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-context-menu-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-context-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-context-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nContextMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'ContextMenuGroup';\n\ntype ContextMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface ContextMenuGroupProps extends MenuGroupProps {}\n\nconst ContextMenuGroup = React.forwardRef<ContextMenuGroupElement, ContextMenuGroupProps>(\n  (props: ScopedProps<ContextMenuGroupProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'ContextMenuLabel';\n\ntype ContextMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface ContextMenuLabelProps extends MenuLabelProps {}\n\nconst ContextMenuLabel = React.forwardRef<ContextMenuLabelElement, ContextMenuLabelProps>(\n  (props: ScopedProps<ContextMenuLabelProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'ContextMenuItem';\n\ntype ContextMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface ContextMenuItemProps extends MenuItemProps {}\n\nconst ContextMenuItem = React.forwardRef<ContextMenuItemElement, ContextMenuItemProps>(\n  (props: ScopedProps<ContextMenuItemProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'ContextMenuCheckboxItem';\n\ntype ContextMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface ContextMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  ContextMenuCheckboxItemElement,\n  ContextMenuCheckboxItemProps\n>((props: ScopedProps<ContextMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nContextMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'ContextMenuRadioGroup';\n\ntype ContextMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface ContextMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst ContextMenuRadioGroup = React.forwardRef<\n  ContextMenuRadioGroupElement,\n  ContextMenuRadioGroupProps\n>((props: ScopedProps<ContextMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nContextMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'ContextMenuRadioItem';\n\ntype ContextMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface ContextMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst ContextMenuRadioItem = React.forwardRef<\n  ContextMenuRadioItemElement,\n  ContextMenuRadioItemProps\n>((props: ScopedProps<ContextMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nContextMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ContextMenuItemIndicator';\n\ntype ContextMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface ContextMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst ContextMenuItemIndicator = React.forwardRef<\n  ContextMenuItemIndicatorElement,\n  ContextMenuItemIndicatorProps\n>((props: ScopedProps<ContextMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nContextMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'ContextMenuSeparator';\n\ntype ContextMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface ContextMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst ContextMenuSeparator = React.forwardRef<\n  ContextMenuSeparatorElement,\n  ContextMenuSeparatorProps\n>((props: ScopedProps<ContextMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nContextMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'ContextMenuArrow';\n\ntype ContextMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface ContextMenuArrowProps extends MenuArrowProps {}\n\nconst ContextMenuArrow = React.forwardRef<ContextMenuArrowElement, ContextMenuArrowProps>(\n  (props: ScopedProps<ContextMenuArrowProps>, forwardedRef) => {\n    const { __scopeContextMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeContextMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nContextMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'ContextMenuSub';\n\ninterface ContextMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst ContextMenuSub: React.FC<ContextMenuSubProps> = (props: ScopedProps<ContextMenuSubProps>) => {\n  const { __scopeContextMenu, children, onOpenChange, open: openProp, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SUB_NAME,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\nContextMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'ContextMenuSubTrigger';\n\ntype ContextMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface ContextMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  ContextMenuSubTriggerElement,\n  ContextMenuSubTriggerProps\n>((props: ScopedProps<ContextMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...triggerItemProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...triggerItemProps} ref={forwardedRef} />;\n});\n\nContextMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ContextMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'ContextMenuSubContent';\n\ntype ContextMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface ContextMenuSubContentProps extends MenuSubContentProps {}\n\nconst ContextMenuSubContent = React.forwardRef<\n  ContextMenuSubContentElement,\n  ContextMenuSubContentProps\n>((props: ScopedProps<ContextMenuSubContentProps>, forwardedRef) => {\n  const { __scopeContextMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeContextMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-context-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-context-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-context-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-context-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-context-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nContextMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction whenTouchOrPen<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType !== 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = ContextMenu;\nconst Trigger = ContextMenuTrigger;\nconst Portal = ContextMenuPortal;\nconst Content = ContextMenuContent;\nconst Group = ContextMenuGroup;\nconst Label = ContextMenuLabel;\nconst Item = ContextMenuItem;\nconst CheckboxItem = ContextMenuCheckboxItem;\nconst RadioGroup = ContextMenuRadioGroup;\nconst RadioItem = ContextMenuRadioItem;\nconst ItemIndicator = ContextMenuItemIndicator;\nconst Separator = ContextMenuSeparator;\nconst Arrow = ContextMenuArrow;\nconst Sub = ContextMenuSub;\nconst SubTrigger = ContextMenuSubTrigger;\nconst SubContent = ContextMenuSubContent;\n\nexport {\n  createContextMenuScope,\n  //\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuPortal,\n  ContextMenuContent,\n  ContextMenuGroup,\n  ContextMenuLabel,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioGroup,\n  ContextMenuRadioItem,\n  ContextMenuItemIndicator,\n  ContextMenuSeparator,\n  ContextMenuArrow,\n  ContextMenuSub,\n  ContextMenuSubTrigger,\n  ContextMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  ContextMenuProps,\n  ContextMenuTriggerProps,\n  ContextMenuPortalProps,\n  ContextMenuContentProps,\n  ContextMenuGroupProps,\n  ContextMenuLabelProps,\n  ContextMenuItemProps,\n  ContextMenuCheckboxItemProps,\n  ContextMenuRadioGroupProps,\n  ContextMenuRadioItemProps,\n  ContextMenuItemIndicatorProps,\n  ContextMenuSeparatorProps,\n  ContextMenuArrowProps,\n  ContextMenuSubProps,\n  ContextMenuSubTriggerProps,\n  ContextMenuSubContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Label as LabelPrimitive } from '@radix-ui/react-label';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P> = P & { __scopeForm?: Scope };\nconst [createFormContext, createFormScope] = createContextScope('Form');\n\n/* -------------------------------------------------------------------------------------------------\n * Form\n * -----------------------------------------------------------------------------------------------*/\n\nconst FORM_NAME = 'Form';\n\ntype ValidityMap = { [fieldName: string]: ValidityState | undefined };\ntype CustomMatcherEntriesMap = { [fieldName: string]: CustomMatcherEntry[] };\ntype CustomErrorsMap = { [fieldName: string]: Record<string, boolean> };\n\ntype ValidationContextValue = {\n  getFieldValidity(fieldName: string): ValidityState | undefined;\n  onFieldValidityChange(fieldName: string, validity: ValidityState): void;\n\n  getFieldCustomMatcherEntries(fieldName: string): CustomMatcherEntry[];\n  onFieldCustomMatcherEntryAdd(fieldName: string, matcherEntry: CustomMatcherEntry): void;\n  onFieldCustomMatcherEntryRemove(fieldName: string, matcherEntryId: string): void;\n\n  getFieldCustomErrors(fieldName: string): Record<string, boolean>;\n  onFieldCustomErrorsChange(fieldName: string, errors: Record<string, boolean>): void;\n\n  onFieldValiditionClear(fieldName: string): void;\n};\nconst [ValidationProvider, useValidationContext] =\n  createFormContext<ValidationContextValue>(FORM_NAME);\n\ntype MessageIdsMap = { [fieldName: string]: Set<string> };\n\ntype AriaDescriptionContextValue = {\n  onFieldMessageIdAdd(fieldName: string, id: string): void;\n  onFieldMessageIdRemove(fieldName: string, id: string): void;\n  getFieldDescription(fieldName: string): string | undefined;\n};\nconst [AriaDescriptionProvider, useAriaDescriptionContext] =\n  createFormContext<AriaDescriptionContextValue>(FORM_NAME);\n\ntype FormElement = React.ComponentRef<typeof Primitive.form>;\ntype PrimitiveFormProps = React.ComponentPropsWithoutRef<typeof Primitive.form>;\ninterface FormProps extends PrimitiveFormProps {\n  onClearServerErrors?(): void;\n}\n\nconst Form = React.forwardRef<FormElement, FormProps>(\n  (props: ScopedProps<FormProps>, forwardedRef) => {\n    const { __scopeForm, onClearServerErrors = () => {}, ...rootProps } = props;\n    const formRef = React.useRef<HTMLFormElement>(null);\n    const composedFormRef = useComposedRefs(forwardedRef, formRef);\n\n    // native validity per field\n    const [validityMap, setValidityMap] = React.useState<ValidityMap>({});\n    const getFieldValidity: ValidationContextValue['getFieldValidity'] = React.useCallback(\n      (fieldName) => validityMap[fieldName],\n      [validityMap]\n    );\n    const handleFieldValidityChange: ValidationContextValue['onFieldValidityChange'] =\n      React.useCallback(\n        (fieldName, validity) =>\n          setValidityMap((prevValidityMap) => ({\n            ...prevValidityMap,\n            [fieldName]: { ...(prevValidityMap[fieldName] ?? {}), ...validity },\n          })),\n        []\n      );\n    const handleFieldValiditionClear: ValidationContextValue['onFieldValiditionClear'] =\n      React.useCallback((fieldName) => {\n        setValidityMap((prevValidityMap) => ({ ...prevValidityMap, [fieldName]: undefined }));\n        setCustomErrorsMap((prevCustomErrorsMap) => ({ ...prevCustomErrorsMap, [fieldName]: {} }));\n      }, []);\n\n    // custom matcher entries per field\n    const [customMatcherEntriesMap, setCustomMatcherEntriesMap] =\n      React.useState<CustomMatcherEntriesMap>({});\n    const getFieldCustomMatcherEntries: ValidationContextValue['getFieldCustomMatcherEntries'] =\n      React.useCallback(\n        (fieldName) => customMatcherEntriesMap[fieldName] ?? [],\n        [customMatcherEntriesMap]\n      );\n    const handleFieldCustomMatcherAdd: ValidationContextValue['onFieldCustomMatcherEntryAdd'] =\n      React.useCallback((fieldName, matcherEntry) => {\n        setCustomMatcherEntriesMap((prevCustomMatcherEntriesMap) => ({\n          ...prevCustomMatcherEntriesMap,\n          [fieldName]: [...(prevCustomMatcherEntriesMap[fieldName] ?? []), matcherEntry],\n        }));\n      }, []);\n    const handleFieldCustomMatcherRemove: ValidationContextValue['onFieldCustomMatcherEntryRemove'] =\n      React.useCallback((fieldName, matcherEntryId) => {\n        setCustomMatcherEntriesMap((prevCustomMatcherEntriesMap) => ({\n          ...prevCustomMatcherEntriesMap,\n          [fieldName]: (prevCustomMatcherEntriesMap[fieldName] ?? []).filter(\n            (matcherEntry) => matcherEntry.id !== matcherEntryId\n          ),\n        }));\n      }, []);\n\n    // custom errors per field\n    const [customErrorsMap, setCustomErrorsMap] = React.useState<CustomErrorsMap>({});\n    const getFieldCustomErrors: ValidationContextValue['getFieldCustomErrors'] = React.useCallback(\n      (fieldName) => customErrorsMap[fieldName] ?? {},\n      [customErrorsMap]\n    );\n    const handleFieldCustomErrorsChange: ValidationContextValue['onFieldCustomErrorsChange'] =\n      React.useCallback((fieldName, customErrors) => {\n        setCustomErrorsMap((prevCustomErrorsMap) => ({\n          ...prevCustomErrorsMap,\n          [fieldName]: { ...(prevCustomErrorsMap[fieldName] ?? {}), ...customErrors },\n        }));\n      }, []);\n\n    // messageIds per field\n    const [messageIdsMap, setMessageIdsMap] = React.useState<MessageIdsMap>({});\n    const handleFieldMessageIdAdd: AriaDescriptionContextValue['onFieldMessageIdAdd'] =\n      React.useCallback((fieldName, id) => {\n        setMessageIdsMap((prevMessageIdsMap) => {\n          const fieldDescriptionIds = new Set(prevMessageIdsMap[fieldName]).add(id);\n          return { ...prevMessageIdsMap, [fieldName]: fieldDescriptionIds };\n        });\n      }, []);\n    const handleFieldMessageIdRemove: AriaDescriptionContextValue['onFieldMessageIdRemove'] =\n      React.useCallback((fieldName, id) => {\n        setMessageIdsMap((prevMessageIdsMap) => {\n          const fieldDescriptionIds = new Set(prevMessageIdsMap[fieldName]);\n          fieldDescriptionIds.delete(id);\n          return { ...prevMessageIdsMap, [fieldName]: fieldDescriptionIds };\n        });\n      }, []);\n    const getFieldDescription: AriaDescriptionContextValue['getFieldDescription'] =\n      React.useCallback(\n        (fieldName) => Array.from(messageIdsMap[fieldName] ?? []).join(' ') || undefined,\n        [messageIdsMap]\n      );\n\n    return (\n      <ValidationProvider\n        scope={__scopeForm}\n        getFieldValidity={getFieldValidity}\n        onFieldValidityChange={handleFieldValidityChange}\n        getFieldCustomMatcherEntries={getFieldCustomMatcherEntries}\n        onFieldCustomMatcherEntryAdd={handleFieldCustomMatcherAdd}\n        onFieldCustomMatcherEntryRemove={handleFieldCustomMatcherRemove}\n        getFieldCustomErrors={getFieldCustomErrors}\n        onFieldCustomErrorsChange={handleFieldCustomErrorsChange}\n        onFieldValiditionClear={handleFieldValiditionClear}\n      >\n        <AriaDescriptionProvider\n          scope={__scopeForm}\n          onFieldMessageIdAdd={handleFieldMessageIdAdd}\n          onFieldMessageIdRemove={handleFieldMessageIdRemove}\n          getFieldDescription={getFieldDescription}\n        >\n          <Primitive.form\n            {...rootProps}\n            ref={composedFormRef}\n            // focus first invalid control when the form is submitted\n            onInvalid={composeEventHandlers(props.onInvalid, (event) => {\n              const firstInvalidControl = getFirstInvalidControl(event.currentTarget);\n              if (firstInvalidControl === event.target) firstInvalidControl.focus();\n\n              // prevent default browser UI for form validation\n              event.preventDefault();\n            })}\n            // clear server errors when the form is re-submitted\n            onSubmit={composeEventHandlers(props.onSubmit, onClearServerErrors, {\n              checkForDefaultPrevented: false,\n            })}\n            // clear server errors when the form is reset\n            onReset={composeEventHandlers(props.onReset, onClearServerErrors)}\n          />\n        </AriaDescriptionProvider>\n      </ValidationProvider>\n    );\n  }\n);\n\nForm.displayName = FORM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormField\n * -----------------------------------------------------------------------------------------------*/\n\nconst FIELD_NAME = 'FormField';\n\ntype FormFieldContextValue = {\n  id: string;\n  name: string;\n  serverInvalid: boolean;\n};\nconst [FormFieldProvider, useFormFieldContext] =\n  createFormContext<FormFieldContextValue>(FIELD_NAME);\n\ntype FormFieldElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FormFieldProps extends PrimitiveDivProps {\n  name: string;\n  serverInvalid?: boolean;\n}\n\nconst FormField = React.forwardRef<FormFieldElement, FormFieldProps>(\n  (props: ScopedProps<FormFieldProps>, forwardedRef) => {\n    const { __scopeForm, name, serverInvalid = false, ...fieldProps } = props;\n    const validationContext = useValidationContext(FIELD_NAME, __scopeForm);\n    const validity = validationContext.getFieldValidity(name);\n    const id = useId();\n\n    return (\n      <FormFieldProvider scope={__scopeForm} id={id} name={name} serverInvalid={serverInvalid}>\n        <Primitive.div\n          data-valid={getValidAttribute(validity, serverInvalid)}\n          data-invalid={getInvalidAttribute(validity, serverInvalid)}\n          {...fieldProps}\n          ref={forwardedRef}\n        />\n      </FormFieldProvider>\n    );\n  }\n);\n\nFormField.displayName = FIELD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'FormLabel';\n\ntype FormLabelElement = React.ComponentRef<typeof LabelPrimitive>;\ntype LabelProps = React.ComponentPropsWithoutRef<typeof LabelPrimitive>;\ninterface FormLabelProps extends LabelProps {}\n\nconst FormLabel = React.forwardRef<FormLabelElement, FormLabelProps>(\n  (props: ScopedProps<FormLabelProps>, forwardedRef) => {\n    const { __scopeForm, ...labelProps } = props;\n    const validationContext = useValidationContext(LABEL_NAME, __scopeForm);\n    const fieldContext = useFormFieldContext(LABEL_NAME, __scopeForm);\n    const htmlFor = labelProps.htmlFor || fieldContext.id;\n    const validity = validationContext.getFieldValidity(fieldContext.name);\n\n    return (\n      <LabelPrimitive\n        data-valid={getValidAttribute(validity, fieldContext.serverInvalid)}\n        data-invalid={getInvalidAttribute(validity, fieldContext.serverInvalid)}\n        {...labelProps}\n        ref={forwardedRef}\n        htmlFor={htmlFor}\n      />\n    );\n  }\n);\n\nFormLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormControl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTROL_NAME = 'FormControl';\n\ntype FormControlElement = React.ComponentRef<typeof Primitive.input>;\ntype PrimitiveInputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface FormControlProps extends PrimitiveInputProps {}\n\nconst FormControl = React.forwardRef<FormControlElement, FormControlProps>(\n  (props: ScopedProps<FormControlProps>, forwardedRef) => {\n    const { __scopeForm, ...controlProps } = props;\n\n    const validationContext = useValidationContext(CONTROL_NAME, __scopeForm);\n    const fieldContext = useFormFieldContext(CONTROL_NAME, __scopeForm);\n    const ariaDescriptionContext = useAriaDescriptionContext(CONTROL_NAME, __scopeForm);\n\n    const ref = React.useRef<FormControlElement>(null);\n    const composedRef = useComposedRefs(forwardedRef, ref);\n    const name = controlProps.name || fieldContext.name;\n    const id = controlProps.id || fieldContext.id;\n    const customMatcherEntries = validationContext.getFieldCustomMatcherEntries(name);\n\n    const { onFieldValidityChange, onFieldCustomErrorsChange, onFieldValiditionClear } =\n      validationContext;\n    const updateControlValidity = React.useCallback(\n      async (control: FormControlElement) => {\n        //------------------------------------------------------------------------------------------\n        // 1. first, if we have built-in errors we stop here\n\n        if (hasBuiltInError(control.validity)) {\n          const controlValidity = validityStateToObject(control.validity);\n          onFieldValidityChange(name, controlValidity);\n          return;\n        }\n\n        //------------------------------------------------------------------------------------------\n        // 2. then gather the form data to give to custom matchers for cross-comparisons\n\n        const formData = control.form ? new FormData(control.form) : new FormData();\n        const matcherArgs: CustomMatcherArgs = [control.value, formData];\n\n        //------------------------------------------------------------------------------------------\n        // 3. split sync and async custom matcher entries\n\n        const syncCustomMatcherEntries: Array<SyncCustomMatcherEntry> = [];\n        const ayncCustomMatcherEntries: Array<AsyncCustomMatcherEntry> = [];\n        customMatcherEntries.forEach((customMatcherEntry) => {\n          if (isAsyncCustomMatcherEntry(customMatcherEntry, matcherArgs)) {\n            ayncCustomMatcherEntries.push(customMatcherEntry);\n          } else if (isSyncCustomMatcherEntry(customMatcherEntry)) {\n            syncCustomMatcherEntries.push(customMatcherEntry);\n          }\n        });\n\n        //------------------------------------------------------------------------------------------\n        // 4. run sync custom matchers and update control validity / internal validity + errors\n\n        const syncCustomErrors = syncCustomMatcherEntries.map(({ id, match }) => {\n          return [id, match(...matcherArgs)] as const;\n        });\n        const syncCustomErrorsById = Object.fromEntries(syncCustomErrors);\n        const hasSyncCustomErrors = Object.values(syncCustomErrorsById).some(Boolean);\n        const hasCustomError = hasSyncCustomErrors;\n        control.setCustomValidity(hasCustomError ? DEFAULT_INVALID_MESSAGE : '');\n        const controlValidity = validityStateToObject(control.validity);\n        onFieldValidityChange(name, controlValidity);\n        onFieldCustomErrorsChange(name, syncCustomErrorsById);\n\n        //------------------------------------------------------------------------------------------\n        // 5. run async custom matchers and update control validity / internal validity + errors\n\n        if (!hasSyncCustomErrors && ayncCustomMatcherEntries.length > 0) {\n          const promisedCustomErrors = ayncCustomMatcherEntries.map(({ id, match }) =>\n            match(...matcherArgs).then((matches) => [id, matches] as const)\n          );\n          const asyncCustomErrors = await Promise.all(promisedCustomErrors);\n          const asyncCustomErrorsById = Object.fromEntries(asyncCustomErrors);\n          const hasAsyncCustomErrors = Object.values(asyncCustomErrorsById).some(Boolean);\n          const hasCustomError = hasAsyncCustomErrors;\n          control.setCustomValidity(hasCustomError ? DEFAULT_INVALID_MESSAGE : '');\n          const controlValidity = validityStateToObject(control.validity);\n          onFieldValidityChange(name, controlValidity);\n          onFieldCustomErrorsChange(name, asyncCustomErrorsById);\n        }\n      },\n      [customMatcherEntries, name, onFieldCustomErrorsChange, onFieldValidityChange]\n    );\n\n    React.useEffect(() => {\n      const control = ref.current;\n      if (control) {\n        // We only want validate on change (native `change` event, not React's `onChange`). This is primarily\n        // a UX decision, we don't want to validate on every keystroke and React's `onChange` is the `input` event.\n        const handleChange = () => updateControlValidity(control);\n        control.addEventListener('change', handleChange);\n        return () => control.removeEventListener('change', handleChange);\n      }\n    }, [updateControlValidity]);\n\n    const resetControlValidity = React.useCallback(() => {\n      const control = ref.current;\n      if (control) {\n        control.setCustomValidity('');\n        onFieldValiditionClear(name);\n      }\n    }, [name, onFieldValiditionClear]);\n\n    // reset validity and errors when the form is reset\n    React.useEffect(() => {\n      const form = ref.current?.form;\n      if (form) {\n        form.addEventListener('reset', resetControlValidity);\n        return () => form.removeEventListener('reset', resetControlValidity);\n      }\n    }, [resetControlValidity]);\n\n    // focus first invalid control when fields are set as invalid by server\n    React.useEffect(() => {\n      const control = ref.current;\n      const form = control?.closest('form');\n      if (form && fieldContext.serverInvalid) {\n        const firstInvalidControl = getFirstInvalidControl(form);\n        if (firstInvalidControl === control) firstInvalidControl.focus();\n      }\n    }, [fieldContext.serverInvalid]);\n\n    const validity = validationContext.getFieldValidity(name);\n\n    return (\n      <Primitive.input\n        data-valid={getValidAttribute(validity, fieldContext.serverInvalid)}\n        data-invalid={getInvalidAttribute(validity, fieldContext.serverInvalid)}\n        aria-invalid={fieldContext.serverInvalid ? true : undefined}\n        aria-describedby={ariaDescriptionContext.getFieldDescription(name)}\n        // disable default browser behaviour of showing built-in error message on hover\n        title=\"\"\n        {...controlProps}\n        ref={composedRef}\n        id={id}\n        name={name}\n        onInvalid={composeEventHandlers(props.onInvalid, (event) => {\n          const control = event.currentTarget;\n          updateControlValidity(control);\n        })}\n        onChange={composeEventHandlers(props.onChange, (_event) => {\n          // reset validity when user changes value\n          resetControlValidity();\n        })}\n      />\n    );\n  }\n);\n\nFormControl.displayName = CONTROL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormMessage\n * -----------------------------------------------------------------------------------------------*/\n\nconst _validityMatchers = [\n  'badInput',\n  'patternMismatch',\n  'rangeOverflow',\n  'rangeUnderflow',\n  'stepMismatch',\n  'tooLong',\n  'tooShort',\n  'typeMismatch',\n  'valid',\n  'valueMissing',\n] as const;\ntype ValidityMatcher = (typeof _validityMatchers)[number];\n\nconst DEFAULT_INVALID_MESSAGE = 'This value is not valid';\nconst DEFAULT_BUILT_IN_MESSAGES: Record<ValidityMatcher, string | undefined> = {\n  badInput: DEFAULT_INVALID_MESSAGE,\n  patternMismatch: 'This value does not match the required pattern',\n  rangeOverflow: 'This value is too large',\n  rangeUnderflow: 'This value is too small',\n  stepMismatch: 'This value does not match the required step',\n  tooLong: 'This value is too long',\n  tooShort: 'This value is too short',\n  typeMismatch: 'This value does not match the required type',\n  valid: undefined,\n  valueMissing: 'This value is missing',\n};\n\nconst MESSAGE_NAME = 'FormMessage';\n\ntype FormMessageElement = FormMessageImplElement;\ninterface FormMessageProps extends Omit<FormMessageImplProps, 'name'> {\n  match?: ValidityMatcher | CustomMatcher;\n  forceMatch?: boolean;\n  name?: string;\n}\n\nconst FormMessage = React.forwardRef<FormMessageElement, FormMessageProps>(\n  (props: ScopedProps<FormMessageProps>, forwardedRef) => {\n    const { match, name: nameProp, ...messageProps } = props;\n    const fieldContext = useFormFieldContext(MESSAGE_NAME, props.__scopeForm);\n    const name = nameProp ?? fieldContext.name;\n\n    if (match === undefined) {\n      return (\n        <FormMessageImpl {...messageProps} ref={forwardedRef} name={name}>\n          {props.children || DEFAULT_INVALID_MESSAGE}\n        </FormMessageImpl>\n      );\n    } else if (typeof match === 'function') {\n      return <FormCustomMessage match={match} {...messageProps} ref={forwardedRef} name={name} />;\n    } else {\n      return <FormBuiltInMessage match={match} {...messageProps} ref={forwardedRef} name={name} />;\n    }\n  }\n);\n\nFormMessage.displayName = MESSAGE_NAME;\n\ntype FormBuiltInMessageElement = FormMessageImplElement;\ninterface FormBuiltInMessageProps extends FormMessageImplProps {\n  match: ValidityMatcher;\n  forceMatch?: boolean;\n  name: string;\n}\n\nconst FormBuiltInMessage = React.forwardRef<FormBuiltInMessageElement, FormBuiltInMessageProps>(\n  (props: ScopedProps<FormBuiltInMessageProps>, forwardedRef) => {\n    const { match, forceMatch = false, name, children, ...messageProps } = props;\n    const validationContext = useValidationContext(MESSAGE_NAME, messageProps.__scopeForm);\n    const validity = validationContext.getFieldValidity(name);\n    const matches = forceMatch || validity?.[match];\n\n    if (matches) {\n      return (\n        <FormMessageImpl ref={forwardedRef} {...messageProps} name={name}>\n          {children ?? DEFAULT_BUILT_IN_MESSAGES[match]}\n        </FormMessageImpl>\n      );\n    }\n\n    return null;\n  }\n);\n\ntype FormCustomMessageElement = React.ComponentRef<typeof FormMessageImpl>;\ninterface FormCustomMessageProps extends React.ComponentPropsWithoutRef<typeof FormMessageImpl> {\n  match: CustomMatcher;\n  forceMatch?: boolean;\n  name: string;\n}\n\nconst FormCustomMessage = React.forwardRef<FormCustomMessageElement, FormCustomMessageProps>(\n  (props: ScopedProps<FormCustomMessageProps>, forwardedRef) => {\n    const { match, forceMatch = false, name, id: idProp, children, ...messageProps } = props;\n    const validationContext = useValidationContext(MESSAGE_NAME, messageProps.__scopeForm);\n    const ref = React.useRef<FormCustomMessageElement>(null);\n    const composedRef = useComposedRefs(forwardedRef, ref);\n    const _id = useId();\n    const id = idProp ?? _id;\n\n    const customMatcherEntry = React.useMemo(() => ({ id, match }), [id, match]);\n    const { onFieldCustomMatcherEntryAdd, onFieldCustomMatcherEntryRemove } = validationContext;\n    React.useEffect(() => {\n      onFieldCustomMatcherEntryAdd(name, customMatcherEntry);\n      return () => onFieldCustomMatcherEntryRemove(name, customMatcherEntry.id);\n    }, [customMatcherEntry, name, onFieldCustomMatcherEntryAdd, onFieldCustomMatcherEntryRemove]);\n\n    const validity = validationContext.getFieldValidity(name);\n    const customErrors = validationContext.getFieldCustomErrors(name);\n    const hasMatchingCustomError = customErrors[id];\n    const matches =\n      forceMatch || (validity && !hasBuiltInError(validity) && hasMatchingCustomError);\n\n    if (matches) {\n      return (\n        <FormMessageImpl id={id} ref={composedRef} {...messageProps} name={name}>\n          {children ?? DEFAULT_INVALID_MESSAGE}\n        </FormMessageImpl>\n      );\n    }\n\n    return null;\n  }\n);\n\ntype FormMessageImplElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface FormMessageImplProps extends PrimitiveSpanProps {\n  name: string;\n}\n\nconst FormMessageImpl = React.forwardRef<FormMessageImplElement, FormMessageImplProps>(\n  (props: ScopedProps<FormMessageImplProps>, forwardedRef) => {\n    const { __scopeForm, id: idProp, name, ...messageProps } = props;\n    const ariaDescriptionContext = useAriaDescriptionContext(MESSAGE_NAME, __scopeForm);\n    const _id = useId();\n    const id = idProp ?? _id;\n\n    const { onFieldMessageIdAdd, onFieldMessageIdRemove } = ariaDescriptionContext;\n    React.useEffect(() => {\n      onFieldMessageIdAdd(name, id);\n      return () => onFieldMessageIdRemove(name, id);\n    }, [name, id, onFieldMessageIdAdd, onFieldMessageIdRemove]);\n\n    return <Primitive.span id={id} {...messageProps} ref={forwardedRef} />;\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * FormValidityState\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALIDITY_STATE_NAME = 'FormValidityState';\n\ninterface FormValidityStateProps {\n  children(validity: ValidityState | undefined): React.ReactNode;\n  name?: string;\n}\n\nconst FormValidityState = (props: ScopedProps<FormValidityStateProps>) => {\n  const { __scopeForm, name: nameProp, children } = props;\n  const validationContext = useValidationContext(VALIDITY_STATE_NAME, __scopeForm);\n  const fieldContext = useFormFieldContext(VALIDITY_STATE_NAME, __scopeForm);\n  const name = nameProp ?? fieldContext.name;\n  const validity = validationContext.getFieldValidity(name);\n  return <>{children(validity)}</>;\n};\n\nFormValidityState.displayName = VALIDITY_STATE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * FormSubmit\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUBMIT_NAME = 'FormSubmit';\n\ntype FormSubmitElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface FormSubmitProps extends PrimitiveButtonProps {}\n\nconst FormSubmit = React.forwardRef<FormSubmitElement, FormSubmitProps>(\n  (props: ScopedProps<FormSubmitProps>, forwardedRef) => {\n    const { __scopeForm, ...submitProps } = props;\n    return <Primitive.button type=\"submit\" {...submitProps} ref={forwardedRef} />;\n  }\n);\n\nFormSubmit.displayName = SUBMIT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ValidityStateKey = keyof ValidityState;\ntype SyncCustomMatcher = (value: string, formData: FormData) => boolean;\ntype AsyncCustomMatcher = (value: string, formData: FormData) => Promise<boolean>;\ntype CustomMatcher = SyncCustomMatcher | AsyncCustomMatcher;\ntype CustomMatcherEntry = { id: string; match: CustomMatcher };\ntype SyncCustomMatcherEntry = { id: string; match: SyncCustomMatcher };\ntype AsyncCustomMatcherEntry = { id: string; match: AsyncCustomMatcher };\ntype CustomMatcherArgs = [string, FormData];\n\nfunction validityStateToObject(validity: ValidityState) {\n  const object: any = {};\n  for (const key in validity) {\n    object[key] = validity[key as ValidityStateKey];\n  }\n  return object as Record<ValidityStateKey, boolean>;\n}\n\nfunction isHTMLElement(element: any): element is HTMLElement {\n  return element instanceof HTMLElement;\n}\n\nfunction isFormControl(element: any): element is { validity: ValidityState } {\n  return 'validity' in element;\n}\n\nfunction isInvalid(control: HTMLElement) {\n  return (\n    isFormControl(control) &&\n    (control.validity.valid === false || control.getAttribute('aria-invalid') === 'true')\n  );\n}\n\nfunction getFirstInvalidControl(form: HTMLFormElement): HTMLElement | undefined {\n  const elements = form.elements;\n  const [firstInvalidControl] = Array.from(elements).filter(isHTMLElement).filter(isInvalid);\n  return firstInvalidControl;\n}\n\nfunction isAsyncCustomMatcherEntry(\n  entry: CustomMatcherEntry,\n  args: CustomMatcherArgs\n): entry is AsyncCustomMatcherEntry {\n  return entry.match.constructor.name === 'AsyncFunction' || returnsPromise(entry.match, args);\n}\n\nfunction isSyncCustomMatcherEntry(entry: CustomMatcherEntry): entry is SyncCustomMatcherEntry {\n  return entry.match.constructor.name === 'Function';\n}\n\nfunction returnsPromise(func: Function, args: Array<unknown>) {\n  return func(...args) instanceof Promise;\n}\n\nfunction hasBuiltInError(validity: ValidityState) {\n  let error = false;\n  for (const validityKey in validity) {\n    const key = validityKey as ValidityStateKey;\n    if (key !== 'valid' && key !== 'customError' && validity[key]) {\n      error = true;\n      break;\n    }\n  }\n  return error;\n}\n\nfunction getValidAttribute(validity: ValidityState | undefined, serverInvalid: boolean) {\n  if (validity?.valid === true && !serverInvalid) return true;\n  return undefined;\n}\nfunction getInvalidAttribute(validity: ValidityState | undefined, serverInvalid: boolean) {\n  if (validity?.valid === false || serverInvalid) return true;\n  return undefined;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Form;\nconst Field = FormField;\nconst Label = FormLabel;\nconst Control = FormControl;\nconst Message = FormMessage;\nconst ValidityState = FormValidityState;\nconst Submit = FormSubmit;\n\nexport {\n  createFormScope,\n  //\n  Form,\n  FormField,\n  FormLabel,\n  FormControl,\n  FormMessage,\n  FormValidityState,\n  FormSubmit,\n  //\n  Root,\n  Field,\n  Label,\n  Control,\n  Message,\n  ValidityState,\n  Submit,\n};\n\nexport type {\n  FormProps,\n  FormFieldProps,\n  FormLabelProps,\n  FormControlProps,\n  FormMessageProps,\n  FormValidityStateProps,\n  FormSubmitProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCard\n * -----------------------------------------------------------------------------------------------*/\n\nlet originalBodyUserSelect: string;\n\nconst HOVERCARD_NAME = 'HoverCard';\n\ntype ScopedProps<P> = P & { __scopeHoverCard?: Scope };\nconst [createHoverCardContext, createHoverCardScope] = createContextScope(HOVERCARD_NAME, [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype HoverCardContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpen(): void;\n  onClose(): void;\n  onDismiss(): void;\n  hasSelectionRef: React.MutableRefObject<boolean>;\n  isPointerDownOnContentRef: React.MutableRefObject<boolean>;\n};\n\nconst [HoverCardProvider, useHoverCardContext] =\n  createHoverCardContext<HoverCardContextValue>(HOVERCARD_NAME);\n\ninterface HoverCardProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  openDelay?: number;\n  closeDelay?: number;\n}\n\nconst HoverCard: React.FC<HoverCardProps> = (props: ScopedProps<HoverCardProps>) => {\n  const {\n    __scopeHoverCard,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    openDelay = 700,\n    closeDelay = 300,\n  } = props;\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const openTimerRef = React.useRef(0);\n  const closeTimerRef = React.useRef(0);\n  const hasSelectionRef = React.useRef(false);\n  const isPointerDownOnContentRef = React.useRef(false);\n\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: HOVERCARD_NAME,\n  });\n\n  const handleOpen = React.useCallback(() => {\n    clearTimeout(closeTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => setOpen(true), openDelay);\n  }, [openDelay, setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    clearTimeout(openTimerRef.current);\n    if (!hasSelectionRef.current && !isPointerDownOnContentRef.current) {\n      closeTimerRef.current = window.setTimeout(() => setOpen(false), closeDelay);\n    }\n  }, [closeDelay, setOpen]);\n\n  const handleDismiss = React.useCallback(() => setOpen(false), [setOpen]);\n\n  // cleanup any queued state updates on unmount\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(openTimerRef.current);\n      clearTimeout(closeTimerRef.current);\n    };\n  }, []);\n\n  return (\n    <HoverCardProvider\n      scope={__scopeHoverCard}\n      open={open}\n      onOpenChange={setOpen}\n      onOpen={handleOpen}\n      onClose={handleClose}\n      onDismiss={handleDismiss}\n      hasSelectionRef={hasSelectionRef}\n      isPointerDownOnContentRef={isPointerDownOnContentRef}\n    >\n      <PopperPrimitive.Root {...popperScope}>{children}</PopperPrimitive.Root>\n    </HoverCardProvider>\n  );\n};\n\nHoverCard.displayName = HOVERCARD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'HoverCardTrigger';\n\ntype HoverCardTriggerElement = React.ComponentRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface HoverCardTriggerProps extends PrimitiveLinkProps {}\n\nconst HoverCardTrigger = React.forwardRef<HoverCardTriggerElement, HoverCardTriggerProps>(\n  (props: ScopedProps<HoverCardTriggerProps>, forwardedRef) => {\n    const { __scopeHoverCard, ...triggerProps } = props;\n    const context = useHoverCardContext(TRIGGER_NAME, __scopeHoverCard);\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.a\n          data-state={context.open ? 'open' : 'closed'}\n          {...triggerProps}\n          ref={forwardedRef}\n          onPointerEnter={composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen))}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose))}\n          onFocus={composeEventHandlers(props.onFocus, context.onOpen)}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          // prevent focus event on touch devices\n          onTouchStart={composeEventHandlers(props.onTouchStart, (event) => event.preventDefault())}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nHoverCardTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'HoverCardPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createHoverCardContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface HoverCardPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst HoverCardPortal: React.FC<HoverCardPortalProps> = (\n  props: ScopedProps<HoverCardPortalProps>\n) => {\n  const { __scopeHoverCard, forceMount, children, container } = props;\n  const context = useHoverCardContext(PORTAL_NAME, __scopeHoverCard);\n  return (\n    <PortalProvider scope={__scopeHoverCard} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nHoverCardPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'HoverCardContent';\n\ntype HoverCardContentElement = HoverCardContentImplElement;\ninterface HoverCardContentProps extends HoverCardContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst HoverCardContent = React.forwardRef<HoverCardContentElement, HoverCardContentProps>(\n  (props: ScopedProps<HoverCardContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeHoverCard);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useHoverCardContext(CONTENT_NAME, props.__scopeHoverCard);\n    return (\n      <Presence present={forceMount || context.open}>\n        <HoverCardContentImpl\n          data-state={context.open ? 'open' : 'closed'}\n          {...contentProps}\n          onPointerEnter={composeEventHandlers(props.onPointerEnter, excludeTouch(context.onOpen))}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, excludeTouch(context.onClose))}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nHoverCardContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype HoverCardContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface HoverCardContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `HoverCard`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  /**\n   * Event handler called when the focus moves outside of the `HoverCard`.\n   * Can be prevented.\n   */\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  /**\n   * Event handler called when an interaction happens outside the `HoverCard`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst HoverCardContentImpl = React.forwardRef<\n  HoverCardContentImplElement,\n  HoverCardContentImplProps\n>((props: ScopedProps<HoverCardContentImplProps>, forwardedRef) => {\n  const {\n    __scopeHoverCard,\n    onEscapeKeyDown,\n    onPointerDownOutside,\n    onFocusOutside,\n    onInteractOutside,\n    ...contentProps\n  } = props;\n  const context = useHoverCardContext(CONTENT_NAME, __scopeHoverCard);\n  const popperScope = usePopperScope(__scopeHoverCard);\n  const ref = React.useRef<HoverCardContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [containSelection, setContainSelection] = React.useState(false);\n\n  React.useEffect(() => {\n    if (containSelection) {\n      const body = document.body;\n\n      // Safari requires prefix\n      originalBodyUserSelect = body.style.userSelect || body.style.webkitUserSelect;\n\n      body.style.userSelect = 'none';\n      body.style.webkitUserSelect = 'none';\n      return () => {\n        body.style.userSelect = originalBodyUserSelect;\n        body.style.webkitUserSelect = originalBodyUserSelect;\n      };\n    }\n  }, [containSelection]);\n\n  React.useEffect(() => {\n    if (ref.current) {\n      const handlePointerUp = () => {\n        setContainSelection(false);\n        context.isPointerDownOnContentRef.current = false;\n\n        // Delay a frame to ensure we always access the latest selection\n        setTimeout(() => {\n          const hasSelection = document.getSelection()?.toString() !== '';\n          if (hasSelection) context.hasSelectionRef.current = true;\n        });\n      };\n\n      document.addEventListener('pointerup', handlePointerUp);\n      return () => {\n        document.removeEventListener('pointerup', handlePointerUp);\n        context.hasSelectionRef.current = false;\n        context.isPointerDownOnContentRef.current = false;\n      };\n    }\n  }, [context.isPointerDownOnContentRef, context.hasSelectionRef]);\n\n  React.useEffect(() => {\n    if (ref.current) {\n      const tabbables = getTabbableNodes(ref.current);\n      tabbables.forEach((tabbable) => tabbable.setAttribute('tabindex', '-1'));\n    }\n  });\n\n  return (\n    <DismissableLayer\n      asChild\n      disableOutsidePointerEvents={false}\n      onInteractOutside={onInteractOutside}\n      onEscapeKeyDown={onEscapeKeyDown}\n      onPointerDownOutside={onPointerDownOutside}\n      onFocusOutside={composeEventHandlers(onFocusOutside, (event) => {\n        event.preventDefault();\n      })}\n      onDismiss={context.onDismiss}\n    >\n      <PopperPrimitive.Content\n        {...popperScope}\n        {...contentProps}\n        onPointerDown={composeEventHandlers(contentProps.onPointerDown, (event) => {\n          // Contain selection to current layer\n          if (event.currentTarget.contains(event.target as HTMLElement)) {\n            setContainSelection(true);\n          }\n          context.hasSelectionRef.current = false;\n          context.isPointerDownOnContentRef.current = true;\n        })}\n        ref={composedRefs}\n        style={{\n          ...contentProps.style,\n          userSelect: containSelection ? 'text' : undefined,\n          // Safari requires prefix\n          WebkitUserSelect: containSelection ? 'text' : undefined,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-hover-card-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-hover-card-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-hover-card-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-hover-card-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-hover-card-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    </DismissableLayer>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * HoverCardArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'HoverCardArrow';\n\ntype HoverCardArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface HoverCardArrowProps extends PopperArrowProps {}\n\nconst HoverCardArrow = React.forwardRef<HoverCardArrowElement, HoverCardArrowProps>(\n  (props: ScopedProps<HoverCardArrowProps>, forwardedRef) => {\n    const { __scopeHoverCard, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeHoverCard);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nHoverCardArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction excludeTouch<E>(eventHandler: () => void) {\n  return (event: React.PointerEvent<E>) =>\n    event.pointerType === 'touch' ? undefined : eventHandler();\n}\n\n/**\n * Returns a list of nodes that can be in the tab sequence.\n * @see: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n */\nfunction getTabbableNodes(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  return nodes;\n}\n\nconst Root = HoverCard;\nconst Trigger = HoverCardTrigger;\nconst Portal = HoverCardPortal;\nconst Content = HoverCardContent;\nconst Arrow = HoverCardArrow;\n\nexport {\n  createHoverCardScope,\n  //\n  HoverCard,\n  HoverCardTrigger,\n  HoverCardPortal,\n  HoverCardContent,\n  HoverCardArrow,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  HoverCardProps,\n  HoverCardTriggerProps,\n  HoverCardPortalProps,\n  HoverCardContentProps,\n  HoverCardArrowProps,\n};\n", "import * as React from 'react';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Menubar\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENUBAR_NAME = 'Menubar';\n\ntype ItemData = { value: string; disabled: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenubarTriggerElement,\n  ItemData\n>(MENUBAR_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenubar?: Scope };\nconst [createMenubarContext, createMenubarScope] = createContextScope(MENUBAR_NAME, [\n  createCollectionScope,\n  createRovingFocusGroupScope,\n]);\n\nconst useMenuScope = createMenuScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenubarContextValue = {\n  value: string;\n  dir: Direction;\n  loop: boolean;\n  onMenuOpen(value: string): void;\n  onMenuClose(): void;\n  onMenuToggle(value: string): void;\n};\n\nconst [MenubarContextProvider, useMenubarContext] =\n  createMenubarContext<MenubarContextValue>(MENUBAR_NAME);\n\ntype MenubarElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenubarProps extends PrimitiveDivProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  loop?: RovingFocusGroupProps['loop'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst Menubar = React.forwardRef<MenubarElement, MenubarProps>(\n  (props: ScopedProps<MenubarProps>, forwardedRef) => {\n    const {\n      __scopeMenubar,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      loop = true,\n      dir,\n      ...menubarProps\n    } = props;\n    const direction = useDirection(dir);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: MENUBAR_NAME,\n    });\n\n    // We need to manage tab stop id manually as `RovingFocusGroup` updates the stop\n    // based on focus, and in some situations our triggers won't ever be given focus\n    // (e.g. click to open and then outside to close)\n    const [currentTabStopId, setCurrentTabStopId] = React.useState<string | null>(null);\n\n    return (\n      <MenubarContextProvider\n        scope={__scopeMenubar}\n        value={value}\n        onMenuOpen={React.useCallback(\n          (value) => {\n            setValue(value);\n            setCurrentTabStopId(value);\n          },\n          [setValue]\n        )}\n        onMenuClose={React.useCallback(() => setValue(''), [setValue])}\n        onMenuToggle={React.useCallback(\n          (value) => {\n            setValue((prevValue) => (prevValue ? '' : value));\n            // `openMenuOpen` and `onMenuToggle` are called exclusively so we\n            // need to update the id in either case.\n            setCurrentTabStopId(value);\n          },\n          [setValue]\n        )}\n        dir={direction}\n        loop={loop}\n      >\n        <Collection.Provider scope={__scopeMenubar}>\n          <Collection.Slot scope={__scopeMenubar}>\n            <RovingFocusGroup.Root\n              asChild\n              {...rovingFocusGroupScope}\n              orientation=\"horizontal\"\n              loop={loop}\n              dir={direction}\n              currentTabStopId={currentTabStopId}\n              onCurrentTabStopIdChange={setCurrentTabStopId}\n            >\n              <Primitive.div role=\"menubar\" {...menubarProps} ref={forwardedRef} />\n            </RovingFocusGroup.Root>\n          </Collection.Slot>\n        </Collection.Provider>\n      </MenubarContextProvider>\n    );\n  }\n);\n\nMenubar.displayName = MENUBAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'MenubarMenu';\n\ntype MenubarMenuContextValue = {\n  value: string;\n  triggerId: string;\n  triggerRef: React.RefObject<MenubarTriggerElement | null>;\n  contentId: string;\n  wasKeyboardTriggerOpenRef: React.MutableRefObject<boolean>;\n};\n\nconst [MenubarMenuProvider, useMenubarMenuContext] =\n  createMenubarContext<MenubarMenuContextValue>(MENU_NAME);\n\ninterface MenubarMenuProps {\n  value?: string;\n  children?: React.ReactNode;\n}\n\nconst MenubarMenu = (props: ScopedProps<MenubarMenuProps>) => {\n  const { __scopeMenubar, value: valueProp, ...menuProps } = props;\n  const autoValue = useId();\n  // We need to provide an initial deterministic value as `useId` will return\n  // empty string on the first render and we don't want to match our internal \"closed\" value.\n  const value = valueProp || autoValue || 'LEGACY_REACT_AUTO_VALUE';\n  const context = useMenubarContext(MENU_NAME, __scopeMenubar);\n  const menuScope = useMenuScope(__scopeMenubar);\n  const triggerRef = React.useRef<MenubarTriggerElement>(null);\n  const wasKeyboardTriggerOpenRef = React.useRef(false);\n  const open = context.value === value;\n\n  React.useEffect(() => {\n    if (!open) wasKeyboardTriggerOpenRef.current = false;\n  }, [open]);\n\n  return (\n    <MenubarMenuProvider\n      scope={__scopeMenubar}\n      value={value}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      wasKeyboardTriggerOpenRef={wasKeyboardTriggerOpenRef}\n    >\n      <MenuPrimitive.Root\n        {...menuScope}\n        open={open}\n        onOpenChange={(open) => {\n          // Menu only calls `onOpenChange` when dismissing so we\n          // want to close our MenuBar based on the same events.\n          if (!open) context.onMenuClose();\n        }}\n        modal={false}\n        dir={context.dir}\n        {...menuProps}\n      />\n    </MenubarMenuProvider>\n  );\n};\n\nMenubarMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'MenubarTrigger';\n\ntype MenubarTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface MenubarTriggerProps extends PrimitiveButtonProps {}\n\nconst MenubarTrigger = React.forwardRef<MenubarTriggerElement, MenubarTriggerProps>(\n  (props: ScopedProps<MenubarTriggerProps>, forwardedRef) => {\n    const { __scopeMenubar, disabled = false, ...triggerProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenubar);\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(TRIGGER_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(TRIGGER_NAME, __scopeMenubar);\n    const ref = React.useRef<MenubarTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, menuContext.triggerRef);\n    const [isFocused, setIsFocused] = React.useState(false);\n    const open = context.value === menuContext.value;\n\n    return (\n      <Collection.ItemSlot scope={__scopeMenubar} value={menuContext.value} disabled={disabled}>\n        <RovingFocusGroup.Item\n          asChild\n          {...rovingFocusGroupScope}\n          focusable={!disabled}\n          tabStopId={menuContext.value}\n        >\n          <MenuPrimitive.Anchor asChild {...menuScope}>\n            <Primitive.button\n              type=\"button\"\n              role=\"menuitem\"\n              id={menuContext.triggerId}\n              aria-haspopup=\"menu\"\n              aria-expanded={open}\n              aria-controls={open ? menuContext.contentId : undefined}\n              data-highlighted={isFocused ? '' : undefined}\n              data-state={open ? 'open' : 'closed'}\n              data-disabled={disabled ? '' : undefined}\n              disabled={disabled}\n              {...triggerProps}\n              ref={composedRefs}\n              onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n                // but not when the control key is pressed (avoiding MacOS right click)\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                  context.onMenuOpen(menuContext.value);\n                  // prevent trigger focusing when opening\n                  // this allows the content to be given focus without competition\n                  if (!open) event.preventDefault();\n                }\n              })}\n              onPointerEnter={composeEventHandlers(props.onPointerEnter, () => {\n                const menubarOpen = Boolean(context.value);\n                if (menubarOpen && !open) {\n                  context.onMenuOpen(menuContext.value);\n                  ref.current?.focus();\n                }\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                if (disabled) return;\n                if (['Enter', ' '].includes(event.key)) context.onMenuToggle(menuContext.value);\n                if (event.key === 'ArrowDown') context.onMenuOpen(menuContext.value);\n                // prevent keydown from scrolling window / first focused item to execute\n                // that keydown (inadvertently closing the menu)\n                if (['Enter', ' ', 'ArrowDown'].includes(event.key)) {\n                  menuContext.wasKeyboardTriggerOpenRef.current = true;\n                  event.preventDefault();\n                }\n              })}\n              onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n              onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n            />\n          </MenuPrimitive.Anchor>\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nMenubarTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenubarPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface MenubarPortalProps extends MenuPortalProps {}\n\nconst MenubarPortal: React.FC<MenubarPortalProps> = (props: ScopedProps<MenubarPortalProps>) => {\n  const { __scopeMenubar, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nMenubarPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenubarContent';\n\ntype MenubarContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface MenubarContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst MenubarContent = React.forwardRef<MenubarContentElement, MenubarContentProps>(\n  (props: ScopedProps<MenubarContentProps>, forwardedRef) => {\n    const { __scopeMenubar, align = 'start', ...contentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    const context = useMenubarContext(CONTENT_NAME, __scopeMenubar);\n    const menuContext = useMenubarMenuContext(CONTENT_NAME, __scopeMenubar);\n    const getItems = useCollection(__scopeMenubar);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={menuContext.contentId}\n        aria-labelledby={menuContext.triggerId}\n        data-radix-menubar-content=\"\"\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        align={align}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          const menubarOpen = Boolean(context.value);\n          if (!menubarOpen && !hasInteractedOutsideRef.current) {\n            menuContext.triggerRef.current?.focus();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n          const target = event.target as HTMLElement;\n          const isMenubarTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          if (isMenubarTrigger) event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, () => {\n          hasInteractedOutsideRef.current = true;\n        })}\n        onEntryFocus={(event) => {\n          if (!menuContext.wasKeyboardTriggerOpenRef.current) event.preventDefault();\n        }}\n        onKeyDown={composeEventHandlers(\n          props.onKeyDown,\n          (event) => {\n            if (['ArrowRight', 'ArrowLeft'].includes(event.key)) {\n              const target = event.target as HTMLElement;\n              const targetIsSubTrigger = target.hasAttribute('data-radix-menubar-subtrigger');\n              const isKeyDownInsideSubMenu =\n                target.closest('[data-radix-menubar-content]') !== event.currentTarget;\n\n              const prevMenuKey = context.dir === 'rtl' ? 'ArrowRight' : 'ArrowLeft';\n              const isPrevKey = prevMenuKey === event.key;\n              const isNextKey = !isPrevKey;\n\n              // Prevent navigation when we're opening a submenu\n              if (isNextKey && targetIsSubTrigger) return;\n              // or we're inside a submenu and are moving backwards to close it\n              if (isKeyDownInsideSubMenu && isPrevKey) return;\n\n              const items = getItems().filter((item) => !item.disabled);\n              let candidateValues = items.map((item) => item.value);\n              if (isPrevKey) candidateValues.reverse();\n\n              const currentIndex = candidateValues.indexOf(menuContext.value);\n\n              candidateValues = context.loop\n                ? wrapArray(candidateValues, currentIndex + 1)\n                : candidateValues.slice(currentIndex + 1);\n\n              const [nextValue] = candidateValues;\n              if (nextValue) context.onMenuOpen(nextValue);\n            }\n          },\n          { checkForDefaultPrevented: false }\n        )}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-menubar-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-menubar-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-menubar-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-menubar-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-menubar-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nMenubarContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenubarGroup';\n\ntype MenubarGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface MenubarGroupProps extends MenuGroupProps {}\n\nconst MenubarGroup = React.forwardRef<MenubarGroupElement, MenubarGroupProps>(\n  (props: ScopedProps<MenubarGroupProps>, forwardedRef) => {\n    const { __scopeMenubar, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenubarLabel';\n\ntype MenubarLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface MenubarLabelProps extends MenuLabelProps {}\n\nconst MenubarLabel = React.forwardRef<MenubarLabelElement, MenubarLabelProps>(\n  (props: ScopedProps<MenubarLabelProps>, forwardedRef) => {\n    const { __scopeMenubar, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenubarItem';\n\ntype MenubarItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface MenubarItemProps extends MenuItemProps {}\n\nconst MenubarItem = React.forwardRef<MenubarItemElement, MenubarItemProps>(\n  (props: ScopedProps<MenubarItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenubarCheckboxItem';\n\ntype MenubarCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface MenubarCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst MenubarCheckboxItem = React.forwardRef<MenubarCheckboxItemElement, MenubarCheckboxItemProps>(\n  (props: ScopedProps<MenubarCheckboxItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenubarRadioGroup';\n\ntype MenubarRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface MenubarRadioGroupProps extends MenuRadioGroupProps {}\n\nconst MenubarRadioGroup = React.forwardRef<MenubarRadioGroupElement, MenubarRadioGroupProps>(\n  (props: ScopedProps<MenubarRadioGroupProps>, forwardedRef) => {\n    const { __scopeMenubar, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenubarRadioItem';\n\ntype MenubarRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface MenubarRadioItemProps extends MenuRadioItemProps {}\n\nconst MenubarRadioItem = React.forwardRef<MenubarRadioItemElement, MenubarRadioItemProps>(\n  (props: ScopedProps<MenubarRadioItemProps>, forwardedRef) => {\n    const { __scopeMenubar, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'MenubarItemIndicator';\n\ntype MenubarItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface MenubarItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst MenubarItemIndicator = React.forwardRef<\n  MenubarItemIndicatorElement,\n  MenubarItemIndicatorProps\n>((props: ScopedProps<MenubarItemIndicatorProps>, forwardedRef) => {\n  const { __scopeMenubar, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nMenubarItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenubarSeparator';\n\ntype MenubarSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface MenubarSeparatorProps extends MenuSeparatorProps {}\n\nconst MenubarSeparator = React.forwardRef<MenubarSeparatorElement, MenubarSeparatorProps>(\n  (props: ScopedProps<MenubarSeparatorProps>, forwardedRef) => {\n    const { __scopeMenubar, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenubarArrow';\n\ntype MenubarArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface MenubarArrowProps extends MenuArrowProps {}\n\nconst MenubarArrow = React.forwardRef<MenubarArrowElement, MenubarArrowProps>(\n  (props: ScopedProps<MenubarArrowProps>, forwardedRef) => {\n    const { __scopeMenubar, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenubarArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenubarSub';\n\ninterface MenubarSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenubarSub: React.FC<MenubarSubProps> = (props: ScopedProps<MenubarSubProps>) => {\n  const { __scopeMenubar, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeMenubar);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SUB_NAME,\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\nMenubarSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenubarSubTrigger';\n\ntype MenubarSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface MenubarSubTriggerProps extends MenuSubTriggerProps {}\n\nconst MenubarSubTrigger = React.forwardRef<MenubarSubTriggerElement, MenubarSubTriggerProps>(\n  (props: ScopedProps<MenubarSubTriggerProps>, forwardedRef) => {\n    const { __scopeMenubar, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n    return (\n      <MenuPrimitive.SubTrigger\n        data-radix-menubar-subtrigger=\"\"\n        {...menuScope}\n        {...subTriggerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenubarSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenubarSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenubarSubContent';\n\ntype MenubarSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface MenubarSubContentProps extends MenuSubContentProps {}\n\nconst MenubarSubContent = React.forwardRef<MenubarSubContentElement, MenubarSubContentProps>(\n  (props: ScopedProps<MenubarSubContentProps>, forwardedRef) => {\n    const { __scopeMenubar, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeMenubar);\n\n    return (\n      <MenuPrimitive.SubContent\n        {...menuScope}\n        data-radix-menubar-content=\"\"\n        {...subContentProps}\n        ref={forwardedRef}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-menubar-content-transform-origin': 'var(--radix-popper-transform-origin)',\n            '--radix-menubar-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-menubar-content-available-height': 'var(--radix-popper-available-height)',\n            '--radix-menubar-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-menubar-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nMenubarSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Menubar;\nconst Menu = MenubarMenu;\nconst Trigger = MenubarTrigger;\nconst Portal = MenubarPortal;\nconst Content = MenubarContent;\nconst Group = MenubarGroup;\nconst Label = MenubarLabel;\nconst Item = MenubarItem;\nconst CheckboxItem = MenubarCheckboxItem;\nconst RadioGroup = MenubarRadioGroup;\nconst RadioItem = MenubarRadioItem;\nconst ItemIndicator = MenubarItemIndicator;\nconst Separator = MenubarSeparator;\nconst Arrow = MenubarArrow;\nconst Sub = MenubarSub;\nconst SubTrigger = MenubarSubTrigger;\nconst SubContent = MenubarSubContent;\n\nexport {\n  createMenubarScope,\n  //\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarPortal,\n  MenubarContent,\n  MenubarGroup,\n  MenubarLabel,\n  MenubarItem,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarItemIndicator,\n  MenubarSeparator,\n  MenubarArrow,\n  MenubarSub,\n  MenubarSubTrigger,\n  MenubarSubContent,\n  //\n  Root,\n  Menu,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenubarProps,\n  MenubarMenuProps,\n  MenubarTriggerProps,\n  MenubarPortalProps,\n  MenubarContentProps,\n  MenubarGroupProps,\n  MenubarLabelProps,\n  MenubarItemProps,\n  MenubarCheckboxItemProps,\n  MenubarRadioGroupProps,\n  MenubarRadioItemProps,\n  MenubarItemIndicatorProps,\n  MenubarSeparatorProps,\n  MenubarArrowProps,\n  MenubarSubProps,\n  MenubarSubTriggerProps,\n  MenubarSubContentProps,\n};\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { composeRefs, useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Orientation = 'vertical' | 'horizontal';\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAVIGATION_MENU_NAME = 'NavigationMenu';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  NavigationMenuTriggerElement,\n  { value: string }\n>(NAVIGATION_MENU_NAME);\n\nconst [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] =\n  createCollection<FocusGroupItemElement, {}>(NAVIGATION_MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeNavigationMenu?: Scope };\nconst [createNavigationMenuContext, createNavigationMenuScope] = createContextScope(\n  NAVIGATION_MENU_NAME,\n  [createCollectionScope, createFocusGroupCollectionScope]\n);\n\ntype ContentData = {\n  ref?: React.Ref<ViewportContentMounterElement>;\n} & ViewportContentMounterProps;\n\ntype NavigationMenuContextValue = {\n  isRootMenu: boolean;\n  value: string;\n  previousValue: string;\n  baseId: string;\n  dir: Direction;\n  orientation: Orientation;\n  rootNavigationMenu: NavigationMenuElement | null;\n  indicatorTrack: HTMLDivElement | null;\n  onIndicatorTrackChange(indicatorTrack: HTMLDivElement | null): void;\n  viewport: NavigationMenuViewportElement | null;\n  onViewportChange(viewport: NavigationMenuViewportElement | null): void;\n  onViewportContentChange(contentValue: string, contentData: ContentData): void;\n  onViewportContentRemove(contentValue: string): void;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave(): void;\n  onContentEnter(): void;\n  onContentLeave(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n};\n\nconst [NavigationMenuProviderImpl, useNavigationMenuContext] =\n  createNavigationMenuContext<NavigationMenuContextValue>(NAVIGATION_MENU_NAME);\n\nconst [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext<{\n  items: Map<string, ContentData>;\n}>(NAVIGATION_MENU_NAME);\n\ntype NavigationMenuElement = React.ComponentRef<typeof Primitive.nav>;\ntype PrimitiveNavProps = React.ComponentPropsWithoutRef<typeof Primitive.nav>;\ninterface NavigationMenuProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveNavProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  dir?: Direction;\n  orientation?: Orientation;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 200\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n}\n\nconst NavigationMenu = React.forwardRef<NavigationMenuElement, NavigationMenuProps>(\n  (props: ScopedProps<NavigationMenuProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      delayDuration = 200,\n      skipDelayDuration = 300,\n      orientation = 'horizontal',\n      dir,\n      ...NavigationMenuProps\n    } = props;\n    const [navigationMenu, setNavigationMenu] = React.useState<NavigationMenuElement | null>(null);\n    const composedRef = useComposedRefs(forwardedRef, (node) => setNavigationMenu(node));\n    const direction = useDirection(dir);\n    const openTimerRef = React.useRef(0);\n    const closeTimerRef = React.useRef(0);\n    const skipDelayTimerRef = React.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: (value) => {\n        const isOpen = value !== '';\n        const hasSkipDelayDuration = skipDelayDuration > 0;\n\n        if (isOpen) {\n          window.clearTimeout(skipDelayTimerRef.current);\n          if (hasSkipDelayDuration) setIsOpenDelayed(false);\n        } else {\n          window.clearTimeout(skipDelayTimerRef.current);\n          skipDelayTimerRef.current = window.setTimeout(\n            () => setIsOpenDelayed(true),\n            skipDelayDuration\n          );\n        }\n\n        onValueChange?.(value);\n      },\n      defaultProp: defaultValue ?? '',\n      caller: NAVIGATION_MENU_NAME,\n    });\n\n    const startCloseTimer = React.useCallback(() => {\n      window.clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = window.setTimeout(() => setValue(''), 150);\n    }, [setValue]);\n\n    const handleOpen = React.useCallback(\n      (itemValue: string) => {\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n      },\n      [setValue]\n    );\n\n    const handleDelayedOpen = React.useCallback(\n      (itemValue: string) => {\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n          // If the item is already open (e.g. we're transitioning from the content to the trigger)\n          // then we want to clear the close timer immediately.\n          window.clearTimeout(closeTimerRef.current);\n        } else {\n          openTimerRef.current = window.setTimeout(() => {\n            window.clearTimeout(closeTimerRef.current);\n            setValue(itemValue);\n          }, delayDuration);\n        }\n      },\n      [value, setValue, delayDuration]\n    );\n\n    React.useEffect(() => {\n      return () => {\n        window.clearTimeout(openTimerRef.current);\n        window.clearTimeout(closeTimerRef.current);\n        window.clearTimeout(skipDelayTimerRef.current);\n      };\n    }, []);\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={true}\n        value={value}\n        dir={direction}\n        orientation={orientation}\n        rootNavigationMenu={navigationMenu}\n        onTriggerEnter={(itemValue) => {\n          window.clearTimeout(openTimerRef.current);\n          if (isOpenDelayed) handleDelayedOpen(itemValue);\n          else handleOpen(itemValue);\n        }}\n        onTriggerLeave={() => {\n          window.clearTimeout(openTimerRef.current);\n          startCloseTimer();\n        }}\n        onContentEnter={() => window.clearTimeout(closeTimerRef.current)}\n        onContentLeave={startCloseTimer}\n        onItemSelect={(itemValue) => {\n          setValue((prevValue) => (prevValue === itemValue ? '' : itemValue));\n        }}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.nav\n          aria-label=\"Main\"\n          data-orientation={orientation}\n          dir={direction}\n          {...NavigationMenuProps}\n          ref={composedRef}\n        />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'NavigationMenuSub';\n\ntype NavigationMenuSubElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface NavigationMenuSubProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveDivProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  orientation?: Orientation;\n}\n\nconst NavigationMenuSub = React.forwardRef<NavigationMenuSubElement, NavigationMenuSubProps>(\n  (props: ScopedProps<NavigationMenuSubProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      ...subProps\n    } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: SUB_NAME,\n    });\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={false}\n        value={value}\n        dir={context.dir}\n        orientation={orientation}\n        rootNavigationMenu={context.rootNavigationMenu}\n        onTriggerEnter={(itemValue) => setValue(itemValue)}\n        onItemSelect={(itemValue) => setValue(itemValue)}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.div data-orientation={orientation} {...subProps} ref={forwardedRef} />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenuSub.displayName = SUB_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface NavigationMenuProviderPrivateProps {\n  isRootMenu: boolean;\n  scope: Scope;\n  children: React.ReactNode;\n  orientation: Orientation;\n  dir: Direction;\n  rootNavigationMenu: NavigationMenuElement | null;\n  value: string;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave?(): void;\n  onContentEnter?(): void;\n  onContentLeave?(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n}\n\ninterface NavigationMenuProviderProps extends NavigationMenuProviderPrivateProps {}\n\nconst NavigationMenuProvider: React.FC<NavigationMenuProviderProps> = (\n  props: ScopedProps<NavigationMenuProviderProps>\n) => {\n  const {\n    scope,\n    isRootMenu,\n    rootNavigationMenu,\n    dir,\n    orientation,\n    children,\n    value,\n    onItemSelect,\n    onItemDismiss,\n    onTriggerEnter,\n    onTriggerLeave,\n    onContentEnter,\n    onContentLeave,\n  } = props;\n  const [viewport, setViewport] = React.useState<NavigationMenuViewportElement | null>(null);\n  const [viewportContent, setViewportContent] = React.useState<Map<string, ContentData>>(new Map());\n  const [indicatorTrack, setIndicatorTrack] = React.useState<HTMLDivElement | null>(null);\n\n  return (\n    <NavigationMenuProviderImpl\n      scope={scope}\n      isRootMenu={isRootMenu}\n      rootNavigationMenu={rootNavigationMenu}\n      value={value}\n      previousValue={usePrevious(value)}\n      baseId={useId()}\n      dir={dir}\n      orientation={orientation}\n      viewport={viewport}\n      onViewportChange={setViewport}\n      indicatorTrack={indicatorTrack}\n      onIndicatorTrackChange={setIndicatorTrack}\n      onTriggerEnter={useCallbackRef(onTriggerEnter)}\n      onTriggerLeave={useCallbackRef(onTriggerLeave)}\n      onContentEnter={useCallbackRef(onContentEnter)}\n      onContentLeave={useCallbackRef(onContentLeave)}\n      onItemSelect={useCallbackRef(onItemSelect)}\n      onItemDismiss={useCallbackRef(onItemDismiss)}\n      onViewportContentChange={React.useCallback((contentValue, contentData) => {\n        setViewportContent((prevContent) => {\n          prevContent.set(contentValue, contentData);\n          return new Map(prevContent);\n        });\n      }, [])}\n      onViewportContentRemove={React.useCallback((contentValue) => {\n        setViewportContent((prevContent) => {\n          if (!prevContent.has(contentValue)) return prevContent;\n          prevContent.delete(contentValue);\n          return new Map(prevContent);\n        });\n      }, [])}\n    >\n      <Collection.Provider scope={scope}>\n        <ViewportContentProvider scope={scope} items={viewportContent}>\n          {children}\n        </ViewportContentProvider>\n      </Collection.Provider>\n    </NavigationMenuProviderImpl>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuList\n * -----------------------------------------------------------------------------------------------*/\n\nconst LIST_NAME = 'NavigationMenuList';\n\ntype NavigationMenuListElement = React.ComponentRef<typeof Primitive.ul>;\ntype PrimitiveUnorderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ul>;\ninterface NavigationMenuListProps extends PrimitiveUnorderedListProps {}\n\nconst NavigationMenuList = React.forwardRef<NavigationMenuListElement, NavigationMenuListProps>(\n  (props: ScopedProps<NavigationMenuListProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n\n    const list = (\n      <Primitive.ul data-orientation={context.orientation} {...listProps} ref={forwardedRef} />\n    );\n\n    return (\n      <Primitive.div style={{ position: 'relative' }} ref={context.onIndicatorTrackChange}>\n        <Collection.Slot scope={__scopeNavigationMenu}>\n          {context.isRootMenu ? <FocusGroup asChild>{list}</FocusGroup> : list}\n        </Collection.Slot>\n      </Primitive.div>\n    );\n  }\n);\n\nNavigationMenuList.displayName = LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'NavigationMenuItem';\n\ntype FocusProxyElement = React.ComponentRef<typeof VisuallyHiddenPrimitive.Root>;\n\ntype NavigationMenuItemContextValue = {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  contentRef: React.RefObject<NavigationMenuContentElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onEntryKeyDown(): void;\n  onFocusProxyEnter(side: 'start' | 'end'): void;\n  onRootContentClose(): void;\n  onContentFocusOutside(): void;\n};\n\nconst [NavigationMenuItemContextProvider, useNavigationMenuItemContext] =\n  createNavigationMenuContext<NavigationMenuItemContextValue>(ITEM_NAME);\n\ntype NavigationMenuItemElement = React.ComponentRef<typeof Primitive.li>;\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface NavigationMenuItemProps extends PrimitiveListItemProps {\n  value?: string;\n}\n\nconst NavigationMenuItem = React.forwardRef<NavigationMenuItemElement, NavigationMenuItemProps>(\n  (props: ScopedProps<NavigationMenuItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = useId();\n    // We need to provide an initial deterministic value as `useId` will return\n    // empty string on the first render and we don't want to match our internal \"closed\" value.\n    const value = valueProp || autoValue || 'LEGACY_REACT_AUTO_VALUE';\n    const contentRef = React.useRef<NavigationMenuContentElement>(null);\n    const triggerRef = React.useRef<NavigationMenuTriggerElement>(null);\n    const focusProxyRef = React.useRef<FocusProxyElement>(null);\n    const restoreContentTabOrderRef = React.useRef(() => {});\n    const wasEscapeCloseRef = React.useRef(false);\n\n    const handleContentEntry = React.useCallback((side = 'start') => {\n      if (contentRef.current) {\n        restoreContentTabOrderRef.current();\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) focusFirst(side === 'start' ? candidates : candidates.reverse());\n      }\n    }, []);\n\n    const handleContentExit = React.useCallback(() => {\n      if (contentRef.current) {\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n      }\n    }, []);\n\n    return (\n      <NavigationMenuItemContextProvider\n        scope={__scopeNavigationMenu}\n        value={value}\n        triggerRef={triggerRef}\n        contentRef={contentRef}\n        focusProxyRef={focusProxyRef}\n        wasEscapeCloseRef={wasEscapeCloseRef}\n        onEntryKeyDown={handleContentEntry}\n        onFocusProxyEnter={handleContentEntry}\n        onRootContentClose={handleContentExit}\n        onContentFocusOutside={handleContentExit}\n      >\n        <Primitive.li {...itemProps} ref={forwardedRef} />\n      </NavigationMenuItemContextProvider>\n    );\n  }\n);\n\nNavigationMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'NavigationMenuTrigger';\n\ntype NavigationMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface NavigationMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst NavigationMenuTrigger = React.forwardRef<\n  NavigationMenuTriggerElement,\n  NavigationMenuTriggerProps\n>((props: ScopedProps<NavigationMenuTriggerProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n  const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuTriggerElement>(null);\n  const composedRefs = useComposedRefs(ref, itemContext.triggerRef, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, itemContext.value);\n  const contentId = makeContentId(context.baseId, itemContext.value);\n  const hasPointerMoveOpenedRef = React.useRef(false);\n  const wasClickCloseRef = React.useRef(false);\n  const open = itemContext.value === context.value;\n\n  return (\n    <>\n      <Collection.ItemSlot scope={__scopeNavigationMenu} value={itemContext.value}>\n        <FocusGroupItem asChild>\n          <Primitive.button\n            id={triggerId}\n            disabled={disabled}\n            data-disabled={disabled ? '' : undefined}\n            data-state={getOpenState(open)}\n            aria-expanded={open}\n            aria-controls={contentId}\n            {...triggerProps}\n            ref={composedRefs}\n            onPointerEnter={composeEventHandlers(props.onPointerEnter, () => {\n              wasClickCloseRef.current = false;\n              itemContext.wasEscapeCloseRef.current = false;\n            })}\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse(() => {\n                if (\n                  disabled ||\n                  wasClickCloseRef.current ||\n                  itemContext.wasEscapeCloseRef.current ||\n                  hasPointerMoveOpenedRef.current\n                )\n                  return;\n                context.onTriggerEnter(itemContext.value);\n                hasPointerMoveOpenedRef.current = true;\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse(() => {\n                if (disabled) return;\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n              })\n            )}\n            onClick={composeEventHandlers(props.onClick, () => {\n              context.onItemSelect(itemContext.value);\n              wasClickCloseRef.current = open;\n            })}\n            onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n              const verticalEntryKey = context.dir === 'rtl' ? 'ArrowLeft' : 'ArrowRight';\n              const entryKey = { horizontal: 'ArrowDown', vertical: verticalEntryKey }[\n                context.orientation\n              ];\n              if (open && event.key === entryKey) {\n                itemContext.onEntryKeyDown();\n                // Prevent FocusGroupItem from handling the event\n                event.preventDefault();\n              }\n            })}\n          />\n        </FocusGroupItem>\n      </Collection.ItemSlot>\n\n      {/* Proxy tab order between trigger and content */}\n      {open && (\n        <>\n          <VisuallyHiddenPrimitive.Root\n            aria-hidden\n            tabIndex={0}\n            ref={itemContext.focusProxyRef}\n            onFocus={(event) => {\n              const content = itemContext.contentRef.current;\n              const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n              const wasTriggerFocused = prevFocusedElement === ref.current;\n              const wasFocusFromContent = content?.contains(prevFocusedElement);\n\n              if (wasTriggerFocused || !wasFocusFromContent) {\n                itemContext.onFocusProxyEnter(wasTriggerFocused ? 'start' : 'end');\n              }\n            }}\n          />\n\n          {/* Restructure a11y tree to make content accessible to screen reader when using the viewport */}\n          {context.viewport && <span aria-owns={contentId} />}\n        </>\n      )}\n    </>\n  );\n});\n\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuLink\n * -----------------------------------------------------------------------------------------------*/\n\nconst LINK_NAME = 'NavigationMenuLink';\nconst LINK_SELECT = 'navigationMenu.linkSelect';\n\ntype NavigationMenuLinkElement = React.ComponentRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface NavigationMenuLinkProps extends Omit<PrimitiveLinkProps, 'onSelect'> {\n  active?: boolean;\n  onSelect?: (event: Event) => void;\n}\n\nconst NavigationMenuLink = React.forwardRef<NavigationMenuLinkElement, NavigationMenuLinkProps>(\n  (props: ScopedProps<NavigationMenuLinkProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n\n    return (\n      <FocusGroupItem asChild>\n        <Primitive.a\n          data-active={active ? '' : undefined}\n          aria-current={active ? 'page' : undefined}\n          {...linkProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(\n            props.onClick,\n            (event) => {\n              const target = event.target as HTMLElement;\n              const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                bubbles: true,\n                cancelable: true,\n              });\n              target.addEventListener(LINK_SELECT, (event) => onSelect?.(event), { once: true });\n              dispatchDiscreteCustomEvent(target, linkSelectEvent);\n\n              if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                  bubbles: true,\n                  cancelable: true,\n                });\n                dispatchDiscreteCustomEvent(target, rootContentDismissEvent);\n              }\n            },\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </FocusGroupItem>\n    );\n  }\n);\n\nNavigationMenuLink.displayName = LINK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'NavigationMenuIndicator';\n\ntype NavigationMenuIndicatorElement = NavigationMenuIndicatorImplElement;\ninterface NavigationMenuIndicatorProps extends NavigationMenuIndicatorImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuIndicator = React.forwardRef<\n  NavigationMenuIndicatorElement,\n  NavigationMenuIndicatorProps\n>((props: ScopedProps<NavigationMenuIndicatorProps>, forwardedRef) => {\n  const { forceMount, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n  const isVisible = Boolean(context.value);\n\n  return context.indicatorTrack\n    ? ReactDOM.createPortal(\n        <Presence present={forceMount || isVisible}>\n          <NavigationMenuIndicatorImpl {...indicatorProps} ref={forwardedRef} />\n        </Presence>,\n        context.indicatorTrack\n      )\n    : null;\n});\n\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\n\ntype NavigationMenuIndicatorImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuIndicatorImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuIndicatorImpl = React.forwardRef<\n  NavigationMenuIndicatorImplElement,\n  NavigationMenuIndicatorImplProps\n>((props: ScopedProps<NavigationMenuIndicatorImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const [activeTrigger, setActiveTrigger] = React.useState<NavigationMenuTriggerElement | null>(\n    null\n  );\n  const [position, setPosition] = React.useState<{ size: number; offset: number } | null>(null);\n  const isHorizontal = context.orientation === 'horizontal';\n  const isVisible = Boolean(context.value);\n\n  React.useEffect(() => {\n    const items = getItems();\n    const triggerNode = items.find((item) => item.value === context.value)?.ref.current;\n    if (triggerNode) setActiveTrigger(triggerNode);\n  }, [getItems, context.value]);\n\n  /**\n   * Update position when the indicator or parent track size changes\n   */\n  const handlePositionChange = () => {\n    if (activeTrigger) {\n      setPosition({\n        size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n        offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop,\n      });\n    }\n  };\n  useResizeObserver(activeTrigger, handlePositionChange);\n  useResizeObserver(context.indicatorTrack, handlePositionChange);\n\n  // We need to wait for the indicator position to be available before rendering to\n  // snap immediately into position rather than transitioning from initial\n  return position ? (\n    <Primitive.div\n      aria-hidden\n      data-state={isVisible ? 'visible' : 'hidden'}\n      data-orientation={context.orientation}\n      {...indicatorProps}\n      ref={forwardedRef}\n      style={{\n        position: 'absolute',\n        ...(isHorizontal\n          ? {\n              left: 0,\n              width: position.size + 'px',\n              transform: `translateX(${position.offset}px)`,\n            }\n          : {\n              top: 0,\n              height: position.size + 'px',\n              transform: `translateY(${position.offset}px)`,\n            }),\n        ...indicatorProps.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'NavigationMenuContent';\n\ntype NavigationMenuContentElement = NavigationMenuContentImplElement;\ninterface NavigationMenuContentProps\n  extends Omit<NavigationMenuContentImplProps, keyof NavigationMenuContentImplPrivateProps> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuContent = React.forwardRef<\n  NavigationMenuContentElement,\n  NavigationMenuContentProps\n>((props: ScopedProps<NavigationMenuContentProps>, forwardedRef) => {\n  const { forceMount, ...contentProps } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const composedRefs = useComposedRefs(itemContext.contentRef, forwardedRef);\n  const open = itemContext.value === context.value;\n\n  const commonProps = {\n    value: itemContext.value,\n    triggerRef: itemContext.triggerRef,\n    focusProxyRef: itemContext.focusProxyRef,\n    wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n    onContentFocusOutside: itemContext.onContentFocusOutside,\n    onRootContentClose: itemContext.onRootContentClose,\n    ...contentProps,\n  };\n\n  return !context.viewport ? (\n    <Presence present={forceMount || open}>\n      <NavigationMenuContentImpl\n        data-state={getOpenState(open)}\n        {...commonProps}\n        ref={composedRefs}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n        onPointerLeave={composeEventHandlers(\n          props.onPointerLeave,\n          whenMouse(context.onContentLeave)\n        )}\n        style={{\n          // Prevent interaction when animating out\n          pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n          ...commonProps.style,\n        }}\n      />\n    </Presence>\n  ) : (\n    <ViewportContentMounter forceMount={forceMount} {...commonProps} ref={composedRefs} />\n  );\n});\n\nNavigationMenuContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ViewportContentMounterElement = NavigationMenuContentImplElement;\ninterface ViewportContentMounterProps extends NavigationMenuContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ViewportContentMounter = React.forwardRef<\n  ViewportContentMounterElement,\n  ViewportContentMounterProps\n>((props: ScopedProps<ViewportContentMounterProps>, forwardedRef) => {\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const { onViewportContentChange, onViewportContentRemove } = context;\n\n  useLayoutEffect(() => {\n    onViewportContentChange(props.value, {\n      ref: forwardedRef,\n      ...props,\n    });\n  }, [props, forwardedRef, onViewportContentChange]);\n\n  useLayoutEffect(() => {\n    return () => onViewportContentRemove(props.value);\n  }, [props.value, onViewportContentRemove]);\n\n  // Content is proxied into the viewport\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_CONTENT_DISMISS = 'navigationMenu.rootContentDismiss';\n\ntype MotionAttribute = 'to-start' | 'to-end' | 'from-start' | 'from-end';\ntype NavigationMenuContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\n\ninterface NavigationMenuContentImplPrivateProps {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onContentFocusOutside(): void;\n  onRootContentClose(): void;\n}\ninterface NavigationMenuContentImplProps\n  extends Omit<DismissableLayerProps, 'onDismiss' | 'disableOutsidePointerEvents'>,\n    NavigationMenuContentImplPrivateProps {}\n\nconst NavigationMenuContentImpl = React.forwardRef<\n  NavigationMenuContentImplElement,\n  NavigationMenuContentImplProps\n>((props: ScopedProps<NavigationMenuContentImplProps>, forwardedRef) => {\n  const {\n    __scopeNavigationMenu,\n    value,\n    triggerRef,\n    focusProxyRef,\n    wasEscapeCloseRef,\n    onRootContentClose,\n    onContentFocusOutside,\n    ...contentProps\n  } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuContentImplElement>(null);\n  const composedRefs = useComposedRefs(ref, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, value);\n  const contentId = makeContentId(context.baseId, value);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const prevMotionAttributeRef = React.useRef<MotionAttribute | null>(null);\n\n  const { onItemDismiss } = context;\n\n  React.useEffect(() => {\n    const content = ref.current;\n\n    // Bubble dismiss to the root content node and focus its trigger\n    if (context.isRootMenu && content) {\n      const handleClose = () => {\n        onItemDismiss();\n        onRootContentClose();\n        if (content.contains(document.activeElement)) triggerRef.current?.focus();\n      };\n      content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n      return () => content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n    }\n  }, [context.isRootMenu, props.value, triggerRef, onItemDismiss, onRootContentClose]);\n\n  const motionAttribute = React.useMemo(() => {\n    const items = getItems();\n    const values = items.map((item) => item.value);\n    if (context.dir === 'rtl') values.reverse();\n    const index = values.indexOf(context.value);\n    const prevIndex = values.indexOf(context.previousValue);\n    const isSelected = value === context.value;\n    const wasSelected = prevIndex === values.indexOf(value);\n\n    // We only want to update selected and the last selected content\n    // this avoids animations being interrupted outside of that range\n    if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n\n    const attribute = (() => {\n      // Don't provide a direction on the initial open\n      if (index !== prevIndex) {\n        // If we're moving to this item from another\n        if (isSelected && prevIndex !== -1) return index > prevIndex ? 'from-end' : 'from-start';\n        // If we're leaving this item for another\n        if (wasSelected && index !== -1) return index > prevIndex ? 'to-start' : 'to-end';\n      }\n      // Otherwise we're entering from closed or leaving the list\n      // entirely and should not animate in any direction\n      return null;\n    })();\n\n    prevMotionAttributeRef.current = attribute;\n    return attribute;\n  }, [context.previousValue, context.value, context.dir, getItems, value]);\n\n  return (\n    <FocusGroup asChild>\n      <DismissableLayer\n        id={contentId}\n        aria-labelledby={triggerId}\n        data-motion={motionAttribute}\n        data-orientation={context.orientation}\n        {...contentProps}\n        ref={composedRefs}\n        disableOutsidePointerEvents={false}\n        onDismiss={() => {\n          const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n            bubbles: true,\n            cancelable: true,\n          });\n          ref.current?.dispatchEvent(rootContentDismissEvent);\n        }}\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n          onContentFocusOutside();\n          const target = event.target as HTMLElement;\n          // Only dismiss content when focus moves outside of the menu\n          if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const target = event.target as HTMLElement;\n          const isTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n          if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n          if (isTabKey) {\n            const candidates = getTabbableCandidates(event.currentTarget);\n            const focusedElement = document.activeElement;\n            const index = candidates.findIndex((candidate) => candidate === focusedElement);\n            const isMovingBackwards = event.shiftKey;\n            const nextCandidates = isMovingBackwards\n              ? candidates.slice(0, index).reverse()\n              : candidates.slice(index + 1, candidates.length);\n\n            if (focusFirst(nextCandidates)) {\n              // prevent browser tab keydown because we've handled focus\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges\n              // so focus the proxy and let browser handle\n              // tab/shift+tab keypress on the proxy instead\n              focusProxyRef.current?.focus();\n            }\n          }\n        })}\n        onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (_event) => {\n          // prevent the dropdown from reopening\n          // after the escape key has been pressed\n          wasEscapeCloseRef.current = true;\n        })}\n      />\n    </FocusGroup>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'NavigationMenuViewport';\n\ntype NavigationMenuViewportElement = NavigationMenuViewportImplElement;\ninterface NavigationMenuViewportProps\n  extends Omit<NavigationMenuViewportImplProps, 'activeContentValue'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuViewport = React.forwardRef<\n  NavigationMenuViewportElement,\n  NavigationMenuViewportProps\n>((props: ScopedProps<NavigationMenuViewportProps>, forwardedRef) => {\n  const { forceMount, ...viewportProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n  const open = Boolean(context.value);\n\n  return (\n    <Presence present={forceMount || open}>\n      <NavigationMenuViewportImpl {...viewportProps} ref={forwardedRef} />\n    </Presence>\n  );\n});\n\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype NavigationMenuViewportImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuViewportImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuViewportImpl = React.forwardRef<\n  NavigationMenuViewportImplElement,\n  NavigationMenuViewportImplProps\n>((props: ScopedProps<NavigationMenuViewportImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n  const composedRefs = useComposedRefs(forwardedRef, context.onViewportChange);\n  const viewportContentContext = useViewportContentContext(\n    CONTENT_NAME,\n    props.__scopeNavigationMenu\n  );\n  const [size, setSize] = React.useState<{ width: number; height: number } | null>(null);\n  const [content, setContent] = React.useState<NavigationMenuContentElement | null>(null);\n  const viewportWidth = size ? size?.width + 'px' : undefined;\n  const viewportHeight = size ? size?.height + 'px' : undefined;\n  const open = Boolean(context.value);\n  // We persist the last active content value as the viewport may be animating out\n  // and we want the content to remain mounted for the lifecycle of the viewport.\n  const activeContentValue = open ? context.value : context.previousValue;\n\n  /**\n   * Update viewport size to match the active content node.\n   * We prefer offset dimensions over `getBoundingClientRect` as the latter respects CSS transform.\n   * For example, if content animates in from `scale(0.5)` the dimensions would be anything\n   * from `0.5` to `1` of the intended size.\n   */\n  const handleSizeChange = () => {\n    if (content) setSize({ width: content.offsetWidth, height: content.offsetHeight });\n  };\n  useResizeObserver(content, handleSizeChange);\n\n  return (\n    <Primitive.div\n      data-state={getOpenState(open)}\n      data-orientation={context.orientation}\n      {...viewportImplProps}\n      ref={composedRefs}\n      style={{\n        // Prevent interaction when animating out\n        pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n        ['--radix-navigation-menu-viewport-width' as any]: viewportWidth,\n        ['--radix-navigation-menu-viewport-height' as any]: viewportHeight,\n        ...viewportImplProps.style,\n      }}\n      onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n      onPointerLeave={composeEventHandlers(props.onPointerLeave, whenMouse(context.onContentLeave))}\n    >\n      {Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props }]) => {\n        const isActive = activeContentValue === value;\n        return (\n          <Presence key={value} present={forceMount || isActive}>\n            <NavigationMenuContentImpl\n              {...props}\n              ref={composeRefs(ref, (node) => {\n                // We only want to update the stored node when another is available\n                // as we need to smoothly transition between them.\n                if (isActive && node) setContent(node);\n              })}\n            />\n          </Presence>\n        );\n      })}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_GROUP_NAME = 'FocusGroup';\n\ntype FocusGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface FocusGroupProps extends PrimitiveDivProps {}\n\nconst FocusGroup = React.forwardRef<FocusGroupElement, FocusGroupProps>(\n  (props: ScopedProps<FocusGroupProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.Provider scope={__scopeNavigationMenu}>\n        <FocusGroupCollection.Slot scope={__scopeNavigationMenu}>\n          <Primitive.div dir={context.dir} {...groupProps} ref={forwardedRef} />\n        </FocusGroupCollection.Slot>\n      </FocusGroupCollection.Provider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_KEYS = ['ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown'];\nconst FOCUS_GROUP_ITEM_NAME = 'FocusGroupItem';\n\ntype FocusGroupItemElement = React.ComponentRef<typeof Primitive.button>;\ninterface FocusGroupItemProps extends PrimitiveButtonProps {}\n\nconst FocusGroupItem = React.forwardRef<FocusGroupItemElement, FocusGroupItemProps>(\n  (props: ScopedProps<FocusGroupItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.ItemSlot scope={__scopeNavigationMenu}>\n        <Primitive.button\n          {...groupProps}\n          ref={forwardedRef}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isFocusNavigationKey = ['Home', 'End', ...ARROW_KEYS].includes(event.key);\n            if (isFocusNavigationKey) {\n              let candidateNodes = getItems().map((item) => item.ref.current!);\n              const prevItemKey = context.dir === 'rtl' ? 'ArrowRight' : 'ArrowLeft';\n              const prevKeys = [prevItemKey, 'ArrowUp', 'End'];\n              if (prevKeys.includes(event.key)) candidateNodes.reverse();\n              if (ARROW_KEYS.includes(event.key)) {\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = candidateNodes.slice(currentIndex + 1);\n              }\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n\n              // Prevent page scroll while navigating\n              event.preventDefault();\n            }\n          })}\n        />\n      </FocusGroupCollection.ItemSlot>\n    );\n  }\n);\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nfunction removeFromTabOrder(candidates: HTMLElement[]) {\n  candidates.forEach((candidate) => {\n    candidate.dataset.tabindex = candidate.getAttribute('tabindex') || '';\n    candidate.setAttribute('tabindex', '-1');\n  });\n  return () => {\n    candidates.forEach((candidate) => {\n      const prevTabIndex = candidate.dataset.tabindex as string;\n      candidate.setAttribute('tabindex', prevTabIndex);\n    });\n  };\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = NavigationMenu;\nconst Sub = NavigationMenuSub;\nconst List = NavigationMenuList;\nconst Item = NavigationMenuItem;\nconst Trigger = NavigationMenuTrigger;\nconst Link = NavigationMenuLink;\nconst Indicator = NavigationMenuIndicator;\nconst Content = NavigationMenuContent;\nconst Viewport = NavigationMenuViewport;\n\nexport {\n  createNavigationMenuScope,\n  //\n  NavigationMenu,\n  NavigationMenuSub,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuContent,\n  NavigationMenuViewport,\n  //\n  Root,\n  Sub,\n  List,\n  Item,\n  Trigger,\n  Link,\n  Indicator,\n  Content,\n  Viewport,\n};\nexport type {\n  NavigationMenuProps,\n  NavigationMenuSubProps,\n  NavigationMenuListProps,\n  NavigationMenuItemProps,\n  NavigationMenuTriggerProps,\n  NavigationMenuLinkProps,\n  NavigationMenuIndicatorProps,\n  NavigationMenuContentProps,\n  NavigationMenuViewportProps,\n};\n", "import * as Primitive from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { unstable_createCollection as createCollection } from '@radix-ui/react-collection';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\nimport * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { clamp } from '@radix-ui/number';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\n\ntype InputValidationType = 'alpha' | 'numeric' | 'alphanumeric' | 'none';\n\nconst INPUT_VALIDATION_MAP = {\n  numeric: {\n    type: 'numeric',\n    regexp: /[^\\d]/g,\n    pattern: '\\\\d{1}',\n    inputMode: 'numeric',\n  },\n  alpha: {\n    type: 'alpha',\n    regexp: /[^a-zA-Z]/g,\n    pattern: '[a-zA-Z]{1}',\n    inputMode: 'text',\n  },\n  alphanumeric: {\n    type: 'alphanumeric',\n    regexp: /[^a-zA-Z0-9]/g,\n    pattern: '[a-zA-Z0-9]{1}',\n    inputMode: 'text',\n  },\n  none: null,\n} satisfies InputValidation;\n\n/* -------------------------------------------------------------------------------------------------\n * OneTimePasswordFieldProvider\n * -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupProps = RovingFocusGroup.RovingFocusGroupProps;\n\ninterface OneTimePasswordFieldContextValue {\n  attemptSubmit: () => void;\n  autoComplete: AutoComplete;\n  autoFocus: boolean;\n  disabled: boolean;\n  dispatch: Dispatcher;\n  form: string | undefined;\n  hiddenInputRef: React.RefObject<HTMLInputElement | null>;\n  isHydrated: boolean;\n  name: string | undefined;\n  orientation: Exclude<RovingFocusGroupProps['orientation'], undefined>;\n  placeholder: string | undefined;\n  readOnly: boolean;\n  type: InputType;\n  userActionRef: React.RefObject<KeyboardActionDetails | null>;\n  validationType: InputValidationType;\n  value: string[];\n  sanitizeValue: (arg: string | string[]) => string[];\n}\n\nconst ONE_TIME_PASSWORD_FIELD_NAME = 'OneTimePasswordField';\nconst [Collection, { useCollection, createCollectionScope, useInitCollection }] =\n  createCollection<HTMLInputElement>(ONE_TIME_PASSWORD_FIELD_NAME);\nconst [createOneTimePasswordFieldContext] = createContextScope(ONE_TIME_PASSWORD_FIELD_NAME, [\n  createCollectionScope,\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\nconst [OneTimePasswordFieldContext, useOneTimePasswordFieldContext] =\n  createOneTimePasswordFieldContext<OneTimePasswordFieldContextValue>(ONE_TIME_PASSWORD_FIELD_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * OneTimePasswordField\n * -----------------------------------------------------------------------------------------------*/\n\ninterface OneTimePasswordFieldOwnProps {\n  /**\n   * Specifies what—if any—permission the user agent has to provide automated\n   * assistance in filling out form field values, as well as guidance to the\n   * browser as to the type of information expected in the field. Allows\n   * `\"one-time-code\"` or `\"off\"`.\n   *\n   * @defaultValue `\"one-time-code\"`\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Attributes/autocomplete\n   */\n  autoComplete?: AutoComplete;\n  /**\n   * Whether or not the first fillable input should be focused on page-load.\n   *\n   * @defaultValue `false`\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Global_attributes/autofocus\n   */\n  autoFocus?: boolean;\n  /**\n   * Whether or not the component should attempt to automatically submit when\n   * all fields are filled. If the field is associated with an HTML `form`\n   * element, the form's `requestSubmit` method will be called.\n   *\n   * @defaultValue `false`\n   */\n  autoSubmit?: boolean;\n  /**\n   * The initial value of the uncontrolled field.\n   */\n  defaultValue?: string;\n  /**\n   * Indicates the horizontal directionality of the parent element's text.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Global_attributes/dir\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether or not the the field's input elements are disabled.\n   */\n  disabled?: boolean;\n  /**\n   * A string specifying the `form` element with which the input is associated.\n   * This string's value, if present, must match the id of a `form` element in\n   * the same document.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#form\n   */\n  form?: string | undefined;\n  /**\n   * A string specifying a name for the input control. This name is submitted\n   * along with the control's value when the form data is submitted.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Elements/input#name\n   */\n  name?: string | undefined;\n  /**\n   * When the `autoSubmit` prop is set to `true`, this callback will be fired\n   * before attempting to submit the associated form. It will be called whether\n   * or not a form is located, or if submission is not allowed.\n   */\n  onAutoSubmit?: (value: string) => void;\n  /**\n   * A callback fired when the field's value changes. When the component is\n   * controlled, this should update the state passed to the `value` prop.\n   */\n  onValueChange?: (value: string) => void;\n  /**\n   * Indicates the vertical directionality of the input elements.\n   *\n   * @defaultValue `\"horizontal\"`\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * Defines the text displayed in a form control when the control has no value.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Attributes/placeholder\n   */\n  placeholder?: string | undefined;\n  /**\n   * Whether or not the input elements can be updated by the user.\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Reference/Attributes/readonly\n   */\n  readOnly?: boolean;\n  /**\n   * Function for custom sanitization when `validationType` is set to `\"none\"`.\n   * This function will be called before updating values in response to user\n   * interactions.\n   */\n  sanitizeValue?: (value: string) => string;\n  /**\n   * The input type of the field's input elements. Can be `\"password\"` or `\"text\"`.\n   */\n  type?: InputType;\n  /**\n   * Specifies the type of input validation to be used. Can be `\"alpha\"`,\n   * `\"numeric\"`, `\"alphanumeric\"` or `\"none\"`.\n   *\n   * @defaultValue `\"numeric\"`\n   */\n  validationType?: InputValidationType;\n  /**\n   * The controlled value of the field.\n   */\n  value?: string;\n}\n\ntype ScopedProps<P> = P & { __scopeOneTimePasswordField?: Scope };\n\ninterface OneTimePasswordFieldProps\n  extends OneTimePasswordFieldOwnProps,\n    Omit<Primitive.PrimitivePropsWithRef<'div'>, keyof OneTimePasswordFieldOwnProps> {}\n\nconst OneTimePasswordField = React.forwardRef<HTMLDivElement, OneTimePasswordFieldProps>(\n  function OneTimePasswordFieldImpl(\n    {\n      __scopeOneTimePasswordField,\n      defaultValue,\n      value: valueProp,\n      onValueChange,\n      autoSubmit = false,\n      children,\n      onPaste,\n      onAutoSubmit,\n      disabled = false,\n      readOnly = false,\n      autoComplete = 'one-time-code',\n      autoFocus = false,\n      form,\n      name,\n      placeholder,\n      type = 'text',\n      // TODO: Change default to vertical when inputs use vertical writing mode\n      orientation = 'horizontal',\n      dir,\n      validationType = 'numeric',\n      sanitizeValue: sanitizeValueProp,\n      ...domProps\n    }: ScopedProps<OneTimePasswordFieldProps>,\n    forwardedRef\n  ) {\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeOneTimePasswordField);\n    const direction = useDirection(dir);\n    const collectionState = useInitCollection();\n    const [collection] = collectionState;\n\n    const validation = INPUT_VALIDATION_MAP[validationType]\n      ? INPUT_VALIDATION_MAP[validationType as keyof InputValidation]\n      : null;\n\n    const sanitizeValue = React.useCallback(\n      (value: string | string[]) => {\n        if (Array.isArray(value)) {\n          value = value.map(removeWhitespace).join('');\n        } else {\n          value = removeWhitespace(value);\n        }\n\n        if (validation) {\n          // global regexp is stateful, so we clone it for each call\n          const regexp = new RegExp(validation.regexp);\n          value = value.replace(regexp, '');\n        } else if (sanitizeValueProp) {\n          value = sanitizeValueProp(value);\n        }\n\n        return value.split('');\n      },\n      [validation, sanitizeValueProp]\n    );\n\n    const controlledValue = React.useMemo(() => {\n      return valueProp != null ? sanitizeValue(valueProp) : undefined;\n    }, [valueProp, sanitizeValue]);\n\n    const [value, setValue] = useControllableState({\n      caller: 'OneTimePasswordField',\n      prop: controlledValue,\n      defaultProp: defaultValue != null ? sanitizeValue(defaultValue) : [],\n      onChange: React.useCallback(\n        (value: string[]) => onValueChange?.(value.join('')),\n        [onValueChange]\n      ),\n    });\n\n    // Update function *specifically* for event handlers.\n    const dispatch = useEffectEvent<Dispatcher>((action) => {\n      switch (action.type) {\n        case 'SET_CHAR': {\n          const { index, char } = action;\n          const currentTarget = collection.at(index)?.element;\n          if (value[index] === char) {\n            const next = currentTarget && collection.from(currentTarget, 1)?.element;\n            focusInput(next);\n            return;\n          }\n\n          // empty values should be handled in the CLEAR_CHAR action\n          if (char === '') {\n            return;\n          }\n\n          if (validation) {\n            const regexp = new RegExp(validation.regexp);\n            const clean = char.replace(regexp, '');\n            if (clean !== char) {\n              // not valid; ignore\n              return;\n            }\n          }\n\n          // no more space\n          if (value.length >= collection.size) {\n            // replace current value; move to next input\n            const newValue = [...value];\n            newValue[index] = char;\n            flushSync(() => setValue(newValue));\n            const next = currentTarget && collection.from(currentTarget, 1)?.element;\n            focusInput(next);\n            return;\n          }\n\n          const newValue = [...value];\n          newValue[index] = char;\n\n          const lastElement = collection.at(-1)?.element;\n          flushSync(() => setValue(newValue));\n          if (currentTarget !== lastElement) {\n            const next = currentTarget && collection.from(currentTarget, 1)?.element;\n            focusInput(next);\n          } else {\n            currentTarget?.select();\n          }\n          return;\n        }\n\n        case 'CLEAR_CHAR': {\n          const { index, reason } = action;\n          if (!value[index]) {\n            return;\n          }\n\n          const newValue = value.filter((_, i) => i !== index);\n          const currentTarget = collection.at(index)?.element;\n          const previous = currentTarget && collection.from(currentTarget, -1)?.element;\n\n          flushSync(() => setValue(newValue));\n          if (reason === 'Backspace') {\n            focusInput(previous);\n          } else if (reason === 'Delete' || reason === 'Cut') {\n            focusInput(currentTarget);\n          }\n          return;\n        }\n\n        case 'CLEAR': {\n          if (value.length === 0) {\n            return;\n          }\n\n          if (action.reason === 'Backspace' || action.reason === 'Delete') {\n            flushSync(() => setValue([]));\n            focusInput(collection.at(0)?.element);\n          } else {\n            setValue([]);\n          }\n          return;\n        }\n\n        case 'PASTE': {\n          const { value: pastedValue } = action;\n          const value = sanitizeValue(pastedValue);\n          if (!value) {\n            return;\n          }\n\n          flushSync(() => setValue(value));\n          focusInput(collection.at(value.length - 1)?.element);\n          return;\n        }\n      }\n    });\n\n    // re-validate when the validation type changes\n    const validationTypeRef = React.useRef(validation);\n    React.useEffect(() => {\n      if (!validation) {\n        return;\n      }\n\n      if (validationTypeRef.current?.type !== validation.type) {\n        validationTypeRef.current = validation;\n        setValue(sanitizeValue(value.join('')));\n      }\n    }, [sanitizeValue, setValue, validation, value]);\n\n    const hiddenInputRef = React.useRef<HTMLInputElement>(null);\n\n    const userActionRef = React.useRef<KeyboardActionDetails | null>(null);\n    const rootRef = React.useRef<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, rootRef);\n\n    const firstInput = collection.at(0)?.element;\n    const locateForm = React.useCallback(() => {\n      let formElement: HTMLFormElement | null | undefined;\n      if (form) {\n        const associatedElement = (rootRef.current?.ownerDocument ?? document).getElementById(form);\n        if (isFormElement(associatedElement)) {\n          formElement = associatedElement;\n        }\n      } else if (hiddenInputRef.current) {\n        formElement = hiddenInputRef.current.form;\n      } else if (firstInput) {\n        formElement = firstInput.form;\n      }\n\n      return formElement ?? null;\n    }, [form, firstInput]);\n\n    const attemptSubmit = React.useCallback(() => {\n      const formElement = locateForm();\n      formElement?.requestSubmit();\n    }, [locateForm]);\n\n    React.useEffect(() => {\n      const form = locateForm();\n      if (form) {\n        const reset = () => dispatch({ type: 'CLEAR', reason: 'Reset' });\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [dispatch, locateForm]);\n\n    const currentValue = value.join('');\n    const valueRef = React.useRef(currentValue);\n    const length = collection.size;\n    React.useEffect(() => {\n      const previousValue = valueRef.current;\n      valueRef.current = currentValue;\n      if (previousValue === currentValue) {\n        return;\n      }\n\n      if (autoSubmit && value.every((char) => char !== '') && value.length === length) {\n        onAutoSubmit?.(value.join(''));\n        attemptSubmit();\n      }\n    }, [attemptSubmit, autoSubmit, currentValue, length, onAutoSubmit, value]);\n    const isHydrated = useIsHydrated();\n\n    return (\n      <OneTimePasswordFieldContext\n        scope={__scopeOneTimePasswordField}\n        value={value}\n        attemptSubmit={attemptSubmit}\n        disabled={disabled}\n        readOnly={readOnly}\n        autoComplete={autoComplete}\n        autoFocus={autoFocus}\n        form={form}\n        name={name}\n        placeholder={placeholder}\n        type={type}\n        hiddenInputRef={hiddenInputRef}\n        userActionRef={userActionRef}\n        dispatch={dispatch}\n        validationType={validationType}\n        orientation={orientation}\n        isHydrated={isHydrated}\n        sanitizeValue={sanitizeValue}\n      >\n        <Collection.Provider scope={__scopeOneTimePasswordField} state={collectionState}>\n          <Collection.Slot scope={__scopeOneTimePasswordField}>\n            <RovingFocusGroup.Root\n              asChild\n              {...rovingFocusGroupScope}\n              orientation={orientation}\n              dir={direction}\n            >\n              <Primitive.Root.div\n                {...domProps}\n                role=\"group\"\n                ref={composedRefs}\n                onPaste={composeEventHandlers(\n                  onPaste,\n                  (event: React.ClipboardEvent<HTMLDivElement>) => {\n                    event.preventDefault();\n                    const pastedValue = event.clipboardData.getData('Text');\n                    dispatch({ type: 'PASTE', value: pastedValue });\n                  }\n                )}\n              >\n                {children}\n              </Primitive.Root.div>\n            </RovingFocusGroup.Root>\n          </Collection.Slot>\n        </Collection.Provider>\n      </OneTimePasswordFieldContext>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * OneTimePasswordFieldHiddenInput\n * -----------------------------------------------------------------------------------------------*/\n\ninterface OneTimePasswordFieldHiddenInputProps\n  extends Omit<\n    React.ComponentProps<'input'>,\n    | keyof 'value'\n    | 'defaultValue'\n    | 'type'\n    | 'onChange'\n    | 'readOnly'\n    | 'disabled'\n    | 'autoComplete'\n    | 'autoFocus'\n  > {}\n\nconst OneTimePasswordFieldHiddenInput = React.forwardRef<\n  HTMLInputElement,\n  OneTimePasswordFieldHiddenInputProps\n>(function OneTimePasswordFieldHiddenInput(\n  { __scopeOneTimePasswordField, ...props }: ScopedProps<OneTimePasswordFieldHiddenInputProps>,\n  forwardedRef\n) {\n  const { value, hiddenInputRef, name } = useOneTimePasswordFieldContext(\n    'OneTimePasswordFieldHiddenInput',\n    __scopeOneTimePasswordField\n  );\n  const ref = useComposedRefs(hiddenInputRef, forwardedRef);\n  return (\n    <input\n      ref={ref}\n      name={name}\n      value={value.join('').trim()}\n      autoComplete=\"off\"\n      autoFocus={false}\n      autoCapitalize=\"off\"\n      autoCorrect=\"off\"\n      autoSave=\"off\"\n      spellCheck={false}\n      {...props}\n      type=\"hidden\"\n      readOnly\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * OneTimePasswordFieldInput\n * -----------------------------------------------------------------------------------------------*/\n\ninterface OneTimePasswordFieldInputProps\n  extends Omit<\n    Primitive.PrimitivePropsWithRef<'input'>,\n    | 'value'\n    | 'defaultValue'\n    | 'disabled'\n    | 'readOnly'\n    | 'autoComplete'\n    | 'autoFocus'\n    | 'form'\n    | 'name'\n    | 'placeholder'\n    | 'type'\n  > {\n  /**\n   * Callback fired when the user input fails native HTML input validation.\n   */\n  onInvalidChange?: (character: string) => void;\n  /**\n   * User-provided index to determine the order of the inputs. This is useful if\n   * you need certain index-based attributes to be set on the initial render,\n   * often to prevent flickering after hydration.\n   */\n  index?: number;\n}\n\nconst OneTimePasswordFieldInput = React.forwardRef<\n  HTMLInputElement,\n  OneTimePasswordFieldInputProps\n>(function OneTimePasswordFieldInput(\n  {\n    __scopeOneTimePasswordField,\n    onInvalidChange,\n    index: indexProp,\n    ...props\n  }: ScopedProps<OneTimePasswordFieldInputProps>,\n  forwardedRef\n) {\n  // TODO: warn if these values are passed\n  const {\n    value: _value,\n    defaultValue: _defaultValue,\n    disabled: _disabled,\n    readOnly: _readOnly,\n    autoComplete: _autoComplete,\n    autoFocus: _autoFocus,\n    form: _form,\n    name: _name,\n    placeholder: _placeholder,\n    type: _type,\n    ...domProps\n  } = props as Primitive.PrimitivePropsWithRef<'input'>;\n\n  const context = useOneTimePasswordFieldContext(\n    'OneTimePasswordFieldInput',\n    __scopeOneTimePasswordField\n  );\n  const { dispatch, userActionRef, validationType, isHydrated } = context;\n  const collection = useCollection(__scopeOneTimePasswordField);\n  const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeOneTimePasswordField);\n\n  const inputRef = React.useRef<HTMLInputElement>(null);\n  const [element, setElement] = React.useState<HTMLInputElement | null>(null);\n\n  const index = indexProp ?? (element ? collection.indexOf(element) : -1);\n  const canSetPlaceholder = indexProp != null || isHydrated;\n  let placeholder: string | undefined;\n  if (canSetPlaceholder && context.placeholder && context.value.length === 0) {\n    // only set placeholder after hydration to prevent flickering when indices\n    // are re-calculated\n    placeholder = context.placeholder[index];\n  }\n\n  const composedInputRef = useComposedRefs(forwardedRef, inputRef, setElement);\n  const char = context.value[index] ?? '';\n\n  const keyboardActionTimeoutRef = React.useRef<number | null>(null);\n  React.useEffect(() => {\n    return () => {\n      window.clearTimeout(keyboardActionTimeoutRef.current!);\n    };\n  }, []);\n\n  const totalValue = context.value.join('').trim();\n  const lastSelectableIndex = clamp(totalValue.length, [0, collection.size - 1]);\n  const isFocusable = index <= lastSelectableIndex;\n\n  const validation =\n    validationType in INPUT_VALIDATION_MAP\n      ? INPUT_VALIDATION_MAP[validationType as keyof InputValidation]\n      : undefined;\n\n  return (\n    <Collection.ItemSlot scope={__scopeOneTimePasswordField}>\n      <RovingFocusGroup.Item\n        {...rovingFocusGroupScope}\n        asChild\n        focusable={!context.disabled && isFocusable}\n        active={index === lastSelectableIndex}\n      >\n        {({ hasTabStop, isCurrentTabStop }) => {\n          const supportsAutoComplete = hasTabStop ? isCurrentTabStop : index === 0;\n          return (\n            <Primitive.Root.input\n              ref={composedInputRef}\n              type={context.type}\n              aria-label={`Character ${index + 1} of ${collection.size}`}\n              autoComplete={supportsAutoComplete ? context.autoComplete : 'off'}\n              data-1p-ignore={supportsAutoComplete ? undefined : 'true'}\n              data-lpignore={supportsAutoComplete ? undefined : 'true'}\n              data-protonpass-ignore={supportsAutoComplete ? undefined : 'true'}\n              data-bwignore={supportsAutoComplete ? undefined : 'true'}\n              inputMode={validation?.inputMode}\n              maxLength={1}\n              pattern={validation?.pattern}\n              readOnly={context.readOnly}\n              value={char}\n              placeholder={placeholder}\n              data-radix-otp-input=\"\"\n              data-radix-index={index}\n              {...domProps}\n              onFocus={composeEventHandlers(props.onFocus, (event) => {\n                event.currentTarget.select();\n              })}\n              onCut={composeEventHandlers(props.onCut, (event) => {\n                const currentValue = event.currentTarget.value;\n                if (currentValue !== '') {\n                  // In this case the value will be cleared, but we don't want to\n                  // set it directly because the user may want to prevent default\n                  // behavior in the onChange handler. The userActionRef will\n                  // is set temporarily so the change handler can behave correctly\n                  // in response to the action.\n                  userActionRef.current = {\n                    type: 'cut',\n                  };\n                  // Set a short timeout to clear the action tracker after the change\n                  // handler has had time to complete.\n                  keyboardActionTimeoutRef.current = window.setTimeout(() => {\n                    userActionRef.current = null;\n                  }, 10);\n                }\n              })}\n              onInput={composeEventHandlers(props.onInput, (event) => {\n                const value = event.currentTarget.value;\n                if (value.length > 1) {\n                  // Password managers may try to insert the code into a single\n                  // input, in which case form validation will fail to prevent\n                  // additional input. Handle this the same as if a user were\n                  // pasting a value.\n                  event.preventDefault();\n                  dispatch({ type: 'PASTE', value });\n                }\n              })}\n              onChange={composeEventHandlers(props.onChange, (event) => {\n                const value = event.target.value;\n                event.preventDefault();\n                const action = userActionRef.current;\n                userActionRef.current = null;\n\n                if (action) {\n                  switch (action.type) {\n                    case 'cut':\n                      // TODO: do we want to assume the user wantt to clear the\n                      // entire value here and copy the code to the clipboard instead\n                      // of just the value of the given input?\n                      dispatch({ type: 'CLEAR_CHAR', index, reason: 'Cut' });\n                      return;\n                    case 'keydown': {\n                      if (action.key === 'Char') {\n                        // update resulting from a keydown event that set a value\n                        // directly. Ignore.\n                        return;\n                      }\n\n                      const isClearing =\n                        action.key === 'Backspace' && (action.metaKey || action.ctrlKey);\n                      if (action.key === 'Clear' || isClearing) {\n                        dispatch({ type: 'CLEAR', reason: 'Backspace' });\n                      } else {\n                        dispatch({ type: 'CLEAR_CHAR', index, reason: action.key });\n                      }\n                      return;\n                    }\n                    default:\n                      return;\n                  }\n                }\n\n                // Only update the value if it matches the input pattern\n                if (event.target.validity.valid) {\n                  if (value === '') {\n                    let reason: 'Backspace' | 'Delete' | 'Cut' = 'Backspace';\n                    if (isInputEvent(event.nativeEvent)) {\n                      const inputType = event.nativeEvent.inputType;\n                      if (inputType === 'deleteContentBackward') {\n                        reason = 'Backspace';\n                      } else if (inputType === 'deleteByCut') {\n                        reason = 'Cut';\n                      }\n                    }\n                    dispatch({ type: 'CLEAR_CHAR', index, reason });\n                  } else {\n                    dispatch({ type: 'SET_CHAR', char: value, index, event });\n                  }\n                } else {\n                  const element = event.target;\n                  onInvalidChange?.(element.value);\n                  requestAnimationFrame(() => {\n                    if (element.ownerDocument.activeElement === element) {\n                      element.select();\n                    }\n                  });\n                }\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                switch (event.key) {\n                  case 'Clear':\n                  case 'Delete':\n                  case 'Backspace': {\n                    const currentValue = event.currentTarget.value;\n                    // if current value is empty, no change event will fire\n                    if (currentValue === '') {\n                      // if the user presses delete when there is no value, noop\n                      if (event.key === 'Delete') return;\n\n                      const isClearing = event.key === 'Clear' || event.metaKey || event.ctrlKey;\n                      if (isClearing) {\n                        dispatch({ type: 'CLEAR', reason: 'Backspace' });\n                      } else {\n                        const element = event.currentTarget;\n                        requestAnimationFrame(() => {\n                          focusInput(collection.from(element, -1)?.element);\n                        });\n                      }\n                    } else {\n                      // In this case the value will be cleared, but we don't want\n                      // to set it directly because the user may want to prevent\n                      // default behavior in the onChange handler. The userActionRef\n                      // will is set temporarily so the change handler can behave\n                      // correctly in response to the key vs. clearing the value by\n                      // setting state externally.\n                      userActionRef.current = {\n                        type: 'keydown',\n                        key: event.key,\n                        metaKey: event.metaKey,\n                        ctrlKey: event.ctrlKey,\n                      };\n                      // Set a short timeout to clear the action tracker after the change\n                      // handler has had time to complete.\n                      keyboardActionTimeoutRef.current = window.setTimeout(() => {\n                        userActionRef.current = null;\n                      }, 10);\n                    }\n\n                    return;\n                  }\n                  case 'Enter': {\n                    event.preventDefault();\n                    context.attemptSubmit();\n                    return;\n                  }\n                  case 'ArrowDown':\n                  case 'ArrowUp': {\n                    if (context.orientation === 'horizontal') {\n                      // in horizontal orientation, the up/down will de-select the\n                      // input instead of moving focus\n                      event.preventDefault();\n                    }\n                    return;\n                  }\n                  // TODO: Handle left/right arrow keys in vertical writing mode\n                  default: {\n                    if (event.currentTarget.value === event.key) {\n                      // if current value is same as the key press, no change event\n                      // will fire. Focus the next input.\n                      const element = event.currentTarget;\n                      event.preventDefault();\n                      focusInput(collection.from(element, 1)?.element);\n                      return;\n                    } else if (\n                      // input already has a value, but...\n                      event.currentTarget.value &&\n                      // the value is not selected\n                      !(\n                        event.currentTarget.selectionStart === 0 &&\n                        event.currentTarget.selectionEnd != null &&\n                        event.currentTarget.selectionEnd > 0\n                      )\n                    ) {\n                      const attemptedValue = event.key;\n                      if (event.key.length > 1 || event.key === ' ') {\n                        // not a character; do nothing\n                        return;\n                      } else {\n                        // user is attempting to enter a character, but the input\n                        // will not update by default since it's limited to a single\n                        // character.\n                        const nextInput = collection.from(event.currentTarget, 1)?.element;\n                        const lastInput = collection.at(-1)?.element;\n                        if (nextInput !== lastInput && event.currentTarget !== lastInput) {\n                          // if selection is before the value, set the value of the\n                          // current input. Otherwise set the value of the next\n                          // input.\n                          if (event.currentTarget.selectionStart === 0) {\n                            dispatch({ type: 'SET_CHAR', char: attemptedValue, index, event });\n                          } else {\n                            dispatch({\n                              type: 'SET_CHAR',\n                              char: attemptedValue,\n                              index: index + 1,\n                              event,\n                            });\n                          }\n\n                          userActionRef.current = {\n                            type: 'keydown',\n                            key: 'Char',\n                            metaKey: event.metaKey,\n                            ctrlKey: event.ctrlKey,\n                          };\n                          keyboardActionTimeoutRef.current = window.setTimeout(() => {\n                            userActionRef.current = null;\n                          }, 10);\n                        }\n                      }\n                    }\n                  }\n                }\n              })}\n              onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                event.preventDefault();\n                const indexToFocus = Math.min(index, lastSelectableIndex);\n                const element = collection.at(indexToFocus)?.element;\n                focusInput(element);\n              })}\n            />\n          );\n        }}\n      </RovingFocusGroup.Item>\n    </Collection.ItemSlot>\n  );\n});\n\nexport {\n  OneTimePasswordField,\n  OneTimePasswordFieldInput,\n  OneTimePasswordFieldHiddenInput,\n  //\n  OneTimePasswordField as Root,\n  OneTimePasswordFieldInput as Input,\n  OneTimePasswordFieldHiddenInput as HiddenInput,\n};\nexport type {\n  OneTimePasswordFieldProps,\n  OneTimePasswordFieldInputProps,\n  OneTimePasswordFieldHiddenInputProps,\n  InputValidationType,\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isFormElement(element: Element | null | undefined): element is HTMLFormElement {\n  return element?.tagName === 'FORM';\n}\n\nfunction removeWhitespace(value: string) {\n  return value.replace(/\\s/g, '');\n}\n\nfunction focusInput(element: HTMLInputElement | null | undefined) {\n  if (!element) return;\n  if (element.ownerDocument.activeElement === element) {\n    // if the element is already focused, select the value in the next\n    // animation frame\n    window.requestAnimationFrame(() => {\n      element.select?.();\n    });\n  } else {\n    element.focus();\n  }\n}\n\nfunction isInputEvent(event: Event): event is InputEvent {\n  return event.type === 'input';\n}\n\ntype InputType = 'password' | 'text';\ntype AutoComplete = 'off' | 'one-time-code';\ntype KeyboardActionDetails =\n  | {\n      type: 'keydown';\n      key: 'Backspace' | 'Delete' | 'Clear' | 'Char';\n      metaKey: boolean;\n      ctrlKey: boolean;\n    }\n  | { type: 'cut' };\n\ntype UpdateAction =\n  | {\n      type: 'SET_CHAR';\n      char: string;\n      index: number;\n      event: React.KeyboardEvent | React.ChangeEvent;\n    }\n  | { type: 'CLEAR_CHAR'; index: number; reason: 'Backspace' | 'Delete' | 'Cut' }\n  | { type: 'CLEAR'; reason: 'Reset' | 'Backspace' | 'Delete' | 'Clear' }\n  | { type: 'PASTE'; value: string };\ntype Dispatcher = React.Dispatch<UpdateAction>;\ntype InputValidation = Record<\n  InputValidationType,\n  {\n    type: InputValidationType;\n    regexp: RegExp;\n    pattern: string;\n    inputMode: 'text' | 'numeric';\n  } | null\n>;\n", "import * as React from 'react';\nimport { flushSync } from 'react-dom';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useId } from '@radix-ui/react-id';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\nimport type { Scope } from '@radix-ui/react-context';\nimport { createContextScope } from '@radix-ui/react-context';\n\nconst PASSWORD_TOGGLE_FIELD_NAME = 'PasswordToggleField';\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldProvider\n * -----------------------------------------------------------------------------------------------*/\n\ntype InternalFocusState = {\n  clickTriggered: boolean;\n  selectionStart: number | null;\n  selectionEnd: number | null;\n};\n\ninterface PasswordToggleFieldContextValue {\n  inputId: string;\n  inputRef: React.RefObject<HTMLInputElement | null>;\n  visible: boolean;\n  setVisible: React.Dispatch<React.SetStateAction<boolean>>;\n  syncInputId: (providedId: string | number | undefined) => void;\n  focusState: React.RefObject<InternalFocusState>;\n}\n\nconst [createPasswordToggleFieldContext] = createContextScope(PASSWORD_TOGGLE_FIELD_NAME);\nconst [PasswordToggleFieldProvider, usePasswordToggleFieldContext] =\n  createPasswordToggleFieldContext<PasswordToggleFieldContextValue>(PASSWORD_TOGGLE_FIELD_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleField\n * -----------------------------------------------------------------------------------------------*/\n\ntype ScopedProps<P> = P & { __scopePasswordToggleField?: Scope };\n\ninterface PasswordToggleFieldProps {\n  id?: string;\n  visible?: boolean;\n  defaultVisible?: boolean;\n  onVisiblityChange?: (visible: boolean) => void;\n  children?: React.ReactNode;\n}\n\nconst INITIAL_FOCUS_STATE: InternalFocusState = {\n  clickTriggered: false,\n  selectionStart: null,\n  selectionEnd: null,\n};\n\nconst PasswordToggleField: React.FC<PasswordToggleFieldProps> = ({\n  __scopePasswordToggleField,\n  ...props\n}: ScopedProps<PasswordToggleFieldProps>) => {\n  const baseId = useId(props.id);\n  const defaultInputId = `${baseId}-input`;\n  const [inputIdState, setInputIdState] = React.useState<null | string>(defaultInputId);\n  const inputId = inputIdState ?? defaultInputId;\n  const syncInputId = React.useCallback(\n    (providedId: string | number | undefined) =>\n      setInputIdState(providedId != null ? String(providedId) : null),\n    []\n  );\n\n  const { visible: visibleProp, defaultVisible, onVisiblityChange, children } = props;\n  const [visible = false, setVisible] = useControllableState({\n    caller: PASSWORD_TOGGLE_FIELD_NAME,\n    prop: visibleProp,\n    defaultProp: defaultVisible ?? false,\n    onChange: onVisiblityChange,\n  });\n\n  const inputRef = React.useRef<HTMLInputElement | null>(null);\n  const focusState = React.useRef<InternalFocusState>(INITIAL_FOCUS_STATE);\n\n  return (\n    <PasswordToggleFieldProvider\n      scope={__scopePasswordToggleField}\n      inputId={inputId}\n      inputRef={inputRef}\n      setVisible={setVisible}\n      syncInputId={syncInputId}\n      visible={visible}\n      focusState={focusState}\n    >\n      {children}\n    </PasswordToggleFieldProvider>\n  );\n};\nPasswordToggleField.displayName = PASSWORD_TOGGLE_FIELD_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_INPUT_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Input';\n\ntype PrimitiveInputProps = React.ComponentPropsWithoutRef<'input'>;\n\ninterface PasswordToggleFieldOwnProps {\n  autoComplete?: 'current-password' | 'new-password';\n}\n\ninterface PasswordToggleFieldInputProps\n  extends PasswordToggleFieldOwnProps,\n    Omit<PrimitiveInputProps, keyof PasswordToggleFieldOwnProps | 'type'> {\n  autoComplete?: 'current-password' | 'new-password';\n}\n\nconst PasswordToggleFieldInput = React.forwardRef<HTMLInputElement, PasswordToggleFieldInputProps>(\n  (\n    {\n      __scopePasswordToggleField,\n      autoComplete = 'current-password',\n      autoCapitalize = 'off',\n      spellCheck = false,\n      id: idProp,\n      ...props\n    }: ScopedProps<PasswordToggleFieldInputProps>,\n    forwardedRef\n  ) => {\n    const { visible, inputRef, inputId, syncInputId, setVisible, focusState } =\n      usePasswordToggleFieldContext(PASSWORD_TOGGLE_FIELD_INPUT_NAME, __scopePasswordToggleField);\n\n    React.useEffect(() => {\n      syncInputId(idProp);\n    }, [idProp, syncInputId]);\n\n    // We want to reset the visibility to `false` to revert the input to\n    // `type=\"password\"` when:\n    // - The form is reset (for consistency with other form controls)\n    // - The form is submitted (to prevent the browser from remembering the\n    //   input's value.\n    //\n    // See \"Keeping things secure\":\n    //   https://technology.blog.gov.uk/2021/04/19/simple-things-are-complicated-making-a-show-password-option/)\n    const _setVisible = useEffectEvent(setVisible);\n    React.useEffect(() => {\n      const inputElement = inputRef.current;\n      const form = inputElement?.form;\n      if (!form) {\n        return;\n      }\n\n      const controller = new AbortController();\n      form.addEventListener(\n        'reset',\n        (event) => {\n          if (!event.defaultPrevented) {\n            _setVisible(false);\n          }\n        },\n        { signal: controller.signal }\n      );\n      form.addEventListener(\n        'submit',\n        () => {\n          // always reset the visibility on submit regardless of whether the\n          // default action is prevented\n          _setVisible(false);\n        },\n        { signal: controller.signal }\n      );\n      return () => {\n        controller.abort();\n      };\n    }, [inputRef, _setVisible]);\n\n    return (\n      <Primitive.input\n        {...props}\n        id={idProp ?? inputId}\n        autoCapitalize={autoCapitalize}\n        autoComplete={autoComplete}\n        ref={useComposedRefs(forwardedRef, inputRef)}\n        spellCheck={spellCheck}\n        type={visible ? 'text' : 'password'}\n        onBlur={composeEventHandlers(props.onBlur, (event) => {\n          // get the cursor position\n          const { selectionStart, selectionEnd } = event.currentTarget;\n          focusState.current.selectionStart = selectionStart;\n          focusState.current.selectionEnd = selectionEnd;\n        })}\n      />\n    );\n  }\n);\nPasswordToggleFieldInput.displayName = PASSWORD_TOGGLE_FIELD_INPUT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldToggle\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_TOGGLE_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Toggle';\n\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<'button'>;\n\ninterface PasswordToggleFieldToggleProps extends Omit<PrimitiveButtonProps, 'type'> {}\n\nconst PasswordToggleFieldToggle = React.forwardRef<\n  HTMLButtonElement,\n  PasswordToggleFieldToggleProps\n>(\n  (\n    {\n      __scopePasswordToggleField,\n      onClick,\n      onPointerDown,\n      onPointerCancel,\n      onPointerUp,\n      onFocus,\n      children,\n      'aria-label': ariaLabelProp,\n      'aria-controls': ariaControls,\n      'aria-hidden': ariaHidden,\n      tabIndex,\n      ...props\n    }: ScopedProps<PasswordToggleFieldToggleProps>,\n    forwardedRef\n  ) => {\n    const { setVisible, visible, inputRef, inputId, focusState } = usePasswordToggleFieldContext(\n      PASSWORD_TOGGLE_FIELD_TOGGLE_NAME,\n      __scopePasswordToggleField\n    );\n    const [internalAriaLabel, setInternalAriaLabel] = React.useState<string | undefined>(undefined);\n    const elementRef = React.useRef<HTMLButtonElement>(null);\n    const ref = useComposedRefs(forwardedRef, elementRef);\n    const isHydrated = useIsHydrated();\n\n    React.useEffect(() => {\n      const element = elementRef.current;\n      if (!element || ariaLabelProp) {\n        setInternalAriaLabel(undefined);\n        return;\n      }\n\n      const DEFAULT_ARIA_LABEL = visible ? 'Hide password' : 'Show password';\n\n      function checkForInnerTextLabel(textContent: string | undefined | null) {\n        const text = textContent ? textContent : undefined;\n        // If the element has inner text, no need to force an aria-label.\n        setInternalAriaLabel(text ? undefined : DEFAULT_ARIA_LABEL);\n      }\n\n      checkForInnerTextLabel(element.textContent);\n\n      const observer = new MutationObserver((entries) => {\n        let textContent: string | undefined;\n        for (const entry of entries) {\n          if (entry.type === 'characterData') {\n            if (element.textContent) {\n              textContent = element.textContent;\n            }\n          }\n        }\n        checkForInnerTextLabel(textContent);\n      });\n      observer.observe(element, { characterData: true, subtree: true });\n      return () => {\n        observer.disconnect();\n      };\n    }, [visible, ariaLabelProp]);\n\n    const ariaLabel = ariaLabelProp || internalAriaLabel;\n\n    // Before hydration the button will not work, but we want to render it\n    // regardless to prevent potential layout shift. Hide it from assistive tech\n    // by default. Post-hydration it will be visible, focusable and associated\n    // with the input via aria-controls.\n    if (!isHydrated) {\n      ariaHidden ??= true;\n      tabIndex ??= -1;\n    } else {\n      ariaControls ??= inputId;\n    }\n\n    React.useEffect(() => {\n      let cleanup = () => {};\n      const ownerWindow = elementRef.current?.ownerDocument?.defaultView || window;\n      const reset = () => (focusState.current.clickTriggered = false);\n      const handlePointerUp = () => (cleanup = requestIdleCallback(ownerWindow, reset));\n      ownerWindow.addEventListener('pointerup', handlePointerUp);\n      return () => {\n        cleanup();\n        ownerWindow.removeEventListener('pointerup', handlePointerUp);\n      };\n    }, [focusState]);\n\n    return (\n      <Primitive.button\n        aria-controls={ariaControls}\n        aria-hidden={ariaHidden}\n        aria-label={ariaLabel}\n        ref={ref}\n        id={inputId}\n        {...props}\n        onPointerDown={composeEventHandlers(onPointerDown, () => {\n          focusState.current.clickTriggered = true;\n        })}\n        onPointerCancel={(event) => {\n          // do not use `composeEventHandlers` here because we always want to\n          // reset the ref on cancellation, regardless of whether the user has\n          // called preventDefault on the event\n          onPointerCancel?.(event);\n          focusState.current = INITIAL_FOCUS_STATE;\n        }}\n        // do not use `composeEventHandlers` here because we always want to\n        // reset the ref after click, regardless of whether the user has\n        // called preventDefault on the event\n        onClick={(event) => {\n          onClick?.(event);\n          if (event.defaultPrevented) {\n            focusState.current = INITIAL_FOCUS_STATE;\n            return;\n          }\n\n          flushSync(() => {\n            setVisible((s) => !s);\n          });\n          if (focusState.current.clickTriggered) {\n            const input = inputRef.current;\n            if (input) {\n              const { selectionStart, selectionEnd } = focusState.current;\n              input.focus();\n              if (selectionStart !== null || selectionEnd !== null) {\n                // wait a tick so that focus has settled, then restore select position\n                requestAnimationFrame(() => {\n                  // make sure the input still has focus (developer may have\n                  // programatically moved focus elsewhere)\n                  if (input.ownerDocument.activeElement === input) {\n                    input.selectionStart = selectionStart;\n                    input.selectionEnd = selectionEnd;\n                  }\n                });\n              }\n            }\n          }\n          focusState.current = INITIAL_FOCUS_STATE;\n        }}\n        onPointerUp={(event) => {\n          onPointerUp?.(event);\n          // if click handler hasn't been called at this point, it may have been\n          // intercepted, in which case we still want to reset our internal\n          // state\n          setTimeout(() => {\n            focusState.current = INITIAL_FOCUS_STATE;\n          }, 50);\n        }}\n        type=\"button\"\n      >\n        {children}\n      </Primitive.button>\n    );\n  }\n);\nPasswordToggleFieldToggle.displayName = PASSWORD_TOGGLE_FIELD_TOGGLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldSlot\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_SLOT_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Slot';\n\ninterface PasswordToggleFieldSlotDeclarativeProps {\n  visible: React.ReactNode;\n  hidden: React.ReactNode;\n}\n\ninterface PasswordToggleFieldSlotRenderProps {\n  render: (args: { visible: boolean }) => React.ReactElement;\n}\n\ntype PasswordToggleFieldSlotProps =\n  | PasswordToggleFieldSlotDeclarativeProps\n  | PasswordToggleFieldSlotRenderProps;\n\nconst PasswordToggleFieldSlot: React.FC<PasswordToggleFieldSlotProps> = ({\n  __scopePasswordToggleField,\n  ...props\n}: ScopedProps<PasswordToggleFieldSlotProps>) => {\n  const { visible } = usePasswordToggleFieldContext(\n    PASSWORD_TOGGLE_FIELD_SLOT_NAME,\n    __scopePasswordToggleField\n  );\n\n  return 'render' in props\n    ? //\n      props.render({ visible })\n    : visible\n      ? props.visible\n      : props.hidden;\n};\nPasswordToggleFieldSlot.displayName = PASSWORD_TOGGLE_FIELD_SLOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordToggleFieldIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst PASSWORD_TOGGLE_FIELD_ICON_NAME = PASSWORD_TOGGLE_FIELD_NAME + 'Icon';\n\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<'svg'>;\n\ninterface PasswordToggleFieldIconProps extends Omit<PrimitiveSvgProps, 'children'> {\n  visible: React.ReactElement;\n  hidden: React.ReactElement;\n}\n\nconst PasswordToggleFieldIcon = React.forwardRef<SVGSVGElement, PasswordToggleFieldIconProps>(\n  (\n    {\n      __scopePasswordToggleField,\n      // @ts-expect-error\n      children,\n      ...props\n    }: ScopedProps<PasswordToggleFieldIconProps>,\n    forwardedRef\n  ) => {\n    const { visible } = usePasswordToggleFieldContext(\n      PASSWORD_TOGGLE_FIELD_ICON_NAME,\n      __scopePasswordToggleField\n    );\n    const { visible: visibleIcon, hidden: hiddenIcon, ...domProps } = props;\n    return (\n      <Primitive.svg {...domProps} ref={forwardedRef} aria-hidden asChild>\n        {visible ? visibleIcon : hiddenIcon}\n      </Primitive.svg>\n    );\n  }\n);\nPasswordToggleFieldIcon.displayName = PASSWORD_TOGGLE_FIELD_ICON_NAME;\n\nexport {\n  PasswordToggleField,\n  PasswordToggleFieldInput,\n  PasswordToggleFieldToggle,\n  PasswordToggleFieldSlot,\n  PasswordToggleFieldIcon,\n  //\n  PasswordToggleField as Root,\n  PasswordToggleFieldInput as Input,\n  PasswordToggleFieldToggle as Toggle,\n  PasswordToggleFieldSlot as Slot,\n  PasswordToggleFieldIcon as Icon,\n};\nexport type {\n  PasswordToggleFieldProps,\n  PasswordToggleFieldInputProps,\n  PasswordToggleFieldToggleProps,\n  PasswordToggleFieldIconProps,\n  PasswordToggleFieldSlotProps,\n};\n\nfunction requestIdleCallback(\n  window: Window,\n  callback: IdleRequestCallback,\n  options?: IdleRequestOptions\n): () => void {\n  if ((window as any).requestIdleCallback) {\n    const id = window.requestIdleCallback(callback, options);\n    return () => {\n      window.cancelIdleCallback(id);\n    };\n  }\n  const start = Date.now();\n  const id = window.setTimeout(() => {\n    const timeRemaining = () => Math.max(0, 50 - (Date.now() - start));\n    callback({ didTimeout: false, timeRemaining });\n  }, 1);\n  return () => {\n    window.clearTimeout(id);\n  };\n}\n", "import * as React from 'react';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createCollection } from '@radix-ui/react-collection';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst PAGE_KEYS = ['PageUp', 'PageDown'];\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\ntype SlideDirection = 'from-left' | 'from-right' | 'from-bottom' | 'from-top';\nconst BACK_KEYS: Record<SlideDirection, string[]> = {\n  'from-left': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-right': ['Home', 'PageDown', 'ArrowDown', 'ArrowRight'],\n  'from-bottom': ['Home', 'PageDown', 'ArrowDown', 'ArrowLeft'],\n  'from-top': ['Home', 'PageDown', 'ArrowUp', 'ArrowLeft'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Slider\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLIDER_NAME = 'Slider';\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<SliderThumbElement>(SLIDER_NAME);\n\ntype ScopedProps<P> = P & { __scopeSlider?: Scope };\nconst [createSliderContext, createSliderScope] = createContextScope(SLIDER_NAME, [\n  createCollectionScope,\n]);\n\ntype SliderContextValue = {\n  name: string | undefined;\n  disabled: boolean | undefined;\n  min: number;\n  max: number;\n  values: number[];\n  valueIndexToChangeRef: React.MutableRefObject<number>;\n  thumbs: Set<SliderThumbElement>;\n  orientation: SliderProps['orientation'];\n  form: string | undefined;\n};\n\nconst [SliderProvider, useSliderContext] = createSliderContext<SliderContextValue>(SLIDER_NAME);\n\ntype SliderElement = SliderHorizontalElement | SliderVerticalElement;\ninterface SliderProps\n  extends Omit<\n    SliderHorizontalProps | SliderVerticalProps,\n    keyof SliderOrientationPrivateProps | 'defaultValue'\n  > {\n  name?: string;\n  disabled?: boolean;\n  orientation?: React.AriaAttributes['aria-orientation'];\n  dir?: Direction;\n  min?: number;\n  max?: number;\n  step?: number;\n  minStepsBetweenThumbs?: number;\n  value?: number[];\n  defaultValue?: number[];\n  onValueChange?(value: number[]): void;\n  onValueCommit?(value: number[]): void;\n  inverted?: boolean;\n  form?: string;\n}\n\nconst Slider = React.forwardRef<SliderElement, SliderProps>(\n  (props: ScopedProps<SliderProps>, forwardedRef) => {\n    const {\n      name,\n      min = 0,\n      max = 100,\n      step = 1,\n      orientation = 'horizontal',\n      disabled = false,\n      minStepsBetweenThumbs = 0,\n      defaultValue = [min],\n      value,\n      onValueChange = () => {},\n      onValueCommit = () => {},\n      inverted = false,\n      form,\n      ...sliderProps\n    } = props;\n    const thumbRefs = React.useRef<SliderContextValue['thumbs']>(new Set());\n    const valueIndexToChangeRef = React.useRef<number>(0);\n    const isHorizontal = orientation === 'horizontal';\n    const SliderOrientation = isHorizontal ? SliderHorizontal : SliderVertical;\n\n    const [values = [], setValues] = useControllableState({\n      prop: value,\n      defaultProp: defaultValue,\n      onChange: (value) => {\n        const thumbs = [...thumbRefs.current];\n        thumbs[valueIndexToChangeRef.current]?.focus();\n        onValueChange(value);\n      },\n    });\n    const valuesBeforeSlideStartRef = React.useRef(values);\n\n    function handleSlideStart(value: number) {\n      const closestIndex = getClosestValueIndex(values, value);\n      updateValues(value, closestIndex);\n    }\n\n    function handleSlideMove(value: number) {\n      updateValues(value, valueIndexToChangeRef.current);\n    }\n\n    function handleSlideEnd() {\n      const prevValue = valuesBeforeSlideStartRef.current[valueIndexToChangeRef.current];\n      const nextValue = values[valueIndexToChangeRef.current];\n      const hasChanged = nextValue !== prevValue;\n      if (hasChanged) onValueCommit(values);\n    }\n\n    function updateValues(value: number, atIndex: number, { commit } = { commit: false }) {\n      const decimalCount = getDecimalCount(step);\n      const snapToStep = roundValue(Math.round((value - min) / step) * step + min, decimalCount);\n      const nextValue = clamp(snapToStep, [min, max]);\n\n      setValues((prevValues = []) => {\n        const nextValues = getNextSortedValues(prevValues, nextValue, atIndex);\n        if (hasMinStepsBetweenValues(nextValues, minStepsBetweenThumbs * step)) {\n          valueIndexToChangeRef.current = nextValues.indexOf(nextValue);\n          const hasChanged = String(nextValues) !== String(prevValues);\n          if (hasChanged && commit) onValueCommit(nextValues);\n          return hasChanged ? nextValues : prevValues;\n        } else {\n          return prevValues;\n        }\n      });\n    }\n\n    return (\n      <SliderProvider\n        scope={props.__scopeSlider}\n        name={name}\n        disabled={disabled}\n        min={min}\n        max={max}\n        valueIndexToChangeRef={valueIndexToChangeRef}\n        thumbs={thumbRefs.current}\n        values={values}\n        orientation={orientation}\n        form={form}\n      >\n        <Collection.Provider scope={props.__scopeSlider}>\n          <Collection.Slot scope={props.__scopeSlider}>\n            <SliderOrientation\n              aria-disabled={disabled}\n              data-disabled={disabled ? '' : undefined}\n              {...sliderProps}\n              ref={forwardedRef}\n              onPointerDown={composeEventHandlers(sliderProps.onPointerDown, () => {\n                if (!disabled) valuesBeforeSlideStartRef.current = values;\n              })}\n              min={min}\n              max={max}\n              inverted={inverted}\n              onSlideStart={disabled ? undefined : handleSlideStart}\n              onSlideMove={disabled ? undefined : handleSlideMove}\n              onSlideEnd={disabled ? undefined : handleSlideEnd}\n              onHomeKeyDown={() => !disabled && updateValues(min, 0, { commit: true })}\n              onEndKeyDown={() =>\n                !disabled && updateValues(max, values.length - 1, { commit: true })\n              }\n              onStepKeyDown={({ event, direction: stepDirection }) => {\n                if (!disabled) {\n                  const isPageKey = PAGE_KEYS.includes(event.key);\n                  const isSkipKey = isPageKey || (event.shiftKey && ARROW_KEYS.includes(event.key));\n                  const multiplier = isSkipKey ? 10 : 1;\n                  const atIndex = valueIndexToChangeRef.current;\n                  const value = values[atIndex]!;\n                  const stepInDirection = step * multiplier * stepDirection;\n                  updateValues(value + stepInDirection, atIndex, { commit: true });\n                }\n              }}\n            />\n          </Collection.Slot>\n        </Collection.Provider>\n      </SliderProvider>\n    );\n  }\n);\n\nSlider.displayName = SLIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderHorizontal\n * -----------------------------------------------------------------------------------------------*/\n\ntype Side = 'top' | 'right' | 'bottom' | 'left';\n\nconst [SliderOrientationProvider, useSliderOrientationContext] = createSliderContext<{\n  startEdge: Side;\n  endEdge: Side;\n  size: keyof NonNullable<ReturnType<typeof useSize>>;\n  direction: number;\n}>(SLIDER_NAME, {\n  startEdge: 'left',\n  endEdge: 'right',\n  size: 'width',\n  direction: 1,\n});\n\ntype SliderOrientationPrivateProps = {\n  min: number;\n  max: number;\n  inverted: boolean;\n  onSlideStart?(value: number): void;\n  onSlideMove?(value: number): void;\n  onSlideEnd?(): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(step: { event: React.KeyboardEvent; direction: number }): void;\n};\ninterface SliderOrientationProps\n  extends Omit<SliderImplProps, keyof SliderImplPrivateProps>,\n    SliderOrientationPrivateProps {}\n\ntype SliderHorizontalElement = SliderImplElement;\ninterface SliderHorizontalProps extends SliderOrientationProps {\n  dir?: Direction;\n}\n\nconst SliderHorizontal = React.forwardRef<SliderHorizontalElement, SliderHorizontalProps>(\n  (props: ScopedProps<SliderHorizontalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      dir,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const [slider, setSlider] = React.useState<SliderImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setSlider(node));\n    const rectRef = React.useRef<DOMRect>(undefined);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n    const isSlidingFromLeft = (isDirectionLTR && !inverted) || (!isDirectionLTR && inverted);\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || slider!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.width];\n      const output: [number, number] = isSlidingFromLeft ? [min, max] : [max, min];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.left);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromLeft ? 'left' : 'right'}\n        endEdge={isSlidingFromLeft ? 'right' : 'left'}\n        direction={isSlidingFromLeft ? 1 : -1}\n        size=\"width\"\n      >\n        <SliderImpl\n          dir={direction}\n          data-orientation=\"horizontal\"\n          {...sliderProps}\n          ref={composedRefs}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateX(-50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientX);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromLeft ? 'from-left' : 'from-right';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderVertical\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderVerticalElement = SliderImplElement;\ninterface SliderVerticalProps extends SliderOrientationProps {}\n\nconst SliderVertical = React.forwardRef<SliderVerticalElement, SliderVerticalProps>(\n  (props: ScopedProps<SliderVerticalProps>, forwardedRef) => {\n    const {\n      min,\n      max,\n      inverted,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const sliderRef = React.useRef<SliderImplElement>(null);\n    const ref = useComposedRefs(forwardedRef, sliderRef);\n    const rectRef = React.useRef<DOMRect>(undefined);\n    const isSlidingFromBottom = !inverted;\n\n    function getValueFromPointer(pointerPosition: number) {\n      const rect = rectRef.current || sliderRef.current!.getBoundingClientRect();\n      const input: [number, number] = [0, rect.height];\n      const output: [number, number] = isSlidingFromBottom ? [max, min] : [min, max];\n      const value = linearScale(input, output);\n\n      rectRef.current = rect;\n      return value(pointerPosition - rect.top);\n    }\n\n    return (\n      <SliderOrientationProvider\n        scope={props.__scopeSlider}\n        startEdge={isSlidingFromBottom ? 'bottom' : 'top'}\n        endEdge={isSlidingFromBottom ? 'top' : 'bottom'}\n        size=\"height\"\n        direction={isSlidingFromBottom ? 1 : -1}\n      >\n        <SliderImpl\n          data-orientation=\"vertical\"\n          {...sliderProps}\n          ref={ref}\n          style={{\n            ...sliderProps.style,\n            ['--radix-slider-thumb-transform' as any]: 'translateY(50%)',\n          }}\n          onSlideStart={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideStart?.(value);\n          }}\n          onSlideMove={(event) => {\n            const value = getValueFromPointer(event.clientY);\n            onSlideMove?.(value);\n          }}\n          onSlideEnd={() => {\n            rectRef.current = undefined;\n            onSlideEnd?.();\n          }}\n          onStepKeyDown={(event) => {\n            const slideDirection = isSlidingFromBottom ? 'from-bottom' : 'from-top';\n            const isBackKey = BACK_KEYS[slideDirection].includes(event.key);\n            onStepKeyDown?.({ event, direction: isBackKey ? -1 : 1 });\n          }}\n        />\n      </SliderOrientationProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderImpl\n * -----------------------------------------------------------------------------------------------*/\n\ntype SliderImplElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ntype SliderImplPrivateProps = {\n  onSlideStart(event: React.PointerEvent): void;\n  onSlideMove(event: React.PointerEvent): void;\n  onSlideEnd(event: React.PointerEvent): void;\n  onHomeKeyDown(event: React.KeyboardEvent): void;\n  onEndKeyDown(event: React.KeyboardEvent): void;\n  onStepKeyDown(event: React.KeyboardEvent): void;\n};\ninterface SliderImplProps extends PrimitiveDivProps, SliderImplPrivateProps {}\n\nconst SliderImpl = React.forwardRef<SliderImplElement, SliderImplProps>(\n  (props: ScopedProps<SliderImplProps>, forwardedRef) => {\n    const {\n      __scopeSlider,\n      onSlideStart,\n      onSlideMove,\n      onSlideEnd,\n      onHomeKeyDown,\n      onEndKeyDown,\n      onStepKeyDown,\n      ...sliderProps\n    } = props;\n    const context = useSliderContext(SLIDER_NAME, __scopeSlider);\n\n    return (\n      <Primitive.span\n        {...sliderProps}\n        ref={forwardedRef}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          if (event.key === 'Home') {\n            onHomeKeyDown(event);\n            // Prevent scrolling to page start\n            event.preventDefault();\n          } else if (event.key === 'End') {\n            onEndKeyDown(event);\n            // Prevent scrolling to page end\n            event.preventDefault();\n          } else if (PAGE_KEYS.concat(ARROW_KEYS).includes(event.key)) {\n            onStepKeyDown(event);\n            // Prevent scrolling for directional key presses\n            event.preventDefault();\n          }\n        })}\n        onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n          const target = event.target as HTMLElement;\n          target.setPointerCapture(event.pointerId);\n          // Prevent browser focus behaviour because we focus a thumb manually when values change.\n          event.preventDefault();\n          // Touch devices have a delay before focusing so won't focus if touch immediately moves\n          // away from target (sliding). We want thumb to focus regardless.\n          if (context.thumbs.has(target)) {\n            target.focus();\n          } else {\n            onSlideStart(event);\n          }\n        })}\n        onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) onSlideMove(event);\n        })}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          const target = event.target as HTMLElement;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n            onSlideEnd(event);\n          }\n        })}\n      />\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * SliderTrack\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRACK_NAME = 'SliderTrack';\n\ntype SliderTrackElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SliderTrackProps extends PrimitiveSpanProps {}\n\nconst SliderTrack = React.forwardRef<SliderTrackElement, SliderTrackProps>(\n  (props: ScopedProps<SliderTrackProps>, forwardedRef) => {\n    const { __scopeSlider, ...trackProps } = props;\n    const context = useSliderContext(TRACK_NAME, __scopeSlider);\n    return (\n      <Primitive.span\n        data-disabled={context.disabled ? '' : undefined}\n        data-orientation={context.orientation}\n        {...trackProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSliderTrack.displayName = TRACK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderRange\n * -----------------------------------------------------------------------------------------------*/\n\nconst RANGE_NAME = 'SliderRange';\n\ntype SliderRangeElement = React.ComponentRef<typeof Primitive.span>;\ninterface SliderRangeProps extends PrimitiveSpanProps {}\n\nconst SliderRange = React.forwardRef<SliderRangeElement, SliderRangeProps>(\n  (props: ScopedProps<SliderRangeProps>, forwardedRef) => {\n    const { __scopeSlider, ...rangeProps } = props;\n    const context = useSliderContext(RANGE_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(RANGE_NAME, __scopeSlider);\n    const ref = React.useRef<HTMLSpanElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const valuesCount = context.values.length;\n    const percentages = context.values.map((value) =>\n      convertValueToPercentage(value, context.min, context.max)\n    );\n    const offsetStart = valuesCount > 1 ? Math.min(...percentages) : 0;\n    const offsetEnd = 100 - Math.max(...percentages);\n\n    return (\n      <Primitive.span\n        data-orientation={context.orientation}\n        data-disabled={context.disabled ? '' : undefined}\n        {...rangeProps}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          [orientation.startEdge]: offsetStart + '%',\n          [orientation.endEdge]: offsetEnd + '%',\n        }}\n      />\n    );\n  }\n);\n\nSliderRange.displayName = RANGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SliderThumb';\n\ntype SliderThumbElement = SliderThumbImplElement;\ninterface SliderThumbProps extends Omit<SliderThumbImplProps, 'index'> {}\n\nconst SliderThumb = React.forwardRef<SliderThumbElement, SliderThumbProps>(\n  (props: ScopedProps<SliderThumbProps>, forwardedRef) => {\n    const getItems = useCollection(props.__scopeSlider);\n    const [thumb, setThumb] = React.useState<SliderThumbImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    const index = React.useMemo(\n      () => (thumb ? getItems().findIndex((item) => item.ref.current === thumb) : -1),\n      [getItems, thumb]\n    );\n    return <SliderThumbImpl {...props} ref={composedRefs} index={index} />;\n  }\n);\n\ntype SliderThumbImplElement = React.ComponentRef<typeof Primitive.span>;\ninterface SliderThumbImplProps extends PrimitiveSpanProps {\n  index: number;\n  name?: string;\n}\n\nconst SliderThumbImpl = React.forwardRef<SliderThumbImplElement, SliderThumbImplProps>(\n  (props: ScopedProps<SliderThumbImplProps>, forwardedRef) => {\n    const { __scopeSlider, index, name, ...thumbProps } = props;\n    const context = useSliderContext(THUMB_NAME, __scopeSlider);\n    const orientation = useSliderOrientationContext(THUMB_NAME, __scopeSlider);\n    const [thumb, setThumb] = React.useState<HTMLSpanElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setThumb(node));\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = thumb ? context.form || !!thumb.closest('form') : true;\n    const size = useSize(thumb);\n    // We cast because index could be `-1` which would return undefined\n    const value = context.values[index] as number | undefined;\n    const percent =\n      value === undefined ? 0 : convertValueToPercentage(value, context.min, context.max);\n    const label = getLabel(index, context.values.length);\n    const orientationSize = size?.[orientation.size];\n    const thumbInBoundsOffset = orientationSize\n      ? getThumbInBoundsOffset(orientationSize, percent, orientation.direction)\n      : 0;\n\n    React.useEffect(() => {\n      if (thumb) {\n        context.thumbs.add(thumb);\n        return () => {\n          context.thumbs.delete(thumb);\n        };\n      }\n    }, [thumb, context.thumbs]);\n\n    return (\n      <span\n        style={{\n          transform: 'var(--radix-slider-thumb-transform)',\n          position: 'absolute',\n          [orientation.startEdge]: `calc(${percent}% + ${thumbInBoundsOffset}px)`,\n        }}\n      >\n        <Collection.ItemSlot scope={props.__scopeSlider}>\n          <Primitive.span\n            role=\"slider\"\n            aria-label={props['aria-label'] || label}\n            aria-valuemin={context.min}\n            aria-valuenow={value}\n            aria-valuemax={context.max}\n            aria-orientation={context.orientation}\n            data-orientation={context.orientation}\n            data-disabled={context.disabled ? '' : undefined}\n            tabIndex={context.disabled ? undefined : 0}\n            {...thumbProps}\n            ref={composedRefs}\n            /**\n             * There will be no value on initial render while we work out the index so we hide thumbs\n             * without a value, otherwise SSR will render them in the wrong position before they\n             * snap into the correct position during hydration which would be visually jarring for\n             * slower connections.\n             */\n            style={value === undefined ? { display: 'none' } : props.style}\n            onFocus={composeEventHandlers(props.onFocus, () => {\n              context.valueIndexToChangeRef.current = index;\n            })}\n          />\n        </Collection.ItemSlot>\n\n        {isFormControl && (\n          <SliderBubbleInput\n            key={index}\n            name={\n              name ??\n              (context.name ? context.name + (context.values.length > 1 ? '[]' : '') : undefined)\n            }\n            form={context.form}\n            value={value}\n          />\n        )}\n      </span>\n    );\n  }\n);\n\nSliderThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SliderBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'RadioBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface SliderBubbleInputProps extends InputProps {}\n\nconst SliderBubbleInput = React.forwardRef<HTMLInputElement, SliderBubbleInputProps>(\n  ({ __scopeSlider, value, ...props }: ScopedProps<SliderBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'value') as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('input', { bubbles: true });\n        setValue.call(input, value);\n        input.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully do not use `type=\"hidden\"` here otherwise forms that\n     * wrap it will not be able to access its value via the FormData API.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange` event.\n     * Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     */\n    return (\n      <Primitive.input\n        style={{ display: 'none' }}\n        {...props}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSliderBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getNextSortedValues(prevValues: number[] = [], nextValue: number, atIndex: number) {\n  const nextValues = [...prevValues];\n  nextValues[atIndex] = nextValue;\n  return nextValues.sort((a, b) => a - b);\n}\n\nfunction convertValueToPercentage(value: number, min: number, max: number) {\n  const maxSteps = max - min;\n  const percentPerStep = 100 / maxSteps;\n  const percentage = percentPerStep * (value - min);\n  return clamp(percentage, [0, 100]);\n}\n\n/**\n * Returns a label for each thumb when there are two or more thumbs\n */\nfunction getLabel(index: number, totalValues: number) {\n  if (totalValues > 2) {\n    return `Value ${index + 1} of ${totalValues}`;\n  } else if (totalValues === 2) {\n    return ['Minimum', 'Maximum'][index];\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Given a `values` array and a `nextValue`, determine which value in\n * the array is closest to `nextValue` and return its index.\n *\n * @example\n * // returns 1\n * getClosestValueIndex([10, 30], 25);\n */\nfunction getClosestValueIndex(values: number[], nextValue: number) {\n  if (values.length === 1) return 0;\n  const distances = values.map((value) => Math.abs(value - nextValue));\n  const closestDistance = Math.min(...distances);\n  return distances.indexOf(closestDistance);\n}\n\n/**\n * Offsets the thumb centre point while sliding to ensure it remains\n * within the bounds of the slider when reaching the edges\n */\nfunction getThumbInBoundsOffset(width: number, left: number, direction: number) {\n  const halfWidth = width / 2;\n  const halfPercent = 50;\n  const offset = linearScale([0, halfPercent], [0, halfWidth]);\n  return (halfWidth - offset(left) * direction) * direction;\n}\n\n/**\n * Gets an array of steps between each value.\n *\n * @example\n * // returns [1, 9]\n * getStepsBetweenValues([10, 11, 20]);\n */\nfunction getStepsBetweenValues(values: number[]) {\n  return values.slice(0, -1).map((value, index) => values[index + 1]! - value);\n}\n\n/**\n * Verifies the minimum steps between all values is greater than or equal\n * to the expected minimum steps.\n *\n * @example\n * // returns false\n * hasMinStepsBetweenValues([1,2,3], 2);\n *\n * @example\n * // returns true\n * hasMinStepsBetweenValues([1,2,3], 1);\n */\nfunction hasMinStepsBetweenValues(values: number[], minStepsBetweenValues: number) {\n  if (minStepsBetweenValues > 0) {\n    const stepsBetweenValues = getStepsBetweenValues(values);\n    const actualMinStepsBetweenValues = Math.min(...stepsBetweenValues);\n    return actualMinStepsBetweenValues >= minStepsBetweenValues;\n  }\n  return true;\n}\n\n// https://github.com/tmcw-up-for-adoption/simple-linear-scale/blob/master/index.js\nfunction linearScale(input: readonly [number, number], output: readonly [number, number]) {\n  return (value: number) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\n\nfunction getDecimalCount(value: number) {\n  return (String(value).split('.')[1] || '').length;\n}\n\nfunction roundValue(value: number, decimalCount: number) {\n  const rounder = Math.pow(10, decimalCount);\n  return Math.round(value * rounder) / rounder;\n}\n\nconst Root = Slider;\nconst Track = SliderTrack;\nconst Range = SliderRange;\nconst Thumb = SliderThumb;\n\nexport {\n  createSliderScope,\n  //\n  Slider,\n  SliderTrack,\n  SliderRange,\n  SliderThumb,\n  //\n  Root,\n  Track,\n  Range,\n  Thumb,\n};\nexport type { SliderProps, SliderTrackProps, SliderRangeProps, SliderThumbProps };\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { createContextScope } from '@radix-ui/react-context';\nimport * as DismissableLayer from '@radix-ui/react-dismissable-layer';\nimport { Portal } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { VisuallyHidden } from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToastProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'ToastProvider';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<ToastElement>('Toast');\n\ntype SwipeDirection = 'up' | 'down' | 'left' | 'right';\ntype ToastProviderContextValue = {\n  label: string;\n  duration: number;\n  swipeDirection: SwipeDirection;\n  swipeThreshold: number;\n  toastCount: number;\n  viewport: ToastViewportElement | null;\n  onViewportChange(viewport: ToastViewportElement): void;\n  onToastAdd(): void;\n  onToastRemove(): void;\n  isFocusedToastEscapeKeyDownRef: React.MutableRefObject<boolean>;\n  isClosePausedRef: React.MutableRefObject<boolean>;\n};\n\ntype ScopedProps<P> = P & { __scopeToast?: Scope };\nconst [createToastContext, createToastScope] = createContextScope('Toast', [createCollectionScope]);\nconst [ToastProviderProvider, useToastProviderContext] =\n  createToastContext<ToastProviderContextValue>(PROVIDER_NAME);\n\ninterface ToastProviderProps {\n  children?: React.ReactNode;\n  /**\n   * An author-localized label for each toast. Used to help screen reader users\n   * associate the interruption with a toast.\n   * @defaultValue 'Notification'\n   */\n  label?: string;\n  /**\n   * Time in milliseconds that each toast should remain visible for.\n   * @defaultValue 5000\n   */\n  duration?: number;\n  /**\n   * Direction of pointer swipe that should close the toast.\n   * @defaultValue 'right'\n   */\n  swipeDirection?: SwipeDirection;\n  /**\n   * Distance in pixels that the swipe must pass before a close is triggered.\n   * @defaultValue 50\n   */\n  swipeThreshold?: number;\n}\n\nconst ToastProvider: React.FC<ToastProviderProps> = (props: ScopedProps<ToastProviderProps>) => {\n  const {\n    __scopeToast,\n    label = 'Notification',\n    duration = 5000,\n    swipeDirection = 'right',\n    swipeThreshold = 50,\n    children,\n  } = props;\n  const [viewport, setViewport] = React.useState<ToastViewportElement | null>(null);\n  const [toastCount, setToastCount] = React.useState(0);\n  const isFocusedToastEscapeKeyDownRef = React.useRef(false);\n  const isClosePausedRef = React.useRef(false);\n\n  if (!label.trim()) {\n    console.error(\n      `Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`\n    );\n  }\n\n  return (\n    <Collection.Provider scope={__scopeToast}>\n      <ToastProviderProvider\n        scope={__scopeToast}\n        label={label}\n        duration={duration}\n        swipeDirection={swipeDirection}\n        swipeThreshold={swipeThreshold}\n        toastCount={toastCount}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        onToastAdd={React.useCallback(() => setToastCount((prevCount) => prevCount + 1), [])}\n        onToastRemove={React.useCallback(() => setToastCount((prevCount) => prevCount - 1), [])}\n        isFocusedToastEscapeKeyDownRef={isFocusedToastEscapeKeyDownRef}\n        isClosePausedRef={isClosePausedRef}\n      >\n        {children}\n      </ToastProviderProvider>\n    </Collection.Provider>\n  );\n};\n\nToastProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'ToastViewport';\nconst VIEWPORT_DEFAULT_HOTKEY = ['F8'];\nconst VIEWPORT_PAUSE = 'toast.viewportPause';\nconst VIEWPORT_RESUME = 'toast.viewportResume';\n\ntype ToastViewportElement = React.ComponentRef<typeof Primitive.ol>;\ntype PrimitiveOrderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ol>;\ninterface ToastViewportProps extends PrimitiveOrderedListProps {\n  /**\n   * The keys to use as the keyboard shortcut that will move focus to the toast viewport.\n   * @defaultValue ['F8']\n   */\n  hotkey?: string[];\n  /**\n   * An author-localized label for the toast viewport to provide context for screen reader users\n   * when navigating page landmarks. The available `{hotkey}` placeholder will be replaced for you.\n   * @defaultValue 'Notifications ({hotkey})'\n   */\n  label?: string;\n}\n\nconst ToastViewport = React.forwardRef<ToastViewportElement, ToastViewportProps>(\n  (props: ScopedProps<ToastViewportProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      hotkey = VIEWPORT_DEFAULT_HOTKEY,\n      label = 'Notifications ({hotkey})',\n      ...viewportProps\n    } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = React.useRef<HTMLDivElement>(null);\n    const headFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const tailFocusProxyRef = React.useRef<FocusProxyElement>(null);\n    const ref = React.useRef<ToastViewportElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const hasToasts = context.toastCount > 0;\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        // we use `event.code` as it is consistent regardless of meta keys that were pressed.\n        // for example, `event.key` for `Control+Alt+t` is `†` and `t !== †`\n        const isHotkeyPressed =\n          hotkey.length !== 0 && hotkey.every((key) => (event as any)[key] || event.code === key);\n        if (isHotkeyPressed) ref.current?.focus();\n      };\n      document.addEventListener('keydown', handleKeyDown);\n      return () => document.removeEventListener('keydown', handleKeyDown);\n    }, [hotkey]);\n\n    React.useEffect(() => {\n      const wrapper = wrapperRef.current;\n      const viewport = ref.current;\n      if (hasToasts && wrapper && viewport) {\n        const handlePause = () => {\n          if (!context.isClosePausedRef.current) {\n            const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n            viewport.dispatchEvent(pauseEvent);\n            context.isClosePausedRef.current = true;\n          }\n        };\n\n        const handleResume = () => {\n          if (context.isClosePausedRef.current) {\n            const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n            viewport.dispatchEvent(resumeEvent);\n            context.isClosePausedRef.current = false;\n          }\n        };\n\n        const handleFocusOutResume = (event: FocusEvent) => {\n          const isFocusMovingOutside = !wrapper.contains(event.relatedTarget as HTMLElement);\n          if (isFocusMovingOutside) handleResume();\n        };\n\n        const handlePointerLeaveResume = () => {\n          const isFocusInside = wrapper.contains(document.activeElement);\n          if (!isFocusInside) handleResume();\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        wrapper.addEventListener('focusin', handlePause);\n        wrapper.addEventListener('focusout', handleFocusOutResume);\n        wrapper.addEventListener('pointermove', handlePause);\n        wrapper.addEventListener('pointerleave', handlePointerLeaveResume);\n        window.addEventListener('blur', handlePause);\n        window.addEventListener('focus', handleResume);\n        return () => {\n          wrapper.removeEventListener('focusin', handlePause);\n          wrapper.removeEventListener('focusout', handleFocusOutResume);\n          wrapper.removeEventListener('pointermove', handlePause);\n          wrapper.removeEventListener('pointerleave', handlePointerLeaveResume);\n          window.removeEventListener('blur', handlePause);\n          window.removeEventListener('focus', handleResume);\n        };\n      }\n    }, [hasToasts, context.isClosePausedRef]);\n\n    const getSortedTabbableCandidates = React.useCallback(\n      ({ tabbingDirection }: { tabbingDirection: 'forwards' | 'backwards' }) => {\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem) => {\n          const toastNode = toastItem.ref.current!;\n          const toastTabbableCandidates = [toastNode, ...getTabbableCandidates(toastNode)];\n          return tabbingDirection === 'forwards'\n            ? toastTabbableCandidates\n            : toastTabbableCandidates.reverse();\n        });\n        return (\n          tabbingDirection === 'forwards' ? tabbableCandidates.reverse() : tabbableCandidates\n        ).flat();\n      },\n      [getItems]\n    );\n\n    React.useEffect(() => {\n      const viewport = ref.current;\n      // We programmatically manage tabbing as we are unable to influence\n      // the source order with portals, this allows us to reverse the\n      // tab order so that it runs from most recent toast to least\n      if (viewport) {\n        const handleKeyDown = (event: KeyboardEvent) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n\n          if (isTabKey) {\n            const focusedElement = document.activeElement;\n            const isTabbingBackwards = event.shiftKey;\n            const targetIsViewport = event.target === viewport;\n\n            // If we're back tabbing after jumping to the viewport then we simply\n            // proxy focus out to the preceding document\n            if (targetIsViewport && isTabbingBackwards) {\n              headFocusProxyRef.current?.focus();\n              return;\n            }\n\n            const tabbingDirection = isTabbingBackwards ? 'backwards' : 'forwards';\n            const sortedCandidates = getSortedTabbableCandidates({ tabbingDirection });\n            const index = sortedCandidates.findIndex((candidate) => candidate === focusedElement);\n            if (focusFirst(sortedCandidates.slice(index + 1))) {\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges so we\n              // proxy to the corresponding exit point and let the browser handle\n              // tab/shift+tab keypress and implicitly pass focus to the next valid element in the document\n              isTabbingBackwards\n                ? headFocusProxyRef.current?.focus()\n                : tailFocusProxyRef.current?.focus();\n            }\n          }\n        };\n\n        // Toasts are not in the viewport React tree so we need to bind DOM events\n        viewport.addEventListener('keydown', handleKeyDown);\n        return () => viewport.removeEventListener('keydown', handleKeyDown);\n      }\n    }, [getItems, getSortedTabbableCandidates]);\n\n    return (\n      <DismissableLayer.Branch\n        ref={wrapperRef}\n        role=\"region\"\n        aria-label={label.replace('{hotkey}', hotkeyLabel)}\n        // Ensure virtual cursor from landmarks menus triggers focus/blur for pause/resume\n        tabIndex={-1}\n        // incase list has size when empty (e.g. padding), we remove pointer events so\n        // it doesn't prevent interactions with page elements that it overlays\n        style={{ pointerEvents: hasToasts ? undefined : 'none' }}\n      >\n        {hasToasts && (\n          <FocusProxy\n            ref={headFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'forwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n        {/**\n         * tabindex on the the list so that it can be focused when items are removed. we focus\n         * the list instead of the viewport so it announces number of items remaining.\n         */}\n        <Collection.Slot scope={__scopeToast}>\n          <Primitive.ol tabIndex={-1} {...viewportProps} ref={composedRefs} />\n        </Collection.Slot>\n        {hasToasts && (\n          <FocusProxy\n            ref={tailFocusProxyRef}\n            onFocusFromOutsideViewport={() => {\n              const tabbableCandidates = getSortedTabbableCandidates({\n                tabbingDirection: 'backwards',\n              });\n              focusFirst(tabbableCandidates);\n            }}\n          />\n        )}\n      </DismissableLayer.Branch>\n    );\n  }\n);\n\nToastViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_PROXY_NAME = 'ToastFocusProxy';\n\ntype FocusProxyElement = React.ComponentRef<typeof VisuallyHidden>;\ntype VisuallyHiddenProps = React.ComponentPropsWithoutRef<typeof VisuallyHidden>;\ninterface FocusProxyProps extends VisuallyHiddenProps {\n  onFocusFromOutsideViewport(): void;\n}\n\nconst FocusProxy = React.forwardRef<FocusProxyElement, ScopedProps<FocusProxyProps>>(\n  (props, forwardedRef) => {\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n\n    return (\n      <VisuallyHidden\n        aria-hidden\n        tabIndex={0}\n        {...proxyProps}\n        ref={forwardedRef}\n        // Avoid page scrolling when focus is on the focus proxy\n        style={{ position: 'fixed' }}\n        onFocus={(event) => {\n          const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n          const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n          if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }}\n      />\n    );\n  }\n);\n\nFocusProxy.displayName = FOCUS_PROXY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Toast\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOAST_NAME = 'Toast';\nconst TOAST_SWIPE_START = 'toast.swipeStart';\nconst TOAST_SWIPE_MOVE = 'toast.swipeMove';\nconst TOAST_SWIPE_CANCEL = 'toast.swipeCancel';\nconst TOAST_SWIPE_END = 'toast.swipeEnd';\n\ntype ToastElement = ToastImplElement;\ninterface ToastProps extends Omit<ToastImplProps, keyof ToastImplPrivateProps> {\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst Toast = React.forwardRef<ToastElement, ToastProps>(\n  (props: ScopedProps<ToastProps>, forwardedRef) => {\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? true,\n      onChange: onOpenChange,\n      caller: TOAST_NAME,\n    });\n    return (\n      <Presence present={forceMount || open}>\n        <ToastImpl\n          open={open}\n          {...toastProps}\n          ref={forwardedRef}\n          onClose={() => setOpen(false)}\n          onPause={useCallbackRef(props.onPause)}\n          onResume={useCallbackRef(props.onResume)}\n          onSwipeStart={composeEventHandlers(props.onSwipeStart, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'start');\n          })}\n          onSwipeMove={composeEventHandlers(props.onSwipeMove, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'move');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-move-y', `${y}px`);\n          })}\n          onSwipeCancel={composeEventHandlers(props.onSwipeCancel, (event) => {\n            event.currentTarget.setAttribute('data-swipe', 'cancel');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-end-y');\n          })}\n          onSwipeEnd={composeEventHandlers(props.onSwipeEnd, (event) => {\n            const { x, y } = event.detail.delta;\n            event.currentTarget.setAttribute('data-swipe', 'end');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-x');\n            event.currentTarget.style.removeProperty('--radix-toast-swipe-move-y');\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-x', `${x}px`);\n            event.currentTarget.style.setProperty('--radix-toast-swipe-end-y', `${y}px`);\n            setOpen(false);\n          })}\n        />\n      </Presence>\n    );\n  }\n);\n\nToast.displayName = TOAST_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype SwipeEvent = { currentTarget: EventTarget & ToastElement } & Omit<\n  CustomEvent<{ originalEvent: React.PointerEvent; delta: { x: number; y: number } }>,\n  'currentTarget'\n>;\n\nconst [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n  onClose() {},\n});\n\ntype ToastImplElement = React.ComponentRef<typeof Primitive.li>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer.Root>;\ntype ToastImplPrivateProps = { open: boolean; onClose(): void };\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface ToastImplProps extends ToastImplPrivateProps, PrimitiveListItemProps {\n  type?: 'foreground' | 'background';\n  /**\n   * Time in milliseconds that toast should remain visible for. Overrides value\n   * given to `ToastProvider`.\n   */\n  duration?: number;\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPause?(): void;\n  onResume?(): void;\n  onSwipeStart?(event: SwipeEvent): void;\n  onSwipeMove?(event: SwipeEvent): void;\n  onSwipeCancel?(event: SwipeEvent): void;\n  onSwipeEnd?(event: SwipeEvent): void;\n}\n\nconst ToastImpl = React.forwardRef<ToastImplElement, ToastImplProps>(\n  (props: ScopedProps<ToastImplProps>, forwardedRef) => {\n    const {\n      __scopeToast,\n      type = 'foreground',\n      duration: durationProp,\n      open,\n      onClose,\n      onEscapeKeyDown,\n      onPause,\n      onResume,\n      onSwipeStart,\n      onSwipeMove,\n      onSwipeCancel,\n      onSwipeEnd,\n      ...toastProps\n    } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = React.useState<ToastImplElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n    const swipeDeltaRef = React.useRef<{ x: number; y: number } | null>(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = React.useRef(0);\n    const closeTimerRemainingTimeRef = React.useRef(duration);\n    const closeTimerRef = React.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = useCallbackRef(() => {\n      // focus viewport if focus is within toast to read the remaining toast\n      // count to SR users and ensure focus isn't lost\n      const isFocusInToast = node?.contains(document.activeElement);\n      if (isFocusInToast) context.viewport?.focus();\n      onClose();\n    });\n\n    const startTimer = React.useCallback(\n      (duration: number) => {\n        if (!duration || duration === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration);\n      },\n      [handleClose]\n    );\n\n    React.useEffect(() => {\n      const viewport = context.viewport;\n      if (viewport) {\n        const handleResume = () => {\n          startTimer(closeTimerRemainingTimeRef.current);\n          onResume?.();\n        };\n        const handlePause = () => {\n          const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n          closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n          window.clearTimeout(closeTimerRef.current);\n          onPause?.();\n        };\n        viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n        viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n        return () => {\n          viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n          viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n        };\n      }\n    }, [context.viewport, duration, onPause, onResume, startTimer]);\n\n    // start timer when toast opens or duration changes.\n    // we include `open` in deps because closed !== unmounted when animating\n    // so it could reopen before being completely unmounted\n    React.useEffect(() => {\n      if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [open, duration, context.isClosePausedRef, startTimer]);\n\n    React.useEffect(() => {\n      onToastAdd();\n      return () => onToastRemove();\n    }, [onToastAdd, onToastRemove]);\n\n    const announceTextContent = React.useMemo(() => {\n      return node ? getAnnounceTextContent(node) : null;\n    }, [node]);\n\n    if (!context.viewport) return null;\n\n    return (\n      <>\n        {announceTextContent && (\n          <ToastAnnounce\n            __scopeToast={__scopeToast}\n            // Toasts are always role=status to avoid stuttering issues with role=alert in SRs.\n            role=\"status\"\n            aria-live={type === 'foreground' ? 'assertive' : 'polite'}\n            aria-atomic\n          >\n            {announceTextContent}\n          </ToastAnnounce>\n        )}\n\n        <ToastInteractiveProvider scope={__scopeToast} onClose={handleClose}>\n          {ReactDOM.createPortal(\n            <Collection.ItemSlot scope={__scopeToast}>\n              <DismissableLayer.Root\n                asChild\n                onEscapeKeyDown={composeEventHandlers(onEscapeKeyDown, () => {\n                  if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                  context.isFocusedToastEscapeKeyDownRef.current = false;\n                })}\n              >\n                <Primitive.li\n                  // Ensure toasts are announced as status list or status when focused\n                  role=\"status\"\n                  aria-live=\"off\"\n                  aria-atomic\n                  tabIndex={0}\n                  data-state={open ? 'open' : 'closed'}\n                  data-swipe-direction={context.swipeDirection}\n                  {...toastProps}\n                  ref={composedRefs}\n                  style={{ userSelect: 'none', touchAction: 'none', ...props.style }}\n                  onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                    if (event.key !== 'Escape') return;\n                    onEscapeKeyDown?.(event.nativeEvent);\n                    if (!event.nativeEvent.defaultPrevented) {\n                      context.isFocusedToastEscapeKeyDownRef.current = true;\n                      handleClose();\n                    }\n                  })}\n                  onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n                    if (event.button !== 0) return;\n                    pointerStartRef.current = { x: event.clientX, y: event.clientY };\n                  })}\n                  onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n                    if (!pointerStartRef.current) return;\n                    const x = event.clientX - pointerStartRef.current.x;\n                    const y = event.clientY - pointerStartRef.current.y;\n                    const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                    const isHorizontalSwipe = ['left', 'right'].includes(context.swipeDirection);\n                    const clamp = ['left', 'up'].includes(context.swipeDirection)\n                      ? Math.min\n                      : Math.max;\n                    const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                    const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                    const moveStartBuffer = event.pointerType === 'touch' ? 10 : 2;\n                    const delta = { x: clampedX, y: clampedY };\n                    const eventDetail = { originalEvent: event, delta };\n                    if (hasSwipeMoveStarted) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                        discrete: false,\n                      });\n                    } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                      swipeDeltaRef.current = delta;\n                      handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                        discrete: false,\n                      });\n                      (event.target as HTMLElement).setPointerCapture(event.pointerId);\n                    } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                      // User is swiping in wrong direction so we disable swipe gesture\n                      // for the current pointer down interaction\n                      pointerStartRef.current = null;\n                    }\n                  })}\n                  onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n                    const delta = swipeDeltaRef.current;\n                    const target = event.target as HTMLElement;\n                    if (target.hasPointerCapture(event.pointerId)) {\n                      target.releasePointerCapture(event.pointerId);\n                    }\n                    swipeDeltaRef.current = null;\n                    pointerStartRef.current = null;\n                    if (delta) {\n                      const toast = event.currentTarget;\n                      const eventDetail = { originalEvent: event, delta };\n                      if (\n                        isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)\n                      ) {\n                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                          discrete: true,\n                        });\n                      } else {\n                        handleAndDispatchCustomEvent(\n                          TOAST_SWIPE_CANCEL,\n                          onSwipeCancel,\n                          eventDetail,\n                          {\n                            discrete: true,\n                          }\n                        );\n                      }\n                      // Prevent click event from triggering on items within the toast when\n                      // pointer up is part of a swipe gesture\n                      toast.addEventListener('click', (event) => event.preventDefault(), {\n                        once: true,\n                      });\n                    }\n                  })}\n                />\n              </DismissableLayer.Root>\n            </Collection.ItemSlot>,\n            context.viewport\n          )}\n        </ToastInteractiveProvider>\n      </>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface ToastAnnounceProps\n  extends Omit<React.ComponentPropsWithoutRef<'div'>, 'children'>,\n    ScopedProps<{ children: string[] }> {}\n\nconst ToastAnnounce: React.FC<ToastAnnounceProps> = (props: ScopedProps<ToastAnnounceProps>) => {\n  const { __scopeToast, children, ...announceProps } = props;\n  const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n  const [renderAnnounceText, setRenderAnnounceText] = React.useState(false);\n  const [isAnnounced, setIsAnnounced] = React.useState(false);\n\n  // render text content in the next frame to ensure toast is announced in NVDA\n  useNextFrame(() => setRenderAnnounceText(true));\n\n  // cleanup after announcing\n  React.useEffect(() => {\n    const timer = window.setTimeout(() => setIsAnnounced(true), 1000);\n    return () => window.clearTimeout(timer);\n  }, []);\n\n  return isAnnounced ? null : (\n    <Portal asChild>\n      <VisuallyHidden {...announceProps}>\n        {renderAnnounceText && (\n          <>\n            {context.label} {children}\n          </>\n        )}\n      </VisuallyHidden>\n    </Portal>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ToastTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'ToastTitle';\n\ntype ToastTitleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToastTitleProps extends PrimitiveDivProps {}\n\nconst ToastTitle = React.forwardRef<ToastTitleElement, ToastTitleProps>(\n  (props: ScopedProps<ToastTitleProps>, forwardedRef) => {\n    const { __scopeToast, ...titleProps } = props;\n    return <Primitive.div {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nToastTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'ToastDescription';\n\ntype ToastDescriptionElement = React.ComponentRef<typeof Primitive.div>;\ninterface ToastDescriptionProps extends PrimitiveDivProps {}\n\nconst ToastDescription = React.forwardRef<ToastDescriptionElement, ToastDescriptionProps>(\n  (props: ScopedProps<ToastDescriptionProps>, forwardedRef) => {\n    const { __scopeToast, ...descriptionProps } = props;\n    return <Primitive.div {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nToastDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'ToastAction';\n\ntype ToastActionElement = ToastCloseElement;\ninterface ToastActionProps extends ToastCloseProps {\n  /**\n   * A short description for an alternate way to carry out the action. For screen reader users\n   * who will not be able to navigate to the button easily/quickly.\n   * @example <ToastAction altText=\"Goto account settings to upgrade\">Upgrade</ToastAction>\n   * @example <ToastAction altText=\"Undo (Alt+U)\">Undo</ToastAction>\n   */\n  altText: string;\n}\n\nconst ToastAction = React.forwardRef<ToastActionElement, ToastActionProps>(\n  (props: ScopedProps<ToastActionProps>, forwardedRef) => {\n    const { altText, ...actionProps } = props;\n\n    if (!altText.trim()) {\n      console.error(\n        `Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`\n      );\n      return null;\n    }\n\n    return (\n      <ToastAnnounceExclude altText={altText} asChild>\n        <ToastClose {...actionProps} ref={forwardedRef} />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToastClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'ToastClose';\n\ntype ToastCloseElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToastCloseProps extends PrimitiveButtonProps {}\n\nconst ToastClose = React.forwardRef<ToastCloseElement, ToastCloseProps>(\n  (props: ScopedProps<ToastCloseProps>, forwardedRef) => {\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n\n    return (\n      <ToastAnnounceExclude asChild>\n        <Primitive.button\n          type=\"button\"\n          {...closeProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(props.onClick, interactiveContext.onClose)}\n        />\n      </ToastAnnounceExclude>\n    );\n  }\n);\n\nToastClose.displayName = CLOSE_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype ToastAnnounceExcludeElement = React.ComponentRef<typeof Primitive.div>;\ninterface ToastAnnounceExcludeProps extends PrimitiveDivProps {\n  altText?: string;\n}\n\nconst ToastAnnounceExclude = React.forwardRef<\n  ToastAnnounceExcludeElement,\n  ToastAnnounceExcludeProps\n>((props: ScopedProps<ToastAnnounceExcludeProps>, forwardedRef) => {\n  const { __scopeToast, altText, ...announceExcludeProps } = props;\n\n  return (\n    <Primitive.div\n      data-radix-toast-announce-exclude=\"\"\n      data-radix-toast-announce-alt={altText || undefined}\n      {...announceExcludeProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nfunction getAnnounceTextContent(container: HTMLElement) {\n  const textContent: string[] = [];\n  const childNodes = Array.from(container.childNodes);\n\n  childNodes.forEach((node) => {\n    if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n    if (isHTMLElement(node)) {\n      const isHidden = node.ariaHidden || node.hidden || node.style.display === 'none';\n      const isExcluded = node.dataset.radixToastAnnounceExclude === '';\n\n      if (!isHidden) {\n        if (isExcluded) {\n          const altText = node.dataset.radixToastAnnounceAlt;\n          if (altText) textContent.push(altText);\n        } else {\n          textContent.push(...getAnnounceTextContent(node));\n        }\n      }\n    }\n  });\n\n  // We return a collection of text rather than a single concatenated string.\n  // This allows SR VO to naturally pause break between nodes while announcing.\n  return textContent;\n}\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction handleAndDispatchCustomEvent<\n  E extends CustomEvent,\n  ReactEvent extends React.SyntheticEvent,\n>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: ReactEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const currentTarget = detail.originalEvent.currentTarget as HTMLElement;\n  const event = new CustomEvent(name, { bubbles: true, cancelable: true, detail });\n  if (handler) currentTarget.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(currentTarget, event);\n  } else {\n    currentTarget.dispatchEvent(event);\n  }\n}\n\nconst isDeltaInDirection = (\n  delta: { x: number; y: number },\n  direction: SwipeDirection,\n  threshold = 0\n) => {\n  const deltaX = Math.abs(delta.x);\n  const deltaY = Math.abs(delta.y);\n  const isDeltaX = deltaX > deltaY;\n  if (direction === 'left' || direction === 'right') {\n    return isDeltaX && deltaX > threshold;\n  } else {\n    return !isDeltaX && deltaY > threshold;\n  }\n};\n\nfunction useNextFrame(callback = () => {}) {\n  const fn = useCallbackRef(callback);\n  useLayoutEffect(() => {\n    let raf1 = 0;\n    let raf2 = 0;\n    raf1 = window.requestAnimationFrame(() => (raf2 = window.requestAnimationFrame(fn)));\n    return () => {\n      window.cancelAnimationFrame(raf1);\n      window.cancelAnimationFrame(raf2);\n    };\n  }, [fn]);\n}\n\nfunction isHTMLElement(node: any): node is HTMLElement {\n  return node.nodeType === node.ELEMENT_NODE;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nconst Provider = ToastProvider;\nconst Viewport = ToastViewport;\nconst Root = Toast;\nconst Title = ToastTitle;\nconst Description = ToastDescription;\nconst Action = ToastAction;\nconst Close = ToastClose;\n\nexport {\n  createToastScope,\n  //\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastAction,\n  ToastClose,\n  //\n  Provider,\n  Viewport,\n  Root,\n  Title,\n  Description,\n  Action,\n  Close,\n};\nexport type {\n  ToastProviderProps,\n  ToastViewportProps,\n  ToastProps,\n  ToastTitleProps,\n  ToastDescriptionProps,\n  ToastActionProps,\n  ToastCloseProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Toggle\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Toggle';\n\ntype ToggleElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToggleProps extends PrimitiveButtonProps {\n  /**\n   * The controlled state of the toggle.\n   */\n  pressed?: boolean;\n  /**\n   * The state of the toggle when initially rendered. Use `defaultPressed`\n   * if you do not need to control the state of the toggle.\n   * @defaultValue false\n   */\n  defaultPressed?: boolean;\n  /**\n   * The callback that fires when the state of the toggle changes.\n   */\n  onPressedChange?(pressed: boolean): void;\n}\n\nconst Toggle = React.forwardRef<ToggleElement, ToggleProps>((props, forwardedRef) => {\n  const { pressed: pressedProp, defaultPressed, onPressedChange, ...buttonProps } = props;\n\n  const [pressed, setPressed] = useControllableState({\n    prop: pressedProp,\n    onChange: onPressedChange,\n    defaultProp: defaultPressed ?? false,\n    caller: NAME,\n  });\n\n  return (\n    <Primitive.button\n      type=\"button\"\n      aria-pressed={pressed}\n      data-state={pressed ? 'on' : 'off'}\n      data-disabled={props.disabled ? '' : undefined}\n      {...buttonProps}\n      ref={forwardedRef}\n      onClick={composeEventHandlers(props.onClick, () => {\n        if (!props.disabled) {\n          setPressed(!pressed);\n        }\n      })}\n    />\n  );\n});\n\nToggle.displayName = NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = Toggle;\n\nexport {\n  Toggle,\n  //\n  Root,\n};\nexport type { ToggleProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Toggle } from '@radix-ui/react-toggle';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_GROUP_NAME = 'ToggleGroup';\n\ntype ScopedProps<P> = P & { __scopeToggleGroup?: Scope };\nconst [createToggleGroupContext, createToggleGroupScope] = createContextScope(TOGGLE_GROUP_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype ToggleGroupElement = ToggleGroupImplSingleElement | ToggleGroupImplMultipleElement;\ninterface ToggleGroupSingleProps extends ToggleGroupImplSingleProps {\n  type: 'single';\n}\ninterface ToggleGroupMultipleProps extends ToggleGroupImplMultipleProps {\n  type: 'multiple';\n}\n\nconst ToggleGroup = React.forwardRef<\n  ToggleGroupElement,\n  ToggleGroupSingleProps | ToggleGroupMultipleProps\n>((props, forwardedRef) => {\n  const { type, ...toggleGroupProps } = props;\n\n  if (type === 'single') {\n    const singleProps = toggleGroupProps as ToggleGroupImplSingleProps;\n    return <ToggleGroupImplSingle {...singleProps} ref={forwardedRef} />;\n  }\n\n  if (type === 'multiple') {\n    const multipleProps = toggleGroupProps as ToggleGroupImplMultipleProps;\n    return <ToggleGroupImplMultiple {...multipleProps} ref={forwardedRef} />;\n  }\n\n  throw new Error(`Missing prop \\`type\\` expected on \\`${TOGGLE_GROUP_NAME}\\``);\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupValueContextValue = {\n  type: 'single' | 'multiple';\n  value: string[];\n  onItemActivate(value: string): void;\n  onItemDeactivate(value: string): void;\n};\n\nconst [ToggleGroupValueProvider, useToggleGroupValueContext] =\n  createToggleGroupContext<ToggleGroupValueContextValue>(TOGGLE_GROUP_NAME);\n\ntype ToggleGroupImplSingleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplSingleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the item that is pressed.\n   */\n  value?: string;\n  /**\n   * The value of the item that is pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the value of the toggle group changes.\n   */\n  onValueChange?(value: string): void;\n}\n\nconst ToggleGroupImplSingle = React.forwardRef<\n  ToggleGroupImplSingleElement,\n  ToggleGroupImplSingleProps\n>((props: ScopedProps<ToggleGroupImplSingleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupSingleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? '',\n    onChange: onValueChange,\n    caller: TOGGLE_GROUP_NAME,\n  });\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"single\"\n      value={React.useMemo(() => (value ? [value] : []), [value])}\n      onItemActivate={setValue}\n      onItemDeactivate={React.useCallback(() => setValue(''), [setValue])}\n    >\n      <ToggleGroupImpl {...toggleGroupSingleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\ntype ToggleGroupImplMultipleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplMultipleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the items that are pressed.\n   */\n  value?: string[];\n  /**\n   * The value of the items that are pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the toggle group changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst ToggleGroupImplMultiple = React.forwardRef<\n  ToggleGroupImplMultipleElement,\n  ToggleGroupImplMultipleProps\n>((props: ScopedProps<ToggleGroupImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: TOGGLE_GROUP_NAME,\n  });\n\n  const handleButtonActivate = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleButtonDeactivate = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"multiple\"\n      value={value}\n      onItemActivate={handleButtonActivate}\n      onItemDeactivate={handleButtonDeactivate}\n    >\n      <ToggleGroupImpl {...toggleGroupMultipleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupContextValue = { rovingFocus: boolean; disabled: boolean };\n\nconst [ToggleGroupContext, useToggleGroupContext] =\n  createToggleGroupContext<ToggleGroupContextValue>(TOGGLE_GROUP_NAME);\n\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype ToggleGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToggleGroupImplProps extends PrimitiveDivProps {\n  /**\n   * Whether the group is disabled from user interaction.\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * Whether the group should maintain roving focus of its buttons.\n   * @defaultValue true\n   */\n  rovingFocus?: boolean;\n  loop?: RovingFocusGroupProps['loop'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst ToggleGroupImpl = React.forwardRef<ToggleGroupImplElement, ToggleGroupImplProps>(\n  (props: ScopedProps<ToggleGroupImplProps>, forwardedRef) => {\n    const {\n      __scopeToggleGroup,\n      disabled = false,\n      rovingFocus = true,\n      orientation,\n      dir,\n      loop = true,\n      ...toggleGroupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToggleGroup);\n    const direction = useDirection(dir);\n    const commonProps = { role: 'group', dir: direction, ...toggleGroupProps };\n    return (\n      <ToggleGroupContext scope={__scopeToggleGroup} rovingFocus={rovingFocus} disabled={disabled}>\n        {rovingFocus ? (\n          <RovingFocusGroup.Root\n            asChild\n            {...rovingFocusGroupScope}\n            orientation={orientation}\n            dir={direction}\n            loop={loop}\n          >\n            <Primitive.div {...commonProps} ref={forwardedRef} />\n          </RovingFocusGroup.Root>\n        ) : (\n          <Primitive.div {...commonProps} ref={forwardedRef} />\n        )}\n      </ToggleGroupContext>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'ToggleGroupItem';\n\ntype ToggleGroupItemElement = ToggleGroupItemImplElement;\ninterface ToggleGroupItemProps extends Omit<ToggleGroupItemImplProps, 'pressed'> {}\n\nconst ToggleGroupItem = React.forwardRef<ToggleGroupItemElement, ToggleGroupItemProps>(\n  (props: ScopedProps<ToggleGroupItemProps>, forwardedRef) => {\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, props.__scopeToggleGroup);\n    const context = useToggleGroupContext(ITEM_NAME, props.__scopeToggleGroup);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(props.__scopeToggleGroup);\n    const pressed = valueContext.value.includes(props.value);\n    const disabled = context.disabled || props.disabled;\n    const commonProps = { ...props, pressed, disabled };\n    const ref = React.useRef<HTMLDivElement>(null);\n    return context.rovingFocus ? (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={pressed}\n        ref={ref}\n      >\n        <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n      </RovingFocusGroup.Item>\n    ) : (\n      <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n    );\n  }\n);\n\nToggleGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupItemImplElement = React.ComponentRef<typeof Toggle>;\ntype ToggleProps = React.ComponentPropsWithoutRef<typeof Toggle>;\ninterface ToggleGroupItemImplProps extends Omit<ToggleProps, 'defaultPressed' | 'onPressedChange'> {\n  /**\n   * A string value for the toggle group item. All items within a toggle group should use a unique value.\n   */\n  value: string;\n}\n\nconst ToggleGroupItemImpl = React.forwardRef<ToggleGroupItemImplElement, ToggleGroupItemImplProps>(\n  (props: ScopedProps<ToggleGroupItemImplProps>, forwardedRef) => {\n    const { __scopeToggleGroup, value, ...itemProps } = props;\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, __scopeToggleGroup);\n    const singleProps = { role: 'radio', 'aria-checked': props.pressed, 'aria-pressed': undefined };\n    const typeProps = valueContext.type === 'single' ? singleProps : undefined;\n    return (\n      <Toggle\n        {...typeProps}\n        {...itemProps}\n        ref={forwardedRef}\n        onPressedChange={(pressed) => {\n          if (pressed) {\n            valueContext.onItemActivate(value);\n          } else {\n            valueContext.onItemDeactivate(value);\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ToggleGroup;\nconst Item = ToggleGroupItem;\n\nexport {\n  createToggleGroupScope,\n  //\n  ToggleGroup,\n  ToggleGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { ToggleGroupSingleProps, ToggleGroupMultipleProps, ToggleGroupItemProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\nimport * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group';\nimport { createToggleGroupScope } from '@radix-ui/react-toggle-group';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Toolbar\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLBAR_NAME = 'Toolbar';\n\ntype ScopedProps<P> = P & { __scopeToolbar?: Scope };\nconst [createToolbarContext, createToolbarScope] = createContextScope(TOOLBAR_NAME, [\n  createRovingFocusGroupScope,\n  createToggleGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useToggleGroupScope = createToggleGroupScope();\n\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype ToolbarContextValue = {\n  orientation: RovingFocusGroupProps['orientation'];\n  dir: RovingFocusGroupProps['dir'];\n};\nconst [ToolbarProvider, useToolbarContext] =\n  createToolbarContext<ToolbarContextValue>(TOOLBAR_NAME);\n\ntype ToolbarElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToolbarProps extends PrimitiveDivProps {\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst Toolbar = React.forwardRef<ToolbarElement, ToolbarProps>(\n  (props: ScopedProps<ToolbarProps>, forwardedRef) => {\n    const { __scopeToolbar, orientation = 'horizontal', dir, loop = true, ...toolbarProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    const direction = useDirection(dir);\n    return (\n      <ToolbarProvider scope={__scopeToolbar} orientation={orientation} dir={direction}>\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"toolbar\"\n            aria-orientation={orientation}\n            dir={direction}\n            {...toolbarProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </ToolbarProvider>\n    );\n  }\n);\n\nToolbar.displayName = TOOLBAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'ToolbarSeparator';\n\ntype ToolbarSeparatorElement = React.ComponentRef<typeof SeparatorPrimitive.Root>;\ntype SeparatorProps = React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>;\ninterface ToolbarSeparatorProps extends SeparatorProps {}\n\nconst ToolbarSeparator = React.forwardRef<ToolbarSeparatorElement, ToolbarSeparatorProps>(\n  (props: ScopedProps<ToolbarSeparatorProps>, forwardedRef) => {\n    const { __scopeToolbar, ...separatorProps } = props;\n    const context = useToolbarContext(SEPARATOR_NAME, __scopeToolbar);\n    return (\n      <SeparatorPrimitive.Root\n        orientation={context.orientation === 'horizontal' ? 'vertical' : 'horizontal'}\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nToolbarSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUTTON_NAME = 'ToolbarButton';\n\ntype ToolbarButtonElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface ToolbarButtonProps extends PrimitiveButtonProps {}\n\nconst ToolbarButton = React.forwardRef<ToolbarButtonElement, ToolbarButtonProps>(\n  (props: ScopedProps<ToolbarButtonProps>, forwardedRef) => {\n    const { __scopeToolbar, ...buttonProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    return (\n      <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!props.disabled}>\n        <Primitive.button type=\"button\" {...buttonProps} ref={forwardedRef} />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nToolbarButton.displayName = BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarLink\n * -----------------------------------------------------------------------------------------------*/\n\nconst LINK_NAME = 'ToolbarLink';\n\ntype ToolbarLinkElement = React.ComponentRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface ToolbarLinkProps extends PrimitiveLinkProps {}\n\nconst ToolbarLink = React.forwardRef<ToolbarLinkElement, ToolbarLinkProps>(\n  (props: ScopedProps<ToolbarLinkProps>, forwardedRef) => {\n    const { __scopeToolbar, ...linkProps } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToolbar);\n    return (\n      <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable>\n        <Primitive.a\n          {...linkProps}\n          ref={forwardedRef}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === ' ') event.currentTarget.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nToolbarLink.displayName = LINK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarToggleGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_GROUP_NAME = 'ToolbarToggleGroup';\n\ntype ToolbarToggleGroupElement = React.ComponentRef<typeof ToggleGroupPrimitive.Root>;\ntype ToggleGroupProps = React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root>;\ninterface ToolbarToggleGroupSingleProps extends Extract<ToggleGroupProps, { type: 'single' }> {}\ninterface ToolbarToggleGroupMultipleProps extends Extract<ToggleGroupProps, { type: 'multiple' }> {}\n\nconst ToolbarToggleGroup = React.forwardRef<\n  ToolbarToggleGroupElement,\n  ToolbarToggleGroupSingleProps | ToolbarToggleGroupMultipleProps\n>(\n  (\n    props: ScopedProps<ToolbarToggleGroupSingleProps | ToolbarToggleGroupMultipleProps>,\n    forwardedRef\n  ) => {\n    const { __scopeToolbar, ...toggleGroupProps } = props;\n    const context = useToolbarContext(TOGGLE_GROUP_NAME, __scopeToolbar);\n    const toggleGroupScope = useToggleGroupScope(__scopeToolbar);\n    return (\n      <ToggleGroupPrimitive.Root\n        data-orientation={context.orientation}\n        dir={context.dir}\n        {...toggleGroupScope}\n        {...toggleGroupProps}\n        ref={forwardedRef}\n        rovingFocus={false}\n      />\n    );\n  }\n);\n\nToolbarToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ToolbarToggleItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_ITEM_NAME = 'ToolbarToggleItem';\n\ntype ToolbarToggleItemElement = React.ComponentRef<typeof ToggleGroupPrimitive.Item>;\ntype ToggleGroupItemProps = React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item>;\ninterface ToolbarToggleItemProps extends ToggleGroupItemProps {}\n\nconst ToolbarToggleItem = React.forwardRef<ToolbarToggleItemElement, ToolbarToggleItemProps>(\n  (props: ScopedProps<ToolbarToggleItemProps>, forwardedRef) => {\n    const { __scopeToolbar, ...toggleItemProps } = props;\n    const toggleGroupScope = useToggleGroupScope(__scopeToolbar);\n    const scope = { __scopeToolbar: props.__scopeToolbar };\n\n    return (\n      <ToolbarButton asChild {...scope}>\n        <ToggleGroupPrimitive.Item {...toggleGroupScope} {...toggleItemProps} ref={forwardedRef} />\n      </ToolbarButton>\n    );\n  }\n);\n\nToolbarToggleItem.displayName = TOGGLE_ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = Toolbar;\nconst Separator = ToolbarSeparator;\nconst Button = ToolbarButton;\nconst Link = ToolbarLink;\nconst ToggleGroup = ToolbarToggleGroup;\nconst ToggleItem = ToolbarToggleItem;\n\nexport {\n  createToolbarScope,\n  //\n  Toolbar,\n  ToolbarSeparator,\n  ToolbarButton,\n  ToolbarLink,\n  ToolbarToggleGroup,\n  ToolbarToggleItem,\n  //\n  Root,\n  Separator,\n  Button,\n  Link,\n  ToggleGroup,\n  ToggleItem,\n};\nexport type {\n  ToolbarProps,\n  ToolbarSeparatorProps,\n  ToolbarButtonProps,\n  ToolbarLinkProps,\n  ToolbarToggleGroupSingleProps,\n  ToolbarToggleGroupMultipleProps,\n  ToolbarToggleItemProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AAiBnB,yBAAA;AAdJ,IAAM,OAAO;AAWb,IAAM,iBAAgD,CAAC,EAAE,UAAU,MAAM,MAAM;AAC7E,QAAM,QAAc,eAAS,KAAK,QAAQ;AAC1C,aACE,yBAAA,6BAAA,EACG,UAAA;IAAM,mBAAa,OAA8D;;MAEhF,eAAe;MACf,WAAW;;IACb,CAAC;QACD,wBAAyBA,OAAxB,EAA8B,UAAA,MAAA,CAAM;EAAA,EAAA,CACvC;AAEJ;AAEA,eAAe,cAAc;AAE7B,IAAMA,SAAO;;;;;;;;;;;;;;;;;AC9Bb,mBAAkB;;;;;;;;;;;;;ACAlB,IAAAC,SAAuB;AAkEf,IAAAC,sBAAA;AAlDR,IAAM,mBAAmB;AAGzB,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,gBAAgB;AAS9F,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,yBAAkD,gBAAgB;AAWpE,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM;MACJ;MACA,MAAM;MACN;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;MAC3C,MAAM;MACN,aAAa,eAAe;MAC5B,UAAU;MACV,QAAQ;IACV,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,WAAW,MAAM;QACjB;QACA,cAAoB,mBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;QAEjF,cAAA;UAAC,UAAU;UAAV;YACC,cAAY,SAAS,IAAI;YACzB,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ,QAAQ;QAC/B,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,WAAW,KAAK;QACvC,UAAU,QAAQ;QACjB,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;MAAA;IACnE;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAWrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,YAAY,GAAG,aAAa,IAAI;AACxC,UAAM,UAAU,sBAAsB,cAAc,MAAM,kBAAkB;AAC5E,eACE,yBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACtC,UAAA,CAAC,EAAE,QAAQ,UACV,yBAAC,wBAAA,EAAwB,GAAG,cAAc,KAAK,cAAc,QAAA,CAAkB,EAAA,CAEnF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AASjC,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,oBAAoB,SAAS,UAAU,GAAG,aAAa,IAAI;AACnE,QAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,QAAM,CAAC,WAAW,YAAY,IAAU,gBAAS,OAAO;AACxD,QAAM,MAAY,cAAsC,IAAI;AAC5D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,YAAkB,cAA2B,CAAC;AACpD,QAAM,SAAS,UAAU;AACzB,QAAM,WAAiB,cAA2B,CAAC;AACnD,QAAM,QAAQ,SAAS;AAGvB,QAAM,SAAS,QAAQ,QAAQ;AAC/B,QAAM,+BAAqC,cAAO,MAAM;AACxD,QAAM,oBAA0B,cAA+B,MAAS;AAElE,EAAA,iBAAU,MAAM;AACpB,UAAM,MAAM,sBAAsB,MAAO,6BAA6B,UAAU,KAAM;AACtF,WAAO,MAAM,qBAAqB,GAAG;EACvC,GAAG,CAAC,CAAC;AAEL,mBAAgB,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,wBAAkB,UAAU,kBAAkB,WAAW;QACvD,oBAAoB,KAAK,MAAM;QAC/B,eAAe,KAAK,MAAM;MAC5B;AAEA,WAAK,MAAM,qBAAqB;AAChC,WAAK,MAAM,gBAAgB;AAG3B,YAAM,OAAO,KAAK,sBAAsB;AACxC,gBAAU,UAAU,KAAK;AACzB,eAAS,UAAU,KAAK;AAGxB,UAAI,CAAC,6BAA6B,SAAS;AACzC,aAAK,MAAM,qBAAqB,kBAAkB,QAAQ;AAC1D,aAAK,MAAM,gBAAgB,kBAAkB,QAAQ;MACvD;AAEA,mBAAa,OAAO;IACtB;EAOF,GAAG,CAAC,QAAQ,MAAM,OAAO,CAAC;AAE1B,aACE;IAAC,UAAU;IAAV;MACC,cAAY,SAAS,QAAQ,IAAI;MACjC,iBAAe,QAAQ,WAAW,KAAK;MACvC,IAAI,QAAQ;MACZ,QAAQ,CAAC;MACR,GAAG;MACJ,KAAK;MACL,OAAO;QACL,CAAC,oCAA2C,GAAG,SAAS,GAAG,MAAM,OAAO;QACxE,CAAC,mCAA0C,GAAG,QAAQ,GAAG,KAAK,OAAO;QACrE,GAAG,MAAM;MACX;MAEC,UAAA,UAAU;IAAA;EACb;AAEJ,CAAC;AAID,SAAS,SAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAMC,QAAO;AACb,IAAMC,WAAU;AAChB,IAAMC,WAAU;;;ADvLN,IAAAC,sBAAA;AA7BV,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,CAAC,QAAQ,OAAO,aAAa,WAAW,aAAa,YAAY;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,IACrD,iBAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,IAAI,mBAAmB,gBAAgB;EACxF;EACA;AACF,CAAC;AACD,IAAM,sBAAsB,uBAAuB;AAUnD,IAAM,YAAY,aAAAC,QAAM;EACtB,CAAC,OAAmE,iBAAiB;AACnF,UAAM,EAAE,MAAM,GAAG,eAAe,IAAI;AACpC,UAAM,cAAc;AACpB,UAAM,gBAAgB;AACtB,eACE,yBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,kBAC/B,UAAA,SAAS,iBACR,yBAAC,uBAAA,EAAuB,GAAG,eAAe,KAAK,aAAA,CAAc,QAE7D,yBAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAE7D;EAEJ;AACF;AAEA,UAAU,cAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,IAAI;EACrE;EACA,EAAE,aAAa,MAAM;AACvB;AAwBA,IAAM,sBAAsB,aAAAA,QAAM;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM;MACJ,OAAO;MACP;MACA,gBAAgB,MAAM;MAAC;MACvB,cAAc;MACd,GAAG;IACL,IAAI;AAEJ,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,aAAa,gBAAgB;MAC7B,UAAU;MACV,QAAQ;IACV,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,OAAO,aAAAA,QAAM,QAAQ,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAI,CAAC,KAAK,CAAC;QAC1D,YAAY;QACZ,aAAa,aAAAA,QAAM,YAAY,MAAM,eAAe,SAAS,EAAE,GAAG,CAAC,aAAa,QAAQ,CAAC;QAEzF,cAAA,yBAAC,8BAAA,EAA6B,OAAO,MAAM,kBAAkB,aAC3D,cAAA,yBAAC,eAAA,EAAe,GAAG,sBAAsB,KAAK,aAAA,CAAc,EAAA,CAC9D;MAAA;IACF;EAEJ;AACF;AAqBA,IAAM,wBAAwB,aAAAA,QAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa,gBAAgB,CAAC;IAC9B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,QAAM,iBAAiB,aAAAA,QAAM;IAC3B,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;IAC7E,CAAC,QAAQ;EACX;AAEA,QAAM,kBAAkB,aAAAA,QAAM;IAC5B,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACC,WAAUA,WAAU,SAAS,CAAC;IAC/E,CAAC,QAAQ;EACX;AAEA,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb;MACA,YAAY;MACZ,aAAa;MAEb,cAAA,yBAAC,8BAAA,EAA6B,OAAO,MAAM,kBAAkB,aAAa,MACxE,cAAA,yBAAC,eAAA,EAAe,GAAG,wBAAwB,KAAK,aAAA,CAAc,EAAA,CAChE;IAAA;EACF;AAEJ,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,IAC/C,uBAAkD,cAAc;AAsBlE,IAAM,gBAAgB,aAAAD,QAAM;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,UAAU,KAAK,cAAc,YAAY,GAAG,eAAe,IAAI;AACzF,UAAM,eAAe,aAAAA,QAAM,OAA6B,IAAI;AAC5D,UAAM,eAAe,gBAAgB,cAAc,YAAY;AAC/D,UAAM,WAAW,cAAc,gBAAgB;AAC/C,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,iBAAiB,cAAc;AAErC,UAAM,gBAAgB,qBAAqB,MAAM,WAAW,CAAC,UAAU;AACrE,UAAI,CAAC,eAAe,SAAS,MAAM,GAAG,EAAG;AACzC,YAAM,SAAS,MAAM;AACrB,YAAM,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,QAAQ;AACjF,YAAM,eAAe,kBAAkB,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,MAAM;AACtF,YAAM,eAAe,kBAAkB;AAEvC,UAAI,iBAAiB,GAAI;AAGzB,YAAM,eAAe;AAErB,UAAI,YAAY;AAChB,YAAM,YAAY;AAClB,YAAM,WAAW,eAAe;AAEhC,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,UAAU;AACxB,sBAAY;QACd;MACF;AAEA,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,WAAW;AACzB,sBAAY;QACd;MACF;AAEA,cAAQ,MAAM,KAAK;QACjB,KAAK;AACH,sBAAY;AACZ;QACF,KAAK;AACH,sBAAY;AACZ;QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;YACX,OAAO;AACL,uBAAS;YACX;UACF;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;UACX;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;YACX,OAAO;AACL,uBAAS;YACX;UACF;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;UACX;AACA;MACJ;AAEA,YAAM,eAAe,YAAY;AACjC,wBAAkB,YAAY,EAAG,IAAI,SAAS,MAAM;IACtD,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,WAAW;QACX;QAEA,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,kBACtB,cAAA;UAAC,UAAU;UAAV;YACE,GAAG;YACJ,oBAAkB;YAClB,KAAK;YACL,WAAW,WAAW,SAAY;UAAA;QACpC,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,uBAAkD,SAAS;AAqB7D,IAAM,gBAAgB,aAAAA,QAAM;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,OAAO,GAAG,mBAAmB,IAAI;AAC3D,UAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;AACxE,UAAM,eAAe,yBAAyB,WAAW,gBAAgB;AACzE,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,UAAM,YAAY,MAAM;AACxB,UAAM,OAAQ,SAAS,aAAa,MAAM,SAAS,KAAK,KAAM;AAC9D,UAAM,WAAW,iBAAiB,YAAY,MAAM;AAEpD,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QAEA,cAAA;UAAsBE;UAArB;YACC,oBAAkB,iBAAiB;YACnC,cAAYC,UAAS,IAAI;YACxB,GAAG;YACH,GAAG;YACJ,KAAK;YACL;YACA;YACA,cAAc,CAACC,UAAS;AACtB,kBAAIA,OAAM;AACR,6BAAa,WAAW,KAAK;cAC/B,OAAO;AACL,6BAAa,YAAY,KAAK;cAChC;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,kBAAkB,aAAAJ,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,kBAAkB,GAAG,YAAY,IAAI;AAC7C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwB,aAAa,gBAAgB;AACzE,eACE;MAAC,UAAU;MAAV;QACC,oBAAkB,iBAAiB;QACnC,cAAYG,UAAS,YAAY,IAAI;QACrC,iBAAe,YAAY,WAAW,KAAK;QAC1C,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAME,gBAAe;AAUrB,IAAM,mBAAmB,aAAAL,QAAM;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwBK,eAAc,gBAAgB;AAC1E,UAAM,qBAAqB,+BAA+BA,eAAc,gBAAgB;AACxF,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,eACE,yBAAC,WAAW,UAAX,EAAoB,OAAO,kBAC1B,cAAA;MAAsBC;MAArB;QACC,iBAAgB,YAAY,QAAQ,CAAC,mBAAmB,eAAgB;QACxE,oBAAkB,iBAAiB;QACnC,IAAI,YAAY;QACf,GAAG;QACH,GAAG;QACJ,KAAK;MAAA;IACP,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAcD;AAM/B,IAAME,gBAAe;AASrB,IAAM,mBAAmB,aAAAP,QAAM;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwBO,eAAc,gBAAgB;AAC1E,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,eACE;MAAsBC;MAArB;QACC,MAAK;QACL,mBAAiB,YAAY;QAC7B,oBAAkB,iBAAiB;QAClC,GAAG;QACH,GAAG;QACJ,KAAK;QACL,OAAO;UACL,CAAC,kCAAyC,GAAG;UAC7C,CAAC,iCAAwC,GAAG;UAC5C,GAAG,MAAM;QACX;MAAA;IACF;EAEJ;AACF;AAEA,iBAAiB,cAAcD;AAI/B,SAASJ,UAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAMD,SAAO;AACb,IAAMO,QAAO;AACb,IAAM,SAAS;AACf,IAAMH,YAAU;AAChB,IAAME,YAAU;;;;;;;;;;;;;;;;;;;;;;;;;AEhgBhB,IAAAE,SAAuB;AA4Bd,IAAAC,sBAAA;AAdT,IAAM,YAAY;AAGlB,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,WAAW;EACvF;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAKzC,IAAM,cAA0C,CAAC,UAAyC;AACxF,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,yBAAiBC,OAAhB,EAAsB,GAAG,aAAc,GAAG,kBAAkB,OAAO,KAAA,CAAM;AACnF;AAEA,YAAY,cAAc;AAK1B,IAAMC,gBAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,yBAAiB,SAAhB,EAAyB,GAAG,aAAc,GAAG,cAAc,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,mBAAmB,cAAcA;AAMjC,IAAM,cAAc;AAKpB,IAAM,oBAAsD,CAC1D,UACG;AACH,QAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,yBAAiBC,SAAhB,EAAwB,GAAG,aAAc,GAAG,YAAA,CAAa;AACnE;AAEA,kBAAkB,cAAc;AAMhC,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,yBAAiB,SAAhB,EAAyB,GAAG,aAAc,GAAG,cAAc,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAMC,gBAAe;AAMrB,IAAM,CAAC,4BAA4B,4BAA4B,IAC7D,yBAAyDA,aAAY;AAOvE,IAAM,YAAY,gBAAgB,oBAAoB;AAEtD,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,UAAU,GAAG,aAAa,IAAI;AAC1D,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,aAAmB,cAAkC,IAAI;AAC/D,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,UAAM,YAAkB,cAAwC,IAAI;AAEpE,eACE;MAAiB;MAAhB;QACC,aAAaA;QACb,WAAW;QACX,UAAS;QAET,cAAA,yBAAC,4BAAA,EAA2B,OAAO,oBAAoB,WACrD,cAAA;UAAiBC;UAAhB;YACC,MAAK;YACJ,GAAG;YACH,GAAG;YACJ,KAAK;YACL,iBAAiB,qBAAqB,aAAa,iBAAiB,CAAC,UAAU;AAC7E,oBAAM,eAAe;AACrB,wBAAU,SAAS,MAAM,EAAE,eAAe,KAAK,CAAC;YAClD,CAAC;YACD,sBAAsB,CAAC,UAAU,MAAM,eAAe;YACtD,mBAAmB,CAAC,UAAU,MAAM,eAAe;YAQnD,UAAA;kBAAA,yBAAC,WAAA,EAAW,SAAA,CAAS;kBAEnB,yBAAC,oBAAA,EAAmB,WAAA,CAAwB;YAAA;UAAA;QAEhD,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,mBAAmB,cAAcD;AAMjC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EACpF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,kBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,yBAAiB,aAAhB,EAA6B,GAAG,aAAc,GAAG,kBAAkB,KAAK,aAAA,CAAc;AAChG,CAAC;AAED,uBAAuB,cAAc;AAMrC,IAAM,cAAc;AAMpB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,aAAa,KAAK,aAAA,CAAc;EACrF;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,cAAc;AAKpB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,UAAM,EAAE,UAAU,IAAI,6BAA6B,aAAa,kBAAkB;AAClF,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,MAAM,gBAAgB,cAAc,SAAS;AACnD,eAAO,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,aAAa,IAAA,CAAU;EAC5E;AACF;AAEA,kBAAkB,cAAc;AAQhC,IAAM,qBAAwD,CAAC,EAAE,WAAW,MAAM;AAChF,QAAM,UAAU,KAAKA,aAAY;;qCAEEA,aAAY,qBAAqB,gBAAgB;;4JAEsEA,aAAY;;;AAIhK,EAAA,iBAAU,MAAM;AACpB,UAAM,iBAAiB,SAAS;MAC9B,WAAW,SAAS,aAAa,kBAAkB;IACrD;AACA,QAAI,CAAC,eAAgB,SAAQ,KAAK,OAAO;EAC3C,GAAG,CAAC,SAAS,UAAU,CAAC;AAExB,SAAO;AACT;AAEA,IAAMH,SAAO;AACb,IAAMK,YAAU;AAChB,IAAMH,WAAS;AACf,IAAMI,WAAU;AAChB,IAAMF,YAAU;AAChB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAMG,SAAQ;AACd,IAAMC,eAAc;;;;;;;;AChRpB,IAAAC,SAAuB;AA6Bf,IAAAC,sBAAA;AAtBR,IAAMC,QAAO;AAQb,IAAM,cAAoB;EACxB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,QAAQ,IAAI,GAAG,OAAO,GAAG,iBAAiB,IAAI;AACtD,eACE;MAAC;MAAA;QACC,OAAO;;UAEL,UAAU;;UAEV,OAAO;UACP,eAAe,GAAG,MAAM,KAAK;QAC/B;QACA,mCAAgC;QAEhC,cAAA;UAAC,UAAU;UAAV;YACE,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG;;cAEH,UAAU;cACV,KAAK;cACL,OAAO;cACP,QAAQ;cACR,MAAM;YACR;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAcA;AAI1B,IAAMC,QAAO;;;;;;;;;;;;;;;;;ACnDb,IAAAC,SAAuB;AAyGnB,IAAAC,sBAAA;AA7FJ,IAAM,gBAAgB;AAGtB,IAAM,CAAC,uBAAuB,mBAAmB,IAAI,mBAAmB,aAAa;AAqBrF,IAAM,CAAC,sBAAsB,kBAAkB,IAC7C,sBAA4C,aAAa;AAkB3D,SAAS,iBACP,OACA;AACA,QAAM;IACJ;IACA,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;;IAER;EACF,IAAI;AAEJ,QAAM,CAAC,SAAS,UAAU,IAAI,qBAAqB;IACjD,MAAM;IACN,aAAa,kBAAkB;IAC/B,UAAU;IACV,QAAQ;EACV,CAAC;AACD,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAmC,IAAI;AAC3E,QAAM,CAAC,aAAa,cAAc,IAAU,gBAAkC,IAAI;AAClF,QAAM,mCAAyC,cAAO,KAAK;AAC3D,QAAMC,iBAAgB,UAClB,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,QAAQ,MAAM;;IAElC;;AAEJ,QAAM,UAAuC;IAC3C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;IAC1D,eAAAA;IACA;IACA;EACF;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACN,GAAI;MAEJ,UAAA,WAAW,0BAA0B,IAAI,2BAA2B,OAAO,IAAI;IAAA;EAClF;AAEJ;AAMA,IAAMC,gBAAe;AAUrB,IAAM,kBAAwB;EAC5B,CACE,EAAE,iBAAiB,WAAW,SAAS,GAAG,cAAc,GACxD,iBACG;AACH,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,eAAAD;MACA;IACF,IAAI,mBAAmBC,eAAc,eAAe;AACpD,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAE7D,UAAM,yBAA+B,cAAO,OAAO;AAC7C,IAAA,iBAAU,MAAM;AACpB,YAAM,OAAO,SAAS;AACtB,UAAI,MAAM;AACR,cAAM,QAAQ,MAAM,WAAW,uBAAuB,OAAO;AAC7D,aAAK,iBAAiB,SAAS,KAAK;AACpC,eAAO,MAAM,KAAK,oBAAoB,SAAS,KAAK;MACtD;IACF,GAAG,CAAC,SAAS,UAAU,CAAC;AAExB,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,MAAK;QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;QACnD,iBAAe;QACf,cAAYC,UAAS,OAAO;QAC5B,iBAAe,WAAW,KAAK;QAC/B;QACA;QACC,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,WAAW,CAAC,UAAU;AAEpD,cAAI,MAAM,QAAQ,QAAS,OAAM,eAAe;QAClD,CAAC;QACD,SAAS,qBAAqB,SAAS,CAAC,UAAU;AAChD,qBAAW,CAAC,gBAAiB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAY;AAChF,cAAI,eAAeF,gBAAe;AAChC,6CAAiC,UAAU,MAAM,qBAAqB;AAMtE,gBAAI,CAAC,iCAAiC,QAAS,OAAM,gBAAgB;UACvE;QACF,CAAC;MAAA;IACH;EAEJ;AACF;AAEA,gBAAgB,cAAcC;AAe9B,IAAM,WAAiB;EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,eACE;MAAC;MAAA;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,4BAA4B,CAAC,EAAE,eAAAD,eAAc,UAC3C,0BAAA,8BAAA,EACE,UAAA;cAAA;YAAC;YAAA;cACE,GAAG;cACJ,KAAK;cAEL;YAAA;UACF;UACCA,sBACC;YAAC;YAAA;cAEC;YAAA;UACF;QAAA,EAAA,CAEJ;MAAA;IAEJ;EAEJ;AACF;AAEA,SAAS,cAAc;AAMvB,IAAM,iBAAiB;AAYvB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,iBAAiB,YAAY,GAAG,eAAe,IAAI;AAC3D,UAAM,UAAU,mBAAmB,gBAAgB,eAAe;AAClE,eACE;MAAC;MAAA;QACC,SAAS,cAAc,gBAAgB,QAAQ,OAAO,KAAK,QAAQ,YAAY;QAE/E,cAAA;UAAC,UAAU;UAAV;YACC,cAAYE,UAAS,QAAQ,OAAO;YACpC,iBAAe,QAAQ,WAAW,KAAK;YACtC,GAAG;YACJ,KAAK;YACL,OAAO,EAAE,eAAe,QAAQ,GAAG,MAAM,MAAM;UAAA;QACjD;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,oBAAoB;AAK1B,IAAM,sBAA4B;EAChC,CAAC,EAAE,iBAAiB,GAAG,MAAM,GAA0C,iBAAiB;AACtF,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,mBAAmB,mBAAmB,eAAe;AAEzD,UAAM,eAAe,gBAAgB,cAAc,cAAc;AACjE,UAAM,cAAc,YAAY,OAAO;AACvC,UAAM,cAAc,QAAQ,OAAO;AAG7B,IAAA,iBAAU,MAAM;AACpB,YAAM,QAAQ;AACd,UAAI,CAAC,MAAO;AAEZ,YAAM,aAAa,OAAO,iBAAiB;AAC3C,YAAM,aAAa,OAAO;QACxB;QACA;MACF;AACA,YAAM,aAAa,WAAW;AAE9B,YAAM,UAAU,CAAC,iCAAiC;AAClD,UAAI,gBAAgB,WAAW,YAAY;AACzC,cAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC;AAC5C,cAAM,gBAAgB,gBAAgB,OAAO;AAC7C,mBAAW,KAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjE,cAAM,cAAc,KAAK;MAC3B;IACF,GAAG,CAAC,aAAa,aAAa,SAAS,gCAAgC,CAAC;AAExE,UAAM,oBAA0B,cAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;AACjF,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,eAAW;QACX,gBAAgB,kBAAkB,kBAAkB;QACpD;QACA;QACA;QACA;QACA;QACC,GAAG;QACJ,UAAU;QACV,KAAK;QACL,OAAO;UACL,GAAG,MAAM;UACT,GAAG;UACH,UAAU;UACV,eAAe;UACf,SAAS;UACT,QAAQ;;;;UAIR,WAAW;QACb;MAAA;IACF;EAEJ;AACF;AAEA,oBAAoB,cAAc;AAIlC,SAAS,WAAW,OAAkD;AACpE,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,gBAAgB,SAAoD;AAC3E,SAAO,YAAY;AACrB;AAEA,SAASA,UAAS,SAAuB;AACvC,SAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpYA,IAAAC,SAAuB;AA+DjB,IAAAC,sBAAA;AA7CN,IAAM,oBAAoB;AAG1B,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,mBAAmB;EAC/F;AACF,CAAC;AACD,IAAM,eAAe,gBAAgB;AAQrC,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,yBAAkD,iBAAiB;AASrE,IAAM,cAA0C,CAAC,UAAyC;AACxF,QAAM,EAAE,oBAAoB,UAAU,cAAc,KAAK,QAAQ,KAAK,IAAI;AAC1E,QAAM,CAAC,MAAM,OAAO,IAAU,gBAAS,KAAK;AAC5C,QAAM,YAAY,aAAa,kBAAkB;AACjD,QAAM,uBAAuB,eAAe,YAAY;AAExD,QAAM,mBAAyB;IAC7B,CAACC,UAAkB;AACjB,cAAQA,KAAI;AACZ,2BAAqBA,KAAI;IAC3B;IACA,CAAC,oBAAoB;EACvB;AAEA,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA,cAAc;MACd;MAEA,cAAA;QAAeC;QAAd;UACE,GAAG;UACJ;UACA;UACA,cAAc;UACd;UAEC;QAAA;MACH;IAAA;EACF;AAEJ;AAEA,YAAY,cAAc;AAM1B,IAAMC,gBAAe;AAQrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,WAAW,OAAO,GAAG,aAAa,IAAI;AAClE,UAAM,UAAU,sBAAsBA,eAAc,kBAAkB;AACtE,UAAM,YAAY,aAAa,kBAAkB;AACjD,UAAM,WAAiB,cAAc,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC;AACnD,UAAM,aAAmB,cAAO;MAC9B,uBAAuB,MAAM,QAAQ,SAAS,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,SAAS,QAAQ,CAAC;IAC5F,CAAC;AACD,UAAM,oBAA0B,cAAO,CAAC;AACxC,UAAM,iBAAuB;MAC3B,MAAM,OAAO,aAAa,kBAAkB,OAAO;MACnD,CAAC;IACH;AACA,UAAM,aAAa,CAAC,UAAiD;AACnE,eAAS,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;AACxD,cAAQ,aAAa,IAAI;IAC3B;AAEM,IAAA,iBAAU,MAAM,gBAAgB,CAAC,cAAc,CAAC;AAChD,IAAA,iBAAU,MAAM,MAAM,YAAY,eAAe,IAAI,CAAC,UAAU,cAAc,CAAC;AAErF,eACE,0BAAA,8BAAA,EACE,UAAA;UAAA,yBAAe,SAAd,EAAsB,GAAG,WAAW,WAAA,CAAwB;UAC7D;QAAC,UAAU;QAAV;UACC,cAAY,QAAQ,OAAO,SAAS;UACpC,iBAAe,WAAW,KAAK;UAC9B,GAAG;UACJ,KAAK;UAEL,OAAO,EAAE,oBAAoB,QAAQ,GAAG,MAAM,MAAM;UAEpD,eACE,WACI,MAAM,gBACN,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAGnD,2BAAe;AACf,uBAAW,KAAK;AAChB,kBAAM,eAAe;UACvB,CAAC;UAEP,eACE,WACI,MAAM,gBACN;YACE,MAAM;YACN,eAAe,CAAC,UAAU;AAExB,6BAAe;AACf,gCAAkB,UAAU,OAAO,WAAW,MAAM,WAAW,KAAK,GAAG,GAAG;YAC5E,CAAC;UACH;UAEN,eACE,WACI,MAAM,gBACN,qBAAqB,MAAM,eAAe,eAAe,cAAc,CAAC;UAE9E,iBACE,WACI,MAAM,kBACN,qBAAqB,MAAM,iBAAiB,eAAe,cAAc,CAAC;UAEhF,aACE,WACI,MAAM,cACN,qBAAqB,MAAM,aAAa,eAAe,cAAc,CAAC;QAAA;MAE9E;IAAA,EAAA,CACF;EAEJ;AACF;AAEA,mBAAmB,cAAcA;AAMjC,IAAMC,eAAc;AAKpB,IAAM,oBAAsD,CAC1D,UACG;AACH,QAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAeC,SAAd,EAAsB,GAAG,WAAY,GAAG,YAAA,CAAa;AAC/D;AAEA,kBAAkB,cAAcD;AAMhC,IAAME,gBAAe;AAOrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,UAAU,sBAAsBA,eAAc,kBAAkB;AACtE,UAAM,YAAY,aAAa,kBAAkB;AACjD,UAAM,0BAAgC,cAAO,KAAK;AAElD,eACE;MAAe;MAAd;QACE,GAAG;QACH,GAAG;QACJ,KAAK;QACL,MAAK;QACL,YAAY;QACZ,OAAM;QACN,kBAAkB,CAAC,UAAU;AAC3B,gBAAM,mBAAmB,KAAK;AAE9B,cAAI,CAAC,MAAM,oBAAoB,wBAAwB,SAAS;AAC9D,kBAAM,eAAe;UACvB;AAEA,kCAAwB,UAAU;QACpC;QACA,mBAAmB,CAAC,UAAU;AAC5B,gBAAM,oBAAoB,KAAK;AAE/B,cAAI,CAAC,MAAM,oBAAoB,CAAC,QAAQ,MAAO,yBAAwB,UAAU;QACnF;QACA,OAAO;UACL,GAAG,MAAM;;UAET,GAAG;YACD,iDAAiD;YACjD,gDAAgD;YAChD,iDAAiD;YACjD,sCAAsC;YACtC,uCAAuC;UACzC;QACF;MAAA;IACF;EAEJ;AACF;AAEA,mBAAmB,cAAcA;AAMjC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,eAAO,yBAAe,OAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,aAAa;AAMnB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,eAAO,yBAAeC,QAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAMC,aAAY;AAMlB,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,oBAAoB,GAAG,UAAU,IAAI;AAC7C,UAAM,YAAY,aAAa,kBAAkB;AACjD,eAAO,yBAAe,OAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,aAAA,CAAc;EAC9E;AACF;AAEA,gBAAgB,cAAcA;AAM9B,IAAM,qBAAqB;AAM3B,IAAM,0BAAgC,kBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,oBAAoB,GAAG,kBAAkB,IAAI;AACrD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,cAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,aAAA,CAAc;AAC9F,CAAC;AAED,wBAAwB,cAAc;AAMtC,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,kBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,gBAAgB,IAAI;AACnD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,YAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,aAAA,CAAc;AAC1F,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,kBAAkB;AAMxB,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,oBAAoB,GAAG,eAAe,IAAI;AAClD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;AACxF,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAMC,kBAAiB;AAMvB,IAAM,2BAAiC,kBAGrC,CAAC,OAAmD,iBAAiB;AACrE,QAAM,EAAE,oBAAoB,GAAG,mBAAmB,IAAI;AACtD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,eAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,aAAA,CAAc;AAChG,CAAC;AAED,yBAAyB,cAAcA;AAMvC,IAAM,iBAAiB;AAMvB,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,oBAAoB,GAAG,eAAe,IAAI;AAClD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;AACxF,CAAC;AAED,qBAAqB,cAAc;AAMnC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,YAAY,aAAa,kBAAkB;AACjD,eAAO,yBAAe,QAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,WAAW;AASjB,IAAM,iBAAgD,CAAC,UAA4C;AACjG,QAAM,EAAE,oBAAoB,UAAU,cAAc,MAAM,UAAU,YAAY,IAAI;AACpF,QAAM,YAAY,aAAa,kBAAkB;AACjD,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,aACE,yBAAe,KAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,SAAA,CACH;AAEJ;AAEA,eAAe,cAAc;AAM7B,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,kBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,YAAY,aAAa,kBAAkB;AACjD,aAAO,yBAAe,YAAd,EAA0B,GAAG,WAAY,GAAG,kBAAkB,KAAK,aAAA,CAAc;AAC3F,CAAC;AAED,sBAAsB,cAAc;AAMpC,IAAM,mBAAmB;AAMzB,IAAM,wBAA8B,kBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,oBAAoB,GAAG,gBAAgB,IAAI;AACnD,QAAM,YAAY,aAAa,kBAAkB;AAEjD,aACE;IAAe;IAAd;MACE,GAAG;MACH,GAAG;MACJ,KAAK;MACL,OAAO;QACL,GAAG,MAAM;;QAET,GAAG;UACD,iDAAiD;UACjD,gDAAgD;UAChD,iDAAiD;UACjD,sCAAsC;UACtC,uCAAuC;QACzC;MACF;IAAA;EACF;AAEJ,CAAC;AAED,sBAAsB,cAAc;AAIpC,SAAS,eAAkB,SAAqE;AAC9F,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAEA,IAAMC,SAAO;AACb,IAAMC,WAAU;AAChB,IAAMN,WAAS;AACf,IAAMO,YAAU;AAChB,IAAMC,SAAQ;AACd,IAAMN,UAAQ;AACd,IAAMO,SAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,UAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa;;;;;;;;;;;;;;;;;;;;;AChiBnB,IAAAC,SAAuB;AAkKb,IAAAC,sBAAA;AAvJV,IAAM,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,MAAM;AAMtE,IAAM,YAAY;AAmBlB,IAAM,CAAC,oBAAoB,oBAAoB,IAC7C,kBAA0C,SAAS;AASrD,IAAM,CAAC,yBAAyB,yBAAyB,IACvD,kBAA+C,SAAS;AAQ1D,IAAM,OAAa;EACjB,CAAC,OAA+B,iBAAiB;AAC/C,UAAM,EAAE,aAAa,sBAAsB,MAAM;IAAC,GAAG,GAAG,UAAU,IAAI;AACtE,UAAM,UAAgB,cAAwB,IAAI;AAClD,UAAM,kBAAkB,gBAAgB,cAAc,OAAO;AAG7D,UAAM,CAAC,aAAa,cAAc,IAAU,gBAAsB,CAAC,CAAC;AACpE,UAAM,mBAAqE;MACzE,CAAC,cAAc,YAAY,SAAS;MACpC,CAAC,WAAW;IACd;AACA,UAAM,4BACE;MACJ,CAAC,WAAW,aACV,eAAe,CAAC,qBAAqB;QACnC,GAAG;QACH,CAAC,SAAS,GAAG,EAAE,GAAI,gBAAgB,SAAS,KAAK,CAAC,GAAI,GAAG,SAAS;MACpE,EAAE;MACJ,CAAC;IACH;AACF,UAAM,6BACE,mBAAY,CAAC,cAAc;AAC/B,qBAAe,CAAC,qBAAqB,EAAE,GAAG,iBAAiB,CAAC,SAAS,GAAG,OAAU,EAAE;AACpF,yBAAmB,CAAC,yBAAyB,EAAE,GAAG,qBAAqB,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE;IAC3F,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,yBAAyB,0BAA0B,IAClD,gBAAkC,CAAC,CAAC;AAC5C,UAAM,+BACE;MACJ,CAAC,cAAc,wBAAwB,SAAS,KAAK,CAAC;MACtD,CAAC,uBAAuB;IAC1B;AACF,UAAM,8BACE,mBAAY,CAAC,WAAW,iBAAiB;AAC7C,iCAA2B,CAAC,iCAAiC;QAC3D,GAAG;QACH,CAAC,SAAS,GAAG,CAAC,GAAI,4BAA4B,SAAS,KAAK,CAAC,GAAI,YAAY;MAC/E,EAAE;IACJ,GAAG,CAAC,CAAC;AACP,UAAM,iCACE,mBAAY,CAAC,WAAW,mBAAmB;AAC/C,iCAA2B,CAAC,iCAAiC;QAC3D,GAAG;QACH,CAAC,SAAS,IAAI,4BAA4B,SAAS,KAAK,CAAC,GAAG;UAC1D,CAAC,iBAAiB,aAAa,OAAO;QACxC;MACF,EAAE;IACJ,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,iBAAiB,kBAAkB,IAAU,gBAA0B,CAAC,CAAC;AAChF,UAAM,uBAA6E;MACjF,CAAC,cAAc,gBAAgB,SAAS,KAAK,CAAC;MAC9C,CAAC,eAAe;IAClB;AACA,UAAM,gCACE,mBAAY,CAAC,WAAW,iBAAiB;AAC7C,yBAAmB,CAAC,yBAAyB;QAC3C,GAAG;QACH,CAAC,SAAS,GAAG,EAAE,GAAI,oBAAoB,SAAS,KAAK,CAAC,GAAI,GAAG,aAAa;MAC5E,EAAE;IACJ,GAAG,CAAC,CAAC;AAGP,UAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAwB,CAAC,CAAC;AAC1E,UAAM,0BACE,mBAAY,CAAC,WAAW,OAAO;AACnC,uBAAiB,CAAC,sBAAsB;AACtC,cAAM,sBAAsB,IAAI,IAAI,kBAAkB,SAAS,CAAC,EAAE,IAAI,EAAE;AACxE,eAAO,EAAE,GAAG,mBAAmB,CAAC,SAAS,GAAG,oBAAoB;MAClE,CAAC;IACH,GAAG,CAAC,CAAC;AACP,UAAM,6BACE,mBAAY,CAAC,WAAW,OAAO;AACnC,uBAAiB,CAAC,sBAAsB;AACtC,cAAM,sBAAsB,IAAI,IAAI,kBAAkB,SAAS,CAAC;AAChE,4BAAoB,OAAO,EAAE;AAC7B,eAAO,EAAE,GAAG,mBAAmB,CAAC,SAAS,GAAG,oBAAoB;MAClE,CAAC;IACH,GAAG,CAAC,CAAC;AACP,UAAM,sBACE;MACJ,CAAC,cAAc,MAAM,KAAK,cAAc,SAAS,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK;MACvE,CAAC,aAAa;IAChB;AAEF,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,uBAAuB;QACvB;QACA,8BAA8B;QAC9B,iCAAiC;QACjC;QACA,2BAA2B;QAC3B,wBAAwB;QAExB,cAAA;UAAC;UAAA;YACC,OAAO;YACP,qBAAqB;YACrB,wBAAwB;YACxB;YAEA,cAAA;cAAC,UAAU;cAAV;gBACE,GAAG;gBACJ,KAAK;gBAEL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,wBAAM,sBAAsB,uBAAuB,MAAM,aAAa;AACtE,sBAAI,wBAAwB,MAAM,OAAQ,qBAAoB,MAAM;AAGpE,wBAAM,eAAe;gBACvB,CAAC;gBAED,UAAU,qBAAqB,MAAM,UAAU,qBAAqB;kBAClE,0BAA0B;gBAC5B,CAAC;gBAED,SAAS,qBAAqB,MAAM,SAAS,mBAAmB;cAAA;YAClE;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,KAAK,cAAc;AAMnB,IAAM,aAAa;AAOnB,IAAM,CAAC,mBAAmB,mBAAmB,IAC3C,kBAAyC,UAAU;AASrD,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,MAAM,gBAAgB,OAAO,GAAG,WAAW,IAAI;AACpE,UAAM,oBAAoB,qBAAqB,YAAY,WAAW;AACtE,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,KAAK,MAAM;AAEjB,eACE,yBAAC,mBAAA,EAAkB,OAAO,aAAa,IAAQ,MAAY,eACzD,cAAA;MAAC,UAAU;MAAV;QACC,cAAY,kBAAkB,UAAU,aAAa;QACrD,gBAAc,oBAAoB,UAAU,aAAa;QACxD,GAAG;QACJ,KAAK;MAAA;IACP,EAAA,CACF;EAEJ;AACF;AAEA,UAAU,cAAc;AAMxB,IAAMC,cAAa;AAMnB,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM,EAAE,aAAa,GAAG,WAAW,IAAI;AACvC,UAAM,oBAAoB,qBAAqBA,aAAY,WAAW;AACtE,UAAM,eAAe,oBAAoBA,aAAY,WAAW;AAChE,UAAM,UAAU,WAAW,WAAW,aAAa;AACnD,UAAM,WAAW,kBAAkB,iBAAiB,aAAa,IAAI;AAErE,eACE;MAAC;MAAA;QACC,cAAY,kBAAkB,UAAU,aAAa,aAAa;QAClE,gBAAc,oBAAoB,UAAU,aAAa,aAAa;QACrE,GAAG;QACJ,KAAK;QACL;MAAA;IACF;EAEJ;AACF;AAEA,UAAU,cAAcA;AAMxB,IAAM,eAAe;AAMrB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,GAAG,aAAa,IAAI;AAEzC,UAAM,oBAAoB,qBAAqB,cAAc,WAAW;AACxE,UAAM,eAAe,oBAAoB,cAAc,WAAW;AAClE,UAAM,yBAAyB,0BAA0B,cAAc,WAAW;AAElF,UAAM,MAAY,cAA2B,IAAI;AACjD,UAAM,cAAc,gBAAgB,cAAc,GAAG;AACrD,UAAM,OAAO,aAAa,QAAQ,aAAa;AAC/C,UAAM,KAAK,aAAa,MAAM,aAAa;AAC3C,UAAM,uBAAuB,kBAAkB,6BAA6B,IAAI;AAEhF,UAAM,EAAE,uBAAuB,2BAA2B,uBAAuB,IAC/E;AACF,UAAM,wBAA8B;MAClC,OAAO,YAAgC;AAIrC,YAAI,gBAAgB,QAAQ,QAAQ,GAAG;AACrC,gBAAMC,mBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,gCAAsB,MAAMA,gBAAe;AAC3C;QACF;AAKA,cAAM,WAAW,QAAQ,OAAO,IAAI,SAAS,QAAQ,IAAI,IAAI,IAAI,SAAS;AAC1E,cAAM,cAAiC,CAAC,QAAQ,OAAO,QAAQ;AAK/D,cAAM,2BAA0D,CAAC;AACjE,cAAM,2BAA2D,CAAC;AAClE,6BAAqB,QAAQ,CAAC,uBAAuB;AACnD,cAAI,0BAA0B,oBAAoB,WAAW,GAAG;AAC9D,qCAAyB,KAAK,kBAAkB;UAClD,WAAW,yBAAyB,kBAAkB,GAAG;AACvD,qCAAyB,KAAK,kBAAkB;UAClD;QACF,CAAC;AAKD,cAAM,mBAAmB,yBAAyB,IAAI,CAAC,EAAE,IAAAC,KAAI,MAAM,MAAM;AACvE,iBAAO,CAACA,KAAI,MAAM,GAAG,WAAW,CAAC;QACnC,CAAC;AACD,cAAM,uBAAuB,OAAO,YAAY,gBAAgB;AAChE,cAAM,sBAAsB,OAAO,OAAO,oBAAoB,EAAE,KAAK,OAAO;AAC5E,cAAM,iBAAiB;AACvB,gBAAQ,kBAAkB,iBAAiB,0BAA0B,EAAE;AACvE,cAAM,kBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,8BAAsB,MAAM,eAAe;AAC3C,kCAA0B,MAAM,oBAAoB;AAKpD,YAAI,CAAC,uBAAuB,yBAAyB,SAAS,GAAG;AAC/D,gBAAM,uBAAuB,yBAAyB;YAAI,CAAC,EAAE,IAAAA,KAAI,MAAM,MACrE,MAAM,GAAG,WAAW,EAAE,KAAK,CAAC,YAAY,CAACA,KAAI,OAAO,CAAU;UAChE;AACA,gBAAM,oBAAoB,MAAM,QAAQ,IAAI,oBAAoB;AAChE,gBAAM,wBAAwB,OAAO,YAAY,iBAAiB;AAClE,gBAAM,uBAAuB,OAAO,OAAO,qBAAqB,EAAE,KAAK,OAAO;AAC9E,gBAAMC,kBAAiB;AACvB,kBAAQ,kBAAkBA,kBAAiB,0BAA0B,EAAE;AACvE,gBAAMF,mBAAkB,sBAAsB,QAAQ,QAAQ;AAC9D,gCAAsB,MAAMA,gBAAe;AAC3C,oCAA0B,MAAM,qBAAqB;QACvD;MACF;MACA,CAAC,sBAAsB,MAAM,2BAA2B,qBAAqB;IAC/E;AAEM,IAAA,iBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,UAAI,SAAS;AAGX,cAAM,eAAe,MAAM,sBAAsB,OAAO;AACxD,gBAAQ,iBAAiB,UAAU,YAAY;AAC/C,eAAO,MAAM,QAAQ,oBAAoB,UAAU,YAAY;MACjE;IACF,GAAG,CAAC,qBAAqB,CAAC;AAE1B,UAAM,uBAA6B,mBAAY,MAAM;AACnD,YAAM,UAAU,IAAI;AACpB,UAAI,SAAS;AACX,gBAAQ,kBAAkB,EAAE;AAC5B,+BAAuB,IAAI;MAC7B;IACF,GAAG,CAAC,MAAM,sBAAsB,CAAC;AAG3B,IAAA,iBAAU,MAAM;AACpB,YAAM,OAAO,IAAI,SAAS;AAC1B,UAAI,MAAM;AACR,aAAK,iBAAiB,SAAS,oBAAoB;AACnD,eAAO,MAAM,KAAK,oBAAoB,SAAS,oBAAoB;MACrE;IACF,GAAG,CAAC,oBAAoB,CAAC;AAGnB,IAAA,iBAAU,MAAM;AACpB,YAAM,UAAU,IAAI;AACpB,YAAM,OAAO,SAAS,QAAQ,MAAM;AACpC,UAAI,QAAQ,aAAa,eAAe;AACtC,cAAM,sBAAsB,uBAAuB,IAAI;AACvD,YAAI,wBAAwB,QAAS,qBAAoB,MAAM;MACjE;IACF,GAAG,CAAC,aAAa,aAAa,CAAC;AAE/B,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AAExD,eACE;MAAC,UAAU;MAAV;QACC,cAAY,kBAAkB,UAAU,aAAa,aAAa;QAClE,gBAAc,oBAAoB,UAAU,aAAa,aAAa;QACtE,gBAAc,aAAa,gBAAgB,OAAO;QAClD,oBAAkB,uBAAuB,oBAAoB,IAAI;QAEjE,OAAM;QACL,GAAG;QACJ,KAAK;QACL;QACA;QACA,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,UAAU,MAAM;AACtB,gCAAsB,OAAO;QAC/B,CAAC;QACD,UAAU,qBAAqB,MAAM,UAAU,CAAC,WAAW;AAEzD,+BAAqB;QACvB,CAAC;MAAA;IACH;EAEJ;AACF;AAEA,YAAY,cAAc;AAoB1B,IAAM,0BAA0B;AAChC,IAAM,4BAAyE;EAC7E,UAAU;EACV,iBAAiB;EACjB,eAAe;EACf,gBAAgB;EAChB,cAAc;EACd,SAAS;EACT,UAAU;EACV,cAAc;EACd,OAAO;EACP,cAAc;AAChB;AAEA,IAAM,eAAe;AASrB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,OAAO,MAAM,UAAU,GAAG,aAAa,IAAI;AACnD,UAAM,eAAe,oBAAoB,cAAc,MAAM,WAAW;AACxE,UAAM,OAAO,YAAY,aAAa;AAEtC,QAAI,UAAU,QAAW;AACvB,iBACE,yBAAC,iBAAA,EAAiB,GAAG,cAAc,KAAK,cAAc,MACnD,UAAA,MAAM,YAAY,wBAAA,CACrB;IAEJ,WAAW,OAAO,UAAU,YAAY;AACtC,iBAAO,yBAAC,mBAAA,EAAkB,OAAe,GAAG,cAAc,KAAK,cAAc,KAAA,CAAY;IAC3F,OAAO;AACL,iBAAO,yBAAC,oBAAA,EAAmB,OAAe,GAAG,cAAc,KAAK,cAAc,KAAA,CAAY;IAC5F;EACF;AACF;AAEA,YAAY,cAAc;AAS1B,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,OAAO,aAAa,OAAO,MAAM,UAAU,GAAG,aAAa,IAAI;AACvE,UAAM,oBAAoB,qBAAqB,cAAc,aAAa,WAAW;AACrF,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,UAAU,cAAc,WAAW,KAAK;AAE9C,QAAI,SAAS;AACX,iBACE,yBAAC,iBAAA,EAAgB,KAAK,cAAe,GAAG,cAAc,MACnD,UAAA,YAAY,0BAA0B,KAAK,EAAA,CAC9C;IAEJ;AAEA,WAAO;EACT;AACF;AASA,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,OAAO,aAAa,OAAO,MAAM,IAAI,QAAQ,UAAU,GAAG,aAAa,IAAI;AACnF,UAAM,oBAAoB,qBAAqB,cAAc,aAAa,WAAW;AACrF,UAAM,MAAY,cAAiC,IAAI;AACvD,UAAM,cAAc,gBAAgB,cAAc,GAAG;AACrD,UAAM,MAAM,MAAM;AAClB,UAAM,KAAK,UAAU;AAErB,UAAM,qBAA2B,eAAQ,OAAO,EAAE,IAAI,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC;AAC3E,UAAM,EAAE,8BAA8B,gCAAgC,IAAI;AACpE,IAAA,iBAAU,MAAM;AACpB,mCAA6B,MAAM,kBAAkB;AACrD,aAAO,MAAM,gCAAgC,MAAM,mBAAmB,EAAE;IAC1E,GAAG,CAAC,oBAAoB,MAAM,8BAA8B,+BAA+B,CAAC;AAE5F,UAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,UAAM,eAAe,kBAAkB,qBAAqB,IAAI;AAChE,UAAM,yBAAyB,aAAa,EAAE;AAC9C,UAAM,UACJ,cAAe,YAAY,CAAC,gBAAgB,QAAQ,KAAK;AAE3D,QAAI,SAAS;AACX,iBACE,yBAAC,iBAAA,EAAgB,IAAQ,KAAK,aAAc,GAAG,cAAc,MAC1D,UAAA,YAAY,wBAAA,CACf;IAEJ;AAEA,WAAO;EACT;AACF;AAQA,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,aAAa,IAAI,QAAQ,MAAM,GAAG,aAAa,IAAI;AAC3D,UAAM,yBAAyB,0BAA0B,cAAc,WAAW;AAClF,UAAM,MAAM,MAAM;AAClB,UAAM,KAAK,UAAU;AAErB,UAAM,EAAE,qBAAqB,uBAAuB,IAAI;AAClD,IAAA,iBAAU,MAAM;AACpB,0BAAoB,MAAM,EAAE;AAC5B,aAAO,MAAM,uBAAuB,MAAM,EAAE;IAC9C,GAAG,CAAC,MAAM,IAAI,qBAAqB,sBAAsB,CAAC;AAE1D,eAAO,yBAAC,UAAU,MAAV,EAAe,IAAS,GAAG,cAAc,KAAK,aAAA,CAAc;EACtE;AACF;AAMA,IAAM,sBAAsB;AAO5B,IAAM,oBAAoB,CAAC,UAA+C;AACxE,QAAM,EAAE,aAAa,MAAM,UAAU,SAAS,IAAI;AAClD,QAAM,oBAAoB,qBAAqB,qBAAqB,WAAW;AAC/E,QAAM,eAAe,oBAAoB,qBAAqB,WAAW;AACzE,QAAM,OAAO,YAAY,aAAa;AACtC,QAAM,WAAW,kBAAkB,iBAAiB,IAAI;AACxD,aAAO,yBAAA,8BAAA,EAAG,UAAA,SAAS,QAAQ,EAAA,CAAE;AAC/B;AAEA,kBAAkB,cAAc;AAMhC,IAAM,cAAc;AAMpB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,aAAa,GAAG,YAAY,IAAI;AACxC,eAAO,yBAAC,UAAU,QAAV,EAAiB,MAAK,UAAU,GAAG,aAAa,KAAK,aAAA,CAAc;EAC7E;AACF;AAEA,WAAW,cAAc;AAazB,SAAS,sBAAsB,UAAyB;AACtD,QAAM,SAAc,CAAC;AACrB,aAAW,OAAO,UAAU;AAC1B,WAAO,GAAG,IAAI,SAAS,GAAuB;EAChD;AACA,SAAO;AACT;AAEA,SAAS,cAAc,SAAsC;AAC3D,SAAO,mBAAmB;AAC5B;AAEA,SAAS,cAAc,SAAsD;AAC3E,SAAO,cAAc;AACvB;AAEA,SAAS,UAAU,SAAsB;AACvC,SACE,cAAc,OAAO,MACpB,QAAQ,SAAS,UAAU,SAAS,QAAQ,aAAa,cAAc,MAAM;AAElF;AAEA,SAAS,uBAAuB,MAAgD;AAC9E,QAAM,WAAW,KAAK;AACtB,QAAM,CAAC,mBAAmB,IAAI,MAAM,KAAK,QAAQ,EAAE,OAAO,aAAa,EAAE,OAAO,SAAS;AACzF,SAAO;AACT;AAEA,SAAS,0BACP,OACA,MACkC;AAClC,SAAO,MAAM,MAAM,YAAY,SAAS,mBAAmB,eAAe,MAAM,OAAO,IAAI;AAC7F;AAEA,SAAS,yBAAyB,OAA4D;AAC5F,SAAO,MAAM,MAAM,YAAY,SAAS;AAC1C;AAEA,SAAS,eAAe,MAAgB,MAAsB;AAC5D,SAAO,KAAK,GAAG,IAAI,aAAa;AAClC;AAEA,SAAS,gBAAgB,UAAyB;AAChD,MAAI,QAAQ;AACZ,aAAW,eAAe,UAAU;AAClC,UAAM,MAAM;AACZ,QAAI,QAAQ,WAAW,QAAQ,iBAAiB,SAAS,GAAG,GAAG;AAC7D,cAAQ;AACR;IACF;EACF;AACA,SAAO;AACT;AAEA,SAAS,kBAAkB,UAAqC,eAAwB;AACtF,MAAI,UAAU,UAAU,QAAQ,CAAC,cAAe,QAAO;AACvD,SAAO;AACT;AACA,SAAS,oBAAoB,UAAqC,eAAwB;AACxF,MAAI,UAAU,UAAU,SAAS,cAAe,QAAO;AACvD,SAAO;AACT;AAIA,IAAMG,QAAO;AACb,IAAM,QAAQ;AACd,IAAMC,SAAQ;AACd,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,gBAAgB;AACtB,IAAM,SAAS;;;;;;;;;;;;;;;;;AC1rBf,IAAAC,SAAuB;AA0GjB,IAAAC,sBAAA;AAxFN,IAAI;AAEJ,IAAM,iBAAiB;AAGvB,IAAM,CAAC,wBAAwB,oBAAoB,IAAI,mBAAmB,gBAAgB;EACxF;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAYzC,IAAM,CAAC,mBAAmB,mBAAmB,IAC3C,uBAA8C,cAAc;AAW9D,IAAM,YAAsC,CAAC,UAAuC;AAClF,QAAM;IACJ;IACA;IACA,MAAM;IACN;IACA;IACA,YAAY;IACZ,aAAa;EACf,IAAI;AACJ,QAAM,cAAc,eAAe,gBAAgB;AACnD,QAAM,eAAqB,cAAO,CAAC;AACnC,QAAM,gBAAsB,cAAO,CAAC;AACpC,QAAM,kBAAwB,cAAO,KAAK;AAC1C,QAAM,4BAAkC,cAAO,KAAK;AAEpD,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,QAAM,aAAmB,mBAAY,MAAM;AACzC,iBAAa,cAAc,OAAO;AAClC,iBAAa,UAAU,OAAO,WAAW,MAAM,QAAQ,IAAI,GAAG,SAAS;EACzE,GAAG,CAAC,WAAW,OAAO,CAAC;AAEvB,QAAM,cAAoB,mBAAY,MAAM;AAC1C,iBAAa,aAAa,OAAO;AACjC,QAAI,CAAC,gBAAgB,WAAW,CAAC,0BAA0B,SAAS;AAClE,oBAAc,UAAU,OAAO,WAAW,MAAM,QAAQ,KAAK,GAAG,UAAU;IAC5E;EACF,GAAG,CAAC,YAAY,OAAO,CAAC;AAExB,QAAM,gBAAsB,mBAAY,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,CAAC;AAGjE,EAAA,iBAAU,MAAM;AACpB,WAAO,MAAM;AACX,mBAAa,aAAa,OAAO;AACjC,mBAAa,cAAc,OAAO;IACpC;EACF,GAAG,CAAC,CAAC;AAEL,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA,cAAc;MACd,QAAQ;MACR,SAAS;MACT,WAAW;MACX;MACA;MAEA,cAAA,yBAAiBC,QAAhB,EAAsB,GAAG,aAAc,SAAA,CAAS;IAAA;EACnD;AAEJ;AAEA,UAAU,cAAc;AAMxB,IAAMC,gBAAe;AAMrB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,UAAU,oBAAoBA,eAAc,gBAAgB;AAClE,UAAM,cAAc,eAAe,gBAAgB;AACnD,eACE,yBAAiB,QAAhB,EAAuB,SAAO,MAAE,GAAG,aAClC,cAAA;MAAC,UAAU;MAAV;QACC,cAAY,QAAQ,OAAO,SAAS;QACnC,GAAG;QACJ,KAAK;QACL,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,MAAM,CAAC;QACvF,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,OAAO,CAAC;QACxF,SAAS,qBAAqB,MAAM,SAAS,QAAQ,MAAM;QAC3D,QAAQ,qBAAqB,MAAM,QAAQ,QAAQ,OAAO;QAE1D,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU,MAAM,eAAe,CAAC;MAAA;IAC1F,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAcA;AAM/B,IAAMC,eAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,uBAA2CA,cAAa;EACjG,YAAY;AACd,CAAC;AAgBD,IAAM,kBAAkD,CACtD,UACG;AACH,QAAM,EAAE,kBAAkB,YAAY,UAAU,UAAU,IAAI;AAC9D,QAAM,UAAU,oBAAoBA,cAAa,gBAAgB;AACjE,aACE,yBAAC,gBAAA,EAAe,OAAO,kBAAkB,YACvC,cAAA,yBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA,yBAAC,QAAA,EAAgB,SAAO,MAAC,WACtB,SAAA,CACH,EAAA,CACF,EAAA,CACF;AAEJ;AAEA,gBAAgB,cAAcA;AAM9B,IAAMC,gBAAe;AAWrB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,gBAAgB,iBAAiBA,eAAc,MAAM,gBAAgB;AAC3E,UAAM,EAAE,aAAa,cAAc,YAAY,GAAG,aAAa,IAAI;AACnE,UAAM,UAAU,oBAAoBA,eAAc,MAAM,gBAAgB;AACxE,eACE,yBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACvC,cAAA;MAAC;MAAA;QACC,cAAY,QAAQ,OAAO,SAAS;QACnC,GAAG;QACJ,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,MAAM,CAAC;QACvF,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,QAAQ,OAAO,CAAC;QACxF,KAAK;MAAA;IACP,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAcA;AA+B/B,IAAM,uBAA6B,kBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA,GAAG;EACL,IAAI;AACJ,QAAM,UAAU,oBAAoBA,eAAc,gBAAgB;AAClE,QAAM,cAAc,eAAe,gBAAgB;AACnD,QAAM,MAAY,cAAoC,IAAI;AAC1D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,gBAAS,KAAK;AAE9D,EAAA,iBAAU,MAAM;AACpB,QAAI,kBAAkB;AACpB,YAAM,OAAO,SAAS;AAGtB,+BAAyB,KAAK,MAAM,cAAc,KAAK,MAAM;AAE7D,WAAK,MAAM,aAAa;AACxB,WAAK,MAAM,mBAAmB;AAC9B,aAAO,MAAM;AACX,aAAK,MAAM,aAAa;AACxB,aAAK,MAAM,mBAAmB;MAChC;IACF;EACF,GAAG,CAAC,gBAAgB,CAAC;AAEf,EAAA,iBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,YAAM,kBAAkB,MAAM;AAC5B,4BAAoB,KAAK;AACzB,gBAAQ,0BAA0B,UAAU;AAG5C,mBAAW,MAAM;AACf,gBAAM,eAAe,SAAS,aAAa,GAAG,SAAS,MAAM;AAC7D,cAAI,aAAc,SAAQ,gBAAgB,UAAU;QACtD,CAAC;MACH;AAEA,eAAS,iBAAiB,aAAa,eAAe;AACtD,aAAO,MAAM;AACX,iBAAS,oBAAoB,aAAa,eAAe;AACzD,gBAAQ,gBAAgB,UAAU;AAClC,gBAAQ,0BAA0B,UAAU;MAC9C;IACF;EACF,GAAG,CAAC,QAAQ,2BAA2B,QAAQ,eAAe,CAAC;AAEzD,EAAA,iBAAU,MAAM;AACpB,QAAI,IAAI,SAAS;AACf,YAAM,YAAY,iBAAiB,IAAI,OAAO;AAC9C,gBAAU,QAAQ,CAAC,aAAa,SAAS,aAAa,YAAY,IAAI,CAAC;IACzE;EACF,CAAC;AAED,aACE;IAAC;IAAA;MACC,SAAO;MACP,6BAA6B;MAC7B;MACA;MACA;MACA,gBAAgB,qBAAqB,gBAAgB,CAAC,UAAU;AAC9D,cAAM,eAAe;MACvB,CAAC;MACD,WAAW,QAAQ;MAEnB,cAAA;QAAiB;QAAhB;UACE,GAAG;UACH,GAAG;UACJ,eAAe,qBAAqB,aAAa,eAAe,CAAC,UAAU;AAEzE,gBAAI,MAAM,cAAc,SAAS,MAAM,MAAqB,GAAG;AAC7D,kCAAoB,IAAI;YAC1B;AACA,oBAAQ,gBAAgB,UAAU;AAClC,oBAAQ,0BAA0B,UAAU;UAC9C,CAAC;UACD,KAAK;UACL,OAAO;YACL,GAAG,aAAa;YAChB,YAAY,mBAAmB,SAAS;;YAExC,kBAAkB,mBAAmB,SAAS;;YAE9C,GAAG;cACD,+CAA+C;cAC/C,8CAA8C;cAC9C,+CAA+C;cAC/C,oCAAoC;cACpC,qCAAqC;YACvC;UACF;QAAA;MACF;IAAA;EACF;AAEJ,CAAC;AAMD,IAAMC,cAAa;AAMnB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,kBAAkB,GAAG,WAAW,IAAI;AAC5C,UAAM,cAAc,eAAe,gBAAgB;AACnD,eAAO,yBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EACpF;AACF;AAEA,eAAe,cAAcA;AAI7B,SAAS,aAAgB,cAA0B;AACjD,SAAO,CAAC,UACN,MAAM,gBAAgB,UAAU,SAAY,aAAa;AAC7D;AAMA,SAAS,iBAAiB,WAAwB;AAChD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AAIzB,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AACtE,SAAO;AACT;AAEA,IAAMC,SAAO;AACb,IAAMC,WAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,YAAU;AAChB,IAAMC,UAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Zd,IAAAC,UAAuB;AA0HT,IAAAC,uBAAA;AApGd,IAAM,eAAe;AAGrB,IAAM,CAACC,aAAYC,gBAAeC,sBAAqB,IAAI,iBAGzD,YAAY;AAGd,IAAM,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,cAAc;EAClFA;EACA;AACF,CAAC;AAED,IAAMC,gBAAe,gBAAgB;AACrC,IAAM,2BAA2B,4BAA4B;AAW7D,IAAM,CAAC,wBAAwB,iBAAiB,IAC9C,qBAA0C,YAAY;AAaxD,IAAM,UAAgB;EACpB,CAAC,OAAkC,iBAAiB;AAClD,UAAM;MACJ;MACA,OAAO;MACP;MACA;MACA,OAAO;MACP;MACA,GAAG;IACL,IAAI;AACJ,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,wBAAwB,yBAAyB,cAAc;AACrE,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,UAAU;MACV,aAAa,gBAAgB;MAC7B,QAAQ;IACV,CAAC;AAKD,UAAM,CAAC,kBAAkB,mBAAmB,IAAU,iBAAwB,IAAI;AAElF,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,YAAkB;UAChB,CAACC,WAAU;AACT,qBAASA,MAAK;AACd,gCAAoBA,MAAK;UAC3B;UACA,CAAC,QAAQ;QACX;QACA,aAAmB,oBAAY,MAAM,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC;QAC7D,cAAoB;UAClB,CAACA,WAAU;AACT,qBAAS,CAAC,cAAe,YAAY,KAAKA,MAAM;AAGhD,gCAAoBA,MAAK;UAC3B;UACA,CAAC,QAAQ;QACX;QACA,KAAK;QACL;QAEA,cAAA,0BAACJ,YAAW,UAAX,EAAoB,OAAO,gBAC1B,cAAA,0BAACA,YAAW,MAAX,EAAgB,OAAO,gBACtB,cAAA;UAAkBK;UAAjB;YACC,SAAO;YACN,GAAG;YACJ,aAAY;YACZ;YACA,KAAK;YACL;YACA,0BAA0B;YAE1B,cAAA,0BAAC,UAAU,KAAV,EAAc,MAAK,WAAW,GAAG,cAAc,KAAK,aAAA,CAAc;UAAA;QACrE,EAAA,CACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,QAAQ,cAAc;AAMtB,IAAM,YAAY;AAUlB,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,qBAA8C,SAAS;AAOzD,IAAM,cAAc,CAAC,UAAyC;AAC5D,QAAM,EAAE,gBAAgB,OAAO,WAAW,GAAG,UAAU,IAAI;AAC3D,QAAM,YAAY,MAAM;AAGxB,QAAM,QAAQ,aAAa,aAAa;AACxC,QAAM,UAAU,kBAAkB,WAAW,cAAc;AAC3D,QAAM,YAAYF,cAAa,cAAc;AAC7C,QAAM,aAAmB,eAA8B,IAAI;AAC3D,QAAM,4BAAkC,eAAO,KAAK;AACpD,QAAM,OAAO,QAAQ,UAAU;AAEzB,EAAA,kBAAU,MAAM;AACpB,QAAI,CAAC,KAAM,2BAA0B,UAAU;EACjD,GAAG,CAAC,IAAI,CAAC;AAET,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA,WAAW,MAAM;MACjB;MACA,WAAW,MAAM;MACjB;MAEA,cAAA;QAAeG;QAAd;UACE,GAAG;UACJ;UACA,cAAc,CAACC,UAAS;AAGtB,gBAAI,CAACA,MAAM,SAAQ,YAAY;UACjC;UACA,OAAO;UACP,KAAK,QAAQ;UACZ,GAAG;QAAA;MACN;IAAA;EACF;AAEJ;AAEA,YAAY,cAAc;AAM1B,IAAMC,gBAAe;AAMrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,WAAW,OAAO,GAAG,aAAa,IAAI;AAC9D,UAAM,wBAAwB,yBAAyB,cAAc;AACrE,UAAM,YAAYL,cAAa,cAAc;AAC7C,UAAM,UAAU,kBAAkBK,eAAc,cAAc;AAC9D,UAAM,cAAc,sBAAsBA,eAAc,cAAc;AACtE,UAAM,MAAY,eAA8B,IAAI;AACpD,UAAM,eAAe,gBAAgB,cAAc,KAAK,YAAY,UAAU;AAC9E,UAAM,CAAC,WAAW,YAAY,IAAU,iBAAS,KAAK;AACtD,UAAM,OAAO,QAAQ,UAAU,YAAY;AAE3C,eACE,0BAACR,YAAW,UAAX,EAAoB,OAAO,gBAAgB,OAAO,YAAY,OAAO,UACpE,cAAA;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ,WAAW,CAAC;QACZ,WAAW,YAAY;QAEvB,cAAA,0BAAe,SAAd,EAAqB,SAAO,MAAE,GAAG,WAChC,cAAA;UAAC,UAAU;UAAV;YACC,MAAK;YACL,MAAK;YACL,IAAI,YAAY;YAChB,iBAAc;YACd,iBAAe;YACf,iBAAe,OAAO,YAAY,YAAY;YAC9C,oBAAkB,YAAY,KAAK;YACnC,cAAY,OAAO,SAAS;YAC5B,iBAAe,WAAW,KAAK;YAC/B;YACC,GAAG;YACJ,KAAK;YACL,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAGlE,kBAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,wBAAQ,WAAW,YAAY,KAAK;AAGpC,oBAAI,CAAC,KAAM,OAAM,eAAe;cAClC;YACF,CAAC;YACD,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM;AAC/D,oBAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,kBAAI,eAAe,CAAC,MAAM;AACxB,wBAAQ,WAAW,YAAY,KAAK;AACpC,oBAAI,SAAS,MAAM;cACrB;YACF,CAAC;YACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,SAAU;AACd,kBAAI,CAAC,SAAS,GAAG,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,aAAa,YAAY,KAAK;AAC9E,kBAAI,MAAM,QAAQ,YAAa,SAAQ,WAAW,YAAY,KAAK;AAGnE,kBAAI,CAAC,SAAS,KAAK,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnD,4BAAY,0BAA0B,UAAU;AAChD,sBAAM,eAAe;cACvB;YACF,CAAC;YACD,SAAS,qBAAqB,MAAM,SAAS,MAAM,aAAa,IAAI,CAAC;YACrE,QAAQ,qBAAqB,MAAM,QAAQ,MAAM,aAAa,KAAK,CAAC;UAAA;QACtE,EAAA,CACF;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,eAAe,cAAcQ;AAM7B,IAAMC,eAAc;AAKpB,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,QAAM,YAAYN,cAAa,cAAc;AAC7C,aAAO,0BAAeO,SAAd,EAAsB,GAAG,WAAY,GAAG,YAAA,CAAa;AAC/D;AAEA,cAAc,cAAcD;AAM5B,IAAME,gBAAe;AAMrB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,gBAAgB,QAAQ,SAAS,GAAG,aAAa,IAAI;AAC7D,UAAM,YAAYR,cAAa,cAAc;AAC7C,UAAM,UAAU,kBAAkBQ,eAAc,cAAc;AAC9D,UAAM,cAAc,sBAAsBA,eAAc,cAAc;AACtE,UAAM,WAAWV,eAAc,cAAc;AAC7C,UAAM,0BAAgC,eAAO,KAAK;AAElD,eACE;MAAe;MAAd;QACC,IAAI,YAAY;QAChB,mBAAiB,YAAY;QAC7B,8BAA2B;QAC1B,GAAG;QACH,GAAG;QACJ,KAAK;QACL;QACA,kBAAkB,qBAAqB,MAAM,kBAAkB,CAAC,UAAU;AACxE,gBAAM,cAAc,QAAQ,QAAQ,KAAK;AACzC,cAAI,CAAC,eAAe,CAAC,wBAAwB,SAAS;AACpD,wBAAY,WAAW,SAAS,MAAM;UACxC;AAEA,kCAAwB,UAAU;AAElC,gBAAM,eAAe;QACvB,CAAC;QACD,gBAAgB,qBAAqB,MAAM,gBAAgB,CAAC,UAAU;AACpE,gBAAM,SAAS,MAAM;AACrB,gBAAM,mBAAmB,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS,SAAS,MAAM,CAAC;AACrF,cAAI,iBAAkB,OAAM,eAAe;QAC7C,CAAC;QACD,mBAAmB,qBAAqB,MAAM,mBAAmB,MAAM;AACrE,kCAAwB,UAAU;QACpC,CAAC;QACD,cAAc,CAAC,UAAU;AACvB,cAAI,CAAC,YAAY,0BAA0B,QAAS,OAAM,eAAe;QAC3E;QACA,WAAW;UACT,MAAM;UACN,CAAC,UAAU;AACT,gBAAI,CAAC,cAAc,WAAW,EAAE,SAAS,MAAM,GAAG,GAAG;AACnD,oBAAM,SAAS,MAAM;AACrB,oBAAM,qBAAqB,OAAO,aAAa,+BAA+B;AAC9E,oBAAM,yBACJ,OAAO,QAAQ,8BAA8B,MAAM,MAAM;AAE3D,oBAAM,cAAc,QAAQ,QAAQ,QAAQ,eAAe;AAC3D,oBAAM,YAAY,gBAAgB,MAAM;AACxC,oBAAM,YAAY,CAAC;AAGnB,kBAAI,aAAa,mBAAoB;AAErC,kBAAI,0BAA0B,UAAW;AAEzC,oBAAM,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,QAAQ;AACxD,kBAAI,kBAAkB,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;AACpD,kBAAI,UAAW,iBAAgB,QAAQ;AAEvC,oBAAM,eAAe,gBAAgB,QAAQ,YAAY,KAAK;AAE9D,gCAAkB,QAAQ,OACtB,UAAU,iBAAiB,eAAe,CAAC,IAC3C,gBAAgB,MAAM,eAAe,CAAC;AAE1C,oBAAM,CAAC,SAAS,IAAI;AACpB,kBAAI,UAAW,SAAQ,WAAW,SAAS;YAC7C;UACF;UACA,EAAE,0BAA0B,MAAM;QACpC;QACA,OAAO;UACL,GAAG,MAAM;;UAET,GAAG;YACD,4CAA4C;YAC5C,2CAA2C;YAC3C,4CAA4C;YAC5C,iCAAiC;YACjC,kCAAkC;UACpC;QACF;MAAA;IACF;EAEJ;AACF;AAEA,eAAe,cAAcU;AAM7B,IAAMC,cAAa;AAMnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAYT,cAAa,cAAc;AAC7C,eAAO,0BAAe,OAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAcS;AAM3B,IAAMC,cAAa;AAMnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAYV,cAAa,cAAc;AAC7C,eAAO,0BAAeW,QAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAcD;AAM3B,IAAME,aAAY;AAMlB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,gBAAgB,GAAG,UAAU,IAAI;AACzC,UAAM,YAAYZ,cAAa,cAAc;AAC7C,eAAO,0BAAe,OAAd,EAAoB,GAAG,WAAY,GAAG,WAAW,KAAK,aAAA,CAAc;EAC9E;AACF;AAEA,YAAY,cAAcY;AAM1B,IAAMC,sBAAqB;AAM3B,IAAM,sBAA4B;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,gBAAgB,GAAG,kBAAkB,IAAI;AACjD,UAAM,YAAYb,cAAa,cAAc;AAC7C,eAAO,0BAAe,cAAd,EAA4B,GAAG,WAAY,GAAG,mBAAmB,KAAK,aAAA,CAAc;EAC9F;AACF;AAEA,oBAAoB,cAAca;AAMlC,IAAMC,oBAAmB;AAMzB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAYd,cAAa,cAAc;AAC7C,eAAO,0BAAe,YAAd,EAA0B,GAAG,WAAY,GAAG,iBAAiB,KAAK,aAAA,CAAc;EAC1F;AACF;AAEA,kBAAkB,cAAcc;AAMhC,IAAMC,mBAAkB;AAMxB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,gBAAgB,GAAG,eAAe,IAAI;AAC9C,UAAM,YAAYf,cAAa,cAAc;AAC7C,eAAO,0BAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,iBAAiB,cAAce;AAM/B,IAAMC,kBAAiB;AAMvB,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,gBAAgB,GAAG,mBAAmB,IAAI;AAClD,QAAM,YAAYhB,cAAa,cAAc;AAC7C,aAAO,0BAAe,eAAd,EAA6B,GAAG,WAAY,GAAG,oBAAoB,KAAK,aAAA,CAAc;AAChG,CAAC;AAED,qBAAqB,cAAcgB;AAMnC,IAAMC,kBAAiB;AAMvB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,gBAAgB,GAAG,eAAe,IAAI;AAC9C,UAAM,YAAYjB,cAAa,cAAc;AAC7C,eAAO,0BAAe,WAAd,EAAyB,GAAG,WAAY,GAAG,gBAAgB,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,iBAAiB,cAAciB;AAM/B,IAAMC,cAAa;AAMnB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,gBAAgB,GAAG,WAAW,IAAI;AAC1C,UAAM,YAAYlB,cAAa,cAAc;AAC7C,eAAO,0BAAe,QAAd,EAAqB,GAAG,WAAY,GAAG,YAAY,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAckB;AAM3B,IAAMC,YAAW;AASjB,IAAM,aAAwC,CAAC,UAAwC;AACrF,QAAM,EAAE,gBAAgB,UAAU,MAAM,UAAU,cAAc,YAAY,IAAI;AAChF,QAAM,YAAYnB,cAAa,cAAc;AAC7C,QAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;IAC3C,MAAM;IACN,aAAa,eAAe;IAC5B,UAAU;IACV,QAAQmB;EACV,CAAC;AAED,aACE,0BAAe,KAAd,EAAmB,GAAG,WAAW,MAAY,cAAc,SACzD,SAAA,CACH;AAEJ;AAEA,WAAW,cAAcA;AAMzB,IAAMC,oBAAmB;AAMzB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAYpB,cAAa,cAAc;AAC7C,eACE;MAAe;MAAd;QACC,iCAA8B;QAC7B,GAAG;QACH,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,kBAAkB,cAAcoB;AAMhC,IAAMC,oBAAmB;AAMzB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,YAAYrB,cAAa,cAAc;AAE7C,eACE;MAAe;MAAd;QACE,GAAG;QACJ,8BAA2B;QAC1B,GAAG;QACJ,KAAK;QACL,OAAO;UACL,GAAG,MAAM;;UAET,GAAG;YACD,4CAA4C;YAC5C,2CAA2C;YAC3C,4CAA4C;YAC5C,iCAAiC;YACjC,kCAAkC;UACpC;QACF;MAAA;IACF;EAEJ;AACF;AAEA,kBAAkB,cAAcqB;AAQhC,SAAS,UAAa,OAAY,YAAoB;AACpD,SAAO,MAAM,IAAO,CAAC,GAAG,UAAU,OAAO,aAAa,SAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMnB,SAAO;AACb,IAAM,OAAO;AACb,IAAMoB,WAAU;AAChB,IAAMf,WAAS;AACf,IAAMgB,YAAU;AAChB,IAAMC,UAAQ;AACd,IAAMb,UAAQ;AACd,IAAMc,SAAO;AACb,IAAMC,iBAAe;AACrB,IAAMC,eAAa;AACnB,IAAMC,cAAY;AAClB,IAAMC,kBAAgB;AACtB,IAAMC,cAAY;AAClB,IAAMC,UAAQ;AACd,IAAMC,QAAM;AACZ,IAAMC,eAAa;AACnB,IAAMC,eAAa;;;;;;;;;;;;;;;;;;;;;;;;;ACjsBnB,IAAAC,UAAuB;AACvB,uBAAqB;AAyMb,IAAAC,uBAAA;AAhLR,IAAM,uBAAuB;AAE7B,IAAM,CAACC,aAAYC,gBAAeC,sBAAqB,IAAI,iBAGzD,oBAAoB;AAEtB,IAAM,CAAC,sBAAsB,yBAAyB,+BAA+B,IACnF,iBAA4C,oBAAoB;AAGlE,IAAM,CAAC,6BAA6B,yBAAyB,IAAI;EAC/D;EACA,CAACA,wBAAuB,+BAA+B;AACzD;AA4BA,IAAM,CAAC,4BAA4B,wBAAwB,IACzD,4BAAwD,oBAAoB;AAE9E,IAAM,CAAC,yBAAyB,yBAAyB,IAAI,4BAE1D,oBAAoB;AAwBvB,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM;MACJ;MACA,OAAO;MACP;MACA;MACA,gBAAgB;MAChB,oBAAoB;MACpB,cAAc;MACd;MACA,GAAG;IACL,IAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,IAAU,iBAAuC,IAAI;AAC7F,UAAM,cAAc,gBAAgB,cAAc,CAAC,SAAS,kBAAkB,IAAI,CAAC;AACnF,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,eAAqB,eAAO,CAAC;AACnC,UAAM,gBAAsB,eAAO,CAAC;AACpC,UAAM,oBAA0B,eAAO,CAAC;AACxC,UAAM,CAAC,eAAe,gBAAgB,IAAU,iBAAS,IAAI;AAC7D,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,UAAU,CAACC,WAAU;AACnB,cAAM,SAASA,WAAU;AACzB,cAAM,uBAAuB,oBAAoB;AAEjD,YAAI,QAAQ;AACV,iBAAO,aAAa,kBAAkB,OAAO;AAC7C,cAAI,qBAAsB,kBAAiB,KAAK;QAClD,OAAO;AACL,iBAAO,aAAa,kBAAkB,OAAO;AAC7C,4BAAkB,UAAU,OAAO;YACjC,MAAM,iBAAiB,IAAI;YAC3B;UACF;QACF;AAEA,wBAAgBA,MAAK;MACvB;MACA,aAAa,gBAAgB;MAC7B,QAAQ;IACV,CAAC;AAED,UAAM,kBAAwB,oBAAY,MAAM;AAC9C,aAAO,aAAa,cAAc,OAAO;AACzC,oBAAc,UAAU,OAAO,WAAW,MAAM,SAAS,EAAE,GAAG,GAAG;IACnE,GAAG,CAAC,QAAQ,CAAC;AAEb,UAAM,aAAmB;MACvB,CAAC,cAAsB;AACrB,eAAO,aAAa,cAAc,OAAO;AACzC,iBAAS,SAAS;MACpB;MACA,CAAC,QAAQ;IACX;AAEA,UAAM,oBAA0B;MAC9B,CAAC,cAAsB;AACrB,cAAM,aAAa,UAAU;AAC7B,YAAI,YAAY;AAGd,iBAAO,aAAa,cAAc,OAAO;QAC3C,OAAO;AACL,uBAAa,UAAU,OAAO,WAAW,MAAM;AAC7C,mBAAO,aAAa,cAAc,OAAO;AACzC,qBAAS,SAAS;UACpB,GAAG,aAAa;QAClB;MACF;MACA,CAAC,OAAO,UAAU,aAAa;IACjC;AAEM,IAAA,kBAAU,MAAM;AACpB,aAAO,MAAM;AACX,eAAO,aAAa,aAAa,OAAO;AACxC,eAAO,aAAa,cAAc,OAAO;AACzC,eAAO,aAAa,kBAAkB,OAAO;MAC/C;IACF,GAAG,CAAC,CAAC;AAEL,eACE;MAAC;MAAA;QACC,OAAO;QACP,YAAY;QACZ;QACA,KAAK;QACL;QACA,oBAAoB;QACpB,gBAAgB,CAAC,cAAc;AAC7B,iBAAO,aAAa,aAAa,OAAO;AACxC,cAAI,cAAe,mBAAkB,SAAS;cACzC,YAAW,SAAS;QAC3B;QACA,gBAAgB,MAAM;AACpB,iBAAO,aAAa,aAAa,OAAO;AACxC,0BAAgB;QAClB;QACA,gBAAgB,MAAM,OAAO,aAAa,cAAc,OAAO;QAC/D,gBAAgB;QAChB,cAAc,CAAC,cAAc;AAC3B,mBAAS,CAAC,cAAe,cAAc,YAAY,KAAK,SAAU;QACpE;QACA,eAAe,MAAM,SAAS,EAAE;QAEhC,cAAA;UAAC,UAAU;UAAV;YACC,cAAW;YACX,oBAAkB;YAClB,KAAK;YACJ,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAM7B,IAAMC,YAAW;AAajB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM;MACJ;MACA,OAAO;MACP;MACA;MACA,cAAc;MACd,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,yBAAyBA,WAAU,qBAAqB;AACxE,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,UAAU;MACV,aAAa,gBAAgB;MAC7B,QAAQA;IACV,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP,YAAY;QACZ;QACA,KAAK,QAAQ;QACb;QACA,oBAAoB,QAAQ;QAC5B,gBAAgB,CAAC,cAAc,SAAS,SAAS;QACjD,cAAc,CAAC,cAAc,SAAS,SAAS;QAC/C,eAAe,MAAM,SAAS,EAAE;QAEhC,cAAA,0BAAC,UAAU,KAAV,EAAc,oBAAkB,aAAc,GAAG,UAAU,KAAK,aAAA,CAAc;MAAA;IACjF;EAEJ;AACF;AAEA,kBAAkB,cAAcA;AAsBhC,IAAM,yBAAgE,CACpE,UACG;AACH,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,iBAA+C,IAAI;AACzF,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,iBAAmC,oBAAI,IAAI,CAAC;AAChG,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,iBAAgC,IAAI;AAEtF,aACE;IAAC;IAAA;MACC;MACA;MACA;MACA;MACA,eAAe,YAAY,KAAK;MAChC,QAAQ,MAAM;MACd;MACA;MACA;MACA,kBAAkB;MAClB;MACA,wBAAwB;MACxB,gBAAgB,eAAe,cAAc;MAC7C,gBAAgB,eAAe,cAAc;MAC7C,gBAAgB,eAAe,cAAc;MAC7C,gBAAgB,eAAe,cAAc;MAC7C,cAAc,eAAe,YAAY;MACzC,eAAe,eAAe,aAAa;MAC3C,yBAA+B,oBAAY,CAAC,cAAc,gBAAgB;AACxE,2BAAmB,CAAC,gBAAgB;AAClC,sBAAY,IAAI,cAAc,WAAW;AACzC,iBAAO,IAAI,IAAI,WAAW;QAC5B,CAAC;MACH,GAAG,CAAC,CAAC;MACL,yBAA+B,oBAAY,CAAC,iBAAiB;AAC3D,2BAAmB,CAAC,gBAAgB;AAClC,cAAI,CAAC,YAAY,IAAI,YAAY,EAAG,QAAO;AAC3C,sBAAY,OAAO,YAAY;AAC/B,iBAAO,IAAI,IAAI,WAAW;QAC5B,CAAC;MACH,GAAG,CAAC,CAAC;MAEL,cAAA,0BAACJ,YAAW,UAAX,EAAoB,OACnB,cAAA,0BAAC,yBAAA,EAAwB,OAAc,OAAO,iBAC3C,SAAA,CACH,EAAA,CACF;IAAA;EACF;AAEJ;AAMA,IAAM,YAAY;AAMlB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,GAAG,UAAU,IAAI;AAChD,UAAM,UAAU,yBAAyB,WAAW,qBAAqB;AAEzE,UAAM,WACJ,0BAAC,UAAU,IAAV,EAAa,oBAAkB,QAAQ,aAAc,GAAG,WAAW,KAAK,aAAA,CAAc;AAGzF,eACE,0BAAC,UAAU,KAAV,EAAc,OAAO,EAAE,UAAU,WAAW,GAAG,KAAK,QAAQ,wBAC3D,cAAA,0BAACA,YAAW,MAAX,EAAgB,OAAO,uBACrB,UAAA,QAAQ,iBAAa,0BAAC,YAAA,EAAW,SAAO,MAAE,UAAA,KAAA,CAAK,IAAgB,KAAA,CAClE,EAAA,CACF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAMK,aAAY;AAgBlB,IAAM,CAAC,mCAAmC,4BAA4B,IACpE,4BAA4DA,UAAS;AAQvE,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,OAAO,WAAW,GAAG,UAAU,IAAI;AAClE,UAAM,YAAY,MAAM;AAGxB,UAAM,QAAQ,aAAa,aAAa;AACxC,UAAM,aAAmB,eAAqC,IAAI;AAClE,UAAM,aAAmB,eAAqC,IAAI;AAClE,UAAM,gBAAsB,eAA0B,IAAI;AAC1D,UAAM,4BAAkC,eAAO,MAAM;IAAC,CAAC;AACvD,UAAM,oBAA0B,eAAO,KAAK;AAE5C,UAAM,qBAA2B,oBAAY,CAAC,OAAO,YAAY;AAC/D,UAAI,WAAW,SAAS;AACtB,kCAA0B,QAAQ;AAClC,cAAM,aAAa,sBAAsB,WAAW,OAAO;AAC3D,YAAI,WAAW,OAAQ,YAAW,SAAS,UAAU,aAAa,WAAW,QAAQ,CAAC;MACxF;IACF,GAAG,CAAC,CAAC;AAEL,UAAM,oBAA0B,oBAAY,MAAM;AAChD,UAAI,WAAW,SAAS;AACtB,cAAM,aAAa,sBAAsB,WAAW,OAAO;AAC3D,YAAI,WAAW,OAAQ,2BAA0B,UAAU,mBAAmB,UAAU;MAC1F;IACF,GAAG,CAAC,CAAC;AAEL,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA;QACA,gBAAgB;QAChB,mBAAmB;QACnB,oBAAoB;QACpB,uBAAuB;QAEvB,cAAA,0BAAC,UAAU,IAAV,EAAc,GAAG,WAAW,KAAK,aAAA,CAAc;MAAA;IAClD;EAEJ;AACF;AAEA,mBAAmB,cAAcA;AAMjC,IAAMC,gBAAe;AAMrB,IAAM,wBAA8B,mBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,uBAAuB,UAAU,GAAG,aAAa,IAAI;AAC7D,QAAM,UAAU,yBAAyBA,eAAc,MAAM,qBAAqB;AAClF,QAAM,cAAc,6BAA6BA,eAAc,MAAM,qBAAqB;AAC1F,QAAM,MAAY,eAAqC,IAAI;AAC3D,QAAM,eAAe,gBAAgB,KAAK,YAAY,YAAY,YAAY;AAC9E,QAAM,YAAY,cAAc,QAAQ,QAAQ,YAAY,KAAK;AACjE,QAAM,YAAY,cAAc,QAAQ,QAAQ,YAAY,KAAK;AACjE,QAAM,0BAAgC,eAAO,KAAK;AAClD,QAAM,mBAAyB,eAAO,KAAK;AAC3C,QAAM,OAAO,YAAY,UAAU,QAAQ;AAE3C,aACE,2BAAA,+BAAA,EACE,UAAA;QAAA,0BAACN,YAAW,UAAX,EAAoB,OAAO,uBAAuB,OAAO,YAAY,OACpE,cAAA,0BAAC,gBAAA,EAAe,SAAO,MACrB,cAAA;MAAC,UAAU;MAAV;QACC,IAAI;QACJ;QACA,iBAAe,WAAW,KAAK;QAC/B,cAAY,aAAa,IAAI;QAC7B,iBAAe;QACf,iBAAe;QACd,GAAG;QACJ,KAAK;QACL,gBAAgB,qBAAqB,MAAM,gBAAgB,MAAM;AAC/D,2BAAiB,UAAU;AAC3B,sBAAY,kBAAkB,UAAU;QAC1C,CAAC;QACD,eAAe;UACb,MAAM;UACN,UAAU,MAAM;AACd,gBACE,YACA,iBAAiB,WACjB,YAAY,kBAAkB,WAC9B,wBAAwB;AAExB;AACF,oBAAQ,eAAe,YAAY,KAAK;AACxC,oCAAwB,UAAU;UACpC,CAAC;QACH;QACA,gBAAgB;UACd,MAAM;UACN,UAAU,MAAM;AACd,gBAAI,SAAU;AACd,oBAAQ,eAAe;AACvB,oCAAwB,UAAU;UACpC,CAAC;QACH;QACA,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,kBAAQ,aAAa,YAAY,KAAK;AACtC,2BAAiB,UAAU;QAC7B,CAAC;QACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,mBAAmB,QAAQ,QAAQ,QAAQ,cAAc;AAC/D,gBAAM,WAAW,EAAE,YAAY,aAAa,UAAU,iBAAiB,EACrE,QAAQ,WACV;AACA,cAAI,QAAQ,MAAM,QAAQ,UAAU;AAClC,wBAAY,eAAe;AAE3B,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF,EAAA,CACF;IAGC,YACC,2BAAA,+BAAA,EACE,UAAA;UAAA;QAAyBO;QAAxB;UACC,eAAW;UACX,UAAU;UACV,KAAK,YAAY;UACjB,SAAS,CAAC,UAAU;AAClB,kBAAM,UAAU,YAAY,WAAW;AACvC,kBAAM,qBAAqB,MAAM;AACjC,kBAAM,oBAAoB,uBAAuB,IAAI;AACrD,kBAAM,sBAAsB,SAAS,SAAS,kBAAkB;AAEhE,gBAAI,qBAAqB,CAAC,qBAAqB;AAC7C,0BAAY,kBAAkB,oBAAoB,UAAU,KAAK;YACnE;UACF;QAAA;MACF;MAGC,QAAQ,gBAAY,0BAAC,QAAA,EAAK,aAAW,UAAA,CAAW;IAAA,EAAA,CACnD;EAAA,EAAA,CAEJ;AAEJ,CAAC;AAED,sBAAsB,cAAcD;AAMpC,IAAM,YAAY;AAClB,IAAM,cAAc;AASpB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,uBAAuB,QAAQ,UAAU,GAAG,UAAU,IAAI;AAElE,eACE,0BAAC,gBAAA,EAAe,SAAO,MACrB,cAAA;MAAC,UAAU;MAAV;QACC,eAAa,SAAS,KAAK;QAC3B,gBAAc,SAAS,SAAS;QAC/B,GAAG;QACJ,KAAK;QACL,SAAS;UACP,MAAM;UACN,CAAC,UAAU;AACT,kBAAM,SAAS,MAAM;AACrB,kBAAM,kBAAkB,IAAI,YAAY,aAAa;cACnD,SAAS;cACT,YAAY;YACd,CAAC;AACD,mBAAO,iBAAiB,aAAa,CAACE,WAAU,WAAWA,MAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AACjF,wCAA4B,QAAQ,eAAe;AAEnD,gBAAI,CAAC,gBAAgB,oBAAoB,CAAC,MAAM,SAAS;AACvD,oBAAM,0BAA0B,IAAI,YAAY,sBAAsB;gBACpE,SAAS;gBACT,YAAY;cACd,CAAC;AACD,0CAA4B,QAAQ,uBAAuB;YAC7D;UACF;UACA,EAAE,0BAA0B,MAAM;QACpC;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAMC,kBAAiB;AAWvB,IAAM,0BAAgC,mBAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM,EAAE,YAAY,GAAG,eAAe,IAAI;AAC1C,QAAM,UAAU,yBAAyBA,iBAAgB,MAAM,qBAAqB;AACpF,QAAM,YAAY,QAAQ,QAAQ,KAAK;AAEvC,SAAO,QAAQ,iBACX,iBAAAC,QAAS;QACP,0BAAC,UAAA,EAAS,SAAS,cAAc,WAC/B,cAAA,0BAAC,6BAAA,EAA6B,GAAG,gBAAgB,KAAK,aAAA,CAAc,EAAA,CACtE;IACA,QAAQ;EACV,IACA;AACN,CAAC;AAED,wBAAwB,cAAcD;AAKtC,IAAM,8BAAoC,mBAGxC,CAAC,OAAsD,iBAAiB;AACxE,QAAM,EAAE,uBAAuB,GAAG,eAAe,IAAI;AACrD,QAAM,UAAU,yBAAyBA,iBAAgB,qBAAqB;AAC9E,QAAM,WAAWR,eAAc,qBAAqB;AACpD,QAAM,CAAC,eAAe,gBAAgB,IAAU;IAC9C;EACF;AACA,QAAM,CAAC,UAAU,WAAW,IAAU,iBAAkD,IAAI;AAC5F,QAAM,eAAe,QAAQ,gBAAgB;AAC7C,QAAM,YAAY,QAAQ,QAAQ,KAAK;AAEjC,EAAA,kBAAU,MAAM;AACpB,UAAM,QAAQ,SAAS;AACvB,UAAM,cAAc,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,QAAQ,KAAK,GAAG,IAAI;AAC5E,QAAI,YAAa,kBAAiB,WAAW;EAC/C,GAAG,CAAC,UAAU,QAAQ,KAAK,CAAC;AAK5B,QAAM,uBAAuB,MAAM;AACjC,QAAI,eAAe;AACjB,kBAAY;QACV,MAAM,eAAe,cAAc,cAAc,cAAc;QAC/D,QAAQ,eAAe,cAAc,aAAa,cAAc;MAClE,CAAC;IACH;EACF;AACA,oBAAkB,eAAe,oBAAoB;AACrD,oBAAkB,QAAQ,gBAAgB,oBAAoB;AAI9D,SAAO,eACL;IAAC,UAAU;IAAV;MACC,eAAW;MACX,cAAY,YAAY,YAAY;MACpC,oBAAkB,QAAQ;MACzB,GAAG;MACJ,KAAK;MACL,OAAO;QACL,UAAU;QACV,GAAI,eACA;UACE,MAAM;UACN,OAAO,SAAS,OAAO;UACvB,WAAW,cAAc,SAAS,MAAM;QAC1C,IACA;UACE,KAAK;UACL,QAAQ,SAAS,OAAO;UACxB,WAAW,cAAc,SAAS,MAAM;QAC1C;QACJ,GAAG,eAAe;MACpB;IAAA;EACF,IACE;AACN,CAAC;AAMD,IAAMU,gBAAe;AAYrB,IAAM,wBAA8B,mBAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM,EAAE,YAAY,GAAG,aAAa,IAAI;AACxC,QAAM,UAAU,yBAAyBA,eAAc,MAAM,qBAAqB;AAClF,QAAM,cAAc,6BAA6BA,eAAc,MAAM,qBAAqB;AAC1F,QAAM,eAAe,gBAAgB,YAAY,YAAY,YAAY;AACzE,QAAM,OAAO,YAAY,UAAU,QAAQ;AAE3C,QAAM,cAAc;IAClB,OAAO,YAAY;IACnB,YAAY,YAAY;IACxB,eAAe,YAAY;IAC3B,mBAAmB,YAAY;IAC/B,uBAAuB,YAAY;IACnC,oBAAoB,YAAY;IAChC,GAAG;EACL;AAEA,SAAO,CAAC,QAAQ,eACd,0BAAC,UAAA,EAAS,SAAS,cAAc,MAC/B,cAAA;IAAC;IAAA;MACC,cAAY,aAAa,IAAI;MAC5B,GAAG;MACJ,KAAK;MACL,gBAAgB,qBAAqB,MAAM,gBAAgB,QAAQ,cAAc;MACjF,gBAAgB;QACd,MAAM;QACN,UAAU,QAAQ,cAAc;MAClC;MACA,OAAO;;QAEL,eAAe,CAAC,QAAQ,QAAQ,aAAa,SAAS;QACtD,GAAG,YAAY;MACjB;IAAA;EACF,EAAA,CACF,QAEA,0BAAC,wBAAA,EAAuB,YAAyB,GAAG,aAAa,KAAK,aAAA,CAAc;AAExF,CAAC;AAED,sBAAsB,cAAcA;AAapC,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,UAAU,yBAAyBA,eAAc,MAAM,qBAAqB;AAClF,QAAM,EAAE,yBAAyB,wBAAwB,IAAI;AAE7D,mBAAgB,MAAM;AACpB,4BAAwB,MAAM,OAAO;MACnC,KAAK;MACL,GAAG;IACL,CAAC;EACH,GAAG,CAAC,OAAO,cAAc,uBAAuB,CAAC;AAEjD,mBAAgB,MAAM;AACpB,WAAO,MAAM,wBAAwB,MAAM,KAAK;EAClD,GAAG,CAAC,MAAM,OAAO,uBAAuB,CAAC;AAGzC,SAAO;AACT,CAAC;AAID,IAAM,uBAAuB;AAkB7B,IAAM,4BAAkC,mBAGtC,CAAC,OAAoD,iBAAiB;AACtE,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EACL,IAAI;AACJ,QAAM,UAAU,yBAAyBA,eAAc,qBAAqB;AAC5E,QAAM,MAAY,eAAyC,IAAI;AAC/D,QAAM,eAAe,gBAAgB,KAAK,YAAY;AACtD,QAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,QAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,QAAM,WAAWV,eAAc,qBAAqB;AACpD,QAAM,yBAA+B,eAA+B,IAAI;AAExE,QAAM,EAAE,cAAc,IAAI;AAEpB,EAAA,kBAAU,MAAM;AACpB,UAAM,UAAU,IAAI;AAGpB,QAAI,QAAQ,cAAc,SAAS;AACjC,YAAM,cAAc,MAAM;AACxB,sBAAc;AACd,2BAAmB;AACnB,YAAI,QAAQ,SAAS,SAAS,aAAa,EAAG,YAAW,SAAS,MAAM;MAC1E;AACA,cAAQ,iBAAiB,sBAAsB,WAAW;AAC1D,aAAO,MAAM,QAAQ,oBAAoB,sBAAsB,WAAW;IAC5E;EACF,GAAG,CAAC,QAAQ,YAAY,MAAM,OAAO,YAAY,eAAe,kBAAkB,CAAC;AAEnF,QAAM,kBAAwB,gBAAQ,MAAM;AAC1C,UAAM,QAAQ,SAAS;AACvB,UAAM,SAAS,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK;AAC7C,QAAI,QAAQ,QAAQ,MAAO,QAAO,QAAQ;AAC1C,UAAM,QAAQ,OAAO,QAAQ,QAAQ,KAAK;AAC1C,UAAM,YAAY,OAAO,QAAQ,QAAQ,aAAa;AACtD,UAAM,aAAa,UAAU,QAAQ;AACrC,UAAM,cAAc,cAAc,OAAO,QAAQ,KAAK;AAItD,QAAI,CAAC,cAAc,CAAC,YAAa,QAAO,uBAAuB;AAE/D,UAAM,aAAa,MAAM;AAEvB,UAAI,UAAU,WAAW;AAEvB,YAAI,cAAc,cAAc,GAAI,QAAO,QAAQ,YAAY,aAAa;AAE5E,YAAI,eAAe,UAAU,GAAI,QAAO,QAAQ,YAAY,aAAa;MAC3E;AAGA,aAAO;IACT,GAAG;AAEH,2BAAuB,UAAU;AACjC,WAAO;EACT,GAAG,CAAC,QAAQ,eAAe,QAAQ,OAAO,QAAQ,KAAK,UAAU,KAAK,CAAC;AAEvE,aACE,0BAAC,YAAA,EAAW,SAAO,MACjB,cAAA;IAAC;IAAA;MACC,IAAI;MACJ,mBAAiB;MACjB,eAAa;MACb,oBAAkB,QAAQ;MACzB,GAAG;MACJ,KAAK;MACL,6BAA6B;MAC7B,WAAW,MAAM;AACf,cAAM,0BAA0B,IAAI,MAAM,sBAAsB;UAC9D,SAAS;UACT,YAAY;QACd,CAAC;AACD,YAAI,SAAS,cAAc,uBAAuB;MACpD;MACA,gBAAgB,qBAAqB,MAAM,gBAAgB,CAAC,UAAU;AACpE,8BAAsB;AACtB,cAAM,SAAS,MAAM;AAErB,YAAI,QAAQ,oBAAoB,SAAS,MAAM,EAAG,OAAM,eAAe;MACzE,CAAC;MACD,sBAAsB,qBAAqB,MAAM,sBAAsB,CAAC,UAAU;AAChF,cAAM,SAAS,MAAM;AACrB,cAAM,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS,KAAK,IAAI,SAAS,SAAS,MAAM,CAAC;AAC9E,cAAM,iBAAiB,QAAQ,cAAc,QAAQ,UAAU,SAAS,MAAM;AAC9E,YAAI,aAAa,kBAAkB,CAAC,QAAQ,WAAY,OAAM,eAAe;MAC/E,CAAC;MACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;AACzD,cAAM,WAAW,MAAM,QAAQ,SAAS,CAAC;AACzC,YAAI,UAAU;AACZ,gBAAM,aAAa,sBAAsB,MAAM,aAAa;AAC5D,gBAAM,iBAAiB,SAAS;AAChC,gBAAM,QAAQ,WAAW,UAAU,CAAC,cAAc,cAAc,cAAc;AAC9E,gBAAM,oBAAoB,MAAM;AAChC,gBAAM,iBAAiB,oBACnB,WAAW,MAAM,GAAG,KAAK,EAAE,QAAQ,IACnC,WAAW,MAAM,QAAQ,GAAG,WAAW,MAAM;AAEjD,cAAI,WAAW,cAAc,GAAG;AAE9B,kBAAM,eAAe;UACvB,OAAO;AAIL,0BAAc,SAAS,MAAM;UAC/B;QACF;MACF,CAAC;MACD,iBAAiB,qBAAqB,MAAM,iBAAiB,CAAC,WAAW;AAGvE,0BAAkB,UAAU;MAC9B,CAAC;IAAA;EACH,EAAA,CACF;AAEJ,CAAC;AAMD,IAAM,gBAAgB;AAYtB,IAAM,yBAA+B,mBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,YAAY,GAAG,cAAc,IAAI;AACzC,QAAM,UAAU,yBAAyB,eAAe,MAAM,qBAAqB;AACnF,QAAM,OAAO,QAAQ,QAAQ,KAAK;AAElC,aACE,0BAAC,UAAA,EAAS,SAAS,cAAc,MAC/B,cAAA,0BAAC,4BAAA,EAA4B,GAAG,eAAe,KAAK,aAAA,CAAc,EAAA,CACpE;AAEJ,CAAC;AAED,uBAAuB,cAAc;AAOrC,IAAM,6BAAmC,mBAGvC,CAAC,OAAqD,iBAAiB;AACvE,QAAM,EAAE,uBAAuB,UAAU,GAAG,kBAAkB,IAAI;AAClE,QAAM,UAAU,yBAAyB,eAAe,qBAAqB;AAC7E,QAAM,eAAe,gBAAgB,cAAc,QAAQ,gBAAgB;AAC3E,QAAM,yBAAyB;IAC7BU;IACA,MAAM;EACR;AACA,QAAM,CAAC,MAAM,OAAO,IAAU,iBAAmD,IAAI;AACrF,QAAM,CAAC,SAAS,UAAU,IAAU,iBAA8C,IAAI;AACtF,QAAM,gBAAgB,OAAO,MAAM,QAAQ,OAAO;AAClD,QAAM,iBAAiB,OAAO,MAAM,SAAS,OAAO;AACpD,QAAM,OAAO,QAAQ,QAAQ,KAAK;AAGlC,QAAM,qBAAqB,OAAO,QAAQ,QAAQ,QAAQ;AAQ1D,QAAM,mBAAmB,MAAM;AAC7B,QAAI,QAAS,SAAQ,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa,CAAC;EACnF;AACA,oBAAkB,SAAS,gBAAgB;AAE3C,aACE;IAAC,UAAU;IAAV;MACC,cAAY,aAAa,IAAI;MAC7B,oBAAkB,QAAQ;MACzB,GAAG;MACJ,KAAK;MACL,OAAO;;QAEL,eAAe,CAAC,QAAQ,QAAQ,aAAa,SAAS;QACtD,CAAC,wCAA+C,GAAG;QACnD,CAAC,yCAAgD,GAAG;QACpD,GAAG,kBAAkB;MACvB;MACA,gBAAgB,qBAAqB,MAAM,gBAAgB,QAAQ,cAAc;MACjF,gBAAgB,qBAAqB,MAAM,gBAAgB,UAAU,QAAQ,cAAc,CAAC;MAE3F,UAAA,MAAM,KAAK,uBAAuB,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,YAAY,GAAGC,OAAM,CAAC,MAAM;AACxF,cAAM,WAAW,uBAAuB;AACxC,mBACE,0BAAC,UAAA,EAAqB,SAAS,cAAc,UAC3C,cAAA;UAAC;UAAA;YACE,GAAGA;YACJ,KAAK,YAAY,KAAK,CAAC,SAAS;AAG9B,kBAAI,YAAY,KAAM,YAAW,IAAI;YACvC,CAAC;UAAA;QACH,EAAA,GARa,KASf;MAEJ,CAAC;IAAA;EACH;AAEJ,CAAC;AAID,IAAM,mBAAmB;AAKzB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,uBAAuB,GAAG,WAAW,IAAI;AACjD,UAAM,UAAU,yBAAyB,kBAAkB,qBAAqB;AAEhF,eACE,0BAAC,qBAAqB,UAArB,EAA8B,OAAO,uBACpC,cAAA,0BAAC,qBAAqB,MAArB,EAA0B,OAAO,uBAChC,cAAA,0BAAC,UAAU,KAAV,EAAc,KAAK,QAAQ,KAAM,GAAG,YAAY,KAAK,aAAA,CAAc,EAAA,CACtE,EAAA,CACF;EAEJ;AACF;AAIA,IAAM,aAAa,CAAC,cAAc,aAAa,WAAW,WAAW;AACrE,IAAM,wBAAwB;AAK9B,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM,EAAE,uBAAuB,GAAG,WAAW,IAAI;AACjD,UAAM,WAAW,wBAAwB,qBAAqB;AAC9D,UAAM,UAAU,yBAAyB,uBAAuB,qBAAqB;AAErF,eACE,0BAAC,qBAAqB,UAArB,EAA8B,OAAO,uBACpC,cAAA;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,gBAAM,uBAAuB,CAAC,QAAQ,OAAO,GAAG,UAAU,EAAE,SAAS,MAAM,GAAG;AAC9E,cAAI,sBAAsB;AACxB,gBAAI,iBAAiB,SAAS,EAAE,IAAI,CAAC,SAAS,KAAK,IAAI,OAAQ;AAC/D,kBAAM,cAAc,QAAQ,QAAQ,QAAQ,eAAe;AAC3D,kBAAM,WAAW,CAAC,aAAa,WAAW,KAAK;AAC/C,gBAAI,SAAS,SAAS,MAAM,GAAG,EAAG,gBAAe,QAAQ;AACzD,gBAAI,WAAW,SAAS,MAAM,GAAG,GAAG;AAClC,oBAAM,eAAe,eAAe,QAAQ,MAAM,aAAa;AAC/D,+BAAiB,eAAe,MAAM,eAAe,CAAC;YACxD;AAKA,uBAAW,MAAM,WAAW,cAAc,CAAC;AAG3C,kBAAM,eAAe;UACvB;QACF,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAYA,SAAS,sBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAEA,SAAS,WAAW,YAA2B;AAC7C,QAAM,2BAA2B,SAAS;AAC1C,SAAO,WAAW,KAAK,CAAC,cAAc;AAEpC,QAAI,cAAc,yBAA0B,QAAO;AACnD,cAAU,MAAM;AAChB,WAAO,SAAS,kBAAkB;EACpC,CAAC;AACH;AAEA,SAAS,mBAAmB,YAA2B;AACrD,aAAW,QAAQ,CAAC,cAAc;AAChC,cAAU,QAAQ,WAAW,UAAU,aAAa,UAAU,KAAK;AACnE,cAAU,aAAa,YAAY,IAAI;EACzC,CAAC;AACD,SAAO,MAAM;AACX,eAAW,QAAQ,CAAC,cAAc;AAChC,YAAM,eAAe,UAAU,QAAQ;AACvC,gBAAU,aAAa,YAAY,YAAY;IACjD,CAAC;EACH;AACF;AAEA,SAAS,kBAAkB,SAA6B,UAAsB;AAC5E,QAAM,eAAe,eAAe,QAAQ;AAC5C,mBAAgB,MAAM;AACpB,QAAI,MAAM;AACV,QAAI,SAAS;AAQX,YAAM,iBAAiB,IAAI,eAAe,MAAM;AAC9C,6BAAqB,GAAG;AACxB,cAAM,OAAO,sBAAsB,YAAY;MACjD,CAAC;AACD,qBAAe,QAAQ,OAAO;AAC9B,aAAO,MAAM;AACX,eAAO,qBAAqB,GAAG;AAC/B,uBAAe,UAAU,OAAO;MAClC;IACF;EACF,GAAG,CAAC,SAAS,YAAY,CAAC;AAC5B;AAEA,SAAS,aAAa,MAAe;AACnC,SAAO,OAAO,SAAS;AACzB;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,UAAa,SAAqE;AACzF,SAAO,CAAC,UAAW,MAAM,gBAAgB,UAAU,QAAQ,KAAK,IAAI;AACtE;AAIA,IAAML,SAAO;AACb,IAAMM,OAAM;AACZ,IAAM,OAAO;AACb,IAAMC,QAAO;AACb,IAAMC,WAAU;AAChB,IAAM,OAAO;AACb,IAAM,YAAY;AAClB,IAAMC,WAAU;AAChB,IAAM,WAAW;A;;;;;;;;;;;AC1tCjB,IAAAC,UAAuB;AACvB,IAAAC,oBAA0B;AAocZ,IAAAC,uBAAA;AA3bd,IAAM,uBAAuB;EAC3B,SAAS;IACP,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;EACb;EACA,OAAO;IACL,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;EACb;EACA,cAAc;IACZ,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;EACb;EACA,MAAM;AACR;AA4BA,IAAM,+BAA+B;AACrC,IAAM,CAACC,aAAY,EAAE,eAAAC,gBAAe,uBAAAC,wBAAuB,kBAAkB,CAAC,IAC5E,kBAAmC,4BAA4B;AACjE,IAAM,CAAC,iCAAiC,IAAI,mBAAmB,8BAA8B;EAC3FA;EACA;AACF,CAAC;AACD,IAAMC,4BAA2B,4BAA4B;AAE7D,IAAM,CAAC,6BAA6B,8BAA8B,IAChE,kCAAoE,4BAA4B;AAuHlG,IAAM,uBAA6B;EACjC,SAAS,yBACP;IACE;IACA;IACA,OAAO;IACP;IACA,aAAa;IACb;IACA;IACA;IACA,WAAW;IACX,WAAW;IACX,eAAe;IACf,YAAY;IACZ;IACA;IACA;IACA,OAAO;;IAEP,cAAc;IACd;IACA,iBAAiB;IACjB,eAAe;IACf,GAAG;EACL,GACA,cACA;AACA,UAAM,wBAAwBA,0BAAyB,2BAA2B;AAClF,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,kBAAkB,kBAAkB;AAC1C,UAAM,CAAC,UAAU,IAAI;AAErB,UAAM,aAAa,qBAAqB,cAAc,IAClD,qBAAqB,cAAuC,IAC5D;AAEJ,UAAM,gBAAsB;MAC1B,CAACC,WAA6B;AAC5B,YAAI,MAAM,QAAQA,MAAK,GAAG;AACxBA,mBAAQA,OAAM,IAAI,gBAAgB,EAAE,KAAK,EAAE;QAC7C,OAAO;AACLA,mBAAQ,iBAAiBA,MAAK;QAChC;AAEA,YAAI,YAAY;AAEd,gBAAM,SAAS,IAAI,OAAO,WAAW,MAAM;AAC3CA,mBAAQA,OAAM,QAAQ,QAAQ,EAAE;QAClC,WAAW,mBAAmB;AAC5BA,mBAAQ,kBAAkBA,MAAK;QACjC;AAEA,eAAOA,OAAM,MAAM,EAAE;MACvB;MACA,CAAC,YAAY,iBAAiB;IAChC;AAEA,UAAM,kBAAwB,gBAAQ,MAAM;AAC1C,aAAO,aAAa,OAAO,cAAc,SAAS,IAAI;IACxD,GAAG,CAAC,WAAW,aAAa,CAAC;AAE7B,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,QAAQ;MACR,MAAM;MACN,aAAa,gBAAgB,OAAO,cAAc,YAAY,IAAI,CAAC;MACnE,UAAgB;QACd,CAACA,WAAoB,gBAAgBA,OAAM,KAAK,EAAE,CAAC;QACnD,CAAC,aAAa;MAChB;IACF,CAAC;AAGD,UAAM,WAAW,eAA2B,CAAC,WAAW;AACtD,cAAQ,OAAO,MAAM;QACnB,KAAK,YAAY;AACf,gBAAM,EAAE,OAAO,KAAK,IAAI;AACxB,gBAAM,gBAAgB,WAAW,GAAG,KAAK,GAAG;AAC5C,cAAI,MAAM,KAAK,MAAM,MAAM;AACzB,kBAAM,OAAO,iBAAiB,WAAW,KAAK,eAAe,CAAC,GAAG;AACjE,uBAAW,IAAI;AACf;UACF;AAGA,cAAI,SAAS,IAAI;AACf;UACF;AAEA,cAAI,YAAY;AACd,kBAAM,SAAS,IAAI,OAAO,WAAW,MAAM;AAC3C,kBAAM,QAAQ,KAAK,QAAQ,QAAQ,EAAE;AACrC,gBAAI,UAAU,MAAM;AAElB;YACF;UACF;AAGA,cAAI,MAAM,UAAU,WAAW,MAAM;AAEnC,kBAAMC,YAAW,CAAC,GAAG,KAAK;AAC1BA,sBAAS,KAAK,IAAI;AAClB,6CAAU,MAAM,SAASA,SAAQ,CAAC;AAClC,kBAAM,OAAO,iBAAiB,WAAW,KAAK,eAAe,CAAC,GAAG;AACjE,uBAAW,IAAI;AACf;UACF;AAEA,gBAAM,WAAW,CAAC,GAAG,KAAK;AAC1B,mBAAS,KAAK,IAAI;AAElB,gBAAM,cAAc,WAAW,GAAG,EAAE,GAAG;AACvC,2CAAU,MAAM,SAAS,QAAQ,CAAC;AAClC,cAAI,kBAAkB,aAAa;AACjC,kBAAM,OAAO,iBAAiB,WAAW,KAAK,eAAe,CAAC,GAAG;AACjE,uBAAW,IAAI;UACjB,OAAO;AACL,2BAAe,OAAO;UACxB;AACA;QACF;QAEA,KAAK,cAAc;AACjB,gBAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,cAAI,CAAC,MAAM,KAAK,GAAG;AACjB;UACF;AAEA,gBAAM,WAAW,MAAM,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK;AACnD,gBAAM,gBAAgB,WAAW,GAAG,KAAK,GAAG;AAC5C,gBAAM,WAAW,iBAAiB,WAAW,KAAK,eAAe,EAAE,GAAG;AAEtE,2CAAU,MAAM,SAAS,QAAQ,CAAC;AAClC,cAAI,WAAW,aAAa;AAC1B,uBAAW,QAAQ;UACrB,WAAW,WAAW,YAAY,WAAW,OAAO;AAClD,uBAAW,aAAa;UAC1B;AACA;QACF;QAEA,KAAK,SAAS;AACZ,cAAI,MAAM,WAAW,GAAG;AACtB;UACF;AAEA,cAAI,OAAO,WAAW,eAAe,OAAO,WAAW,UAAU;AAC/D,6CAAU,MAAM,SAAS,CAAC,CAAC,CAAC;AAC5B,uBAAW,WAAW,GAAG,CAAC,GAAG,OAAO;UACtC,OAAO;AACL,qBAAS,CAAC,CAAC;UACb;AACA;QACF;QAEA,KAAK,SAAS;AACZ,gBAAM,EAAE,OAAO,YAAY,IAAI;AAC/B,gBAAMD,SAAQ,cAAc,WAAW;AACvC,cAAI,CAACA,QAAO;AACV;UACF;AAEA,2CAAU,MAAM,SAASA,MAAK,CAAC;AAC/B,qBAAW,WAAW,GAAGA,OAAM,SAAS,CAAC,GAAG,OAAO;AACnD;QACF;MACF;IACF,CAAC;AAGD,UAAM,oBAA0B,eAAO,UAAU;AAC3C,IAAA,kBAAU,MAAM;AACpB,UAAI,CAAC,YAAY;AACf;MACF;AAEA,UAAI,kBAAkB,SAAS,SAAS,WAAW,MAAM;AACvD,0BAAkB,UAAU;AAC5B,iBAAS,cAAc,MAAM,KAAK,EAAE,CAAC,CAAC;MACxC;IACF,GAAG,CAAC,eAAe,UAAU,YAAY,KAAK,CAAC;AAE/C,UAAM,iBAAuB,eAAyB,IAAI;AAE1D,UAAM,gBAAsB,eAAqC,IAAI;AACrE,UAAM,UAAgB,eAA8B,IAAI;AACxD,UAAM,eAAe,gBAAgB,cAAc,OAAO;AAE1D,UAAM,aAAa,WAAW,GAAG,CAAC,GAAG;AACrC,UAAM,aAAmB,oBAAY,MAAM;AACzC,UAAI;AACJ,UAAI,MAAM;AACR,cAAM,qBAAqB,QAAQ,SAAS,iBAAiB,UAAU,eAAe,IAAI;AAC1F,YAAI,cAAc,iBAAiB,GAAG;AACpC,wBAAc;QAChB;MACF,WAAW,eAAe,SAAS;AACjC,sBAAc,eAAe,QAAQ;MACvC,WAAW,YAAY;AACrB,sBAAc,WAAW;MAC3B;AAEA,aAAO,eAAe;IACxB,GAAG,CAAC,MAAM,UAAU,CAAC;AAErB,UAAM,gBAAsB,oBAAY,MAAM;AAC5C,YAAM,cAAc,WAAW;AAC/B,mBAAa,cAAc;IAC7B,GAAG,CAAC,UAAU,CAAC;AAET,IAAA,kBAAU,MAAM;AACpB,YAAME,QAAO,WAAW;AACxB,UAAIA,OAAM;AACR,cAAM,QAAQ,MAAM,SAAS,EAAE,MAAM,SAAS,QAAQ,QAAQ,CAAC;AAC/DA,cAAK,iBAAiB,SAAS,KAAK;AACpC,eAAO,MAAMA,MAAK,oBAAoB,SAAS,KAAK;MACtD;IACF,GAAG,CAAC,UAAU,UAAU,CAAC;AAEzB,UAAM,eAAe,MAAM,KAAK,EAAE;AAClC,UAAM,WAAiB,eAAO,YAAY;AAC1C,UAAM,SAAS,WAAW;AACpB,IAAA,kBAAU,MAAM;AACpB,YAAM,gBAAgB,SAAS;AAC/B,eAAS,UAAU;AACnB,UAAI,kBAAkB,cAAc;AAClC;MACF;AAEA,UAAI,cAAc,MAAM,MAAM,CAAC,SAAS,SAAS,EAAE,KAAK,MAAM,WAAW,QAAQ;AAC/E,uBAAe,MAAM,KAAK,EAAE,CAAC;AAC7B,sBAAc;MAChB;IACF,GAAG,CAAC,eAAe,YAAY,cAAc,QAAQ,cAAc,KAAK,CAAC;AACzE,UAAM,aAAa,cAAc;AAEjC,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,cAAA,0BAACN,YAAW,UAAX,EAAoB,OAAO,6BAA6B,OAAO,iBAC9D,cAAA,0BAACA,YAAW,MAAX,EAAgB,OAAO,6BACtB,cAAA;UAAkBO;UAAjB;YACC,SAAO;YACN,GAAG;YACJ;YACA,KAAK;YAEL,cAAA;cAAW,KAAK;cAAf;gBACE,GAAG;gBACJ,MAAK;gBACL,KAAK;gBACL,SAAS;kBACP;kBACA,CAAC,UAAgD;AAC/C,0BAAM,eAAe;AACrB,0BAAM,cAAc,MAAM,cAAc,QAAQ,MAAM;AACtD,6BAAS,EAAE,MAAM,SAAS,OAAO,YAAY,CAAC;kBAChD;gBACF;gBAEC;cAAA;YACH;UAAA;QACF,EAAA,CACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAmBA,IAAM,kCAAwC,mBAG5C,SAASC,iCACT,EAAE,6BAA6B,GAAG,MAAM,GACxC,cACA;AACA,QAAM,EAAE,OAAO,gBAAgB,KAAK,IAAI;IACtC;IACA;EACF;AACA,QAAM,MAAM,gBAAgB,gBAAgB,YAAY;AACxD,aACE;IAAC;IAAA;MACC;MACA;MACA,OAAO,MAAM,KAAK,EAAE,EAAE,KAAK;MAC3B,cAAa;MACb,WAAW;MACX,gBAAe;MACf,aAAY;MACZ,UAAS;MACT,YAAY;MACX,GAAG;MACJ,MAAK;MACL,UAAQ;IAAA;EACV;AAEJ,CAAC;AAgCD,IAAM,4BAAkC,mBAGtC,SAASC,2BACT;EACE;EACA;EACA,OAAO;EACP,GAAG;AACL,GACA,cACA;AAEA,QAAM;IACJ,OAAO;IACP,cAAc;IACd,UAAU;IACV,UAAU;IACV,cAAc;IACd,WAAW;IACX,MAAM;IACN,MAAM;IACN,aAAa;IACb,MAAM;IACN,GAAG;EACL,IAAI;AAEJ,QAAM,UAAU;IACd;IACA;EACF;AACA,QAAM,EAAE,UAAU,eAAe,gBAAgB,WAAW,IAAI;AAChE,QAAM,aAAaR,eAAc,2BAA2B;AAC5D,QAAM,wBAAwBE,0BAAyB,2BAA2B;AAElF,QAAM,WAAiB,eAAyB,IAAI;AACpD,QAAM,CAAC,SAAS,UAAU,IAAU,iBAAkC,IAAI;AAE1E,QAAM,QAAQ,cAAc,UAAU,WAAW,QAAQ,OAAO,IAAI;AACpE,QAAM,oBAAoB,aAAa,QAAQ;AAC/C,MAAI;AACJ,MAAI,qBAAqB,QAAQ,eAAe,QAAQ,MAAM,WAAW,GAAG;AAG1E,kBAAc,QAAQ,YAAY,KAAK;EACzC;AAEA,QAAM,mBAAmB,gBAAgB,cAAc,UAAU,UAAU;AAC3E,QAAM,OAAO,QAAQ,MAAM,KAAK,KAAK;AAErC,QAAM,2BAAiC,eAAsB,IAAI;AAC3D,EAAA,kBAAU,MAAM;AACpB,WAAO,MAAM;AACX,aAAO,aAAa,yBAAyB,OAAQ;IACvD;EACF,GAAG,CAAC,CAAC;AAEL,QAAM,aAAa,QAAQ,MAAM,KAAK,EAAE,EAAE,KAAK;AAC/C,QAAM,sBAAsB,MAAM,WAAW,QAAQ,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC;AAC7E,QAAM,cAAc,SAAS;AAE7B,QAAM,aACJ,kBAAkB,uBACd,qBAAqB,cAAuC,IAC5D;AAEN,aACE,0BAACH,YAAW,UAAX,EAAoB,OAAO,6BAC1B,cAAA;IAAkB;IAAjB;MACE,GAAG;MACJ,SAAO;MACP,WAAW,CAAC,QAAQ,YAAY;MAChC,QAAQ,UAAU;MAEjB,UAAA,CAAC,EAAE,YAAY,iBAAiB,MAAM;AACrC,cAAM,uBAAuB,aAAa,mBAAmB,UAAU;AACvE,mBACE;UAAW,KAAK;UAAf;YACC,KAAK;YACL,MAAM,QAAQ;YACd,cAAY,aAAa,QAAQ,CAAC,OAAO,WAAW,IAAI;YACxD,cAAc,uBAAuB,QAAQ,eAAe;YAC5D,kBAAgB,uBAAuB,SAAY;YACnD,iBAAe,uBAAuB,SAAY;YAClD,0BAAwB,uBAAuB,SAAY;YAC3D,iBAAe,uBAAuB,SAAY;YAClD,WAAW,YAAY;YACvB,WAAW;YACX,SAAS,YAAY;YACrB,UAAU,QAAQ;YAClB,OAAO;YACP;YACA,wBAAqB;YACrB,oBAAkB;YACjB,GAAG;YACJ,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,oBAAM,cAAc,OAAO;YAC7B,CAAC;YACD,OAAO,qBAAqB,MAAM,OAAO,CAAC,UAAU;AAClD,oBAAM,eAAe,MAAM,cAAc;AACzC,kBAAI,iBAAiB,IAAI;AAMvB,8BAAc,UAAU;kBACtB,MAAM;gBACR;AAGA,yCAAyB,UAAU,OAAO,WAAW,MAAM;AACzD,gCAAc,UAAU;gBAC1B,GAAG,EAAE;cACP;YACF,CAAC;YACD,SAAS,qBAAqB,MAAM,SAAS,CAAC,UAAU;AACtD,oBAAM,QAAQ,MAAM,cAAc;AAClC,kBAAI,MAAM,SAAS,GAAG;AAKpB,sBAAM,eAAe;AACrB,yBAAS,EAAE,MAAM,SAAS,MAAM,CAAC;cACnC;YACF,CAAC;YACD,UAAU,qBAAqB,MAAM,UAAU,CAAC,UAAU;AACxD,oBAAM,QAAQ,MAAM,OAAO;AAC3B,oBAAM,eAAe;AACrB,oBAAM,SAAS,cAAc;AAC7B,4BAAc,UAAU;AAExB,kBAAI,QAAQ;AACV,wBAAQ,OAAO,MAAM;kBACnB,KAAK;AAIH,6BAAS,EAAE,MAAM,cAAc,OAAO,QAAQ,MAAM,CAAC;AACrD;kBACF,KAAK,WAAW;AACd,wBAAI,OAAO,QAAQ,QAAQ;AAGzB;oBACF;AAEA,0BAAM,aACJ,OAAO,QAAQ,gBAAgB,OAAO,WAAW,OAAO;AAC1D,wBAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,+BAAS,EAAE,MAAM,SAAS,QAAQ,YAAY,CAAC;oBACjD,OAAO;AACL,+BAAS,EAAE,MAAM,cAAc,OAAO,QAAQ,OAAO,IAAI,CAAC;oBAC5D;AACA;kBACF;kBACA;AACE;gBACJ;cACF;AAGA,kBAAI,MAAM,OAAO,SAAS,OAAO;AAC/B,oBAAI,UAAU,IAAI;AAChB,sBAAI,SAAyC;AAC7C,sBAAI,aAAa,MAAM,WAAW,GAAG;AACnC,0BAAM,YAAY,MAAM,YAAY;AACpC,wBAAI,cAAc,yBAAyB;AACzC,+BAAS;oBACX,WAAW,cAAc,eAAe;AACtC,+BAAS;oBACX;kBACF;AACA,2BAAS,EAAE,MAAM,cAAc,OAAO,OAAO,CAAC;gBAChD,OAAO;AACL,2BAAS,EAAE,MAAM,YAAY,MAAM,OAAO,OAAO,MAAM,CAAC;gBAC1D;cACF,OAAO;AACL,sBAAMU,WAAU,MAAM;AACtB,kCAAkBA,SAAQ,KAAK;AAC/B,sCAAsB,MAAM;AAC1B,sBAAIA,SAAQ,cAAc,kBAAkBA,UAAS;AACnDA,6BAAQ,OAAO;kBACjB;gBACF,CAAC;cACH;YACF,CAAC;YACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,sBAAQ,MAAM,KAAK;gBACjB,KAAK;gBACL,KAAK;gBACL,KAAK,aAAa;AAChB,wBAAM,eAAe,MAAM,cAAc;AAEzC,sBAAI,iBAAiB,IAAI;AAEvB,wBAAI,MAAM,QAAQ,SAAU;AAE5B,0BAAM,aAAa,MAAM,QAAQ,WAAW,MAAM,WAAW,MAAM;AACnE,wBAAI,YAAY;AACd,+BAAS,EAAE,MAAM,SAAS,QAAQ,YAAY,CAAC;oBACjD,OAAO;AACL,4BAAMA,WAAU,MAAM;AACtB,4CAAsB,MAAM;AAC1B,mCAAW,WAAW,KAAKA,UAAS,EAAE,GAAG,OAAO;sBAClD,CAAC;oBACH;kBACF,OAAO;AAOL,kCAAc,UAAU;sBACtB,MAAM;sBACN,KAAK,MAAM;sBACX,SAAS,MAAM;sBACf,SAAS,MAAM;oBACjB;AAGA,6CAAyB,UAAU,OAAO,WAAW,MAAM;AACzD,oCAAc,UAAU;oBAC1B,GAAG,EAAE;kBACP;AAEA;gBACF;gBACA,KAAK,SAAS;AACZ,wBAAM,eAAe;AACrB,0BAAQ,cAAc;AACtB;gBACF;gBACA,KAAK;gBACL,KAAK,WAAW;AACd,sBAAI,QAAQ,gBAAgB,cAAc;AAGxC,0BAAM,eAAe;kBACvB;AACA;gBACF;;gBAEA,SAAS;AACP,sBAAI,MAAM,cAAc,UAAU,MAAM,KAAK;AAG3C,0BAAMA,WAAU,MAAM;AACtB,0BAAM,eAAe;AACrB,+BAAW,WAAW,KAAKA,UAAS,CAAC,GAAG,OAAO;AAC/C;kBACF;;oBAEE,MAAM,cAAc;oBAEpB,EACE,MAAM,cAAc,mBAAmB,KACvC,MAAM,cAAc,gBAAgB,QACpC,MAAM,cAAc,eAAe;oBAErC;AACA,0BAAM,iBAAiB,MAAM;AAC7B,wBAAI,MAAM,IAAI,SAAS,KAAK,MAAM,QAAQ,KAAK;AAE7C;oBACF,OAAO;AAIL,4BAAM,YAAY,WAAW,KAAK,MAAM,eAAe,CAAC,GAAG;AAC3D,4BAAM,YAAY,WAAW,GAAG,EAAE,GAAG;AACrC,0BAAI,cAAc,aAAa,MAAM,kBAAkB,WAAW;AAIhE,4BAAI,MAAM,cAAc,mBAAmB,GAAG;AAC5C,mCAAS,EAAE,MAAM,YAAY,MAAM,gBAAgB,OAAO,MAAM,CAAC;wBACnE,OAAO;AACL,mCAAS;4BACP,MAAM;4BACN,MAAM;4BACN,OAAO,QAAQ;4BACf;0BACF,CAAC;wBACH;AAEA,sCAAc,UAAU;0BACtB,MAAM;0BACN,KAAK;0BACL,SAAS,MAAM;0BACf,SAAS,MAAM;wBACjB;AACA,iDAAyB,UAAU,OAAO,WAAW,MAAM;AACzD,wCAAc,UAAU;wBAC1B,GAAG,EAAE;sBACP;oBACF;kBACF;gBACF;cACF;YACF,CAAC;YACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,oBAAM,eAAe;AACrB,oBAAM,eAAe,KAAK,IAAI,OAAO,mBAAmB;AACxD,oBAAMA,WAAU,WAAW,GAAG,YAAY,GAAG;AAC7C,yBAAWA,QAAO;YACpB,CAAC;UAAA;QACH;MAEJ;IAAA;EACF,EAAA,CACF;AAEJ,CAAC;AAoBD,SAAS,cAAc,SAAiE;AACtF,SAAO,SAAS,YAAY;AAC9B;AAEA,SAAS,iBAAiB,OAAe;AACvC,SAAO,MAAM,QAAQ,OAAO,EAAE;AAChC;AAEA,SAAS,WAAW,SAA8C;AAChE,MAAI,CAAC,QAAS;AACd,MAAI,QAAQ,cAAc,kBAAkB,SAAS;AAGnD,WAAO,sBAAsB,MAAM;AACjC,cAAQ,SAAS;IACnB,CAAC;EACH,OAAO;AACL,YAAQ,MAAM;EAChB;AACF;AAEA,SAAS,aAAa,OAAmC;AACvD,SAAO,MAAM,SAAS;AACxB;;;;;;;;;;;;;;;;ACv5BA,IAAAC,UAAuB;AACvB,IAAAC,oBAA0B;AAkFtB,IAAAC,uBAAA;AAvEJ,IAAM,6BAA6B;AAqBnC,IAAM,CAAC,gCAAgC,IAAI,mBAAmB,0BAA0B;AACxF,IAAM,CAAC,6BAA6B,6BAA6B,IAC/D,iCAAkE,0BAA0B;AAgB9F,IAAM,sBAA0C;EAC9C,gBAAgB;EAChB,gBAAgB;EAChB,cAAc;AAChB;AAEA,IAAM,sBAA0D,CAAC;EAC/D;EACA,GAAG;AACL,MAA6C;AAC3C,QAAM,SAAS,MAAM,MAAM,EAAE;AAC7B,QAAM,iBAAiB,GAAG,MAAM;AAChC,QAAM,CAAC,cAAc,eAAe,IAAU,iBAAwB,cAAc;AACpF,QAAM,UAAU,gBAAgB;AAChC,QAAM,cAAoB;IACxB,CAAC,eACC,gBAAgB,cAAc,OAAO,OAAO,UAAU,IAAI,IAAI;IAChE,CAAC;EACH;AAEA,QAAM,EAAE,SAAS,aAAa,gBAAgB,mBAAmB,SAAS,IAAI;AAC9E,QAAM,CAAC,UAAU,OAAO,UAAU,IAAI,qBAAqB;IACzD,QAAQ;IACR,MAAM;IACN,aAAa,kBAAkB;IAC/B,UAAU;EACZ,CAAC;AAED,QAAM,WAAiB,eAAgC,IAAI;AAC3D,QAAM,aAAmB,eAA2B,mBAAmB;AAEvE,aACE;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA;MACA;MACA;MACA;MAEC;IAAA;EACH;AAEJ;AACA,oBAAoB,cAAc;AAMlC,IAAM,mCAAmC,6BAA6B;AActE,IAAM,2BAAiC;EACrC,CACE;IACE;IACA,eAAe;IACf,iBAAiB;IACjB,aAAa;IACb,IAAI;IACJ,GAAG;EACL,GACA,iBACG;AACH,UAAM,EAAE,SAAS,UAAU,SAAS,aAAa,YAAY,WAAW,IACtE,8BAA8B,kCAAkC,0BAA0B;AAEtF,IAAA,kBAAU,MAAM;AACpB,kBAAY,MAAM;IACpB,GAAG,CAAC,QAAQ,WAAW,CAAC;AAUxB,UAAM,cAAc,eAAe,UAAU;AACvC,IAAA,kBAAU,MAAM;AACpB,YAAM,eAAe,SAAS;AAC9B,YAAM,OAAO,cAAc;AAC3B,UAAI,CAAC,MAAM;AACT;MACF;AAEA,YAAM,aAAa,IAAI,gBAAgB;AACvC,WAAK;QACH;QACA,CAAC,UAAU;AACT,cAAI,CAAC,MAAM,kBAAkB;AAC3B,wBAAY,KAAK;UACnB;QACF;QACA,EAAE,QAAQ,WAAW,OAAO;MAC9B;AACA,WAAK;QACH;QACA,MAAM;AAGJ,sBAAY,KAAK;QACnB;QACA,EAAE,QAAQ,WAAW,OAAO;MAC9B;AACA,aAAO,MAAM;AACX,mBAAW,MAAM;MACnB;IACF,GAAG,CAAC,UAAU,WAAW,CAAC;AAE1B,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,IAAI,UAAU;QACd;QACA;QACA,KAAK,gBAAgB,cAAc,QAAQ;QAC3C;QACA,MAAM,UAAU,SAAS;QACzB,QAAQ,qBAAqB,MAAM,QAAQ,CAAC,UAAU;AAEpD,gBAAM,EAAE,gBAAgB,aAAa,IAAI,MAAM;AAC/C,qBAAW,QAAQ,iBAAiB;AACpC,qBAAW,QAAQ,eAAe;QACpC,CAAC;MAAA;IACH;EAEJ;AACF;AACA,yBAAyB,cAAc;AAMvC,IAAM,oCAAoC,6BAA6B;AAMvE,IAAM,4BAAkC;EAItC,CACE;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd,iBAAiB;IACjB,eAAe;IACf;IACA,GAAG;EACL,GACA,iBACG;AACH,UAAM,EAAE,YAAY,SAAS,UAAU,SAAS,WAAW,IAAI;MAC7D;MACA;IACF;AACA,UAAM,CAAC,mBAAmB,oBAAoB,IAAU,iBAA6B,MAAS;AAC9F,UAAM,aAAmB,eAA0B,IAAI;AACvD,UAAM,MAAM,gBAAgB,cAAc,UAAU;AACpD,UAAM,aAAa,cAAc;AAE3B,IAAA,kBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,UAAI,CAAC,WAAW,eAAe;AAC7B,6BAAqB,MAAS;AAC9B;MACF;AAEA,YAAM,qBAAqB,UAAU,kBAAkB;AAEvD,eAAS,uBAAuB,aAAwC;AACtE,cAAM,OAAO,cAAc,cAAc;AAEzC,6BAAqB,OAAO,SAAY,kBAAkB;MAC5D;AAEA,6BAAuB,QAAQ,WAAW;AAE1C,YAAM,WAAW,IAAI,iBAAiB,CAAC,YAAY;AACjD,YAAI;AACJ,mBAAW,SAAS,SAAS;AAC3B,cAAI,MAAM,SAAS,iBAAiB;AAClC,gBAAI,QAAQ,aAAa;AACvB,4BAAc,QAAQ;YACxB;UACF;QACF;AACA,+BAAuB,WAAW;MACpC,CAAC;AACD,eAAS,QAAQ,SAAS,EAAE,eAAe,MAAM,SAAS,KAAK,CAAC;AAChE,aAAO,MAAM;AACX,iBAAS,WAAW;MACtB;IACF,GAAG,CAAC,SAAS,aAAa,CAAC;AAE3B,UAAM,YAAY,iBAAiB;AAMnC,QAAI,CAAC,YAAY;AACf,qBAAe;AACf,mBAAa;IACf,OAAO;AACL,uBAAiB;IACnB;AAEM,IAAA,kBAAU,MAAM;AACpB,UAAI,UAAU,MAAM;MAAC;AACrB,YAAM,cAAc,WAAW,SAAS,eAAe,eAAe;AACtE,YAAM,QAAQ,MAAO,WAAW,QAAQ,iBAAiB;AACzD,YAAM,kBAAkB,MAAO,UAAU,oBAAoB,aAAa,KAAK;AAC/E,kBAAY,iBAAiB,aAAa,eAAe;AACzD,aAAO,MAAM;AACX,gBAAQ;AACR,oBAAY,oBAAoB,aAAa,eAAe;MAC9D;IACF,GAAG,CAAC,UAAU,CAAC;AAEf,eACE;MAAC,UAAU;MAAV;QACC,iBAAe;QACf,eAAa;QACb,cAAY;QACZ;QACA,IAAI;QACH,GAAG;QACJ,eAAe,qBAAqB,eAAe,MAAM;AACvD,qBAAW,QAAQ,iBAAiB;QACtC,CAAC;QACD,iBAAiB,CAAC,UAAU;AAI1B,4BAAkB,KAAK;AACvB,qBAAW,UAAU;QACvB;QAIA,SAAS,CAAC,UAAU;AAClB,oBAAU,KAAK;AACf,cAAI,MAAM,kBAAkB;AAC1B,uBAAW,UAAU;AACrB;UACF;AAEA,2CAAU,MAAM;AACd,uBAAW,CAAC,MAAM,CAAC,CAAC;UACtB,CAAC;AACD,cAAI,WAAW,QAAQ,gBAAgB;AACrC,kBAAM,QAAQ,SAAS;AACvB,gBAAI,OAAO;AACT,oBAAM,EAAE,gBAAgB,aAAa,IAAI,WAAW;AACpD,oBAAM,MAAM;AACZ,kBAAI,mBAAmB,QAAQ,iBAAiB,MAAM;AAEpD,sCAAsB,MAAM;AAG1B,sBAAI,MAAM,cAAc,kBAAkB,OAAO;AAC/C,0BAAM,iBAAiB;AACvB,0BAAM,eAAe;kBACvB;gBACF,CAAC;cACH;YACF;UACF;AACA,qBAAW,UAAU;QACvB;QACA,aAAa,CAAC,UAAU;AACtB,wBAAc,KAAK;AAInB,qBAAW,MAAM;AACf,uBAAW,UAAU;UACvB,GAAG,EAAE;QACP;QACA,MAAK;QAEJ;MAAA;IACH;EAEJ;AACF;AACA,0BAA0B,cAAc;AAMxC,IAAM,kCAAkC,6BAA6B;AAerE,IAAM,0BAAkE,CAAC;EACvE;EACA,GAAG;AACL,MAAiD;AAC/C,QAAM,EAAE,QAAQ,IAAI;IAClB;IACA;EACF;AAEA,SAAO,YAAY;;IAEf,MAAM,OAAO,EAAE,QAAQ,CAAC;MACxB,UACE,MAAM,UACN,MAAM;AACd;AACA,wBAAwB,cAAc;AAMtC,IAAM,kCAAkC,6BAA6B;AASrE,IAAM,0BAAgC;EACpC,CACE;IACE;;IAEA;IACA,GAAG;EACL,GACA,iBACG;AACH,UAAM,EAAE,QAAQ,IAAI;MAClB;MACA;IACF;AACA,UAAM,EAAE,SAAS,aAAa,QAAQ,YAAY,GAAG,SAAS,IAAI;AAClE,eACE,0BAAC,UAAU,KAAV,EAAe,GAAG,UAAU,KAAK,cAAc,eAAW,MAAC,SAAO,MAChE,UAAA,UAAU,cAAc,WAAA,CAC3B;EAEJ;AACF;AACA,wBAAwB,cAAc;AAuBtC,SAAS,oBACPC,SACA,UACA,SACY;AACZ,MAAKA,QAAe,qBAAqB;AACvC,UAAMC,MAAKD,QAAO,oBAAoB,UAAU,OAAO;AACvD,WAAO,MAAM;AACXA,cAAO,mBAAmBC,GAAE;IAC9B;EACF;AACA,QAAM,QAAQ,KAAK,IAAI;AACvB,QAAM,KAAKD,QAAO,WAAW,MAAM;AACjC,UAAM,gBAAgB,MAAM,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM;AACjE,aAAS,EAAE,YAAY,OAAO,cAAc,CAAC;EAC/C,GAAG,CAAC;AACJ,SAAO,MAAM;AACXA,YAAO,aAAa,EAAE;EACxB;AACF;;;;;;;;;;;;;;;AC9dA,IAAAE,UAAuB;AAgKX,IAAAC,uBAAA;AAhJZ,IAAM,YAAY,CAAC,UAAU,UAAU;AACvC,IAAMC,cAAa,CAAC,WAAW,aAAa,aAAa,YAAY;AAGrE,IAAM,YAA8C;EAClD,aAAa,CAAC,QAAQ,YAAY,aAAa,WAAW;EAC1D,cAAc,CAAC,QAAQ,YAAY,aAAa,YAAY;EAC5D,eAAe,CAAC,QAAQ,YAAY,aAAa,WAAW;EAC5D,YAAY,CAAC,QAAQ,YAAY,WAAW,WAAW;AACzD;AAMA,IAAM,cAAc;AAEpB,IAAM,CAACC,aAAYC,gBAAeC,sBAAqB,IACrD,iBAAqC,WAAW;AAGlD,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,aAAa;EAC/EA;AACF,CAAC;AAcD,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAwB9F,IAAM,SAAe;EACnB,CAAC,OAAiC,iBAAiB;AACjD,UAAM;MACJ;MACA,MAAM;MACN,MAAM;MACN,OAAO;MACP,cAAc;MACd,WAAW;MACX,wBAAwB;MACxB,eAAe,CAAC,GAAG;MACnB;MACA,gBAAgB,MAAM;MAAC;MACvB,gBAAgB,MAAM;MAAC;MACvB,WAAW;MACX;MACA,GAAG;IACL,IAAI;AACJ,UAAM,YAAkB,eAAqC,oBAAI,IAAI,CAAC;AACtE,UAAM,wBAA8B,eAAe,CAAC;AACpD,UAAM,eAAe,gBAAgB;AACrC,UAAM,oBAAoB,eAAe,mBAAmB;AAE5D,UAAM,CAAC,SAAS,CAAC,GAAG,SAAS,IAAI,qBAAqB;MACpD,MAAM;MACN,aAAa;MACb,UAAU,CAACC,WAAU;AACnB,cAAM,SAAS,CAAC,GAAG,UAAU,OAAO;AACpC,eAAO,sBAAsB,OAAO,GAAG,MAAM;AAC7C,sBAAcA,MAAK;MACrB;IACF,CAAC;AACD,UAAM,4BAAkC,eAAO,MAAM;AAErD,aAAS,iBAAiBA,QAAe;AACvC,YAAM,eAAe,qBAAqB,QAAQA,MAAK;AACvD,mBAAaA,QAAO,YAAY;IAClC;AAEA,aAAS,gBAAgBA,QAAe;AACtC,mBAAaA,QAAO,sBAAsB,OAAO;IACnD;AAEA,aAAS,iBAAiB;AACxB,YAAM,YAAY,0BAA0B,QAAQ,sBAAsB,OAAO;AACjF,YAAM,YAAY,OAAO,sBAAsB,OAAO;AACtD,YAAM,aAAa,cAAc;AACjC,UAAI,WAAY,eAAc,MAAM;IACtC;AAEA,aAAS,aAAaA,QAAe,SAAiB,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM,GAAG;AACpF,YAAM,eAAe,gBAAgB,IAAI;AACzC,YAAM,aAAa,WAAW,KAAK,OAAOA,SAAQ,OAAO,IAAI,IAAI,OAAO,KAAK,YAAY;AACzF,YAAM,YAAY,MAAM,YAAY,CAAC,KAAK,GAAG,CAAC;AAE9C,gBAAU,CAAC,aAAa,CAAC,MAAM;AAC7B,cAAM,aAAa,oBAAoB,YAAY,WAAW,OAAO;AACrE,YAAI,yBAAyB,YAAY,wBAAwB,IAAI,GAAG;AACtE,gCAAsB,UAAU,WAAW,QAAQ,SAAS;AAC5D,gBAAM,aAAa,OAAO,UAAU,MAAM,OAAO,UAAU;AAC3D,cAAI,cAAc,OAAQ,eAAc,UAAU;AAClD,iBAAO,aAAa,aAAa;QACnC,OAAO;AACL,iBAAO;QACT;MACF,CAAC;IACH;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb;QACA;QACA;QACA;QACA;QACA,QAAQ,UAAU;QAClB;QACA;QACA;QAEA,cAAA,0BAACH,YAAW,UAAX,EAAoB,OAAO,MAAM,eAChC,cAAA,0BAACA,YAAW,MAAX,EAAgB,OAAO,MAAM,eAC5B,cAAA;UAAC;UAAA;YACC,iBAAe;YACf,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;YACL,eAAe,qBAAqB,YAAY,eAAe,MAAM;AACnE,kBAAI,CAAC,SAAU,2BAA0B,UAAU;YACrD,CAAC;YACD;YACA;YACA;YACA,cAAc,WAAW,SAAY;YACrC,aAAa,WAAW,SAAY;YACpC,YAAY,WAAW,SAAY;YACnC,eAAe,MAAM,CAAC,YAAY,aAAa,KAAK,GAAG,EAAE,QAAQ,KAAK,CAAC;YACvE,cAAc,MACZ,CAAC,YAAY,aAAa,KAAK,OAAO,SAAS,GAAG,EAAE,QAAQ,KAAK,CAAC;YAEpE,eAAe,CAAC,EAAE,OAAO,WAAW,cAAc,MAAM;AACtD,kBAAI,CAAC,UAAU;AACb,sBAAM,YAAY,UAAU,SAAS,MAAM,GAAG;AAC9C,sBAAM,YAAY,aAAc,MAAM,YAAYD,YAAW,SAAS,MAAM,GAAG;AAC/E,sBAAM,aAAa,YAAY,KAAK;AACpC,sBAAM,UAAU,sBAAsB;AACtC,sBAAMI,SAAQ,OAAO,OAAO;AAC5B,sBAAM,kBAAkB,OAAO,aAAa;AAC5C,6BAAaA,SAAQ,iBAAiB,SAAS,EAAE,QAAQ,KAAK,CAAC;cACjE;YACF;UAAA;QACF,EAAA,CACF,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,OAAO,cAAc;AAQrB,IAAM,CAAC,2BAA2B,2BAA2B,IAAI,oBAK9D,aAAa;EACd,WAAW;EACX,SAAS;EACT,MAAM;EACN,WAAW;AACb,CAAC;AAsBD,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,CAAC,QAAQ,SAAS,IAAU,iBAAmC,IAAI;AACzE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,UAAU,IAAI,CAAC;AAC5E,UAAM,UAAgB,eAAgB,MAAS;AAC/C,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,iBAAiB,cAAc;AACrC,UAAM,oBAAqB,kBAAkB,CAAC,YAAc,CAAC,kBAAkB;AAE/E,aAAS,oBAAoB,iBAAyB;AACpD,YAAM,OAAO,QAAQ,WAAW,OAAQ,sBAAsB;AAC9D,YAAM,QAA0B,CAAC,GAAG,KAAK,KAAK;AAC9C,YAAM,SAA2B,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAC3E,YAAM,QAAQ,YAAY,OAAO,MAAM;AAEvC,cAAQ,UAAU;AAClB,aAAO,MAAM,kBAAkB,KAAK,IAAI;IAC1C;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,WAAW,oBAAoB,SAAS;QACxC,SAAS,oBAAoB,UAAU;QACvC,WAAW,oBAAoB,IAAI;QACnC,MAAK;QAEL,cAAA;UAAC;UAAA;YACC,KAAK;YACL,oBAAiB;YAChB,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,YAAY;cACf,CAAC,gCAAuC,GAAG;YAC7C;YACA,cAAc,CAAC,UAAU;AACvB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,6BAAe,KAAK;YACtB;YACA,aAAa,CAAC,UAAU;AACtB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,4BAAc,KAAK;YACrB;YACA,YAAY,MAAM;AAChB,sBAAQ,UAAU;AAClB,2BAAa;YACf;YACA,eAAe,CAAC,UAAU;AACxB,oBAAM,iBAAiB,oBAAoB,cAAc;AACzD,oBAAM,YAAY,UAAU,cAAc,EAAE,SAAS,MAAM,GAAG;AAC9D,8BAAgB,EAAE,OAAO,WAAW,YAAY,KAAK,EAAE,CAAC;YAC1D;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AASA,IAAM,iBAAuB;EAC3B,CAAC,OAAyC,iBAAiB;AACzD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,YAAkB,eAA0B,IAAI;AACtD,UAAM,MAAM,gBAAgB,cAAc,SAAS;AACnD,UAAM,UAAgB,eAAgB,MAAS;AAC/C,UAAM,sBAAsB,CAAC;AAE7B,aAAS,oBAAoB,iBAAyB;AACpD,YAAM,OAAO,QAAQ,WAAW,UAAU,QAAS,sBAAsB;AACzE,YAAM,QAA0B,CAAC,GAAG,KAAK,MAAM;AAC/C,YAAM,SAA2B,sBAAsB,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;AAC7E,YAAM,QAAQ,YAAY,OAAO,MAAM;AAEvC,cAAQ,UAAU;AAClB,aAAO,MAAM,kBAAkB,KAAK,GAAG;IACzC;AAEA,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,WAAW,sBAAsB,WAAW;QAC5C,SAAS,sBAAsB,QAAQ;QACvC,MAAK;QACL,WAAW,sBAAsB,IAAI;QAErC,cAAA;UAAC;UAAA;YACC,oBAAiB;YAChB,GAAG;YACJ;YACA,OAAO;cACL,GAAG,YAAY;cACf,CAAC,gCAAuC,GAAG;YAC7C;YACA,cAAc,CAAC,UAAU;AACvB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,6BAAe,KAAK;YACtB;YACA,aAAa,CAAC,UAAU;AACtB,oBAAM,QAAQ,oBAAoB,MAAM,OAAO;AAC/C,4BAAc,KAAK;YACrB;YACA,YAAY,MAAM;AAChB,sBAAQ,UAAU;AAClB,2BAAa;YACf;YACA,eAAe,CAAC,UAAU;AACxB,oBAAM,iBAAiB,sBAAsB,gBAAgB;AAC7D,oBAAM,YAAY,UAAU,cAAc,EAAE,SAAS,MAAM,GAAG;AAC9D,8BAAgB,EAAE,OAAO,WAAW,YAAY,KAAK,EAAE,CAAC;YAC1D;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAkBA,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAE3D,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAI,MAAM,QAAQ,QAAQ;AACxB,0BAAc,KAAK;AAEnB,kBAAM,eAAe;UACvB,WAAW,MAAM,QAAQ,OAAO;AAC9B,yBAAa,KAAK;AAElB,kBAAM,eAAe;UACvB,WAAW,UAAU,OAAOJ,WAAU,EAAE,SAAS,MAAM,GAAG,GAAG;AAC3D,0BAAc,KAAK;AAEnB,kBAAM,eAAe;UACvB;QACF,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,SAAS,MAAM;AACrB,iBAAO,kBAAkB,MAAM,SAAS;AAExC,gBAAM,eAAe;AAGrB,cAAI,QAAQ,OAAO,IAAI,MAAM,GAAG;AAC9B,mBAAO,MAAM;UACf,OAAO;AACL,yBAAa,KAAK;UACpB;QACF,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,EAAG,aAAY,KAAK;QAClE,CAAC;QACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,gBAAM,SAAS,MAAM;AACrB,cAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,mBAAO,sBAAsB,MAAM,SAAS;AAC5C,uBAAW,KAAK;UAClB;QACF,CAAC;MAAA;IACH;EAEJ;AACF;AAMA,IAAM,aAAa;AAMnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,eACE;MAAC,UAAU;MAAV;QACC,iBAAe,QAAQ,WAAW,KAAK;QACvC,oBAAkB,QAAQ;QACzB,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,cAAc,4BAA4B,YAAY,aAAa;AACzE,UAAM,MAAY,eAAwB,IAAI;AAC9C,UAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,UAAM,cAAc,QAAQ,OAAO;AACnC,UAAM,cAAc,QAAQ,OAAO;MAAI,CAAC,UACtC,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,GAAG;IAC1D;AACA,UAAM,cAAc,cAAc,IAAI,KAAK,IAAI,GAAG,WAAW,IAAI;AACjE,UAAM,YAAY,MAAM,KAAK,IAAI,GAAG,WAAW;AAE/C,eACE;MAAC,UAAU;MAAV;QACC,oBAAkB,QAAQ;QAC1B,iBAAe,QAAQ,WAAW,KAAK;QACtC,GAAG;QACJ,KAAK;QACL,OAAO;UACL,GAAG,MAAM;UACT,CAAC,YAAY,SAAS,GAAG,cAAc;UACvC,CAAC,YAAY,OAAO,GAAG,YAAY;QACrC;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,WAAWE,eAAc,MAAM,aAAa;AAClD,UAAM,CAAC,OAAO,QAAQ,IAAU,iBAAwC,IAAI;AAC5E,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,IAAI,CAAC;AAC3E,UAAM,QAAc;MAClB,MAAO,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,KAAK,IAAI;MAC5E,CAAC,UAAU,KAAK;IAClB;AACA,eAAO,0BAAC,iBAAA,EAAiB,GAAG,OAAO,KAAK,cAAc,MAAA,CAAc;EACtE;AACF;AAQA,IAAM,kBAAwB;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,eAAe,OAAO,MAAM,GAAG,WAAW,IAAI;AACtD,UAAM,UAAU,iBAAiB,YAAY,aAAa;AAC1D,UAAM,cAAc,4BAA4B,YAAY,aAAa;AACzE,UAAM,CAAC,OAAO,QAAQ,IAAU,iBAAiC,IAAI;AACrE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,SAAS,IAAI,CAAC;AAE3E,UAAMG,iBAAgB,QAAQ,QAAQ,QAAQ,CAAC,CAAC,MAAM,QAAQ,MAAM,IAAI;AACxE,UAAM,OAAO,QAAQ,KAAK;AAE1B,UAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,UAAM,UACJ,UAAU,SAAY,IAAI,yBAAyB,OAAO,QAAQ,KAAK,QAAQ,GAAG;AACpF,UAAM,QAAQ,SAAS,OAAO,QAAQ,OAAO,MAAM;AACnD,UAAM,kBAAkB,OAAO,YAAY,IAAI;AAC/C,UAAM,sBAAsB,kBACxB,uBAAuB,iBAAiB,SAAS,YAAY,SAAS,IACtE;AAEE,IAAA,kBAAU,MAAM;AACpB,UAAI,OAAO;AACT,gBAAQ,OAAO,IAAI,KAAK;AACxB,eAAO,MAAM;AACX,kBAAQ,OAAO,OAAO,KAAK;QAC7B;MACF;IACF,GAAG,CAAC,OAAO,QAAQ,MAAM,CAAC;AAE1B,eACE;MAAC;MAAA;QACC,OAAO;UACL,WAAW;UACX,UAAU;UACV,CAAC,YAAY,SAAS,GAAG,QAAQ,OAAO,OAAO,mBAAmB;QACpE;QAEA,UAAA;cAAA,0BAACJ,YAAW,UAAX,EAAoB,OAAO,MAAM,eAChC,cAAA;YAAC,UAAU;YAAV;cACC,MAAK;cACL,cAAY,MAAM,YAAY,KAAK;cACnC,iBAAe,QAAQ;cACvB,iBAAe;cACf,iBAAe,QAAQ;cACvB,oBAAkB,QAAQ;cAC1B,oBAAkB,QAAQ;cAC1B,iBAAe,QAAQ,WAAW,KAAK;cACvC,UAAU,QAAQ,WAAW,SAAY;cACxC,GAAG;cACJ,KAAK;cAOL,OAAO,UAAU,SAAY,EAAE,SAAS,OAAO,IAAI,MAAM;cACzD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,wBAAQ,sBAAsB,UAAU;cAC1C,CAAC;YAAA;UACH,EAAA,CACF;UAECI,sBACC;YAAC;YAAA;cAEC,MACE,SACC,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,OAAO,SAAS,IAAI,OAAO,MAAM;cAE3E,MAAM,QAAQ;cACd;YAAA;YANK;UAOP;QAAA;MAAA;IAEJ;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAMC,qBAAoB;AAK1B,IAAM,oBAA0B;EAC9B,CAAC,EAAE,eAAe,OAAO,GAAG,MAAM,GAAwC,iBAAiB;AACzF,UAAM,MAAY,eAAyB,IAAI;AAC/C,UAAM,eAAe,gBAAgB,KAAK,YAAY;AACtD,UAAM,YAAY,YAAY,KAAK;AAG7B,IAAA,kBAAU,MAAM;AACpB,YAAM,QAAQ,IAAI;AAClB,UAAI,CAAC,MAAO;AAEZ,YAAM,aAAa,OAAO,iBAAiB;AAC3C,YAAM,aAAa,OAAO,yBAAyB,YAAY,OAAO;AACtE,YAAM,WAAW,WAAW;AAC5B,UAAI,cAAc,SAAS,UAAU;AACnC,cAAM,QAAQ,IAAI,MAAM,SAAS,EAAE,SAAS,KAAK,CAAC;AAClD,iBAAS,KAAK,OAAO,KAAK;AAC1B,cAAM,cAAc,KAAK;MAC3B;IACF,GAAG,CAAC,WAAW,KAAK,CAAC;AAWrB,eACE;MAAC,UAAU;MAAV;QACC,OAAO,EAAE,SAAS,OAAO;QACxB,GAAG;QACJ,KAAK;QACL,cAAc;MAAA;IAChB;EAEJ;AACF;AAEA,kBAAkB,cAAcA;AAIhC,SAAS,oBAAoB,aAAuB,CAAC,GAAG,WAAmB,SAAiB;AAC1F,QAAM,aAAa,CAAC,GAAG,UAAU;AACjC,aAAW,OAAO,IAAI;AACtB,SAAO,WAAW,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACxC;AAEA,SAAS,yBAAyB,OAAe,KAAa,KAAa;AACzE,QAAM,WAAW,MAAM;AACvB,QAAM,iBAAiB,MAAM;AAC7B,QAAM,aAAa,kBAAkB,QAAQ;AAC7C,SAAO,MAAM,YAAY,CAAC,GAAG,GAAG,CAAC;AACnC;AAKA,SAAS,SAAS,OAAe,aAAqB;AACpD,MAAI,cAAc,GAAG;AACnB,WAAO,SAAS,QAAQ,CAAC,OAAO,WAAW;EAC7C,WAAW,gBAAgB,GAAG;AAC5B,WAAO,CAAC,WAAW,SAAS,EAAE,KAAK;EACrC,OAAO;AACL,WAAO;EACT;AACF;AAUA,SAAS,qBAAqB,QAAkB,WAAmB;AACjE,MAAI,OAAO,WAAW,EAAG,QAAO;AAChC,QAAM,YAAY,OAAO,IAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,SAAS,CAAC;AACnE,QAAM,kBAAkB,KAAK,IAAI,GAAG,SAAS;AAC7C,SAAO,UAAU,QAAQ,eAAe;AAC1C;AAMA,SAAS,uBAAuB,OAAe,MAAc,WAAmB;AAC9E,QAAM,YAAY,QAAQ;AAC1B,QAAM,cAAc;AACpB,QAAM,SAAS,YAAY,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,SAAS,CAAC;AAC3D,UAAQ,YAAY,OAAO,IAAI,IAAI,aAAa;AAClD;AASA,SAAS,sBAAsB,QAAkB;AAC/C,SAAO,OAAO,MAAM,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,UAAU,OAAO,QAAQ,CAAC,IAAK,KAAK;AAC7E;AAcA,SAAS,yBAAyB,QAAkB,uBAA+B;AACjF,MAAI,wBAAwB,GAAG;AAC7B,UAAM,qBAAqB,sBAAsB,MAAM;AACvD,UAAM,8BAA8B,KAAK,IAAI,GAAG,kBAAkB;AAClE,WAAO,+BAA+B;EACxC;AACA,SAAO;AACT;AAGA,SAAS,YAAY,OAAkC,QAAmC;AACxF,SAAO,CAAC,UAAkB;AACxB,QAAI,MAAM,CAAC,MAAM,MAAM,CAAC,KAAK,OAAO,CAAC,MAAM,OAAO,CAAC,EAAG,QAAO,OAAO,CAAC;AACrE,UAAM,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,MAAM,CAAC;AAC3D,WAAO,OAAO,CAAC,IAAI,SAAS,QAAQ,MAAM,CAAC;EAC7C;AACF;AAEA,SAAS,gBAAgB,OAAe;AACtC,UAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI;AAC7C;AAEA,SAAS,WAAW,OAAe,cAAsB;AACvD,QAAM,UAAU,KAAK,IAAI,IAAI,YAAY;AACzC,SAAO,KAAK,MAAM,QAAQ,OAAO,IAAI;AACvC;AAEA,IAAMC,SAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;AC1xBd,IAAAC,UAAuB;AACvB,IAAAC,YAA0B;AA2FpB,IAAAC,uBAAA;AAvEN,IAAM,gBAAgB;AAEtB,IAAM,CAACC,aAAYC,gBAAeC,sBAAqB,IAAI,iBAA+B,OAAO;AAkBjG,IAAM,CAAC,oBAAoB,gBAAgB,IAAI,mBAAmB,SAAS,CAACA,sBAAqB,CAAC;AAClG,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,mBAA8C,aAAa;AA2B7D,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM;IACJ;IACA,QAAQ;IACR,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB;EACF,IAAI;AACJ,QAAM,CAAC,UAAU,WAAW,IAAU,iBAAsC,IAAI;AAChF,QAAM,CAAC,YAAY,aAAa,IAAU,iBAAS,CAAC;AACpD,QAAM,iCAAuC,eAAO,KAAK;AACzD,QAAM,mBAAyB,eAAO,KAAK;AAE3C,MAAI,CAAC,MAAM,KAAK,GAAG;AACjB,YAAQ;MACN,wCAAwC,aAAa;IACvD;EACF;AAEA,aACE,0BAACF,YAAW,UAAX,EAAoB,OAAO,cAC1B,cAAA;IAAC;IAAA;MACC,OAAO;MACP;MACA;MACA;MACA;MACA;MACA;MACA,kBAAkB;MAClB,YAAkB,oBAAY,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC;MACnF,eAAqB,oBAAY,MAAM,cAAc,CAAC,cAAc,YAAY,CAAC,GAAG,CAAC,CAAC;MACtF;MACA;MAEC;IAAA;EACH,EAAA,CACF;AAEJ;AAEA,cAAc,cAAc;AAM5B,IAAMG,iBAAgB;AACtB,IAAM,0BAA0B,CAAC,IAAI;AACrC,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAkBxB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM;MACJ;MACA,SAAS;MACT,QAAQ;MACR,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,wBAAwBA,gBAAe,YAAY;AACnE,UAAM,WAAWF,eAAc,YAAY;AAC3C,UAAM,aAAmB,eAAuB,IAAI;AACpD,UAAM,oBAA0B,eAA0B,IAAI;AAC9D,UAAM,oBAA0B,eAA0B,IAAI;AAC9D,UAAM,MAAY,eAA6B,IAAI;AACnD,UAAM,eAAe,gBAAgB,cAAc,KAAK,QAAQ,gBAAgB;AAChF,UAAM,cAAc,OAAO,KAAK,GAAG,EAAE,QAAQ,QAAQ,EAAE,EAAE,QAAQ,UAAU,EAAE;AAC7E,UAAM,YAAY,QAAQ,aAAa;AAEjC,IAAA,kBAAU,MAAM;AACpB,YAAM,gBAAgB,CAAC,UAAyB;AAG9C,cAAM,kBACJ,OAAO,WAAW,KAAK,OAAO,MAAM,CAAC,QAAS,MAAc,GAAG,KAAK,MAAM,SAAS,GAAG;AACxF,YAAI,gBAAiB,KAAI,SAAS,MAAM;MAC1C;AACA,eAAS,iBAAiB,WAAW,aAAa;AAClD,aAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;IACpE,GAAG,CAAC,MAAM,CAAC;AAEL,IAAA,kBAAU,MAAM;AACpB,YAAM,UAAU,WAAW;AAC3B,YAAM,WAAW,IAAI;AACrB,UAAI,aAAa,WAAW,UAAU;AACpC,cAAM,cAAc,MAAM;AACxB,cAAI,CAAC,QAAQ,iBAAiB,SAAS;AACrC,kBAAM,aAAa,IAAI,YAAY,cAAc;AACjD,qBAAS,cAAc,UAAU;AACjC,oBAAQ,iBAAiB,UAAU;UACrC;QACF;AAEA,cAAM,eAAe,MAAM;AACzB,cAAI,QAAQ,iBAAiB,SAAS;AACpC,kBAAM,cAAc,IAAI,YAAY,eAAe;AACnD,qBAAS,cAAc,WAAW;AAClC,oBAAQ,iBAAiB,UAAU;UACrC;QACF;AAEA,cAAM,uBAAuB,CAAC,UAAsB;AAClD,gBAAM,uBAAuB,CAAC,QAAQ,SAAS,MAAM,aAA4B;AACjF,cAAI,qBAAsB,cAAa;QACzC;AAEA,cAAM,2BAA2B,MAAM;AACrC,gBAAM,gBAAgB,QAAQ,SAAS,SAAS,aAAa;AAC7D,cAAI,CAAC,cAAe,cAAa;QACnC;AAGA,gBAAQ,iBAAiB,WAAW,WAAW;AAC/C,gBAAQ,iBAAiB,YAAY,oBAAoB;AACzD,gBAAQ,iBAAiB,eAAe,WAAW;AACnD,gBAAQ,iBAAiB,gBAAgB,wBAAwB;AACjE,eAAO,iBAAiB,QAAQ,WAAW;AAC3C,eAAO,iBAAiB,SAAS,YAAY;AAC7C,eAAO,MAAM;AACX,kBAAQ,oBAAoB,WAAW,WAAW;AAClD,kBAAQ,oBAAoB,YAAY,oBAAoB;AAC5D,kBAAQ,oBAAoB,eAAe,WAAW;AACtD,kBAAQ,oBAAoB,gBAAgB,wBAAwB;AACpE,iBAAO,oBAAoB,QAAQ,WAAW;AAC9C,iBAAO,oBAAoB,SAAS,YAAY;QAClD;MACF;IACF,GAAG,CAAC,WAAW,QAAQ,gBAAgB,CAAC;AAExC,UAAM,8BAAoC;MACxC,CAAC,EAAE,iBAAiB,MAAsD;AACxE,cAAM,aAAa,SAAS;AAC5B,cAAM,qBAAqB,WAAW,IAAI,CAAC,cAAc;AACvD,gBAAM,YAAY,UAAU,IAAI;AAChC,gBAAM,0BAA0B,CAAC,WAAW,GAAGG,uBAAsB,SAAS,CAAC;AAC/E,iBAAO,qBAAqB,aACxB,0BACA,wBAAwB,QAAQ;QACtC,CAAC;AACD,gBACE,qBAAqB,aAAa,mBAAmB,QAAQ,IAAI,oBACjE,KAAK;MACT;MACA,CAAC,QAAQ;IACX;AAEM,IAAA,kBAAU,MAAM;AACpB,YAAM,WAAW,IAAI;AAIrB,UAAI,UAAU;AACZ,cAAM,gBAAgB,CAAC,UAAyB;AAC9C,gBAAM,YAAY,MAAM,UAAU,MAAM,WAAW,MAAM;AACzD,gBAAM,WAAW,MAAM,QAAQ,SAAS,CAAC;AAEzC,cAAI,UAAU;AACZ,kBAAM,iBAAiB,SAAS;AAChC,kBAAM,qBAAqB,MAAM;AACjC,kBAAM,mBAAmB,MAAM,WAAW;AAI1C,gBAAI,oBAAoB,oBAAoB;AAC1C,gCAAkB,SAAS,MAAM;AACjC;YACF;AAEA,kBAAM,mBAAmB,qBAAqB,cAAc;AAC5D,kBAAM,mBAAmB,4BAA4B,EAAE,iBAAiB,CAAC;AACzE,kBAAM,QAAQ,iBAAiB,UAAU,CAAC,cAAc,cAAc,cAAc;AACpF,gBAAIC,YAAW,iBAAiB,MAAM,QAAQ,CAAC,CAAC,GAAG;AACjD,oBAAM,eAAe;YACvB,OAAO;AAIL,mCACI,kBAAkB,SAAS,MAAM,IACjC,kBAAkB,SAAS,MAAM;YACvC;UACF;QACF;AAGA,iBAAS,iBAAiB,WAAW,aAAa;AAClD,eAAO,MAAM,SAAS,oBAAoB,WAAW,aAAa;MACpE;IACF,GAAG,CAAC,UAAU,2BAA2B,CAAC;AAE1C,eACE;MAAkB;MAAjB;QACC,KAAK;QACL,MAAK;QACL,cAAY,MAAM,QAAQ,YAAY,WAAW;QAEjD,UAAU;QAGV,OAAO,EAAE,eAAe,YAAY,SAAY,OAAO;QAEtD,UAAA;UAAA,iBACC;YAAC;YAAA;cACC,KAAK;cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;kBACrD,kBAAkB;gBACpB,CAAC;AACD,gBAAAA,YAAW,kBAAkB;cAC/B;YAAA;UACF;cAMF,0BAACL,YAAW,MAAX,EAAgB,OAAO,cACtB,cAAA,0BAAC,UAAU,IAAV,EAAa,UAAU,IAAK,GAAG,eAAe,KAAK,aAAA,CAAc,EAAA,CACpE;UACC,iBACC;YAAC;YAAA;cACC,KAAK;cACL,4BAA4B,MAAM;AAChC,sBAAM,qBAAqB,4BAA4B;kBACrD,kBAAkB;gBACpB,CAAC;AACD,gBAAAK,YAAW,kBAAkB;cAC/B;YAAA;UACF;QAAA;MAAA;IAEJ;EAEJ;AACF;AAEA,cAAc,cAAcF;AAI5B,IAAM,mBAAmB;AAQzB,IAAM,aAAmB;EACvB,CAAC,OAAO,iBAAiB;AACvB,UAAM,EAAE,cAAc,4BAA4B,GAAG,WAAW,IAAI;AACpE,UAAM,UAAU,wBAAwB,kBAAkB,YAAY;AAEtE,eACE;MAAC;MAAA;QACC,eAAW;QACX,UAAU;QACT,GAAG;QACJ,KAAK;QAEL,OAAO,EAAE,UAAU,QAAQ;QAC3B,SAAS,CAAC,UAAU;AAClB,gBAAM,qBAAqB,MAAM;AACjC,gBAAM,6BAA6B,CAAC,QAAQ,UAAU,SAAS,kBAAkB;AACjF,cAAI,2BAA4B,4BAA2B;QAC7D;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;AAMzB,IAAM,aAAa;AACnB,IAAM,oBAAoB;AAC1B,IAAM,mBAAmB;AACzB,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AAcxB,IAAM,QAAc;EAClB,CAAC,OAAgC,iBAAiB;AAChD,UAAM,EAAE,YAAY,MAAM,UAAU,aAAa,cAAc,GAAG,WAAW,IAAI;AACjF,UAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;MAC3C,MAAM;MACN,aAAa,eAAe;MAC5B,UAAU;MACV,QAAQ;IACV,CAAC;AACD,eACE,0BAAC,UAAA,EAAS,SAAS,cAAc,MAC/B,cAAA;MAAC;MAAA;QACC;QACC,GAAG;QACJ,KAAK;QACL,SAAS,MAAM,QAAQ,KAAK;QAC5B,SAAS,eAAe,MAAM,OAAO;QACrC,UAAU,eAAe,MAAM,QAAQ;QACvC,cAAc,qBAAqB,MAAM,cAAc,CAAC,UAAU;AAChE,gBAAM,cAAc,aAAa,cAAc,OAAO;QACxD,CAAC;QACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,gBAAM,EAAE,GAAG,EAAE,IAAI,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,MAAM;AACrD,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;AAC5E,gBAAM,cAAc,MAAM,YAAY,8BAA8B,GAAG,CAAC,IAAI;QAC9E,CAAC;QACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,gBAAM,cAAc,aAAa,cAAc,QAAQ;AACvD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;AACpE,gBAAM,cAAc,MAAM,eAAe,2BAA2B;QACtE,CAAC;QACD,YAAY,qBAAqB,MAAM,YAAY,CAAC,UAAU;AAC5D,gBAAM,EAAE,GAAG,EAAE,IAAI,MAAM,OAAO;AAC9B,gBAAM,cAAc,aAAa,cAAc,KAAK;AACpD,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,eAAe,4BAA4B;AACrE,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,gBAAM,cAAc,MAAM,YAAY,6BAA6B,GAAG,CAAC,IAAI;AAC3E,kBAAQ,KAAK;QACf,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,MAAM,cAAc;AASpB,IAAM,CAAC,0BAA0B,0BAA0B,IAAI,mBAAmB,YAAY;EAC5F,UAAU;EAAC;AACb,CAAC;AAsBD,IAAM,YAAkB;EACtB,CAAC,OAAoC,iBAAiB;AACpD,UAAM;MACJ;MACA,OAAO;MACP,UAAU;MACV;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,UAAM,CAAC,MAAM,OAAO,IAAU,iBAAkC,IAAI;AACpE,UAAM,eAAe,gBAAgB,cAAc,CAACG,UAAS,QAAQA,KAAI,CAAC;AAC1E,UAAM,kBAAwB,eAAwC,IAAI;AAC1E,UAAM,gBAAsB,eAAwC,IAAI;AACxE,UAAM,WAAW,gBAAgB,QAAQ;AACzC,UAAM,yBAA+B,eAAO,CAAC;AAC7C,UAAM,6BAAmC,eAAO,QAAQ;AACxD,UAAM,gBAAsB,eAAO,CAAC;AACpC,UAAM,EAAE,YAAY,cAAc,IAAI;AACtC,UAAM,cAAc,eAAe,MAAM;AAGvC,YAAM,iBAAiB,MAAM,SAAS,SAAS,aAAa;AAC5D,UAAI,eAAgB,SAAQ,UAAU,MAAM;AAC5C,cAAQ;IACV,CAAC;AAED,UAAM,aAAmB;MACvB,CAACC,cAAqB;AACpB,YAAI,CAACA,aAAYA,cAAa,SAAU;AACxC,eAAO,aAAa,cAAc,OAAO;AACzC,+BAAuB,WAAU,oBAAI,KAAK,GAAE,QAAQ;AACpD,sBAAc,UAAU,OAAO,WAAW,aAAaA,SAAQ;MACjE;MACA,CAAC,WAAW;IACd;AAEM,IAAA,kBAAU,MAAM;AACpB,YAAM,WAAW,QAAQ;AACzB,UAAI,UAAU;AACZ,cAAM,eAAe,MAAM;AACzB,qBAAW,2BAA2B,OAAO;AAC7C,qBAAW;QACb;AACA,cAAM,cAAc,MAAM;AACxB,gBAAM,eAAc,oBAAI,KAAK,GAAE,QAAQ,IAAI,uBAAuB;AAClE,qCAA2B,UAAU,2BAA2B,UAAU;AAC1E,iBAAO,aAAa,cAAc,OAAO;AACzC,oBAAU;QACZ;AACA,iBAAS,iBAAiB,gBAAgB,WAAW;AACrD,iBAAS,iBAAiB,iBAAiB,YAAY;AACvD,eAAO,MAAM;AACX,mBAAS,oBAAoB,gBAAgB,WAAW;AACxD,mBAAS,oBAAoB,iBAAiB,YAAY;QAC5D;MACF;IACF,GAAG,CAAC,QAAQ,UAAU,UAAU,SAAS,UAAU,UAAU,CAAC;AAKxD,IAAA,kBAAU,MAAM;AACpB,UAAI,QAAQ,CAAC,QAAQ,iBAAiB,QAAS,YAAW,QAAQ;IACpE,GAAG,CAAC,MAAM,UAAU,QAAQ,kBAAkB,UAAU,CAAC;AAEnD,IAAA,kBAAU,MAAM;AACpB,iBAAW;AACX,aAAO,MAAM,cAAc;IAC7B,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,UAAM,sBAA4B,gBAAQ,MAAM;AAC9C,aAAO,OAAO,uBAAuB,IAAI,IAAI;IAC/C,GAAG,CAAC,IAAI,CAAC;AAET,QAAI,CAAC,QAAQ,SAAU,QAAO;AAE9B,eACE,2BAAA,+BAAA,EACG,UAAA;MAAA,2BACC;QAAC;QAAA;UACC;UAEA,MAAK;UACL,aAAW,SAAS,eAAe,cAAc;UACjD,eAAW;UAEV,UAAA;QAAA;MACH;UAGF,0BAAC,0BAAA,EAAyB,OAAO,cAAc,SAAS,aACrD,UAAS;YACR,0BAACP,YAAW,UAAX,EAAoB,OAAO,cAC1B,cAAA;UAAkBQ;UAAjB;YACC,SAAO;YACP,iBAAiB,qBAAqB,iBAAiB,MAAM;AAC3D,kBAAI,CAAC,QAAQ,+BAA+B,QAAS,aAAY;AACjE,sBAAQ,+BAA+B,UAAU;YACnD,CAAC;YAED,cAAA;cAAC,UAAU;cAAV;gBAEC,MAAK;gBACL,aAAU;gBACV,eAAW;gBACX,UAAU;gBACV,cAAY,OAAO,SAAS;gBAC5B,wBAAsB,QAAQ;gBAC7B,GAAG;gBACJ,KAAK;gBACL,OAAO,EAAE,YAAY,QAAQ,aAAa,QAAQ,GAAG,MAAM,MAAM;gBACjE,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,sBAAI,MAAM,QAAQ,SAAU;AAC5B,oCAAkB,MAAM,WAAW;AACnC,sBAAI,CAAC,MAAM,YAAY,kBAAkB;AACvC,4BAAQ,+BAA+B,UAAU;AACjD,gCAAY;kBACd;gBACF,CAAC;gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,MAAM,WAAW,EAAG;AACxB,kCAAgB,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,MAAM,QAAQ;gBACjE,CAAC;gBACD,eAAe,qBAAqB,MAAM,eAAe,CAAC,UAAU;AAClE,sBAAI,CAAC,gBAAgB,QAAS;AAC9B,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,IAAI,MAAM,UAAU,gBAAgB,QAAQ;AAClD,wBAAM,sBAAsB,QAAQ,cAAc,OAAO;AACzD,wBAAM,oBAAoB,CAAC,QAAQ,OAAO,EAAE,SAAS,QAAQ,cAAc;AAC3E,wBAAMC,SAAQ,CAAC,QAAQ,IAAI,EAAE,SAAS,QAAQ,cAAc,IACxD,KAAK,MACL,KAAK;AACT,wBAAM,WAAW,oBAAoBA,OAAM,GAAG,CAAC,IAAI;AACnD,wBAAM,WAAW,CAAC,oBAAoBA,OAAM,GAAG,CAAC,IAAI;AACpD,wBAAM,kBAAkB,MAAM,gBAAgB,UAAU,KAAK;AAC7D,wBAAM,QAAQ,EAAE,GAAG,UAAU,GAAG,SAAS;AACzC,wBAAM,cAAc,EAAE,eAAe,OAAO,MAAM;AAClD,sBAAI,qBAAqB;AACvB,kCAAc,UAAU;AACxB,iDAA6B,kBAAkB,aAAa,aAAa;sBACvE,UAAU;oBACZ,CAAC;kBACH,WAAW,mBAAmB,OAAO,QAAQ,gBAAgB,eAAe,GAAG;AAC7E,kCAAc,UAAU;AACxB,iDAA6B,mBAAmB,cAAc,aAAa;sBACzE,UAAU;oBACZ,CAAC;AACA,0BAAM,OAAuB,kBAAkB,MAAM,SAAS;kBACjE,WAAW,KAAK,IAAI,CAAC,IAAI,mBAAmB,KAAK,IAAI,CAAC,IAAI,iBAAiB;AAGzE,oCAAgB,UAAU;kBAC5B;gBACF,CAAC;gBACD,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAC9D,wBAAM,QAAQ,cAAc;AAC5B,wBAAM,SAAS,MAAM;AACrB,sBAAI,OAAO,kBAAkB,MAAM,SAAS,GAAG;AAC7C,2BAAO,sBAAsB,MAAM,SAAS;kBAC9C;AACA,gCAAc,UAAU;AACxB,kCAAgB,UAAU;AAC1B,sBAAI,OAAO;AACT,0BAAM,QAAQ,MAAM;AACpB,0BAAM,cAAc,EAAE,eAAe,OAAO,MAAM;AAClD,wBACE,mBAAmB,OAAO,QAAQ,gBAAgB,QAAQ,cAAc,GACxE;AACA,mDAA6B,iBAAiB,YAAY,aAAa;wBACrE,UAAU;sBACZ,CAAC;oBACH,OAAO;AACL;wBACE;wBACA;wBACA;wBACA;0BACE,UAAU;wBACZ;sBACF;oBACF;AAGA,0BAAM,iBAAiB,SAAS,CAACC,WAAUA,OAAM,eAAe,GAAG;sBACjE,MAAM;oBACR,CAAC;kBACH;gBACF,CAAC;cAAA;YACH;UAAA;QACF,EAAA,CACF;QACA,QAAQ;MACV,EAAA,CACF;IAAA,EAAA,CACF;EAEJ;AACF;AAQA,IAAM,gBAA8C,CAAC,UAA2C;AAC9F,QAAM,EAAE,cAAc,UAAU,GAAG,cAAc,IAAI;AACrD,QAAM,UAAU,wBAAwB,YAAY,YAAY;AAChE,QAAM,CAAC,oBAAoB,qBAAqB,IAAU,iBAAS,KAAK;AACxE,QAAM,CAAC,aAAa,cAAc,IAAU,iBAAS,KAAK;AAG1D,eAAa,MAAM,sBAAsB,IAAI,CAAC;AAGxC,EAAA,kBAAU,MAAM;AACpB,UAAM,QAAQ,OAAO,WAAW,MAAM,eAAe,IAAI,GAAG,GAAI;AAChE,WAAO,MAAM,OAAO,aAAa,KAAK;EACxC,GAAG,CAAC,CAAC;AAEL,SAAO,cAAc,WACnB,0BAAC,QAAA,EAAO,SAAO,MACb,cAAA,0BAAC,gBAAA,EAAgB,GAAG,eACjB,UAAA,0BACC,2BAAA,+BAAA,EACG,UAAA;IAAA,QAAQ;IAAM;IAAE;EAAA,EAAA,CACnB,EAAA,CAEJ,EAAA,CACF;AAEJ;AAMA,IAAMC,cAAa;AAMnB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,cAAc,GAAG,WAAW,IAAI;AACxC,eAAO,0BAAC,UAAU,KAAV,EAAe,GAAG,YAAY,KAAK,aAAA,CAAc;EAC3D;AACF;AAEA,WAAW,cAAcA;AAMzB,IAAMC,oBAAmB;AAKzB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,cAAc,GAAG,iBAAiB,IAAI;AAC9C,eAAO,0BAAC,UAAU,KAAV,EAAe,GAAG,kBAAkB,KAAK,aAAA,CAAc;EACjE;AACF;AAEA,iBAAiB,cAAcA;AAM/B,IAAMC,eAAc;AAapB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,SAAS,GAAG,YAAY,IAAI;AAEpC,QAAI,CAAC,QAAQ,KAAK,GAAG;AACnB,cAAQ;QACN,0CAA0CA,YAAW;MACvD;AACA,aAAO;IACT;AAEA,eACE,0BAAC,sBAAA,EAAqB,SAAkB,SAAO,MAC7C,cAAA,0BAAC,YAAA,EAAY,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAClD;EAEJ;AACF;AAEA,YAAY,cAAcA;AAM1B,IAAM,aAAa;AAMnB,IAAM,aAAmB;EACvB,CAAC,OAAqC,iBAAiB;AACrD,UAAM,EAAE,cAAc,GAAG,WAAW,IAAI;AACxC,UAAM,qBAAqB,2BAA2B,YAAY,YAAY;AAE9E,eACE,0BAAC,sBAAA,EAAqB,SAAO,MAC3B,cAAA;MAAC,UAAU;MAAV;QACC,MAAK;QACJ,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,OAAO;MAAA;IACzE,EAAA,CACF;EAEJ;AACF;AAEA,WAAW,cAAc;AASzB,IAAM,uBAA6B,mBAGjC,CAAC,OAA+C,iBAAiB;AACjE,QAAM,EAAE,cAAc,SAAS,GAAG,qBAAqB,IAAI;AAE3D,aACE;IAAC,UAAU;IAAV;MACC,qCAAkC;MAClC,iCAA+B,WAAW;MACzC,GAAG;MACJ,KAAK;IAAA;EACP;AAEJ,CAAC;AAED,SAAS,uBAAuB,WAAwB;AACtD,QAAM,cAAwB,CAAC;AAC/B,QAAM,aAAa,MAAM,KAAK,UAAU,UAAU;AAElD,aAAW,QAAQ,CAAC,SAAS;AAC3B,QAAI,KAAK,aAAa,KAAK,aAAa,KAAK,YAAa,aAAY,KAAK,KAAK,WAAW;AAC3F,QAAIC,eAAc,IAAI,GAAG;AACvB,YAAM,WAAW,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,YAAY;AAC1E,YAAM,aAAa,KAAK,QAAQ,8BAA8B;AAE9D,UAAI,CAAC,UAAU;AACb,YAAI,YAAY;AACd,gBAAM,UAAU,KAAK,QAAQ;AAC7B,cAAI,QAAS,aAAY,KAAK,OAAO;QACvC,OAAO;AACL,sBAAY,KAAK,GAAG,uBAAuB,IAAI,CAAC;QAClD;MACF;IACF;EACF,CAAC;AAID,SAAO;AACT;AAIA,SAAS,6BAIP,MACA,SACA,QACA,EAAE,SAAS,GACX;AACA,QAAM,gBAAgB,OAAO,cAAc;AAC3C,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,MAAM,YAAY,MAAM,OAAO,CAAC;AAC/E,MAAI,QAAS,eAAc,iBAAiB,MAAM,SAA0B,EAAE,MAAM,KAAK,CAAC;AAE1F,MAAI,UAAU;AACZ,gCAA4B,eAAe,KAAK;EAClD,OAAO;AACL,kBAAc,cAAc,KAAK;EACnC;AACF;AAEA,IAAM,qBAAqB,CACzB,OACA,WACA,YAAY,MACT;AACH,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,SAAS,KAAK,IAAI,MAAM,CAAC;AAC/B,QAAM,WAAW,SAAS;AAC1B,MAAI,cAAc,UAAU,cAAc,SAAS;AACjD,WAAO,YAAY,SAAS;EAC9B,OAAO;AACL,WAAO,CAAC,YAAY,SAAS;EAC/B;AACF;AAEA,SAAS,aAAa,WAAW,MAAM;AAAC,GAAG;AACzC,QAAM,KAAK,eAAe,QAAQ;AAClC,mBAAgB,MAAM;AACpB,QAAI,OAAO;AACX,QAAI,OAAO;AACX,WAAO,OAAO,sBAAsB,MAAO,OAAO,OAAO,sBAAsB,EAAE,CAAE;AACnF,WAAO,MAAM;AACX,aAAO,qBAAqB,IAAI;AAChC,aAAO,qBAAqB,IAAI;IAClC;EACF,GAAG,CAAC,EAAE,CAAC;AACT;AAEA,SAASA,eAAc,MAAgC;AACrD,SAAO,KAAK,aAAa,KAAK;AAChC;AAYA,SAASV,uBAAsB,WAAwB;AACrD,QAAM,QAAuB,CAAC;AAC9B,QAAM,SAAS,SAAS,iBAAiB,WAAW,WAAW,cAAc;IAC3E,YAAY,CAAC,SAAc;AACzB,YAAM,gBAAgB,KAAK,YAAY,WAAW,KAAK,SAAS;AAChE,UAAI,KAAK,YAAY,KAAK,UAAU,cAAe,QAAO,WAAW;AAIrE,aAAO,KAAK,YAAY,IAAI,WAAW,gBAAgB,WAAW;IACpE;EACF,CAAC;AACD,SAAO,OAAO,SAAS,EAAG,OAAM,KAAK,OAAO,WAA0B;AAGtE,SAAO;AACT;AAEA,SAASC,YAAW,YAA2B;AAC7C,QAAM,2BAA2B,SAAS;AAC1C,SAAO,WAAW,KAAK,CAAC,cAAc;AAEpC,QAAI,cAAc,yBAA0B,QAAO;AACnD,cAAU,MAAM;AAChB,WAAO,SAAS,kBAAkB;EACpC,CAAC;AACH;AAEA,IAAM,WAAW;AACjB,IAAMU,YAAW;AACjB,IAAMP,SAAO;AACb,IAAMQ,SAAQ;AACd,IAAMC,eAAc;AACpB,IAAMC,UAAS;AACf,IAAMC,SAAQ;;;;;;;;AC97Bd,IAAAC,UAAuB;AAyCnB,IAAAC,uBAAA;AAhCJ,IAAMC,QAAO;AAqBb,IAAM,SAAe,mBAAuC,CAAC,OAAO,iBAAiB;AACnF,QAAM,EAAE,SAAS,aAAa,gBAAgB,iBAAiB,GAAG,YAAY,IAAI;AAElF,QAAM,CAAC,SAAS,UAAU,IAAI,qBAAqB;IACjD,MAAM;IACN,UAAU;IACV,aAAa,kBAAkB;IAC/B,QAAQA;EACV,CAAC;AAED,aACE;IAAC,UAAU;IAAV;MACC,MAAK;MACL,gBAAc;MACd,cAAY,UAAU,OAAO;MAC7B,iBAAe,MAAM,WAAW,KAAK;MACpC,GAAG;MACJ,KAAK;MACL,SAAS,qBAAqB,MAAM,SAAS,MAAM;AACjD,YAAI,CAAC,MAAM,UAAU;AACnB,qBAAW,CAAC,OAAO;QACrB;MACF,CAAC;IAAA;EACH;AAEJ,CAAC;AAED,OAAO,cAAcA;AAIrB,IAAMC,SAAO;;;;;;;;;;;AC7Db,IAAAC,gBAAkB;AAuCP,IAAAC,uBAAA;AAxBX,IAAM,oBAAoB;AAG1B,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,mBAAmB;EAC/F;AACF,CAAC;AACD,IAAMC,4BAA2B,4BAA4B;AAU7D,IAAM,cAAc,cAAAC,QAAM,WAGxB,CAAC,OAAO,iBAAiB;AACzB,QAAM,EAAE,MAAM,GAAG,iBAAiB,IAAI;AAEtC,MAAI,SAAS,UAAU;AACrB,UAAM,cAAc;AACpB,eAAO,0BAAC,uBAAA,EAAuB,GAAG,aAAa,KAAK,aAAA,CAAc;EACpE;AAEA,MAAI,SAAS,YAAY;AACvB,UAAM,gBAAgB;AACtB,eAAO,0BAAC,yBAAA,EAAyB,GAAG,eAAe,KAAK,aAAA,CAAc;EACxE;AAEA,QAAM,IAAI,MAAM,uCAAuC,iBAAiB,IAAI;AAC9E,CAAC;AAED,YAAY,cAAc;AAW1B,IAAM,CAAC,0BAA0B,0BAA0B,IACzD,yBAAuD,iBAAiB;AAmB1E,IAAM,wBAAwB,cAAAA,QAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa,gBAAgB;IAC7B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb,MAAK;MACL,OAAO,cAAAA,QAAM,QAAQ,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAI,CAAC,KAAK,CAAC;MAC1D,gBAAgB;MAChB,kBAAkB,cAAAA,QAAM,YAAY,MAAM,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC;MAElE,cAAA,0BAAC,iBAAA,EAAiB,GAAG,wBAAwB,KAAK,aAAA,CAAc;IAAA;EAClE;AAEJ,CAAC;AAmBD,IAAM,0BAA0B,cAAAA,QAAM,WAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa,gBAAgB,CAAC;IAC9B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,QAAM,uBAAuB,cAAAA,QAAM;IACjC,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;IAC7E,CAAC,QAAQ;EACX;AAEA,QAAM,yBAAyB,cAAAA,QAAM;IACnC,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACC,WAAUA,WAAU,SAAS,CAAC;IAC/E,CAAC,QAAQ;EACX;AAEA,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb,MAAK;MACL;MACA,gBAAgB;MAChB,kBAAkB;MAElB,cAAA,0BAAC,iBAAA,EAAiB,GAAG,0BAA0B,KAAK,aAAA,CAAc;IAAA;EACpE;AAEJ,CAAC;AAED,YAAY,cAAc;AAM1B,IAAM,CAAC,oBAAoB,qBAAqB,IAC9C,yBAAkD,iBAAiB;AAqBrE,IAAM,kBAAkB,cAAAD,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;MACJ;MACA,WAAW;MACX,cAAc;MACd;MACA;MACA,OAAO;MACP,GAAG;IACL,IAAI;AACJ,UAAM,wBAAwBD,0BAAyB,kBAAkB;AACzE,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,cAAc,EAAE,MAAM,SAAS,KAAK,WAAW,GAAG,iBAAiB;AACzE,eACE,0BAAC,oBAAA,EAAmB,OAAO,oBAAoB,aAA0B,UACtE,UAAA,kBACC;MAAkBG;MAAjB;QACC,SAAO;QACN,GAAG;QACJ;QACA,KAAK;QACL;QAEA,cAAA,0BAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc;MAAA;IACrD,QAEA,0BAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAEvD;EAEJ;AACF;AAMA,IAAMC,aAAY;AAKlB,IAAM,kBAAkB,cAAAH,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,eAAe,2BAA2BG,YAAW,MAAM,kBAAkB;AACnF,UAAM,UAAU,sBAAsBA,YAAW,MAAM,kBAAkB;AACzE,UAAM,wBAAwBJ,0BAAyB,MAAM,kBAAkB;AAC/E,UAAM,UAAU,aAAa,MAAM,SAAS,MAAM,KAAK;AACvD,UAAM,WAAW,QAAQ,YAAY,MAAM;AAC3C,UAAM,cAAc,EAAE,GAAG,OAAO,SAAS,SAAS;AAClD,UAAM,MAAM,cAAAC,QAAM,OAAuB,IAAI;AAC7C,WAAO,QAAQ,kBACb;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ,WAAW,CAAC;QACZ,QAAQ;QACR;QAEA,cAAA,0BAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc;MAAA;IAC3D,QAEA,0BAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc;EAE7D;AACF;AAEA,gBAAgB,cAAcG;AAa9B,IAAM,sBAAsB,cAAAH,QAAM;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,oBAAoB,OAAO,GAAG,UAAU,IAAI;AACpD,UAAM,eAAe,2BAA2BG,YAAW,kBAAkB;AAC7E,UAAM,cAAc,EAAE,MAAM,SAAS,gBAAgB,MAAM,SAAS,gBAAgB,OAAU;AAC9F,UAAM,YAAY,aAAa,SAAS,WAAW,cAAc;AACjE,eACE;MAAC;MAAA;QACE,GAAG;QACH,GAAG;QACJ,KAAK;QACL,iBAAiB,CAAC,YAAY;AAC5B,cAAI,SAAS;AACX,yBAAa,eAAe,KAAK;UACnC,OAAO;AACL,yBAAa,iBAAiB,KAAK;UACrC;QACF;MAAA;IACF;EAEJ;AACF;AAIA,IAAMD,UAAO;AACb,IAAME,SAAO;;;;;;;;;;;;;;;;;;;AClTb,IAAAC,UAAuB;AAyDb,IAAAC,uBAAA;AAxCV,IAAM,eAAe;AAGrB,IAAM,CAAC,sBAAsB,kBAAkB,IAAI,mBAAmB,cAAc;EAClF;EACA;AACF,CAAC;AACD,IAAMC,4BAA2B,4BAA4B;AAC7D,IAAM,sBAAsB,uBAAuB;AAOnD,IAAM,CAAC,iBAAiB,iBAAiB,IACvC,qBAA0C,YAAY;AAUxD,IAAM,UAAgB;EACpB,CAAC,OAAkC,iBAAiB;AAClD,UAAM,EAAE,gBAAgB,cAAc,cAAc,KAAK,OAAO,MAAM,GAAG,aAAa,IAAI;AAC1F,UAAM,wBAAwBA,0BAAyB,cAAc;AACrE,UAAM,YAAY,aAAa,GAAG;AAClC,eACE,0BAAC,iBAAA,EAAgB,OAAO,gBAAgB,aAA0B,KAAK,WACrE,cAAA;MAAkBC;MAAjB;QACC,SAAO;QACN,GAAG;QACJ;QACA,KAAK;QACL;QAEA,cAAA;UAAC,UAAU;UAAV;YACC,MAAK;YACL,oBAAkB;YAClB,KAAK;YACJ,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF,EAAA,CACF;EAEJ;AACF;AAEA,QAAQ,cAAc;AAMtB,IAAMC,kBAAiB;AAMvB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,gBAAgB,GAAG,eAAe,IAAI;AAC9C,UAAM,UAAU,kBAAkBA,iBAAgB,cAAc;AAChE,eACE;MAAoBD;MAAnB;QACC,aAAa,QAAQ,gBAAgB,eAAe,aAAa;QAChE,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,iBAAiB,cAAcC;AAM/B,IAAM,cAAc;AAMpB,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,gBAAgB,GAAG,YAAY,IAAI;AAC3C,UAAM,wBAAwBF,0BAAyB,cAAc;AACrE,eACE,0BAAkB,MAAjB,EAAsB,SAAO,MAAE,GAAG,uBAAuB,WAAW,CAAC,MAAM,UAC1E,cAAA,0BAAC,UAAU,QAAV,EAAiB,MAAK,UAAU,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CACtE;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAMG,aAAY;AAMlB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,gBAAgB,GAAG,UAAU,IAAI;AACzC,UAAM,wBAAwBH,0BAAyB,cAAc;AACrE,eACE,0BAAkB,MAAjB,EAAsB,SAAO,MAAE,GAAG,uBAAuB,WAAS,MACjE,cAAA;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,cAAI,MAAM,QAAQ,IAAK,OAAM,cAAc,MAAM;QACnD,CAAC;MAAA;IACH,EAAA,CACF;EAEJ;AACF;AAEA,YAAY,cAAcG;AAM1B,IAAMC,qBAAoB;AAO1B,IAAM,qBAA2B;EAI/B,CACE,OACA,iBACG;AACH,UAAM,EAAE,gBAAgB,GAAG,iBAAiB,IAAI;AAChD,UAAM,UAAU,kBAAkBA,oBAAmB,cAAc;AACnE,UAAM,mBAAmB,oBAAoB,cAAc;AAC3D,eACE;MAAsBC;MAArB;QACC,oBAAkB,QAAQ;QAC1B,KAAK,QAAQ;QACZ,GAAG;QACH,GAAG;QACJ,KAAK;QACL,aAAa;MAAA;IACf;EAEJ;AACF;AAEA,mBAAmB,cAAcD;AAMjC,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,gBAAgB,GAAG,gBAAgB,IAAI;AAC/C,UAAM,mBAAmB,oBAAoB,cAAc;AAC3D,UAAM,QAAQ,EAAE,gBAAgB,MAAM,eAAe;AAErD,eACE,0BAAC,eAAA,EAAc,SAAO,MAAE,GAAG,OACzB,cAAA,0BAAsBE,QAArB,EAA2B,GAAG,kBAAmB,GAAG,iBAAiB,KAAK,aAAA,CAAc,EAAA,CAC3F;EAEJ;AACF;AAEA,kBAAkB,cAAc;AAIhC,IAAML,SAAO;AACb,IAAMM,aAAY;AAClB,IAAM,SAAS;AACf,IAAMC,QAAO;AACb,IAAMC,eAAc;AACpB,IAAM,aAAa;", "names": ["Root", "React", "import_jsx_runtime", "Root", "<PERSON><PERSON>", "Content", "import_jsx_runtime", "React", "value", "Root", "getState", "open", "TRIGGER_NAME", "<PERSON><PERSON>", "CONTENT_NAME", "Content", "<PERSON><PERSON>", "React", "import_jsx_runtime", "Root", "TRIGGER_NAME", "Portal", "CONTENT_NAME", "Content", "<PERSON><PERSON>", "Overlay", "Title", "Description", "React", "import_jsx_runtime", "NAME", "Root", "React", "import_jsx_runtime", "isFormControl", "TRIGGER_NAME", "getState", "React", "import_jsx_runtime", "open", "Root3", "TRIGGER_NAME", "PORTAL_NAME", "Portal", "CONTENT_NAME", "Label", "ITEM_NAME", "INDICATOR_NAME", "Root", "<PERSON><PERSON>", "Content", "Group", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent", "React", "import_jsx_runtime", "LABEL_NAME", "controlValidity", "id", "hasCustomError", "Root", "Label", "React", "import_jsx_runtime", "Root2", "TRIGGER_NAME", "PORTAL_NAME", "CONTENT_NAME", "ARROW_NAME", "Root", "<PERSON><PERSON>", "Portal", "Content", "Arrow", "React", "import_jsx_runtime", "Collection", "useCollection", "createCollectionScope", "useMenuScope", "value", "Root", "Root3", "open", "TRIGGER_NAME", "PORTAL_NAME", "Portal", "CONTENT_NAME", "GROUP_NAME", "LABEL_NAME", "Label", "ITEM_NAME", "CHECKBOX_ITEM_NAME", "RADIO_GROUP_NAME", "RADIO_ITEM_NAME", "INDICATOR_NAME", "SEPARATOR_NAME", "ARROW_NAME", "SUB_NAME", "SUB_TRIGGER_NAME", "SUB_CONTENT_NAME", "<PERSON><PERSON>", "Content", "Group", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent", "React", "import_jsx_runtime", "Collection", "useCollection", "createCollectionScope", "value", "SUB_NAME", "ITEM_NAME", "TRIGGER_NAME", "Root", "event", "INDICATOR_NAME", "ReactDOM", "CONTENT_NAME", "props", "Sub", "<PERSON><PERSON>", "<PERSON><PERSON>", "Content", "React", "import_react_dom", "import_jsx_runtime", "Collection", "useCollection", "createCollectionScope", "useRovingFocusGroupScope", "value", "newValue", "form", "Root", "OneTimePasswordFieldHiddenInput", "OneTimePasswordFieldInput", "element", "React", "import_react_dom", "import_jsx_runtime", "window", "id", "React", "import_jsx_runtime", "ARROW_KEYS", "Collection", "useCollection", "createCollectionScope", "value", "isFormControl", "BUBBLE_INPUT_NAME", "Root", "React", "ReactDOM", "import_jsx_runtime", "Collection", "useCollection", "createCollectionScope", "VIEWPORT_NAME", "getTabbableCandidates", "focusFirst", "node", "duration", "Root", "clamp", "event", "TITLE_NAME", "DESCRIPTION_NAME", "ACTION_NAME", "isHTMLElement", "Viewport", "Title", "Description", "Action", "Close", "React", "import_jsx_runtime", "NAME", "Root", "import_react", "import_jsx_runtime", "useRovingFocusGroupScope", "React", "value", "Root", "ITEM_NAME", "<PERSON><PERSON>", "React", "import_jsx_runtime", "useRovingFocusGroupScope", "Root", "SEPARATOR_NAME", "LINK_NAME", "TOGGLE_GROUP_NAME", "Root2", "Item2", "Separator", "Link", "ToggleGroup"]}