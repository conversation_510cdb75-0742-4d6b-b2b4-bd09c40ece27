{"version": 3, "sources": ["../../src/utils/hasConfigurableGlobal.ts"], "names": [], "mappings": ";AAIO,SAAS,sBAAsB,cAA+B;AACnE,QAAM,aAAa,OAAO,yBAAyB,YAAY,YAAY;AAG3E,MAAI,OAAO,eAAe,aAAa;AACrC,WAAO;AAAA,EACT;AAGA,MACE,OAAO,WAAW,QAAQ,cAC1B,OAAO,WAAW,IAAI,MAAM,aAC5B;AACA,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,WAAW,QAAQ,eAAe,WAAW,SAAS,MAAM;AACrE,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,WAAW,QAAQ,eAAe,CAAC,WAAW,cAAc;AACrE,YAAQ;AAAA,MACN,mDAAmD;AAAA,IACrD;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT", "sourcesContent": ["/**\n * Returns a boolean indicating whether the given global property\n * is defined and is configurable.\n */\nexport function hasConfigurableGlobal(propertyName: string): boolean {\n  const descriptor = Object.getOwnPropertyDescriptor(globalThis, propertyName)\n\n  // The property is not set at all.\n  if (typeof descriptor === 'undefined') {\n    return false\n  }\n\n  // The property is set to a getter that returns undefined.\n  if (\n    typeof descriptor.get === 'function' &&\n    typeof descriptor.get() === 'undefined'\n  ) {\n    return false\n  }\n\n  // The property is set to a value equal to undefined.\n  if (typeof descriptor.get === 'undefined' && descriptor.value == null) {\n    return false\n  }\n\n  if (typeof descriptor.set === 'undefined' && !descriptor.configurable) {\n    console.error(\n      `[MSW] Failed to apply interceptor: the global \\`${propertyName}\\` property is non-configurable. This is likely an issue with your environment. If you are using a framework, please open an issue about this in their repository.`\n    )\n    return false\n  }\n\n  return true\n}\n"]}