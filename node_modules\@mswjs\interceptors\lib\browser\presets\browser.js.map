{"version": 3, "sources": ["../../../src/presets/browser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAOA,IAAO,kBAAQ;AAAA,EACb,IAAI,iBAAiB;AAAA,EACrB,IAAI,0BAA0B;AAChC", "sourcesContent": ["import { FetchInterceptor } from '../interceptors/fetch'\nimport { XMLHttpRequestInterceptor } from '../interceptors/XMLHttpRequest'\n\n/**\n * The default preset provisions the interception of requests\n * regardless of their type (fetch/XMLHttpRequest).\n */\nexport default [\n  new FetchInterceptor(),\n  new XMLHttpRequestInterceptor(),\n] as const\n"]}