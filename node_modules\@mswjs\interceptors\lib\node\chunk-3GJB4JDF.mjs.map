{"version": 3, "sources": ["../../src/getRawRequest.ts"], "sourcesContent": ["const kRawRequest = Symbol('kRawRequest')\n\n/**\n * Returns a raw request instance associated with this request.\n *\n * @example\n * interceptor.on('request', ({ request }) => {\n *   const rawRequest = getRawRequest(request)\n *\n *   if (rawRequest instanceof http.ClientRequest) {\n *     console.log(rawRequest.rawHeaders)\n *   }\n * })\n */\nexport function getRawRequest(request: Request): unknown | undefined {\n  return Reflect.get(request, kRawRequest)\n}\n\nexport function setRawRequest(request: Request, rawRequest: unknown): void {\n  Reflect.set(request, kRawRequest, rawRequest)\n}\n"], "mappings": ";AAAA,IAAM,cAAc,OAAO,aAAa;AAcjC,SAAS,cAAc,SAAuC;AACnE,SAAO,QAAQ,IAAI,SAAS,WAAW;AACzC;AAEO,SAAS,cAAc,SAAkB,YAA2B;AACzE,UAAQ,IAAI,SAAS,aAAa,UAAU;AAC9C;", "names": []}