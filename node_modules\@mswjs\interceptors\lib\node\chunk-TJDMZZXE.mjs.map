{"version": 3, "sources": ["../../src/utils/node/index.ts"], "sourcesContent": ["import { ClientRequest } from 'node:http'\nimport { Readable } from 'node:stream'\nimport { invariant } from 'outvariant'\nimport { getRawRequest } from '../../getRawRequest'\n\nconst kRawRequestBodyStream = Symbol('kRawRequestBodyStream')\n\n/**\n * Returns the request body stream of the given request.\n * @note This is only relevant in the context of `http.ClientRequest`.\n * This function will throw if the given `request` wasn't created based on\n * the `http.ClientRequest` instance.\n * You must rely on the web stream consumers for other request clients.\n */\nexport function getClientRequestBodyStream(request: Request): Readable {\n  const rawRequest = getRawRequest(request)\n\n  invariant(\n    rawRequest instanceof ClientRequest,\n    `Failed to retrieve raw request body stream: request is not an instance of \"http.ClientRequest\". Note that you can only use the \"getClientRequestBodyStream\" function with the requests issued by \"http.clientRequest\".`\n  )\n\n  const requestBodyStream = Reflect.get(request, kRawRequestBodyStream)\n\n  invariant(\n    requestBodyStream instanceof Readable,\n    'Failed to retrieve raw request body stream: corrupted stream (%s)',\n    typeof requestBodyStream\n  )\n\n  return requestBodyStream\n}\n\nexport function setRawRequestBodyStream(\n  request: Request,\n  stream: Readable\n): void {\n  Reflect.set(request, kRawRequestBodyStream, stream)\n}\n"], "mappings": ";;;;;AAAA,SAAS,qBAAqB;AAC9B,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAG1B,IAAM,wBAAwB,OAAO,uBAAuB;AASrD,SAAS,2BAA2B,SAA4B;AACrE,QAAM,aAAa,cAAc,OAAO;AAExC;AAAA,IACE,sBAAsB;AAAA,IACtB;AAAA,EACF;AAEA,QAAM,oBAAoB,QAAQ,IAAI,SAAS,qBAAqB;AAEpE;AAAA,IACE,6BAA6B;AAAA,IAC7B;AAAA,IACA,OAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,wBACd,SACA,QACM;AACN,UAAQ,IAAI,SAAS,uBAAuB,MAAM;AACpD;", "names": []}