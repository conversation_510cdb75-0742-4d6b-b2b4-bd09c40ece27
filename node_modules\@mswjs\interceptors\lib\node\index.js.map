{"version": 3, "sources": ["../../src/utils/getCleanUrl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,YAAY,KAAU,aAAsB,MAAc;AACxE,SAAO,CAAC,cAAc,IAAI,QAAQ,IAAI,QAAQ,EAAE,OAAO,OAAO,EAAE,KAAK,EAAE;AACzE", "sourcesContent": ["/**\n * Removes query parameters and hashes from a given URL.\n */\nexport function getCleanUrl(url: URL, isAbsolute: boolean = true): string {\n  return [isAbsolute && url.origin, url.pathname].filter(Boolean).join('')\n}\n"]}