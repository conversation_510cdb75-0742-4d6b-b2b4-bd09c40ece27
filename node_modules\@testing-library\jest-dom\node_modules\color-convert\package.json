{"name": "color-convert", "description": "Plain color conversion functions", "version": "2.0.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "Qix-/color-convert", "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "engines": {"node": ">=7.0.0"}, "keywords": ["color", "colour", "convert", "converter", "conversion", "rgb", "hsl", "hsv", "hwb", "cmyk", "ansi", "ansi16"], "files": ["index.js", "conversions.js", "route.js"], "xo": {"rules": {"default-case": 0, "no-inline-comments": 0, "operator-linebreak": 0}}, "devDependencies": {"chalk": "^2.4.2", "xo": "^0.24.0"}, "dependencies": {"color-name": "~1.1.4"}}