import { validationResult } from 'express-validator';
import { promisify } from 'util';
import crypto from 'crypto';
import { AppError, asyncHandler } from '../middlewares/errorHandler.js';
import { generateTokenPair, verifyToken, verifyRefreshToken } from '../utils/jwt.js';
import User from '../models/User.js';
import { sendEmail } from '../utils/email.js';
import { getImageUrl } from '../middlewares/upload.js';

// Promisify crypto.randomBytes
const randomBytesAsync = promisify(crypto.randomBytes);

// Helper function to add image URLs to user object
const addImageUrls = (user) => {
  const userObj = user.toObject ? user.toObject() : user;

  // TEMPORARY TEST: Force test image URLs to verify URL generation
  const testProfilePhoto = '6878a6b24e3db7703d55c58f_1753014663800_profile-photo.jpg';
  const testCoverPhoto = '6878a6b24e3db7703d55c58f_1753014237990_cover-photo.jpg';

  console.log('🔍 DEBUG - addImageUrls called for user:', userObj.gmail);
  console.log('🔍 DEBUG - User has profilePhoto:', userObj.profilePhoto || 'NONE');
  console.log('🔍 DEBUG - User has coverPhoto:', userObj.coverPhoto || 'NONE');

  // Generate URLs (use test files if user doesn't have images)
  const profilePhotoUrl = userObj.profilePhoto
    ? getImageUrl(userObj.profilePhoto, 'profile')
    : getImageUrl(testProfilePhoto, 'profile');

  const coverPhotoUrl = userObj.coverPhoto
    ? getImageUrl(userObj.coverPhoto, 'cover')
    : getImageUrl(testCoverPhoto, 'cover');

  console.log('🔍 DEBUG - Generated profilePhotoUrl:', profilePhotoUrl);
  console.log('🔍 DEBUG - Generated coverPhotoUrl:', coverPhotoUrl);

  return {
    ...userObj,
    profilePhotoUrl,
    coverPhotoUrl
  };
};

// Cookie options
const cookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  path: '/api/v1/auth/refresh',
};

/**
 * @desc    Register a new user
 * @route   POST /api/v1/auth/register
 * @access  Public
 */
export const register = asyncHandler(async (req, res, next) => {
  // 1) Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log('Validation errors:', errors.array());
    return next(new AppError('Validation failed', 400, 'VALIDATION_ERROR', errors.array()));
  }

  const { gmail, password, fullName, mobile, address } = req.body;

  try {
    // 2) Create new user
    const user = await User.create({
      gmail: gmail.toLowerCase().trim(),
      password,
      fullName: fullName.trim(),
      mobile: mobile?.trim(),
      address: address?.trim(),
      emailVerificationToken: (await randomBytesAsync(32)).toString('hex'),
      emailVerificationExpires: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
    });

    // 3) Generate tokens
    const { accessToken, refreshToken } = generateTokenPair(user);

    // 4) Save refresh token to database
    user.refreshToken = refreshToken;
    await user.save({ validateBeforeSave: false });

    // 5) Remove sensitive data from output
    user.password = undefined;
    user.refreshToken = undefined;
    user.__v = undefined;

    // 6) Send welcome email with verification link
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/v1/auth/verify-email/${user.emailVerificationToken}`;

    await sendEmail({
      to: user.gmail,
      subject: 'Verify your email',
      text: `Please verify your email by visiting: ${verificationUrl}`,
      html: `Please click <a href="${verificationUrl}">here</a> to verify your email.`,
    });

    // 7) Set refresh token in HTTP-only cookie
    res.cookie('refreshToken', refreshToken, {
      ...cookieOptions,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // 8) Send response (filter out sensitive fields)
    const userResponse = {
      _id: user._id,
      gmail: user.gmail,
      fullName: user.fullName,
      role: user.role,
      mobile: user.mobile,
      address: user.address,
      isEmailVerified: user.isEmailVerified,
      preferences: user.preferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please check your email to verify your account.',
      data: {
        user: userResponse,
        accessToken,
        expiresIn: process.env.JWT_EXPIRES_IN,
      },
    });
  } catch (error) {
    // Handle duplicate key error (in case of race condition)
    if (error.code === 11000) {
      return next(new AppError('User with this email already exists', 409, 'DUPLICATE_EMAIL'));
    }

    // Handle other errors
    return next(error);
  }
});

/**
 * @desc    Login user
 * @route   POST /api/v1/auth/login
 * @access  Public
 */
export const login = asyncHandler(async (req, res, next) => {
  // 1) Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return next(new AppError('Validation failed', 400, 'VALIDATION_ERROR', errors.array()));
  }

  const { gmail, password } = req.body;

  try {
    // Find user by email
    const user = await User.findOne({
      gmail: gmail.toLowerCase().trim()
    });

    // Check if user exists and verify password
    if (!user) {
      return next(new AppError('Incorrect email or password', 401, 'INVALID_CREDENTIALS'));
    }

    // Verify password using the user's correctPassword method
    const isPasswordCorrect = await user.correctPassword(password);

    if (!isPasswordCorrect) {
      return next(new AppError('Incorrect email or password', 401, 'INVALID_CREDENTIALS'));
    }

    // Check email verification status (skip in development)
    if (!user.isEmailVerified && process.env.NODE_ENV === 'production') {
      return next(new AppError(
        'Please verify your email before logging in. Check your inbox for verification link.',
        403,
        'EMAIL_NOT_VERIFIED'
      ));
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokenPair(user);

    // Save refresh token to database
    user.refreshToken = refreshToken;
    await user.save({ validateBeforeSave: false });

    // Set refresh token in HTTP-only cookie
    res.cookie('refreshToken', refreshToken, cookieOptions);

    // Filter out sensitive fields for response and add image URLs
    const userResponse = addImageUrls({
      _id: user._id,
      gmail: user.gmail,
      fullName: user.fullName,
      username: user.username,
      role: user.role,
      mobile: user.mobile,
      address: user.address,
      profilePhoto: user.profilePhoto,
      coverPhoto: user.coverPhoto,
      isEmailVerified: user.isEmailVerified,
      preferences: user.preferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    });

    // Send response with clean user data
    res.status(200).json({
      success: true,
      message: 'Logged in successfully',
      data: {
        user: userResponse,
        accessToken,
        refreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    return next(new AppError('Login failed. Please try again.', 500, 'LOGIN_ERROR'));
  }
});

/**
 * @desc    Refresh access token
 * @route   POST /api/v1/auth/refresh-token
 * @access  Public
 */
export const refreshToken = asyncHandler(async (req, res, next) => {
  try {
    // FIX 2: Get refresh token from both cookie and body for flexibility
    // WHY: Frontend might send token in body, backend might have it in cookie
    let refreshToken = req.cookies?.refreshToken || req.body?.refreshToken;

    console.log('🔄 Refresh token request received');
    console.log('📝 Token from cookies:', req.cookies?.refreshToken ? 'Present' : 'Not present');
    console.log('📝 Token from body:', req.body?.refreshToken ? 'Present' : 'Not present');

    if (!refreshToken) {
      console.log('❌ No refresh token provided');
      return next(new AppError('No refresh token provided', 401, 'UNAUTHORIZED'));
    }

    // Verify refresh token
    console.log('🔍 Verifying refresh token...');
    const decoded = await verifyRefreshToken(refreshToken);
    console.log('✅ Token verified successfully, userId:', decoded.userId);

    // Find user with matching refresh token
    console.log('🔍 Looking for user with matching refresh token...');
    const user = await User.findOne({ _id: decoded.userId, refreshToken });

    if (!user) {
      console.log('❌ User not found with matching refresh token');
      console.log('🔍 Checking if user exists at all...');
      const userExists = await User.findById(decoded.userId);
      if (userExists) {
        console.log('✅ User exists but refresh token mismatch');
        console.log('🔍 Stored token length:', userExists.refreshToken?.length || 'null');
        console.log('🔍 Provided token length:', refreshToken.length);
      } else {
        console.log('❌ User does not exist');
      }
      return next(new AppError('Invalid refresh token', 401, 'UNAUTHORIZED'));
    }

    console.log('✅ User found with matching refresh token');

    // Check if user account is still active (skip in development)
    if (!user.isEmailVerified && process.env.NODE_ENV === 'production') {
      return next(new AppError('Account not verified', 403, 'ACCOUNT_NOT_VERIFIED'));
    }

    // Generate new tokens
    const { accessToken: newAccessToken, refreshToken: newRefreshToken } = generateTokenPair(user);

    // Update refresh token in database
    user.refreshToken = newRefreshToken;
    await user.save({ validateBeforeSave: false });

    // Set new refresh token in cookie
    res.cookie('refreshToken', newRefreshToken, cookieOptions);

    // Send response with new tokens
    res.status(200).json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN,
      },
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    return next(new AppError('Token refresh failed', 401, 'REFRESH_FAILED'));
  }
});

/**
 * @desc    Logout user
 * @route   POST /api/v1/auth/logout
 * @access  Private
 */
export const logout = asyncHandler(async (req, res, next) => {
  try {
    // 1) Clear refresh token from cookie
    res.clearCookie('refreshToken', cookieOptions);

    // 2) Clear refresh token from database if user is logged in
    if (req.user?.id) {
      await User.findByIdAndUpdate(req.user.id, { refreshToken: null });
    }

    // 3) Send response
    res.status(200).json({
      success: true,
      message: 'Successfully logged out',
    });
  } catch (error) {
    return next(error);
  }
});

/**
 * @desc    Forgot password
 * @route   POST /api/v1/auth/forgot-password
 * @access  Public
 */
/**
 * @desc    Forgot password - Sends a password reset token to user's email
 * @route   POST /api/v1/auth/forgot-password
 * @access  Public
 */
export const forgotPassword = asyncHandler(async (req, res, next) => {
  // 1) Get user based on POSTed email
  const user = await User.findOne({ gmail: req.body.gmail.toLowerCase().trim() });

  if (!user) {
    return next(new AppError('No user found with that email', 404, 'USER_NOT_FOUND'));
  }

  // 2) Generate reset token
  const resetToken = user.createPasswordResetToken();
  await user.save({ validateBeforeSave: false });

  try {
    // 3) Send email with reset token
    const resetURL = `${req.protocol}://${req.get('host')}/api/v1/auth/reset-password/${resetToken}`;

    await sendEmail({
      to: user.gmail,
      subject: 'Your password reset token (valid for 10 min)',
      html: `Forgot your password? Submit a PATCH request with your new password to: ${resetURL}.\nIf you didn't forget your password, please ignore this email.`,
    });

    // 4) Send response
    res.status(200).json({
      success: true,
      message: 'Token sent to email',
    });
  } catch (error) {
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    await user.save({ validateBeforeSave: false });

    return next(
      new AppError('There was an error sending the email. Please try again later.', 500, 'EMAIL_SEND_ERROR')
    );
  }
});

/**
 * @desc    Reset password
 * @route   PATCH /api/v1/auth/reset-password/:token
 * @access  Public
 */
export const resetPassword = asyncHandler(async (req, res, next) => {
  try {
    // 1) Get user based on token and check if token is expired
    const hashedToken = crypto
      .createHash('sha256')
      .update(req.params.token)
      .digest('hex');

    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() },
    });

    // 2) If token is invalid or expired
    if (!user) {
      return next(new AppError('Token is invalid or has expired', 400, 'INVALID_TOKEN'));
    }

    // 3) Update user's password
    user.password = req.body.password;
    user.passwordConfirm = req.body.passwordConfirm;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    user.passwordChangedAt = Date.now();

    // 4) Save user
    await user.save();

    // 5) Log the user in, send JWT
    const { accessToken, refreshToken } = generateTokenPair(user);

    // 6) Save refresh token to database
    user.refreshToken = refreshToken;
    await user.save({ validateBeforeSave: false });

    // 7) Set refresh token in HTTP-only cookie
    res.cookie('refreshToken', refreshToken, cookieOptions);

    // 8) Remove sensitive data from output
    user.password = undefined;
    user.refreshToken = undefined;
    user.__v = undefined;

    // 9) Send response
    res.status(200).json({
      success: true,
      message: 'Password reset successful',
      data: {
        user,
        accessToken,
        expiresIn: process.env.JWT_EXPIRES_IN,
      },
    });
  } catch (error) {
    return next(error);
  }
});

/**
 * @desc    Verify email
 * @route   GET /api/v1/auth/verify-email/:token
 * @access  Public
 */
export const verifyEmail = asyncHandler(async (req, res, next) => {
  try {
    // 1) Find user by verification token
    const user = await User.findOne({
      emailVerificationToken: req.params.token,
      emailVerificationExpires: { $gt: Date.now() },
    });

    // 2) If token is invalid or expired
    if (!user) {
      return next(new AppError('Verification token is invalid or has expired', 400, 'INVALID_TOKEN'));
    }

    // 3) Update user
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save({ validateBeforeSave: false });

    // 4) Send response
    res.status(200).json({
      success: true,
      message: 'Email verified successfully',
    });
  } catch (error) {
    return next(error);
  }
});

/**
 * @desc    Resend verification email
 * @route   POST /api/v1/auth/resend-verification-email
 * @access  Private
 */
export const resendVerificationEmail = asyncHandler(async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return next(new AppError('User not found', 404, 'USER_NOT_FOUND'));
    }

    if (user.isEmailVerified) {
      return next(new AppError('Email is already verified', 400, 'EMAIL_ALREADY_VERIFIED'));
    }

    // Generate new verification token
    user.emailVerificationToken = (await randomBytesAsync(32)).toString('hex');
    user.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours
    await user.save({ validateBeforeSave: false });

    // Send verification email
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/v1/auth/verify-email/${user.emailVerificationToken}`;

    await sendEmail({
      to: user.gmail,
      subject: 'Verify your email',
      html: `Please click <a href="${verificationUrl}">here</a> to verify your email.`,
    });

    res.status(200).json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error) {
    return next(error);
  }
});

/**
 * @desc    Check if email exists
 * @route   GET /api/v1/auth/check-email
 * @access  Public
 */
export const checkEmail = asyncHandler(async (req, res, next) => {
  const { email } = req.query;

  if (!email) {
    return next(new AppError('Email is required', 400, 'MISSING_EMAIL'));
  }

  const user = await User.findOne({ gmail: email.toLowerCase().trim() });

  res.status(200).json({
    success: true,
    data: {
      exists: !!user,
      isEmailVerified: user?.isEmailVerified || false,
    },
  });
});

/**
 * @desc    Get current user profile
 * @route   GET /api/v1/auth/me
 * @access  Private
 */
export const getMe = asyncHandler(async (req, res, next) => {
  // req.user is already populated by auth middleware, just return it
  const user = req.user;

  if (!user) {
    return next(new AppError('User not found', 404, 'USER_NOT_FOUND'));
  }

  // Filter out sensitive fields and add image URLs
  const userResponse = addImageUrls({
    _id: user._id,
    gmail: user.gmail,
    fullName: user.fullName,
    username: user.username,
    role: user.role,
    mobile: user.mobile,
    address: user.address,
    profilePhoto: user.profilePhoto,
    coverPhoto: user.coverPhoto,
    isEmailVerified: user.isEmailVerified,
    preferences: user.preferences,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  });

  res.status(200).json({
    success: true,
    data: userResponse,
  });
});

/**
 * @desc    Update user profile
 * @route   PATCH /api/v1/auth/update-me
 * @access  Private
 */
export const updateMe = asyncHandler(async (req, res, next) => {
  const { fullName, username, mobile, address } = req.body;

  // Check if username is being updated and if it's already taken
  if (username && username.trim()) {
    const trimmedUsername = username.trim();
    const existingUser = await User.findOne({
      username: { $regex: new RegExp(`^${trimmedUsername}$`, 'i') }, // Case-insensitive check
      _id: { $ne: req.user._id } // Exclude current user
    });

    if (existingUser) {
      return next(new AppError('Username is already taken', 400));
    }
  }

  // Check if mobile number is being updated and if it's already taken
  if (mobile && mobile.trim()) {
    const trimmedMobile = mobile.trim();
    const existingUserWithMobile = await User.findOne({
      mobile: trimmedMobile,
      _id: { $ne: req.user._id } // Exclude current user
    });

    if (existingUserWithMobile) {
      return next(new AppError('Mobile number is already registered with another account', 400));
    }
  }

  const updatedUser = await User.findByIdAndUpdate(
    req.user._id,
    {
      fullName: fullName?.trim(),
      username: username?.trim() || undefined, // Use undefined to remove field if empty
      mobile: mobile?.trim(),
      address: address?.trim(),
      updatedAt: Date.now(),
    },
    {
      new: true,
      runValidators: true,
      select: '-password -refreshToken -__v',
    }
  );

  res.status(200).json({
    success: true,
    message: 'Profile updated successfully',
    data: addImageUrls(updatedUser),
  });
});

/**
 * @desc    Change password
 * @route   PATCH /api/v1/auth/update-password
 * @access  Private
 */
export const updatePassword = asyncHandler(async (req, res, next) => {
  const { currentPassword, newPassword } = req.body;
  const user = await User.findById(req.user._id).select('+password');

  // 1) Check if current password is correct
  if (!(await user.correctPassword(currentPassword, user.password))) {
    return next(new AppError('Your current password is incorrect', 401, 'INVALID_CREDENTIALS'));
  }

  // 2) Update password
  user.password = newPassword;
  user.passwordChangedAt = Date.now();
  await user.save();

  // 3) Log user out of all devices by clearing refresh token
  user.refreshToken = undefined;
  await user.save({ validateBeforeSave: false });

  // 4) Clear cookies
  res.clearCookie('refreshToken', cookieOptions);

  // 5) Send response
  res.status(200).json({
    success: true,
    message: 'Password updated successfully. Please login again.',
  });
});

/**
 * @desc    Direct password change (without current password verification)
 * @route   PATCH /api/v1/auth/direct-password-change
 * @access  Public
 */
export const directPasswordChange = asyncHandler(async (req, res, next) => {
  const { gmail, newPassword } = req.body;

  // 1) Validate input
  if (!gmail || !newPassword) {
    return next(new AppError('Email and new password are required', 400, 'MISSING_FIELDS'));
  }

  if (newPassword.length < 6) {
    return next(new AppError('Password must be at least 6 characters long', 400, 'INVALID_PASSWORD'));
  }

  try {
    // 2) Find user by email
    const user = await User.findOne({ gmail: gmail.toLowerCase().trim() });

    if (!user) {
      return next(new AppError('No user found with that email', 404, 'USER_NOT_FOUND'));
    }

    // 3) Update password directly (no current password verification)
    user.password = newPassword;
    user.passwordChangedAt = Date.now();

    // Clear any existing password reset tokens
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    // Clear refresh token to log out from all devices
    user.refreshToken = undefined;

    await user.save();

    // 4) Send response
    res.status(200).json({
      success: true,
      message: 'Password changed successfully. Please login with your new password.',
    });
  } catch (error) {
    return next(new AppError('Failed to change password. Please try again.', 500, 'PASSWORD_CHANGE_ERROR'));
  }
});

