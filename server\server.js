import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { connectDB } from './config/database.js';
import { config } from './config/environment.js';
import { securityHeaders, rateLimiters } from './config/security.js';
import { errorHandler } from './middlewares/errorHandler.js';
import setupSocketIO from './sockets/index.js';

// Import routes
import authRoutes from './routes/authRoutes.js';
import uploadRoutes from './routes/uploadRoutes.js';
import grievanceRoutes from './routes/grievanceRoutes.js';

// Load environment variables
dotenv.config();

// Initialize express app
const app = express();

// Security middleware
app.use(securityHeaders);

// CORS configuration
const corsOptions = {
    origin: config.CORS_ORIGIN.split(',').map(origin => origin.trim()),
    credentials: true, // Always allow credentials for development
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Origin', 'Accept'],
    exposedHeaders: ['Set-Cookie'],
    optionsSuccessStatus: 200 // For legacy browser support
};
app.use(cors(corsOptions));

// Rate limiting (set to 1 million requests for testing)
app.use('/api/v1/auth', rateLimiters.auth);
app.use('/api', rateLimiters.general);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static file serving for uploads
app.use('/uploads/profile-photos', express.static('server/uploads/profile-photos'));
app.use('/uploads/cover-photos', express.static('server/uploads/cover-photos'));
app.use('/uploads/grievance-attachments', express.static('server/uploads/grievance-attachments'));


// Health check route
app.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Server is healthy',
        timestamp: new Date().toISOString()
    });
});

// Basic route
app.get('/', (req, res) => {
    res.json({
        success: true,
        message: 'Civic Assist API Server',
        version: '1.0.0'
    });
});

// API Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/uploads', uploadRoutes);
app.use('/api/v1/grievances', grievanceRoutes);

// Global error handler
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        error: 'NOT_FOUND'
    });
});

// Connect to database and start server
const PORT = process.env.PORT || 5000;

const startServer = async () => {
    try {
        // Try to connect to database, but don't fail if it's not available
        try {
            await connectDB();
            console.log('✅ Database connected successfully');
        } catch (dbError) {
            console.warn('⚠️  Database connection failed, continuing without database:', dbError.message);
            console.warn('⚠️  Some features may not work properly');
        }

        // Create HTTP server
        const httpServer = createServer(app);

        // Initialize Socket.IO
        const io = new Server(httpServer, {
            cors: {
                origin: config.CORS_ORIGIN.split(',').map(origin => origin.trim()),
                credentials: true,
                methods: ['GET', 'POST']
            },
            transports: ['websocket', 'polling'],
            allowEIO3: true
        });

        // Attach Socket.IO instance to Express app for access in controllers
        app.set('io', io);

        // Setup Socket.IO event handlers
        setupSocketIO(io);

        // Start server
        const server = httpServer.listen(PORT, () => {
            console.log(`🚀 Server running on port ${PORT}`);
            console.log(`📍 Environment: ${config.NODE_ENV}`);
            console.log(`🌐 API Base URL: http://localhost:${PORT}/api/v1`);
            console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
            console.log(`🔌 Socket.IO server running on ws://localhost:${PORT}`);
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
            console.log('SIGTERM received, shutting down gracefully');
            server.close(() => {
                console.log('Process terminated');
            });
        });

    } catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
};

startServer();

export default app;