/**
 * Grievance Validation Rules
 * Comprehensive validation for all grievance-related operations
 */

import { body, param, query } from 'express-validator';

// Valid categories and priorities (matching Grievance model)
const VALID_CATEGORIES = [
  'infrastructure', 'utilities', 'transportation', 'healthcare',
  'education', 'environment', 'safety', 'corruption', 'other'
];

const VALID_PRIORITIES = ['low', 'medium', 'high', 'urgent'];

const VALID_STATUSES = [
  'submitted', 'pending', 'desk_1', 'desk_2', 'desk_3', 'officer', 'in_progress',
  'resolved', 'closed', 'reopened', 'rejected', 'cancelled'
];

/**
 * Validation for creating a new grievance
 */
export const validateCreateGrievance = [
  // Title validation
  body('title')
    .trim()
    .isLength({ min: 10, max: 200 })
    .withMessage('Title must be between 10 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\,\!\?\'\"\(\)]+$/)
    .withMessage('Title contains invalid characters')
    .escape(),

  // Description validation
  body('description')
    .trim()
    .isLength({ min: 20, max: 2000 })
    .withMessage('Description must be between 20 and 2000 characters')
    .escape(),

  // Category validation
  body('category')
    .isIn(VALID_CATEGORIES)
    .withMessage(`Category must be one of: ${VALID_CATEGORIES.join(', ')}`),

  // Sub-category validation (optional)
  body('subCategory')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Sub-category cannot exceed 100 characters')
    .escape(),

  // Priority validation
  body('priority')
    .optional()
    .isIn(VALID_PRIORITIES)
    .withMessage(`Priority must be one of: ${VALID_PRIORITIES.join(', ')}`),

  // Location validation
  body('location.address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Address cannot exceed 500 characters')
    .escape(),

  body('location.city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City cannot exceed 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('City contains invalid characters')
    .escape(),

  body('location.state')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('State cannot exceed 100 characters')
    .matches(/^[a-zA-Z\s\-\.]+$/)
    .withMessage('State contains invalid characters')
    .escape(),

  body('location.pincode')
    .optional()
    .trim()
    .matches(/^[0-9]{6}$/)
    .withMessage('Pincode must be exactly 6 digits'),

  body('location.coordinates.latitude')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),

  body('location.coordinates.longitude')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),

  // Tags validation (optional)
  body('tags')
    .optional()
    .isArray({ max: 10 })
    .withMessage('Maximum 10 tags allowed'),

  body('tags.*')
    .optional()
    .trim()
    .isLength({ min: 1, max: 50 })
    .withMessage('Each tag must be between 1 and 50 characters')
    .matches(/^[a-zA-Z0-9\s\-]+$/)
    .withMessage('Tags can only contain letters, numbers, spaces, and hyphens')
    .escape(),

  // Privacy settings
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('isPublic must be a boolean'),

  body('isAnonymous')
    .optional()
    .isBoolean()
    .withMessage('isAnonymous must be a boolean'),

  // Department validation
  body('department')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Department cannot exceed 100 characters')
    .escape(),

  // Contact information validation
  body('contactInfo.phone')
    .optional()
    .trim()
    .matches(/^[0-9]{10}$/)
    .withMessage('Phone number must be exactly 10 digits'),

  body('contactInfo.email')
    .optional()
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('contactInfo.preferredContact')
    .optional()
    .isIn(['phone', 'email', 'none'])
    .withMessage('Preferred contact must be phone, email, or none'),

  // Note: Additional context fields (affectedPeopleCount, previousComplaintRef) removed
  // Contact information is now auto-populated from user profile
];

/**
 * Validation for updating a grievance
 */
export const validateUpdateGrievance = [
  // Grievance ID validation
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID'),

  // Same validations as create, but all optional
  body('title')
    .optional()
    .trim()
    .isLength({ min: 10, max: 200 })
    .withMessage('Title must be between 10 and 200 characters')
    .matches(/^[a-zA-Z0-9\s\-\.\,\!\?\'\"\(\)]+$/)
    .withMessage('Title contains invalid characters')
    .escape(),

  body('description')
    .optional()
    .trim()
    .isLength({ min: 20, max: 2000 })
    .withMessage('Description must be between 20 and 2000 characters')
    .escape(),

  body('category')
    .optional()
    .isIn(VALID_CATEGORIES)
    .withMessage(`Category must be one of: ${VALID_CATEGORIES.join(', ')}`),

  body('priority')
    .optional()
    .isIn(VALID_PRIORITIES)
    .withMessage(`Priority must be one of: ${VALID_PRIORITIES.join(', ')}`),

  // Location updates
  body('location.address')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Address cannot exceed 500 characters')
    .escape(),

  body('location.city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City cannot exceed 100 characters')
    .escape(),

  body('location.state')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('State cannot exceed 100 characters')
    .escape(),

  body('location.pincode')
    .optional()
    .trim()
    .matches(/^[0-9]{6}$/)
    .withMessage('Pincode must be exactly 6 digits')
];

/**
 * Validation for status change
 */
export const validateStatusChange = [
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID'),

  body('status')
    .isIn(VALID_STATUSES)
    .withMessage(`Status must be one of: ${VALID_STATUSES.join(', ')}`),

  body('reason')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 3, max: 500 })
    .withMessage('Reason must be between 3 and 500 characters')
    .escape(),

  body('assignedTo')
    .optional()
    .isMongoId()
    .withMessage('Invalid user ID for assignment')
];

/**
 * Validation for adding comments
 */
export const validateAddComment = [
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID'),

  body('message')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('Comment must be between 1 and 1000 characters')
    .escape(),

  body('isInternal')
    .optional()
    .isBoolean()
    .withMessage('isInternal must be a boolean')
];

/**
 * Validation for feedback submission
 */
export const validateFeedback = [
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID'),

  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),

  body('comment')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Feedback comment cannot exceed 500 characters')
    .escape()
];

/**
 * Validation for query parameters (filtering, pagination)
 */
export const validateGrievanceQuery = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),

  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000000 })
    .withMessage('Limit must be between 1 and 1000000'),

  query('status')
    .optional()
    .isIn(VALID_STATUSES)
    .withMessage(`Status must be one of: ${VALID_STATUSES.join(', ')}`),

  query('priority')
    .optional()
    .isIn(VALID_PRIORITIES)
    .withMessage(`Priority must be one of: ${VALID_PRIORITIES.join(', ')}`),

  query('category')
    .optional()
    .isIn(VALID_CATEGORIES)
    .withMessage(`Category must be one of: ${VALID_CATEGORIES.join(', ')}`),

  query('sortBy')
    .optional()
    .isIn(['submittedAt', 'lastUpdatedAt', 'priority', 'status', 'title'])
    .withMessage('Invalid sort field'),

  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),

  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for dateFrom'),

  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for dateTo'),

  query('city')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('City filter cannot exceed 100 characters')
    .escape(),

  query('state')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('State filter cannot exceed 100 characters')
    .escape()
];

/**
 * Validation for grievance ID parameter
 */
export const validateGrievanceId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID')
];

/**
 * File upload validation for grievance attachments
 */
export const validateGrievanceAttachment = [
  param('id')
    .isMongoId()
    .withMessage('Invalid grievance ID')
];

/**
 * Validation for priority change
 */
export const validatePriorityChange = [
  param('id').isMongoId().withMessage('Invalid grievance ID'),
  body('priority')
    .isIn(VALID_PRIORITIES)
    .withMessage(`Priority must be one of: ${VALID_PRIORITIES.join(', ')}`),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Note cannot exceed 500 characters')
    .escape()
];

/**
 * Validation for assignment change
 */
export const validateAssignmentChange = [
  param('id').isMongoId().withMessage('Invalid grievance ID'),
  body('assigneeId')
    .optional()
    .isMongoId()
    .withMessage('Invalid assignee ID'),
  body('note')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Note cannot exceed 500 characters')
    .escape()
];

/**
 * Validation for adding notes
 */
export const validateAddNote = [
  param('id').isMongoId().withMessage('Invalid grievance ID'),
  body('note')
    .trim()
    .isLength({ min: 5, max: 1000 })
    .withMessage('Note must be between 5 and 1000 characters')
    .escape()
];

export default {
  validateCreateGrievance,
  validateUpdateGrievance,
  validateStatusChange,
  validateAddComment,
  validateFeedback,
  validateGrievanceQuery,
  validateGrievanceId,
  validateGrievanceAttachment,
  validatePriorityChange,
  validateAssignmentChange,
  validateAddNote,
  VALID_CATEGORIES,
  VALID_PRIORITIES,
  VALID_STATUSES
};
