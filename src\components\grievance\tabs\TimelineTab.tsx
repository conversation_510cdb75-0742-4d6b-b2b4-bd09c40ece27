/**
 * Timeline Tab Component - ENHANCED VERSION
 * Using advanced timeline components with full checkpoint support
 */

import React, { useState, useMemo, useEffect } from "react";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  RefreshCw,
  Clock,
  Activity,
  BarChart3,
  History,
} from "lucide-react";

// Import the advanced timeline components
import {
  HistoryTimeline,
  type TimelineEntry,
} from "@/components/ui/history-timeline";
import { EnhancedTimelineDisplay } from "@/components/timeline/EnhancedTimelineDisplay";
import { TimelineHistoryDropdowns } from "@/components/timeline/TimelineHistoryDropdowns";

// 🛡️ PRODUCTION SAFETY: Import error boundary and type guards
import { TimelineErrorBoundary } from "@/components/timeline/TimelineErrorBoundary";
import { isValidGrievance, safeGetString } from "@/utils/typeGuards";

// Import timeline store for synchronized data
import { useTimelineStore } from "@/store/timelineStore";

// Types
interface TimelineTabProps {
  grievance: any;
  timeline?: any[];
  showPreviousTimeline?: boolean;
  expandedTimelines?: Set<string>;
  onTogglePreviousTimeline?: (show: boolean) => void;
  onToggleTimelineExpansion?: (timelineId: string) => void;
  onTimelineEntryClick?: (entry: any) => void;
}

// Enhanced Timeline Tab Component with Advanced Timeline Components
export const TimelineTab: React.FC<TimelineTabProps> = ({
  grievance,
  onTimelineEntryClick,
}) => {
  const [activeTab, setActiveTab] = useState("tracking");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Connect to timeline store for synchronized data
  const {
    currentTimeline,
    previousTimelines,
    isLoading: timelineLoading,
    error: timelineError,
    generateTimeline,
    refreshTimeline,
  } = useTimelineStore();

  // Generate timeline when grievance changes
  useEffect(() => {
    // 🛡️ PRODUCTION SAFETY: Only process if grievance exists and has basic required fields
    if (grievance && grievance._id) {
      console.log(
        "🔄 TimelineTab: Generating timeline for grievance:",
        grievance._id
      );
      try {
        generateTimeline(grievance);
      } catch (error) {
        console.warn("🚨 TimelineTab: Error generating timeline:", error);
      }
    }
  }, [grievance, generateTimeline]);

  // Define refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      console.log("🔄 TimelineTab: Refreshing timeline data");

      // Refresh timeline from store
      if (grievance && grievance._id) {
        await refreshTimeline(grievance._id);
        console.log("✅ TimelineTab: Timeline refreshed successfully");
      }

      // Trigger additional refresh if callback provided
      if (onTimelineEntryClick) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error("❌ TimelineTab: Error refreshing timeline:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 🛡️ CRITICAL FIX: Move useMemo before any conditional returns to follow Rules of Hooks
  const timelineEntries: TimelineEntry[] = useMemo(() => {
    try {
      console.log("🔍 TimelineTab: Processing timeline data", {
        hasCurrentTimeline: !!currentTimeline,
        currentTimelineEntries: currentTimeline?.entries?.length || 0,
        hasGrievance: !!grievance,
        grievanceStatusHistory: grievance?.statusHistory?.length || 0,
      });

      // Prioritize timeline store data if available
      if (
        currentTimeline &&
        currentTimeline.entries &&
        currentTimeline.entries.length > 0
      ) {
        console.log("📊 TimelineTab: Using timeline store data");
        // Filter out entries that don't have required fields - be more lenient
        return currentTimeline.entries
          .filter(
            (entry: any) =>
              entry && entry.status && (entry.timestamp || entry.id)
          )
          .map((entry: any, index: number) => ({
            id: entry.id,
            status: entry.status,
            timestamp: entry.timestamp,
            triggeredBy: {
              name: entry.triggeredBy?.name || entry.changedBy,
              role: entry.triggeredBy?.role || entry.role,
              id: entry.triggeredBy?.id || entry.userId,
            },
            reason: entry.reason || entry.notes,
            notes: entry.notes,
            autoMoved: entry.autoMoved,
            attachments: entry.attachments,
            duration: entry.duration,
            // Include SLA and checkpoint information from timeline store
            checkpointInfo: entry.checkpointInfo,
            slaStatus: entry.slaStatus,
            metadata: entry.metadata,
          }));
      }

      // Fallback to grievance status history if timeline store data not available
      if (!grievance || !grievance.status) {
        return [];
      }

      if (!grievance.statusHistory || grievance.statusHistory.length === 0) {
        // No mock data - return empty array if no real history exists
        return [];
      }

      console.log("📊 TimelineTab: Using grievance status history");
      // Filter out entries that don't have required fields - be more lenient with timestamp field
      return grievance.statusHistory
        .filter(
          (entry: any) =>
            entry && entry.status && (entry.changedAt || entry.timestamp)
        )
        .map((entry: any, index: number) => ({
          id: entry.id || `history-${index}`,
          status: entry.status,
          timestamp: entry.changedAt || entry.timestamp,
          triggeredBy: {
            name: entry.changedBy || entry.triggeredBy?.name,
            role: entry.role || entry.triggeredBy?.role,
            id: entry.userId || entry.triggeredBy?.id,
          },
          reason: entry.reason || entry.notes,
          notes: entry.notes,
          autoMoved: entry.autoMoved,
          attachments: entry.attachments,
          duration: entry.duration,
        }));
    } catch (error) {
      console.error("Error processing timeline entries:", error);
      return [];
    }
  }, [grievance, currentTimeline]);

  // 🛡️ CRITICAL FIX: All conditional returns moved after hooks to follow Rules of Hooks

  // Check for missing grievance data
  if (!grievance) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">No grievance data available</p>
        </div>
      </div>
    );
  }

  // Show loading state for timeline
  if (timelineLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 text-muted-foreground mx-auto mb-4 animate-spin" />
          <p className="text-muted-foreground">Loading timeline data...</p>
        </div>
      </div>
    );
  }

  // Show error state for timeline
  if (timelineError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-500 mb-4">
            Error loading timeline: {timelineError}
          </p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <TimelineErrorBoundary
      onError={(error, errorInfo) => {
        console.error("🚨 Timeline Error:", error);
        console.error("📊 Error Info:", errorInfo);
      }}
    >
      <div className="h-full bg-background/50 dark:bg-[#0D0D0D]/50 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
        <div className="space-y-6 p-6">
          {/* Enhanced Header with Rich Information */}
          <Card className="bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold text-foreground">
                      Grievance Timeline
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                      Track progress and manage status changes
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Badge
                    variant="outline"
                    className="bg-background/50 dark:bg-[#0D0D0D]/50"
                  >
                    ID: {grievance._id?.slice(-6) || "N/A"}
                  </Badge>
                  <Badge
                    variant={
                      grievance.status === "resolved" ? "default" : "secondary"
                    }
                    className="bg-background/50 dark:bg-[#0D0D0D]/50"
                  >
                    {grievance.status?.toUpperCase()}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                    className="bg-background/50 dark:bg-[#0D0D0D]/50 hover:bg-background/70 dark:hover:bg-[#0D0D0D]/70"
                  >
                    <RefreshCw
                      className={`h-4 w-4 mr-1 ${
                        isRefreshing ? "animate-spin" : ""
                      }`}
                    />
                    Refresh
                  </Button>
                </div>
              </div>

              {/* Rich Statistics Row */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20 rounded-lg p-4 border border-blue-200/50 dark:border-blue-800/50">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {timelineEntries.length}
                  </div>
                  <div className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                    Timeline Events
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20 rounded-lg p-4 border border-green-200/50 dark:border-green-800/50">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {Math.round(((timelineEntries.length || 1) / 9) * 100)}%
                  </div>
                  <div className="text-sm text-green-700 dark:text-green-300 font-medium">
                    Progress
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20 rounded-lg p-4 border border-purple-200/50 dark:border-purple-800/50">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {grievance.reopenCount || 0}
                  </div>
                  <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">
                    Times Reopened
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20 rounded-lg p-4 border border-orange-200/50 dark:border-orange-800/50">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {(() => {
                      const submitted = new Date(grievance.submittedAt);
                      const now = new Date();
                      const diffHours = Math.floor(
                        (now.getTime() - submitted.getTime()) / (1000 * 60 * 60)
                      );
                      if (diffHours < 24) return `${diffHours}h`;
                      return `${Math.floor(diffHours / 24)}d`;
                    })()}
                  </div>
                  <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">
                    Total Duration
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Enhanced SLA Status Container */}
          <Card className="bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                    <BarChart3 className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">
                      SLA Status
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Service Level Agreement Tracking
                    </p>
                  </div>
                </div>

                <Badge
                  variant={
                    grievance.priority === "high"
                      ? "destructive"
                      : grievance.priority === "medium"
                      ? "default"
                      : "secondary"
                  }
                  className="bg-background/50 dark:bg-[#0D0D0D]/50 font-semibold"
                >
                  {(grievance.priority || "medium").toUpperCase()} PRIORITY
                </Badge>
              </div>

              {/* SLA Progress Line */}
              <div className="space-y-4">
                <div className="relative">
                  <div className="w-full bg-muted/30 rounded-full h-3 shadow-inner">
                    <div
                      className={`h-3 rounded-full transition-all duration-500 ease-out shadow-sm ${
                        timelineEntries.length >= 8
                          ? "bg-gradient-to-r from-red-500 to-red-600"
                          : timelineEntries.length >= 6
                          ? "bg-gradient-to-r from-orange-500 to-orange-600"
                          : timelineEntries.length >= 4
                          ? "bg-gradient-to-r from-yellow-500 to-yellow-600"
                          : "bg-gradient-to-r from-green-500 to-green-600"
                      }`}
                      style={{
                        width: `${Math.min(
                          ((timelineEntries.length || 1) / 9) * 100,
                          100
                        )}%`,
                      }}
                    />
                  </div>
                  <div className="absolute -top-1 -right-1 text-xs font-medium text-foreground bg-background/80 dark:bg-[#0D0D0D]/80 px-2 py-1 rounded-full border border-border/30 shadow-sm">
                    {Math.round(((timelineEntries.length || 1) / 9) * 100)}%
                  </div>
                </div>

                {/* SLA Details Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                        Expected Resolution
                      </p>
                    </div>
                    <p className="text-sm font-semibold text-foreground">
                      {(() => {
                        const submitted = new Date(grievance.submittedAt);
                        const priority = grievance.priority || "medium";
                        const slaHours =
                          priority === "high"
                            ? 72
                            : priority === "medium"
                            ? 120
                            : 168;
                        const expectedDate = new Date(
                          submitted.getTime() + slaHours * 60 * 60 * 1000
                        );
                        return expectedDate.toLocaleDateString();
                      })()}
                    </p>
                  </div>

                  <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <Activity className="h-4 w-4 text-green-600 dark:text-green-400" />
                      <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                        Time Remaining
                      </p>
                    </div>
                    <p className="text-sm font-semibold text-foreground">
                      {(() => {
                        const submitted = new Date(grievance.submittedAt);
                        const now = new Date();
                        const priority = grievance.priority || "medium";
                        const slaHours =
                          priority === "high"
                            ? 72
                            : priority === "medium"
                            ? 120
                            : 168;
                        const expectedDate = new Date(
                          submitted.getTime() + slaHours * 60 * 60 * 1000
                        );
                        const remaining =
                          expectedDate.getTime() - now.getTime();

                        if (remaining <= 0) return "Overdue";

                        const remainingHours = Math.floor(
                          remaining / (1000 * 60 * 60)
                        );
                        if (remainingHours < 24) return `${remainingHours}h`;
                        return `${Math.floor(remainingHours / 24)}d`;
                      })()}
                    </p>
                  </div>

                  <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <History className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                      <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                        Current Stage
                      </p>
                    </div>
                    <p className="text-sm font-semibold text-foreground">
                      {grievance.status
                        ?.replace("_", " ")
                        .replace(/\b\w/g, (l: string) => l.toUpperCase()) ||
                        "Unknown"}
                    </p>
                  </div>

                  <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-3 border border-border/30 shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                      <RefreshCw className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                      <p className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                        SLA Compliance
                      </p>
                    </div>
                    <p
                      className={`text-sm font-semibold ${
                        timelineEntries.length >= 8
                          ? "text-red-600 dark:text-red-400"
                          : timelineEntries.length >= 6
                          ? "text-orange-600 dark:text-orange-400"
                          : timelineEntries.length >= 4
                          ? "text-yellow-600 dark:text-yellow-400"
                          : "text-green-600 dark:text-green-400"
                      }`}
                    >
                      {timelineEntries.length >= 8
                        ? "BREACHED"
                        : timelineEntries.length >= 6
                        ? "CRITICAL"
                        : timelineEntries.length >= 4
                        ? "URGENT"
                        : "ON TRACK"}
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Timeline Tabs */}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full bg-background/50 dark:bg-[#0D0D0D]/50 rounded-lg border border-border/30 shadow-lg backdrop-blur-sm"
          >
            <TabsList className="grid w-full grid-cols-2 bg-background/30 dark:bg-[#0D0D0D]/30 border border-border/30">
              <TabsTrigger
                value="tracking"
                className="flex items-center gap-2 data-[state=active]:bg-background/50 dark:data-[state=active]:bg-[#0D0D0D]/50"
              >
                <BarChart3 className="h-4 w-4" />
                Progress Tracking
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="flex items-center gap-2 data-[state=active]:bg-background/50 dark:data-[state=active]:bg-[#0D0D0D]/50"
              >
                <History className="h-4 w-4" />
                Timeline History
              </TabsTrigger>
            </TabsList>

            {/* Progress Tracking Tab */}
            <TabsContent value="tracking" className="mt-6">
              <EnhancedTimelineDisplay
                grievance={grievance}
                onCheckpointClick={(checkpoint) => {
                  console.log("Checkpoint clicked:", checkpoint);
                  onTimelineEntryClick?.(checkpoint);
                }}
                className="w-full"
              />
            </TabsContent>

            {/* Timeline History Tab */}
            <TabsContent value="history" className="mt-6">
              <div className="space-y-6">
                {/* Current Timeline History */}
                <div className="w-full bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg border border-border/30 shadow-lg backdrop-blur-sm p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                    <History className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    Current Timeline History
                  </h3>
                  <HistoryTimeline
                    entries={timelineEntries}
                    className="w-full"
                    showDuration={true}
                    collapsible={true}
                  />
                </div>

                {/* Previous Timeline Cycles */}
                {((previousTimelines && previousTimelines.length > 0) ||
                  (grievance.previousTimelines &&
                    grievance.previousTimelines.length > 0)) && (
                  <TimelineHistoryDropdowns
                    previousTimelines={(
                      previousTimelines || grievance.previousTimelines
                    ).map((timeline: any) => ({
                      id:
                        timeline.id ||
                        timeline._id ||
                        `timeline-${timeline.cycleNumber}`,
                      cycleNumber: timeline.cycleNumber || 1,
                      startDate:
                        timeline.startDate ||
                        timeline.createdAt ||
                        new Date().toISOString(),
                      endDate:
                        timeline.endDate ||
                        timeline.completedAt ||
                        new Date().toISOString(),
                      finalStatus: timeline.finalStatus || "closed",
                      completionReason:
                        timeline.completionReason || timeline.reason,
                      statusHistory: timeline.statusHistory || [],
                      slaCompliance: timeline.slaCompliance || {
                        status: "ON_TRACK",
                        totalDuration: 0,
                        expectedDuration: 72,
                      },
                    }))}
                    className="w-full"
                  />
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </TimelineErrorBoundary>
  );
};

export default TimelineTab;
