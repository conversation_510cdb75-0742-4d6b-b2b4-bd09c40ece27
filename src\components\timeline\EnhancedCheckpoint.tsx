/**
 * Enhanced Checkpoint Component
 * Beautiful checkpoint display with proper coloring and animations
 */

import React from "react";
import { motion } from "framer-motion";
import {
  CheckCircle,
  Clock,
  User,
  Calendar,
  MessageSquare,
  Eye,
  Shield,
  UserCheck,
  Settings,
  Play,
  Archive,
  XCircle,
  RotateCcw,
  Send,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

// Checkpoint status type
type CheckpointStatus =
  | "completed"
  | "in_progress"
  | "pending"
  | "rejected"
  | "cancelled";

// Checkpoint data interface
interface CheckpointData {
  id: string;
  status: string;
  title: string;
  description: string;
  timestamp?: string;
  triggeredBy?: {
    name: string;
    role: string;
  };
  notes?: string;
  duration?: number;
  checkpointStatus: CheckpointStatus;
}

interface EnhancedCheckpointProps {
  checkpoint: CheckpointData;
  isLast?: boolean;
  onClick?: (checkpoint: CheckpointData) => void;
}

// Icon mapping for different statuses
const STATUS_ICONS = {
  submitted: Send,
  pending: Clock,
  desk_1: Eye,
  desk_2: Shield,
  desk_3: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  officer: Setting<PERSON>,
  in_progress: Play,
  resolved: CheckCircle,
  closed: Archive,
  rejected: XCircle,
  cancelled: XCircle,
  reopened: RotateCcw,
};

// Color schemes for different checkpoint statuses
const COLOR_SCHEMES = {
  completed: {
    bg: "bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20",
    border: "border-green-200 dark:border-green-800",
    text: "text-green-800 dark:text-green-200",
    icon: "text-green-600 dark:text-green-400",
    indicator: "bg-gradient-to-r from-green-500 to-green-600",
    line: "bg-green-400",
  },
  in_progress: {
    bg: "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20",
    border: "border-blue-200 dark:border-blue-800",
    text: "text-blue-800 dark:text-blue-200",
    icon: "text-blue-600 dark:text-blue-400",
    indicator: "bg-gradient-to-r from-blue-500 to-blue-600",
    line: "bg-blue-400",
  },
  pending: {
    bg: "bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-950/20 dark:to-gray-900/20",
    border: "border-gray-200 dark:border-gray-800",
    text: "text-gray-600 dark:text-gray-400",
    icon: "text-gray-400 dark:text-gray-500",
    indicator: "bg-gradient-to-r from-gray-400 to-gray-500",
    line: "bg-gray-300",
  },
  rejected: {
    bg: "bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20",
    border: "border-red-200 dark:border-red-800",
    text: "text-red-800 dark:text-red-200",
    icon: "text-red-600 dark:text-red-400",
    indicator: "bg-gradient-to-r from-red-500 to-red-600",
    line: "bg-red-400",
  },
  cancelled: {
    bg: "bg-gradient-to-r from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20",
    border: "border-red-200 dark:border-red-800",
    text: "text-red-800 dark:text-red-200",
    icon: "text-red-600 dark:text-red-400",
    indicator: "bg-gradient-to-r from-red-500 to-red-600",
    line: "bg-red-400",
  },
};

export const EnhancedCheckpoint: React.FC<EnhancedCheckpointProps> = ({
  checkpoint,
  isLast = false,
  onClick,
}) => {
  const colorScheme = COLOR_SCHEMES[checkpoint.checkpointStatus];
  const IconComponent =
    STATUS_ICONS[checkpoint.status as keyof typeof STATUS_ICONS] || Clock;

  // Format timestamp
  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return "Pending";

    const date = new Date(timestamp);
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(date);
  };

  // Format duration
  const formatDuration = (minutes?: number) => {
    if (!minutes) return null;

    if (minutes < 60) return `${minutes}m`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ${minutes % 60}m`;
    return `${Math.floor(minutes / 1440)}d ${Math.floor(
      (minutes % 1440) / 60
    )}h`;
  };

  return (
    <div className="relative flex items-start gap-6">
      {/* Timeline line */}
      {!isLast && (
        <div
          className={cn(
            "absolute left-8 top-20 w-0.5 h-24 -ml-px z-0",
            colorScheme.line
          )}
        />
      )}

      {/* Checkpoint indicator - centered */}
      <div className="relative flex-shrink-0 z-10">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
          className={cn(
            "w-16 h-16 rounded-full border-4 flex items-center justify-center shadow-lg transition-all duration-300 hover:scale-105",
            colorScheme.border,
            colorScheme.indicator,
            checkpoint.checkpointStatus === "in_progress" &&
              "ring-4 ring-blue-400/30 animate-pulse"
          )}
        >
          <IconComponent
            className={cn(
              "w-7 h-7 text-white transition-transform duration-300 hover:scale-110"
            )}
          />
        </motion.div>

        {/* Status badge */}
        <div className="absolute -bottom-2 -right-2">
          <Badge
            variant={
              checkpoint.checkpointStatus === "completed"
                ? "default"
                : checkpoint.checkpointStatus === "in_progress"
                ? "secondary"
                : checkpoint.checkpointStatus === "rejected" ||
                  checkpoint.checkpointStatus === "cancelled"
                ? "destructive"
                : "outline"
            }
            className="text-xs font-bold shadow-md"
          >
            {checkpoint.checkpointStatus.replace("_", " ").toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Checkpoint content */}
      <div className="flex-1 min-w-0">
        <motion.div
          initial={{ x: 20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <Card
            className={cn(
              "transition-all duration-300 hover:shadow-xl cursor-pointer transform hover:-translate-y-1",
              colorScheme.bg,
              colorScheme.border,
              "border-2 shadow-lg backdrop-blur-sm"
            )}
            onClick={() => onClick?.(checkpoint)}
          >
            <CardContent className="p-6">
              {/* Header */}
              <div className="flex items-start justify-between gap-4 mb-4">
                <div className="flex-1 min-w-0">
                  <h3
                    className={cn(
                      "text-xl font-bold leading-tight mb-2",
                      colorScheme.text
                    )}
                  >
                    {checkpoint.title}
                  </h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {checkpoint.description}
                  </p>
                </div>

                {/* Timestamp */}
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground mb-1">
                    <Calendar className="w-4 h-4" />
                    <span>{formatTimestamp(checkpoint.timestamp)}</span>
                  </div>

                  {checkpoint.duration && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Clock className="w-4 h-4" />
                      <span>{formatDuration(checkpoint.duration)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Details */}
              <div className="space-y-3">
                {/* Triggered by */}
                {checkpoint.triggeredBy && (
                  <div className="flex items-center gap-2 text-sm">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Processed by:</span>
                    <span className="font-medium">
                      {typeof checkpoint.triggeredBy.name === "string"
                        ? checkpoint.triggeredBy.name
                        : String(checkpoint.triggeredBy.name || "System")}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {typeof checkpoint.triggeredBy.role === "string"
                        ? checkpoint.triggeredBy.role
                        : String(checkpoint.triggeredBy.role || "Officer")}
                    </Badge>
                  </div>
                )}

                {/* Notes */}
                {checkpoint.notes && (
                  <div className="flex items-start gap-2 text-sm">
                    <MessageSquare className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <div>
                      <span className="text-muted-foreground">Notes:</span>
                      <p className="mt-1 text-foreground font-medium leading-relaxed">
                        {checkpoint.notes}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedCheckpoint;
