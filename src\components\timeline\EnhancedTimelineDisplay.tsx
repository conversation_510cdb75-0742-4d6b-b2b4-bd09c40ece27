/**
 * Enhanced Timeline Display Component
 * Beautiful timeline with enhanced checkpoints and rich features
 */

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, Calendar, User, FileText } from "lucide-react";
import { EnhancedCheckpoint } from "./EnhancedCheckpoint";
import { cn } from "@/lib/utils";

// Checkpoint status mapping
const getCheckpointStatus = (status: string, currentStatus: string) => {
  const statusOrder = [
    "submitted",
    "pending",
    "desk_1",
    "desk_2",
    "desk_3",
    "officer",
    "in_progress",
    "resolved",
    "closed",
  ];

  const currentIndex = statusOrder.indexOf(currentStatus);
  const checkpointIndex = statusOrder.indexOf(status);

  // Handle special cases
  if (status === "rejected" || status === "cancelled") return "rejected";
  if (status === "reopened") return "in_progress";

  // Handle submitted status - it's always completed once the grievance exists
  if (status === "submitted") {
    return "completed"; // Submitted is always completed if the grievance exists
  }

  // Handle other statuses
  if (checkpointIndex < currentIndex) return "completed";
  if (checkpointIndex === currentIndex) return "in_progress";
  return "pending";
};

// Checkpoint definitions with beautiful titles and descriptions
const CHECKPOINT_DEFINITIONS = {
  submitted: {
    title: "Grievance Submitted",
    description:
      "Your grievance has been successfully submitted and is now in the system for processing.",
  },
  pending: {
    title: "Initial Review",
    description:
      "Your grievance is being reviewed for completeness and proper categorization.",
  },
  desk_1: {
    title: "Initial Verification",
    description:
      "First level verification of facts and preliminary assessment of the grievance.",
  },
  desk_2: {
    title: "Fact Checking",
    description:
      "Detailed fact-checking and validation of the information provided in your grievance.",
  },
  desk_3: {
    title: "Administrative Review",
    description:
      "Administrative review and preparation for officer assignment or resolution.",
  },
  officer: {
    title: "Officer Assignment",
    description:
      "A dedicated officer has been assigned to investigate and resolve your grievance.",
  },
  in_progress: {
    title: "Investigation in Progress",
    description:
      "Active investigation is underway. The assigned officer is working on your case.",
  },
  resolved: {
    title: "Grievance Resolved",
    description:
      "Your grievance has been successfully resolved. Resolution details are available.",
  },
  closed: {
    title: "Case Closed",
    description:
      "The grievance case has been officially closed and archived in the system.",
  },
  rejected: {
    title: "Grievance Rejected",
    description:
      "Your grievance has been rejected. Please review the rejection reason provided.",
  },
  cancelled: {
    title: "Grievance Cancelled",
    description:
      "The grievance has been cancelled either by request or due to specific circumstances.",
  },
  reopened: {
    title: "Grievance Reopened",
    description:
      "The grievance has been reopened for further investigation or review.",
  },
};

interface EnhancedTimelineDisplayProps {
  grievance: any;
  onCheckpointClick?: (checkpoint: any) => void;
  className?: string;
}

export const EnhancedTimelineDisplay: React.FC<
  EnhancedTimelineDisplayProps
> = ({ grievance, onCheckpointClick, className }) => {
  // Generate checkpoint data from grievance
  const generateCheckpoints = () => {
    try {
      const checkpoints: any[] = [];
      const statusHistory = grievance?.statusHistory || [];
      const currentStatus = grievance?.status || "submitted";

      console.log(
        "🔄 EnhancedTimelineDisplay: Generating checkpoints for status:",
        currentStatus
      );
      console.log("📊 Status history entries:", statusHistory.length);

      // Create checkpoints for all possible statuses - CONSISTENT ORDER
      const allStatuses = [
        "submitted",
        "pending",
        "desk_1",
        "desk_2",
        "desk_3",
        "officer",
        "in_progress",
        "resolved",
        "closed",
      ];

      allStatuses.forEach((status) => {
        const historyEntry = statusHistory.find(
          (entry: any) => entry.status === status
        );
        const checkpointStatus = getCheckpointStatus(status, currentStatus);
        const definition =
          CHECKPOINT_DEFINITIONS[status as keyof typeof CHECKPOINT_DEFINITIONS];

        checkpoints.push({
          id: `checkpoint-${status}`,
          status,
          title: definition.title,
          description: definition.description,
          timestamp: historyEntry?.changedAt || historyEntry?.timestamp,
          triggeredBy: historyEntry
            ? {
                name: (() => {
                  // Safely extract name from various possible formats
                  if (
                    typeof historyEntry.changedBy === "object" &&
                    historyEntry.changedBy !== null
                  ) {
                    return String(
                      historyEntry.changedBy.fullName ||
                        historyEntry.changedBy.name ||
                        "System"
                    );
                  }
                  if (typeof historyEntry.changedBy === "string") {
                    return historyEntry.changedBy;
                  }
                  return "System";
                })(),
                role: (() => {
                  // Safely extract role from various possible formats
                  if (
                    typeof historyEntry.changedBy === "object" &&
                    historyEntry.changedBy !== null
                  ) {
                    return String(historyEntry.changedBy.role || "Officer");
                  }
                  return String(historyEntry.role || "Officer");
                })(),
              }
            : undefined,
          notes: historyEntry?.reason || historyEntry?.notes,
          duration: historyEntry?.duration,
          checkpointStatus: checkpointStatus as any,
        });
      });

      // Add special statuses if they exist
      if (
        currentStatus === "rejected" ||
        statusHistory.some((entry: any) => entry.status === "rejected")
      ) {
        const rejectedEntry = statusHistory.find(
          (entry: any) => entry.status === "rejected"
        );
        checkpoints.push({
          id: "checkpoint-rejected",
          status: "rejected",
          title: CHECKPOINT_DEFINITIONS.rejected.title,
          description: CHECKPOINT_DEFINITIONS.rejected.description,
          timestamp: rejectedEntry?.changedAt || rejectedEntry?.timestamp,
          triggeredBy: rejectedEntry
            ? {
                name: (() => {
                  // Safely extract name from various possible formats
                  if (
                    typeof rejectedEntry.changedBy === "object" &&
                    rejectedEntry.changedBy !== null
                  ) {
                    return String(
                      rejectedEntry.changedBy.fullName ||
                        rejectedEntry.changedBy.name ||
                        "System"
                    );
                  }
                  if (typeof rejectedEntry.changedBy === "string") {
                    return rejectedEntry.changedBy;
                  }
                  return "System";
                })(),
                role: (() => {
                  // Safely extract role from various possible formats
                  if (
                    typeof rejectedEntry.changedBy === "object" &&
                    rejectedEntry.changedBy !== null
                  ) {
                    return String(rejectedEntry.changedBy.role || "Officer");
                  }
                  return String(rejectedEntry.role || "Officer");
                })(),
              }
            : undefined,
          notes: rejectedEntry?.reason || rejectedEntry?.notes,
          duration: rejectedEntry?.duration,
          checkpointStatus: "rejected" as any,
        });
      }

      const filteredCheckpoints = checkpoints.filter(
        (cp) =>
          cp.checkpointStatus === "completed" ||
          cp.checkpointStatus === "in_progress" ||
          cp.checkpointStatus === "rejected" ||
          (cp.checkpointStatus === "pending" &&
            checkpoints.some((c) => c.checkpointStatus === "in_progress"))
      );

      console.log(
        "✅ EnhancedTimelineDisplay: Generated",
        filteredCheckpoints.length,
        "checkpoints"
      );
      return filteredCheckpoints;
    } catch (error) {
      console.error(
        "❌ EnhancedTimelineDisplay: Error generating checkpoints:",
        error
      );
      return [];
    }
  };

  const checkpoints = generateCheckpoints();

  return (
    <div className={cn("space-y-6", className)}>
      {/* Timeline Header */}
      <Card className="bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              Progress Timeline
            </CardTitle>

            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className="bg-background/50 dark:bg-[#0D0D0D]/50"
              >
                {
                  checkpoints.filter(
                    (cp) => cp.checkpointStatus === "completed"
                  ).length
                }{" "}
                / {checkpoints.length} Steps
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Enhanced Timeline */}
      <Card className="bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm">
        <CardContent className="p-8">
          <div className="space-y-8">
            <AnimatePresence>
              {checkpoints.map((checkpoint, index) => (
                <motion.div
                  key={checkpoint.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <EnhancedCheckpoint
                    checkpoint={checkpoint}
                    isLast={index === checkpoints.length - 1}
                    onClick={onCheckpointClick}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Empty state */}
          {checkpoints.length === 0 && (
            <div className="text-center py-12">
              <Clock className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground text-lg">
                No timeline data available
              </p>
              <p className="text-muted-foreground text-sm mt-2">
                Timeline will appear as your grievance progresses
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Timeline Summary */}
      <Card className="bg-background/50 dark:bg-[#0D0D0D]/50 border-border/30 shadow-lg backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            Timeline Summary
          </CardTitle>
        </CardHeader>

        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <p className="text-sm font-semibold text-muted-foreground">
                  Started
                </p>
              </div>
              <p className="text-lg font-bold text-foreground">
                {grievance?.submittedAt
                  ? new Date(grievance.submittedAt).toLocaleDateString()
                  : "Unknown"}
              </p>
            </div>

            <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-green-600 dark:text-green-400" />
                <p className="text-sm font-semibold text-muted-foreground">
                  Duration
                </p>
              </div>
              <p className="text-lg font-bold text-foreground">
                {(() => {
                  if (!grievance?.submittedAt) return "Unknown";
                  const submitted = new Date(grievance.submittedAt);
                  const now = new Date();
                  const diffDays = Math.floor(
                    (now.getTime() - submitted.getTime()) /
                      (1000 * 60 * 60 * 24)
                  );
                  return `${diffDays} days`;
                })()}
              </p>
            </div>

            <div className="bg-background/30 dark:bg-[#0D0D0D]/30 rounded-lg p-4 border border-border/30">
              <div className="flex items-center gap-2 mb-2">
                <User className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                <p className="text-sm font-semibold text-muted-foreground">
                  Current Stage
                </p>
              </div>
              <p className="text-lg font-bold text-foreground">
                {grievance?.status
                  ?.replace("_", " ")
                  .replace(/\b\w/g, (l: string) => l.toUpperCase()) ||
                  "Unknown"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedTimelineDisplay;
