import { io, Socket } from "socket.io-client";
import { useAuthStore } from "../store/authStore";
import { useGrievanceStore } from "../store/grievanceStore";

class SocketClient {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect() {
    const { accessToken } = useAuthStore.getState();

    if (!accessToken || this.socket?.connected) {
      return;
    }

    this.socket = io(
      (import.meta as any).env.VITE_WEBSOCKET_URL || "http://localhost:5000",
      {
        auth: {
          token: accessToken,
        },
        transports: ["polling", "websocket"], // Try polling first, then websocket
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: 1000,
        timeout: 20000,
        upgrade: true,
        rememberUpgrade: false,
      }
    );

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on("connect", () => {
      console.log("Socket connected successfully");
      this.reconnectAttempts = 0;
    });

    this.socket.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
    });

    this.socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error.message);

      // Check if it's an authentication error
      if (
        error.message.includes("Authentication error") ||
        error.message.includes("jwt expired")
      ) {
        console.log(
          "Authentication error detected, attempting token refresh..."
        );
        this.handleAuthenticationError();
        return;
      }

      this.reconnectAttempts++;
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error(
          "Max reconnection attempts reached, disconnecting socket"
        );
        this.disconnect();
      }
    });

    // Grievance-related events
    this.socket.on("grievance:update", () => {
      // Refresh grievances to get latest data
      const grievanceStore = useGrievanceStore.getState();

      // Refresh grievances using unified function
      grievanceStore.getGrievances();
    });

    this.socket.on("grievance:status_change", (data) => {
      const { grievanceId, newStatus, updatedGrievance } = data;
      console.log("🔄 WebSocket: Received status change event:", {
        grievanceId,
        newStatus,
      });

      // Update specific grievance in both grievances arrays
      const state = useGrievanceStore.getState();

      // Update main grievances array (for admin/officer view)
      const updatedGrievances = state.grievances.map((g) =>
        g._id === grievanceId ? { ...g, status: newStatus } : g
      );

      // Update myGrievances array (for user view)
      const updatedMyGrievances = state.myGrievances.map((g) =>
        g._id === grievanceId ? { ...g, status: newStatus } : g
      );

      // CRITICAL: Update currentGrievance if it's the one being viewed
      let updatedCurrentGrievance = state.currentGrievance;
      if (state.currentGrievance?._id === grievanceId) {
        console.log("🔄 WebSocket: Updating current grievance with new status");
        updatedCurrentGrievance = updatedGrievance || {
          ...state.currentGrievance,
          status: newStatus,
          lastUpdatedAt: new Date().toISOString(),
        };

        // CRITICAL: Refresh timeline when current grievance status changes
        console.log(
          "🔄 WebSocket: Triggering timeline refresh for current grievance"
        );
        // Import and use timeline store to refresh timeline
        import("@/store/timelineStore").then(({ useTimelineStore }) => {
          const timelineActions = useTimelineStore.getState();
          if (timelineActions.refreshTimeline) {
            timelineActions.refreshTimeline(grievanceId);
          }
        });
      }

      useGrievanceStore.setState({
        grievances: updatedGrievances,
        myGrievances: updatedMyGrievances,
        currentGrievance: updatedCurrentGrievance,
      });

      console.log("✅ WebSocket: Store updated with new status");

      // Show notification for status change
      console.log(`Grievance status updated to ${newStatus}`);
    });

    // Notification events
    this.socket.on("notification", () => {
      // TODO: Handle notifications (show toast, update notification store, etc.)
    });

    // Chat events
    this.socket.on("chat:message", () => {
      // TODO: Handle chat messages
    });

    // System events
    this.socket.on("system:announcement", () => {
      // TODO: Handle system announcements
    });
  }

  private async handleAuthenticationError() {
    try {
      console.log("Attempting to refresh token for socket connection...");

      // Disconnect current socket
      this.disconnect();

      // Get current auth state
      const { refreshToken } = useAuthStore.getState();

      if (!refreshToken) {
        console.error("No refresh token available for socket reconnection");
        useAuthStore.getState().logout();
        return;
      }

      // Attempt to refresh the token using the same logic as apiClient
      const response = await fetch(
        `${
          import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api/v1"
        }/auth/refresh`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
          body: JSON.stringify({ refreshToken }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data?.accessToken) {
          console.log("Token refreshed successfully for socket connection");

          // Update tokens in store
          useAuthStore
            .getState()
            .setTokens(
              data.data.accessToken,
              data.data.refreshToken || refreshToken
            );

          // Reconnect socket with new token
          setTimeout(() => {
            this.connect();
          }, 1000);
        } else {
          throw new Error("Invalid refresh response");
        }
      } else {
        throw new Error(`Refresh failed: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to refresh token for socket connection:", error);
      useAuthStore.getState().logout();
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  emit(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

export const socketClient = new SocketClient();
export default socketClient;
