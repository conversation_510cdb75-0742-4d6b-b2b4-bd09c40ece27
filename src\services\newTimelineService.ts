/**
 * New Timeline Service
 * Unified timeline generation and management service
 */

import type {
  TimelineDisplay,
  TimelineEntry,
  PreviousTimeline,
  TimelineStatistics,
  Grievance,
  GrievanceStatus,
  SLAStatus,
} from "@/types/timeline";
import {
  getCheckpointInfo,
  getSLAConfig,
  isTerminalStatus,
} from "@/config/timelineConfig";

// 🛡️ PRODUCTION SAFETY: Import type guards and validation utilities
import {
  validateAndSanitizeGrievance,
  validateAndSanitizeStatusHistory,
} from "@/utils/typeGuards";

export class TimelineService {
  /**
   * Generate complete timeline display from grievance data
   */
  generateTimeline(grievance: Grievance): TimelineDisplay {
    try {
      // 🛡️ PRODUCTION SAFETY: Validate and sanitize input
      const sanitizedGrievance = validateAndSanitizeGrievance(grievance);
      if (!sanitizedGrievance) {
        console.error("❌ TimelineService: Invalid grievance data provided");
        throw new Error("Invalid grievance data - cannot generate timeline");
      }

      console.log(
        "🔄 TimelineService: Generating timeline for grievance:",
        sanitizedGrievance._id
      );
      console.log("📊 Input data:", {
        status: grievance.status,
        priority: grievance.priority,
        statusHistoryCount: grievance.statusHistory?.length || 0,
        previousTimelinesCount: grievance.previousTimelines?.length || 0,
        reopenCount: grievance.reopenCount || 0,
      });

      // 🔍 DEBUG: Log the actual data structures
      console.log("🔍 Raw statusHistory:", grievance.statusHistory);
      console.log("🔍 Raw lifecycleHistory:", grievance.lifecycleHistory);
      console.log("🔍 Raw previousTimelines:", grievance.previousTimelines);

      // 🔍 PHASE 2 DIAGNOSTIC: Detailed data validation
      console.log(
        "📊 PHASE 2 DIAGNOSTIC - Input grievance.lifecycleHistory:",
        grievance.lifecycleHistory
      );
      console.log(
        "📊 PHASE 2 DIAGNOSTIC - statusHistory type:",
        typeof grievance.statusHistory
      );
      console.log(
        "📊 PHASE 2 DIAGNOSTIC - statusHistory isArray:",
        Array.isArray(grievance.statusHistory)
      );
      console.log(
        "📊 PHASE 2 DIAGNOSTIC - statusHistory length:",
        grievance.statusHistory?.length || "N/A"
      );

      // Generate timeline entries using sanitized data
      const entries = this.generateTimelineEntries(sanitizedGrievance);

      // Calculate statistics
      const statistics = this.calculateStatistics(entries, grievance);

      // Determine timeline properties
      const isCompleted = isTerminalStatus(grievance.status);
      const isTerminal = [
        "resolved",
        "closed",
        "rejected",
        "cancelled",
      ].includes(grievance.status);

      const timeline: TimelineDisplay = {
        id: `timeline-${grievance._id}-${grievance.reopenCount || 0}`,
        entries,
        startDate: grievance.submittedAt,
        endDate: isCompleted ? grievance.lastUpdatedAt : undefined,
        finalStatus: grievance.status,
        cycleNumber: (grievance.reopenCount || 0) + 1,
        isCompleted,
        isTerminal,
        duration: this.calculateDuration(
          grievance.submittedAt,
          grievance.lastUpdatedAt
        ),
        statistics,
      };

      console.log("✅ TimelineService: Timeline generated:", {
        entriesCount: entries.length,
        completedSteps: statistics.completedSteps,
        totalSteps: statistics.totalSteps,
        progress: `${(
          (statistics.completedSteps / statistics.totalSteps) *
          100
        ).toFixed(1)}%`,
      });

      return timeline;
    } catch (error) {
      console.error(
        "❌ TimelineService: Critical error in generateTimeline:",
        error
      );
      console.error("❌ Grievance data that caused the error:", grievance);

      // Re-throw the error instead of creating mock data
      throw error;
    }
  }

  /**
   * Generate timeline entries from grievance data
   */
  private generateTimelineEntries(grievance: Grievance): TimelineEntry[] {
    const entries: TimelineEntry[] = [];

    // 🛡️ PRODUCTION SAFETY: Validate and sanitize status history
    let statusHistory = validateAndSanitizeStatusHistory(
      grievance.statusHistory
    );

    // Additional safety check
    if (!Array.isArray(statusHistory)) {
      console.error(
        "❌ TimelineService: statusHistory validation failed, using empty array"
      );
      statusHistory = [];
    }

    // 📊 PHASE 2 DIAGNOSTIC: Validate filtered array
    if (!Array.isArray(statusHistory)) {
      console.error(
        "❌ PHASE 2 DIAGNOSTIC: statusHistory is not a valid array after filtering:",
        statusHistory
      );
      return [];
    }

    const currentStatus = grievance.status;

    // 🔍 DEBUG: Log what we're working with
    console.log("🔍 generateTimelineEntries called with:", {
      originalStatusHistory: grievance.statusHistory,
      filteredStatusHistory: statusHistory,
      statusHistoryLength: statusHistory.length,
      currentStatus,
      grievanceId: grievance._id,
    });

    // 📊 PHASE 2 DIAGNOSTIC: Entries received validation
    console.log("📊 PHASE 2 DIAGNOSTIC - Entries received:", statusHistory);
    console.log(
      "📊 PHASE 2 DIAGNOSTIC - Processing",
      statusHistory.length,
      "status history entries"
    );

    // Removed workflow sequence - only use real data

    // Create entries based on status history
    statusHistory.forEach((historyEntry, index) => {
      const entry = this.createTimelineEntry(
        historyEntry,
        index + 1,
        grievance,
        index === statusHistory.length - 1 // is current step
      );
      entries.push(entry);
    });

    // No synthetic data creation - only use real status history

    return entries;
  }

  /**
   * Create timeline entry from status history
   */
  private createTimelineEntry(
    historyEntry: any,
    stepNumber: number,
    grievance: Grievance,
    isCurrentStep: boolean
  ): TimelineEntry {
    const checkpointInfo = getCheckpointInfo(
      historyEntry.status as GrievanceStatus
    );
    const slaStatus = this.calculateSLAStatus(
      historyEntry.changedAt,
      grievance.priority
    );

    return {
      id: `entry-${grievance._id}-${stepNumber}`,
      status: historyEntry.status as GrievanceStatus,
      timestamp: historyEntry.changedAt,
      reason: historyEntry.reason,
      notes: historyEntry.notes,
      triggeredBy: {
        id: historyEntry.userId || "system",
        name: this.getUserName(historyEntry.changedBy),
        role: this.getUserRole(historyEntry.changedBy),
      },
      metadata: {
        stepNumber,
        hasBeenReached: true,
        isCurrentStep,
        duration: this.calculateStepDuration(
          historyEntry,
          grievance.statusHistory
        ),
        slaStatus,
        checkpointInfo,
      },
    };
  }

  // Removed createPendingTimelineEntry - no mock data creation

  /**
   * Calculate timeline statistics
   */
  private calculateStatistics(
    entries: TimelineEntry[],
    grievance: Grievance
  ): TimelineStatistics {
    const completedSteps = entries.filter(
      (entry) => entry.metadata.hasBeenReached
    ).length;
    const totalSteps = entries.length;

    // Calculate average step duration
    const completedEntries = entries.filter(
      (entry) => entry.metadata.hasBeenReached && entry.metadata.duration
    );
    const totalDuration = completedEntries.reduce(
      (sum, entry) => sum + (entry.metadata.duration || 0),
      0
    );
    const averageStepDuration =
      completedEntries.length > 0 ? totalDuration / completedEntries.length : 0;

    // Calculate SLA compliance
    const slaCompliance = this.calculateSLACompliance(grievance);

    return {
      totalSteps,
      completedSteps,
      averageStepDuration,
      totalDuration,
      slaCompliance,
    };
  }

  /**
   * Calculate SLA compliance status
   */
  private calculateSLACompliance(grievance: Grievance) {
    try {
      const submittedDate = new Date(grievance.submittedAt);
      const currentDate = new Date();
      const slaConfig = getSLAConfig(grievance.priority);

      // CRITICAL FIX: Handle undefined slaConfig
      if (!slaConfig || !slaConfig.totalHours) {
        console.warn(
          "⚠️ TimelineService: Invalid SLA config for priority:",
          grievance.priority
        );
        return {
          status: "ON_TRACK" as SLAStatus,
          progress: 0,
          timeRemaining: 72 * 60 * 60 * 1000, // Default 72 hours
          isOverdue: false,
        };
      }

      const totalSlaTime = slaConfig.totalHours * 60 * 60 * 1000; // Convert to milliseconds
      const timeElapsed = currentDate.getTime() - submittedDate.getTime();
      const timeRemaining = totalSlaTime - timeElapsed;
      const isOverdue = timeRemaining < 0;

      // Calculate progress percentage
      const progress = Math.min((timeElapsed / totalSlaTime) * 100, 100);

      // Determine SLA status
      let status: SLAStatus;
      if (progress >= 100) status = "BREACHED";
      else if (progress >= 85) status = "CRITICAL";
      else if (progress >= 70) status = "URGENT";
      else status = "ON_TRACK";

      return {
        status,
        progress,
        timeRemaining: Math.max(timeRemaining, 0),
        isOverdue,
      };
    } catch (error) {
      console.error(
        "❌ TimelineService: Error calculating SLA compliance:",
        error
      );
      return {
        status: "ON_TRACK" as SLAStatus,
        progress: 0,
        timeRemaining: 72 * 60 * 60 * 1000, // Default 72 hours
        isOverdue: false,
      };
    }
  }

  /**
   * Calculate SLA status for specific timestamp
   */
  private calculateSLAStatus(timestamp: string, priority: string): SLAStatus {
    try {
      const date = new Date(timestamp);
      const currentDate = new Date();
      const slaConfig = getSLAConfig(priority as any);

      // CRITICAL FIX: Handle undefined slaConfig
      if (!slaConfig || !slaConfig.totalHours) {
        console.warn(
          "⚠️ TimelineService: Invalid SLA config for priority:",
          priority
        );
        return "ON_TRACK"; // Default fallback
      }

      const totalSlaTime = slaConfig.totalHours * 60 * 60 * 1000;
      const timeElapsed = currentDate.getTime() - date.getTime();
      const progress = (timeElapsed / totalSlaTime) * 100;

      if (progress >= 100) return "BREACHED";
      if (progress >= 85) return "CRITICAL";
      if (progress >= 70) return "URGENT";
      return "ON_TRACK";
    } catch (error) {
      console.error("❌ TimelineService: Error calculating SLA status:", error);
      return "ON_TRACK"; // Safe fallback
    }
  }

  /**
   * Calculate duration between two dates
   */
  private calculateDuration(startDate: string, endDate: string): string {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffMs = end.getTime() - start.getTime();

    const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  }

  /**
   * Calculate step duration
   */
  private calculateStepDuration(
    currentEntry: any,
    statusHistory: any[]
  ): number | undefined {
    // 🔍 DEBUG: Log inputs to identify the issue
    console.log("🔍 calculateStepDuration called with:", {
      currentEntry,
      statusHistory,
      statusHistoryType: typeof statusHistory,
      statusHistoryIsArray: Array.isArray(statusHistory),
    });

    // 📊 PHASE 2 DIAGNOSTIC: Check for this.steps issue
    console.log(
      "📊 PHASE 2 DIAGNOSTIC - Steps:",
      typeof this === "undefined" ? "this is undefined" : "this exists"
    );
    if (typeof this !== "undefined" && "steps" in this) {
      console.log("📊 PHASE 2 DIAGNOSTIC - this.steps:", (this as any).steps);
    } else {
      console.log("📊 PHASE 2 DIAGNOSTIC - No this.steps property found");
    }

    // 🚨 CRITICAL FIX: Validate inputs
    if (!statusHistory || !Array.isArray(statusHistory)) {
      console.error(
        "❌ TimelineService: statusHistory is not a valid array:",
        statusHistory
      );
      return undefined;
    }

    if (!currentEntry) {
      console.error("❌ TimelineService: currentEntry is undefined");
      return undefined;
    }

    const currentIndex = statusHistory.findIndex(
      (entry) => entry === currentEntry
    );
    if (currentIndex <= 0) return undefined;

    const previousEntry = statusHistory[currentIndex - 1];
    const currentTime = new Date(currentEntry.changedAt).getTime();
    const previousTime = new Date(previousEntry.changedAt).getTime();

    return Math.floor((currentTime - previousTime) / (1000 * 60)); // Duration in minutes
  }

  /**
   * Get user name from identifier
   */
  private getUserName(identifier: string): string {
    // This would typically fetch from user service
    // For now, return the identifier or a formatted version
    if (identifier === "system") return "System";
    return identifier || "Unknown User";
  }

  /**
   * Get user role from identifier
   */
  private getUserRole(identifier: string): any {
    // This would typically fetch from user service
    // For now, return a default role
    if (identifier === "system") return "admin";
    return "user";
  }

  /**
   * Generate previous timelines from grievance data
   */
  generatePreviousTimelines(grievance: Grievance): PreviousTimeline[] {
    const previousTimelines: PreviousTimeline[] = [];

    if (grievance.previousTimelines && grievance.previousTimelines.length > 0) {
      grievance.previousTimelines.forEach((timeline, index) => {
        const previousTimeline: PreviousTimeline = {
          id: `prev-timeline-${grievance._id}-${index}`,
          cycleNumber: index + 1,
          startDate: timeline.startDate || grievance.submittedAt,
          endDate: timeline.endDate || timeline.completedAt || "",
          finalStatus: timeline.finalStatus as GrievanceStatus,
          entries: this.generateEntriesFromHistory(
            timeline.statusHistory || []
          ),
          completionReason: timeline.completionReason,
          reopenReason: timeline.reopenReason,
          statistics: this.calculateStatisticsFromHistory(
            timeline.statusHistory || []
          ),
        };

        previousTimelines.push(previousTimeline);
      });
    }

    return previousTimelines;
  }

  /**
   * Generate entries from status history
   */
  private generateEntriesFromHistory(statusHistory: any[]): TimelineEntry[] {
    return statusHistory.map((historyEntry, index) => ({
      id: `hist-entry-${index}`,
      status: historyEntry.status as GrievanceStatus,
      timestamp: historyEntry.changedAt,
      reason: historyEntry.reason,
      triggeredBy: {
        id: historyEntry.userId || "system",
        name: this.getUserName(historyEntry.changedBy),
        role: this.getUserRole(historyEntry.changedBy),
      },
      metadata: {
        stepNumber: index + 1,
        hasBeenReached: true,
        isCurrentStep: false,
        slaStatus: "ON_TRACK",
        checkpointInfo: getCheckpointInfo(
          historyEntry.status as GrievanceStatus
        ),
      },
    }));
  }

  /**
   * Calculate statistics from status history
   */
  private calculateStatisticsFromHistory(
    statusHistory: any[]
  ): TimelineStatistics {
    return {
      totalSteps: statusHistory.length,
      completedSteps: statusHistory.length,
      averageStepDuration: 0, // Would need to calculate from timestamps
      totalDuration: 0, // Would need to calculate from start/end dates
      slaCompliance: {
        status: "ON_TRACK",
        progress: 100,
        timeRemaining: 0,
        isOverdue: false,
      },
    };
  }
}
