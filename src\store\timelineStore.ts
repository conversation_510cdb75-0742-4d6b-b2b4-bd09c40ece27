/**
 * Timeline Store
 * Centralized state management for timeline data
 */

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type {
  TimelineStore,
  TimelineState,
  TimelineDisplay,
  TimelineEntry,
  GrievanceStatus,
  Grievance,
} from "@/types/timeline";
import { TimelineService } from "@/services/newTimelineService";
import apiClient from "@/lib/apiClient";

// Initial state
const initialState: TimelineState = {
  currentTimeline: null,
  previousTimelines: [],
  isLoading: false,
  error: null,
  lastUpdated: null,
};

// Create timeline store
export const useTimelineStore = create<TimelineStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Generate timeline from grievance data
      generateTimeline: (grievance: Grievance): TimelineDisplay => {
        console.log(
          "🔄 TimelineStore: Generating timeline for grievance:",
          grievance._id
        );

        try {
          const timelineService = new TimelineService();
          const timeline = timelineService.generateTimeline(grievance);

          set({
            currentTimeline: timeline,
            error: null,
            lastUpdated: new Date().toISOString(),
          });

          console.log("✅ TimelineStore: Timeline generated successfully");
          return timeline;
        } catch (error) {
          console.error("❌ TimelineStore: Error generating timeline:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to generate timeline",
            currentTimeline: null,
          });
          throw error;
        }
      },

      // Update specific timeline entry
      updateTimelineEntry: (
        entryId: string,
        updates: Partial<TimelineEntry>
      ) => {
        const state = get();
        if (!state.currentTimeline) return;

        console.log("🔄 TimelineStore: Updating timeline entry:", entryId);

        const updatedEntries = state.currentTimeline.entries.map((entry) =>
          entry.id === entryId ? { ...entry, ...updates } : entry
        );

        const updatedTimeline = {
          ...state.currentTimeline,
          entries: updatedEntries,
        };

        set({
          currentTimeline: updatedTimeline,
          lastUpdated: new Date().toISOString(),
        });

        console.log("✅ TimelineStore: Timeline entry updated");
      },

      // Add new timeline entry
      addTimelineEntry: (entry: Omit<TimelineEntry, "id">) => {
        const state = get();
        if (!state.currentTimeline) return;

        console.log("🔄 TimelineStore: Adding timeline entry:", entry.status);

        const newEntry: TimelineEntry = {
          ...entry,
          id: `entry-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        };

        const updatedEntries = [...state.currentTimeline.entries, newEntry];
        const updatedTimeline = {
          ...state.currentTimeline,
          entries: updatedEntries,
        };

        set({
          currentTimeline: updatedTimeline,
          lastUpdated: new Date().toISOString(),
        });

        console.log("✅ TimelineStore: Timeline entry added");
      },

      // Move to next status
      moveToNextStatus: async (
        newStatus: GrievanceStatus
      ): Promise<boolean> => {
        const state = get();
        if (!state.currentTimeline) {
          console.error("❌ TimelineStore: No current timeline available");
          return false;
        }

        console.log("🔄 TimelineStore: Moving to status:", newStatus);
        set({ isLoading: true, error: null });

        try {
          // This will be handled by the grievance store
          // Timeline store just manages the timeline state
          console.log("✅ TimelineStore: Status change initiated");
          return true;
        } catch (error) {
          console.error("❌ TimelineStore: Error changing status:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to change status",
            isLoading: false,
          });
          return false;
        } finally {
          set({ isLoading: false });
        }
      },

      // Refresh timeline from backend
      refreshTimeline: async (grievanceId: string): Promise<void> => {
        console.log(
          "🔄 TimelineStore: Refreshing timeline for grievance:",
          grievanceId
        );
        set({ isLoading: true, error: null });

        try {
          // Fetch fresh grievance data
          const response = await apiClient.get(
            `/grievances/${grievanceId}?_t=${Date.now()}`
          );

          if (response.data.success && response.data.data?.grievance) {
            const grievance = response.data.data.grievance;
            console.log("📦 TimelineStore: Fresh grievance data received:", {
              id: grievance._id,
              status: grievance.status,
              statusHistoryCount: grievance.statusHistory?.length || 0,
            });

            // Generate new timeline
            const timelineService = new TimelineService();
            const timeline = timelineService.generateTimeline(grievance);

            set({
              currentTimeline: timeline,
              isLoading: false,
              error: null,
              lastUpdated: new Date().toISOString(),
            });

            console.log("✅ TimelineStore: Timeline refreshed successfully");
          } else {
            throw new Error("Failed to fetch grievance data");
          }
        } catch (error) {
          console.error("❌ TimelineStore: Error refreshing timeline:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to refresh timeline",
            isLoading: false,
          });
        }
      },

      // Clear timeline data
      clearTimeline: () => {
        console.log("🔄 TimelineStore: Clearing timeline data");
        set({
          ...initialState,
          lastUpdated: new Date().toISOString(),
        });
      },

      // Store management methods
      subscribe: (listener: () => void) => {
        return useTimelineStore.subscribe(listener);
      },

      getState: () => {
        return get();
      },

      setState: (partial: Partial<TimelineState>) => {
        set(partial);
      },
    }),
    {
      name: "timeline-store",
      enabled: process.env.NODE_ENV === "development",
    }
  )
);

// Selector hooks for better performance
export const useCurrentTimeline = () =>
  useTimelineStore((state) => state.currentTimeline);
export const usePreviousTimelines = () =>
  useTimelineStore((state) => state.previousTimelines);
export const useTimelineLoading = () =>
  useTimelineStore((state) => state.isLoading);
export const useTimelineError = () => useTimelineStore((state) => state.error);
export const useTimelineActions = () =>
  useTimelineStore((state) => ({
    generateTimeline: state.generateTimeline,
    updateTimelineEntry: state.updateTimelineEntry,
    addTimelineEntry: state.addTimelineEntry,
    moveToNextStatus: state.moveToNextStatus,
    refreshTimeline: state.refreshTimeline,
    clearTimeline: state.clearTimeline,
  }));

// Timeline store utilities
export const timelineStoreUtils = {
  // Get current timeline status
  getCurrentStatus: (): GrievanceStatus | null => {
    const timeline = useTimelineStore.getState().currentTimeline;
    if (!timeline || timeline.entries.length === 0) return null;

    const currentEntry = timeline.entries.find(
      (entry) => entry.metadata.isCurrentStep
    );
    return currentEntry?.status || null;
  },

  // Get timeline progress percentage
  getProgress: (): number => {
    const timeline = useTimelineStore.getState().currentTimeline;
    if (!timeline) return 0;

    const completedSteps = timeline.entries.filter(
      (entry) => entry.metadata.hasBeenReached
    ).length;
    const totalSteps = timeline.entries.length;

    return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  },

  // Check if timeline is loading
  isLoading: (): boolean => {
    return useTimelineStore.getState().isLoading;
  },

  // Get last update timestamp
  getLastUpdated: (): string | null => {
    return useTimelineStore.getState().lastUpdated;
  },

  // Reset store to initial state
  reset: (): void => {
    useTimelineStore.setState(initialState);
  },
};

// Export store instance for external access
export { useTimelineStore as timelineStore };
