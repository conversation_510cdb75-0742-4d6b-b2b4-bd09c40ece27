/**
 * Unified Timeline Type Definitions
 * Single source of truth for all timeline-related types
 */

// Core timeline entry interface
export interface TimelineEntry {
  id: string;
  status: GrievanceStatus;
  timestamp: string;
  reason?: string;
  notes?: string;
  triggeredBy: {
    id: string;
    name: string;
    role: UserRole;
  };
  metadata: {
    stepNumber: number;
    hasBeenReached: boolean;
    isCurrentStep: boolean;
    duration?: number; // Time spent in this status (minutes)
    slaStatus: SLAStatus;
    checkpointInfo: CheckpointInfo;
  };
}

// Timeline display configuration
export interface TimelineDisplay {
  id: string;
  entries: TimelineEntry[];
  startDate: string;
  endDate?: string;
  finalStatus: GrievanceStatus;
  cycleNumber: number;
  isCompleted: boolean;
  isTerminal: boolean;
  duration?: string;
  statistics: TimelineStatistics;
}

// Previous timeline for reopened grievances
export interface PreviousTimeline {
  id: string;
  cycleNumber: number;
  startDate: string;
  endDate: string;
  finalStatus: GrievanceStatus;
  entries: TimelineEntry[];
  completionReason?: string;
  reopenReason?: string;
  statistics: TimelineStatistics;
}

// Timeline statistics
export interface TimelineStatistics {
  totalSteps: number;
  completedSteps: number;
  averageStepDuration: number; // in minutes
  totalDuration: number; // in minutes
  slaCompliance: {
    status: SLAStatus;
    progress: number;
    timeRemaining: number;
    isOverdue: boolean;
  };
}

// Checkpoint information
export interface CheckpointInfo {
  title: string;
  description: string;
  icon: string;
  color: string;
  weight: number;
  estimatedDuration: number; // in minutes
  requirements?: string[];
  nextPossibleStatuses: GrievanceStatus[];
}

// Status transition configuration
export interface StatusTransition {
  from: GrievanceStatus;
  to: GrievanceStatus;
  label: string;
  description: string;
  icon: string;
  color: string;
  requiresNote: boolean;
  permissions: UserRole[];
  validationRules?: string[];
}

// Timeline configuration
export interface TimelineConfiguration {
  statuses: GrievanceStatus[];
  checkpoints: Record<GrievanceStatus, CheckpointInfo>;
  transitions: StatusTransition[];
  slaConfig: Record<Priority, SLAConfiguration>;
}

// SLA configuration
export interface SLAConfiguration {
  totalHours: number;
  checkpoints: Record<
    GrievanceStatus,
    {
      targetHours: number;
      warningThreshold: number;
      criticalThreshold: number;
    }
  >;
}

// Timeline state management
export interface TimelineState {
  currentTimeline: TimelineDisplay | null;
  previousTimelines: PreviousTimeline[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

// Timeline actions
export interface TimelineActions {
  generateTimeline: (grievance: Grievance) => TimelineDisplay;
  updateTimelineEntry: (
    entryId: string,
    updates: Partial<TimelineEntry>
  ) => void;
  addTimelineEntry: (entry: Omit<TimelineEntry, "id">) => void;
  moveToNextStatus: (
    newStatus: GrievanceStatus,
    reason?: string
  ) => Promise<boolean>;
  refreshTimeline: (grievanceId: string) => Promise<void>;
  clearTimeline: () => void;
}

// Timeline store interface
export interface TimelineStore extends TimelineState, TimelineActions {
  // Store-specific methods
  subscribe: (listener: () => void) => () => void;
  getState: () => TimelineState;
  setState: (partial: Partial<TimelineState>) => void;
}

// Component props interfaces
export interface TimelineTabProps {
  grievanceId: string;
  className?: string;
}

export interface TimelineEntryProps {
  entry: TimelineEntry;
  isLast: boolean;
  onEntryClick?: (entry: TimelineEntry) => void;
  showDetails?: boolean;
  compact?: boolean;
}

export interface PreviousTimelinesProps {
  timelines: PreviousTimeline[];
  expanded: Set<string>;
  onToggleExpansion: (timelineId: string) => void;
}

// Event types for timeline updates
export interface TimelineUpdateEvent {
  type:
    | "STATUS_CHANGE"
    | "ENTRY_ADDED"
    | "ENTRY_UPDATED"
    | "TIMELINE_REFRESHED";
  grievanceId: string;
  data: any;
  timestamp: string;
}

// Supporting types (to be imported from other files)
export type GrievanceStatus =
  | "submitted"
  | "pending"
  | "desk_1"
  | "desk_2"
  | "desk_3"
  | "officer"
  | "in_progress"
  | "resolved"
  | "closed"
  | "rejected"
  | "cancelled"
  | "reopened";

export type UserRole = "user" | "officer" | "admin";

export type Priority = "urgent" | "high" | "medium" | "low";

export type SLAStatus = "ON_TRACK" | "URGENT" | "CRITICAL" | "BREACHED";

// Import from existing grievance types
export interface Grievance {
  _id: string;
  status: GrievanceStatus;
  priority: Priority;
  submittedAt: string;
  lastUpdatedAt: string;
  statusHistory: Array<{
    status: GrievanceStatus;
    changedBy: string;
    changedAt: string;
    reason?: string;
  }>;
  lifecycleHistory?: Array<{
    fromStatus: string;
    toStatus: string;
    triggeredBy: string;
    autoMoved?: boolean;
    reason?: string;
    metadata?: any;
    timestamp: string;
  }>;
  previousTimelines?: any[];
  reopenCount?: number;
  // ... other grievance properties
}
