/**
 * Type Guards and Validation Utilities
 * Production-ready type checking and data validation
 */

import type {
  Grievance,
  TimelineEntry,
  TimelineDisplay,
} from "@/types/timeline";

// ============================================================================
// BASIC TYPE GUARDS
// ============================================================================

export const isString = (value: unknown): value is string => {
  return typeof value === "string";
};

export const isNumber = (value: unknown): value is number => {
  return typeof value === "number" && !isNaN(value);
};

export const isArray = (value: unknown): value is unknown[] => {
  return Array.isArray(value);
};

export const isObject = (value: unknown): value is Record<string, unknown> => {
  return value !== null && typeof value === "object" && !Array.isArray(value);
};

export const isNonEmptyString = (value: unknown): value is string => {
  return isString(value) && value.trim().length > 0;
};

export const isValidDate = (value: unknown): value is string => {
  if (!isString(value)) return false;
  const date = new Date(value);
  return !isNaN(date.getTime());
};

// ============================================================================
// GRIEVANCE TYPE GUARDS
// ============================================================================

export const isValidGrievance = (value: unknown): value is Grievance => {
  if (!isObject(value)) {
    console.warn("🚨 Type Guard: Grievance is not an object");
    return false;
  }

  const grievance = value as Record<string, unknown>;

  // Only require _id and status as absolutely essential fields
  if (!isNonEmptyString(grievance._id)) {
    console.warn("🚨 Type Guard: Grievance missing _id");
    return false;
  }

  if (!isNonEmptyString(grievance.status)) {
    console.warn("🚨 Type Guard: Grievance missing status");
    return false;
  }

  // Other fields are optional for more lenient validation
  // This prevents the component from crashing during data loading/refresh

  return true;
};

export const isValidStatusHistoryEntry = (value: unknown): boolean => {
  if (!isObject(value)) return false;

  const entry = value as Record<string, unknown>;

  // More lenient validation - only require status and some form of timestamp
  return (
    isNonEmptyString(entry.status) &&
    (isValidDate(entry.changedAt) ||
      isValidDate(entry.timestamp) ||
      entry.changedAt ||
      entry.timestamp)
  );
};

// ============================================================================
// TIMELINE TYPE GUARDS
// ============================================================================

export const isValidTimelineEntry = (
  value: unknown
): value is TimelineEntry => {
  if (!isObject(value)) {
    console.warn("🚨 Type Guard: Timeline entry is not an object");
    return false;
  }

  const entry = value as Record<string, unknown>;

  // Required fields
  if (!isNonEmptyString(entry.id)) {
    console.warn("🚨 Type Guard: Timeline entry missing id");
    return false;
  }

  if (!isNonEmptyString(entry.status)) {
    console.warn("🚨 Type Guard: Timeline entry missing status");
    return false;
  }

  if (!isValidDate(entry.timestamp)) {
    console.warn("🚨 Type Guard: Timeline entry has invalid timestamp");
    return false;
  }

  if (!isObject(entry.triggeredBy)) {
    console.warn("🚨 Type Guard: Timeline entry missing triggeredBy");
    return false;
  }

  if (!isObject(entry.metadata)) {
    console.warn("🚨 Type Guard: Timeline entry missing metadata");
    return false;
  }

  return true;
};

export const isValidTimelineDisplay = (
  value: unknown
): value is TimelineDisplay => {
  if (!isObject(value)) {
    console.warn("🚨 Type Guard: Timeline display is not an object");
    return false;
  }

  const timeline = value as Record<string, unknown>;

  // Required fields
  if (!isNonEmptyString(timeline.id)) {
    console.warn("🚨 Type Guard: Timeline display missing id");
    return false;
  }

  if (!isArray(timeline.entries)) {
    console.warn("🚨 Type Guard: Timeline display entries is not an array");
    return false;
  }

  // Validate each entry
  for (const entry of timeline.entries) {
    if (!isValidTimelineEntry(entry)) {
      console.warn("🚨 Type Guard: Invalid timeline entry found");
      return false;
    }
  }

  if (!isValidDate(timeline.startDate)) {
    console.warn("🚨 Type Guard: Timeline display has invalid startDate");
    return false;
  }

  if (!isNonEmptyString(timeline.finalStatus)) {
    console.warn("🚨 Type Guard: Timeline display missing finalStatus");
    return false;
  }

  return true;
};

// ============================================================================
// SAFE DATA EXTRACTION
// ============================================================================

export const safeGetString = (
  obj: unknown,
  key: string,
  defaultValue = ""
): string => {
  if (!isObject(obj)) return defaultValue;
  const value = (obj as Record<string, unknown>)[key];
  return isString(value) ? value : defaultValue;
};

export const safeGetNumber = (
  obj: unknown,
  key: string,
  defaultValue = 0
): number => {
  if (!isObject(obj)) return defaultValue;
  const value = (obj as Record<string, unknown>)[key];
  return isNumber(value) ? value : defaultValue;
};

export const safeGetArray = <T>(
  obj: unknown,
  key: string,
  defaultValue: T[] = []
): T[] => {
  if (!isObject(obj)) return defaultValue;
  const value = (obj as Record<string, unknown>)[key];
  return isArray(value) ? (value as T[]) : defaultValue;
};

export const safeGetObject = (
  obj: unknown,
  key: string,
  defaultValue = {}
): Record<string, unknown> => {
  if (!isObject(obj)) return defaultValue;
  const value = (obj as Record<string, unknown>)[key];
  return isObject(value) ? value : defaultValue;
};

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export const validateAndSanitizeGrievance = (
  grievance: unknown
): Grievance | null => {
  if (!isValidGrievance(grievance)) {
    console.error("🚨 Invalid grievance data provided");
    return null;
  }

  // Additional sanitization can be added here
  return grievance;
};

export const validateAndSanitizeStatusHistory = (
  statusHistory: unknown
): any[] => {
  if (!isArray(statusHistory)) {
    console.warn("🚨 Status history is not an array, returning empty array");
    return [];
  }

  // Filter out invalid entries
  return statusHistory.filter((entry) => {
    if (!isValidStatusHistoryEntry(entry)) {
      console.warn("🚨 Filtering out invalid status history entry:", entry);
      return false;
    }
    return true;
  });
};

export const validateAndSanitizeTimeline = (
  timeline: unknown
): TimelineDisplay | null => {
  if (!isValidTimelineDisplay(timeline)) {
    console.error("🚨 Invalid timeline data provided");
    return null;
  }

  return timeline;
};

// ============================================================================
// ERROR RECOVERY HELPERS
// ============================================================================
// Note: Fallback data creation functions removed to prevent mock data display
