/**
 * Timeline Data Flow Test
 * Test the complete data flow from API to timeline display
 */

import axios from 'axios';

const log = (message, type = 'info') => {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    error: '\x1b[31m',
    warning: '\x1b[33m',
    header: '\x1b[35m\x1b[1m'
  };
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[type]}[${timestamp}] ${message}\x1b[0m`);
};

const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

async function testTimelineDataFlow() {
  log('🚀 TESTING TIMELINE DATA FLOW', 'header');
  console.log('='.repeat(80));

  try {
    // Step 1: Test server health
    log('1️⃣ Testing server health...', 'info');
    const healthResponse = await axios.get('http://localhost:5000/health');
    if (healthResponse.data.success) {
      log('✅ Server is healthy', 'success');
    }

    // Step 2: Try to authenticate
    log('2️⃣ Attempting authentication...', 'info');
    let authToken = null;
    
    try {
      const loginResponse = await apiClient.post('/auth/login', {
        gmail: '<EMAIL>',
        password: 'TestPassword123!'
      });

      if (loginResponse.data.success) {
        authToken = loginResponse.data.data.accessToken;
        log('✅ Authentication successful', 'success');
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
      }
    } catch (authError) {
      log('⚠️  Authentication failed, continuing without auth...', 'warning');
    }

    // Step 3: Get grievances list
    log('3️⃣ Fetching grievances list...', 'info');
    try {
      const grievancesResponse = await apiClient.get('/grievances');
      
      if (grievancesResponse.data.success) {
        const grievances = grievancesResponse.data.data.grievances || [];
        log(`✅ Found ${grievances.length} grievances`, 'success');
        
        if (grievances.length > 0) {
          const testGrievance = grievances[0];
          log(`📋 Testing with grievance: ${testGrievance.referenceId}`, 'info');
          
          // Step 4: Get detailed grievance data
          log('4️⃣ Fetching detailed grievance data...', 'info');
          const detailResponse = await apiClient.get(`/grievances/${testGrievance._id}`);
          
          if (detailResponse.data.success && detailResponse.data.data.grievance) {
            const grievance = detailResponse.data.data.grievance;
            
            log('📊 Grievance Data Analysis:', 'info');
            console.log(`   ID: ${grievance._id}`);
            console.log(`   Reference: ${grievance.referenceId}`);
            console.log(`   Status: ${grievance.status}`);
            console.log(`   Title: ${grievance.title}`);
            console.log(`   Status History: ${grievance.statusHistory?.length || 0} entries`);
            console.log(`   Timeline: ${grievance.timeline?.length || 0} entries`);
            console.log(`   Previous Timelines: ${grievance.previousTimelines?.length || 0} entries`);
            
            // Step 5: Analyze status history structure
            if (grievance.statusHistory && grievance.statusHistory.length > 0) {
              log('5️⃣ Analyzing status history structure...', 'info');
              
              grievance.statusHistory.forEach((entry, index) => {
                console.log(`   Entry ${index + 1}:`);
                console.log(`     Status: ${entry.status || 'MISSING'}`);
                console.log(`     Changed At: ${entry.changedAt || entry.timestamp || 'MISSING'}`);
                console.log(`     Changed By: ${entry.changedBy || 'MISSING'}`);
                console.log(`     Reason: ${entry.reason || entry.notes || 'MISSING'}`);
                console.log(`     Valid: ${entry.status && (entry.changedAt || entry.timestamp) ? '✅' : '❌'}`);
              });
              
              log('✅ Status history analysis complete', 'success');
            } else {
              log('❌ No status history found', 'error');
            }
            
            // Step 6: Test timeline generation
            log('6️⃣ Testing timeline generation...', 'info');
            
            // Simulate the timeline service logic
            const validEntries = grievance.statusHistory?.filter(entry => 
              entry && entry.status && (entry.changedAt || entry.timestamp)
            ) || [];
            
            log(`📊 Timeline Generation Results:`, 'info');
            console.log(`   Original entries: ${grievance.statusHistory?.length || 0}`);
            console.log(`   Valid entries after filtering: ${validEntries.length}`);
            
            if (validEntries.length > 0) {
              log('✅ Timeline data is available for display', 'success');
              validEntries.forEach((entry, index) => {
                console.log(`   Timeline Entry ${index + 1}: ${entry.status} at ${entry.changedAt || entry.timestamp}`);
              });
            } else {
              log('❌ No valid timeline entries found', 'error');
            }
            
          } else {
            log('❌ Failed to fetch detailed grievance data', 'error');
          }
        } else {
          log('❌ No grievances found to test', 'error');
        }
      } else {
        log('❌ Failed to fetch grievances list', 'error');
      }
    } catch (grievanceError) {
      log(`❌ Error fetching grievances: ${grievanceError.message}`, 'error');
    }

    log('🎯 TIMELINE DATA FLOW TEST COMPLETE', 'header');
    
  } catch (error) {
    log(`❌ CRITICAL ERROR: ${error.message}`, 'error');
    console.error('Full error:', error);
  }
}

// Run the test
testTimelineDataFlow();
